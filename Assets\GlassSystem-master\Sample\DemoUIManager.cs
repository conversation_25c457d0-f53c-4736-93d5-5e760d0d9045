using UnityEngine;
using UnityEngine.UI;
using GlassSystem.Scripts;

namespace GlassSystem.Sample
{
    public class DemoUIManager : MonoBehaviour
    {
        [Header("UI Elements")]
        public Canvas uiCanvas;
        public Text instructionText;
        public Text statsText;
        public Text forceText;
        public Slider forceSlider;
        public Button resetButton;
        
        [Header("Settings")]
        public bool createUIOnStart = true;
        public bool showInstructions = true;
        
        private DemoGun demoGun;
        private int totalGlassCount;
        private int brokenGlassCount;
        
        void Start()
        {
            if (createUIOnStart)
            {
                CreateDemoUI();
            }
            
            demoGun = FindObjectOfType<DemoGun>();
            CountGlassObjects();
            
            InvokeRepeating(nameof(UpdateStats), 0f, 0.5f);
        }
        
        void CreateDemoUI()
        {
            // 创建Canvas
            if (uiCanvas == null)
            {
                GameObject canvasGO = new GameObject("Demo UI Canvas");
                uiCanvas = canvasGO.AddComponent<Canvas>();
                uiCanvas.renderMode = RenderMode.ScreenSpaceOverlay;
                canvasGO.AddComponent<CanvasScaler>();
                canvasGO.AddComponent<GraphicRaycaster>();
            }
            
            // 创建说明文本
            if (instructionText == null && showInstructions)
            {
                instructionText = CreateTextElement("Instructions", new Vector2(-300, 200), new Vector2(250, 150));
                instructionText.text = "玻璃破碎演示\n\n" +
                                     "左键: 破碎玻璃\n" +
                                     "WASD: 移动相机\n" +
                                     "空格/C: 上升/下降\n" +
                                     "Shift: 快速移动\n" +
                                     "Ctrl: 慢速移动\n" +
                                     "鼠标: 旋转视角\n" +
                                     "1-5: 调整力度\n" +
                                     "R: 重置位置\n" +
                                     "H: 切换帮助\n" +
                                     "ESC: 解锁鼠标";
                instructionText.fontSize = 14;
                instructionText.color = Color.white;
                
                // 添加背景
                Image bg = instructionText.gameObject.AddComponent<Image>();
                bg.color = new Color(0, 0, 0, 0.7f);
            }
            
            // 创建统计文本
            if (statsText == null)
            {
                statsText = CreateTextElement("Stats", new Vector2(300, 200), new Vector2(200, 100));
                statsText.fontSize = 12;
                statsText.color = Color.yellow;
                statsText.alignment = TextAnchor.UpperRight;
                
                Image statsBg = statsText.gameObject.AddComponent<Image>();
                statsBg.color = new Color(0, 0, 0, 0.5f);
            }
            
            // 创建力度控制
            if (forceSlider == null)
            {
                CreateForceSlider();
            }
            
            // 创建重置按钮
            if (resetButton == null)
            {
                CreateResetButton();
            }
        }
        
        Text CreateTextElement(string name, Vector2 anchoredPosition, Vector2 sizeDelta)
        {
            GameObject textGO = new GameObject(name);
            textGO.transform.SetParent(uiCanvas.transform, false);
            
            RectTransform rectTransform = textGO.AddComponent<RectTransform>();
            rectTransform.anchorMin = new Vector2(0.5f, 0.5f);
            rectTransform.anchorMax = new Vector2(0.5f, 0.5f);
            rectTransform.anchoredPosition = anchoredPosition;
            rectTransform.sizeDelta = sizeDelta;
            
            Text text = textGO.AddComponent<Text>();
            text.font = Resources.GetBuiltinResource<Font>("Arial.ttf");
            text.fontSize = 16;
            text.color = Color.white;
            text.alignment = TextAnchor.UpperLeft;
            
            return text;
        }
        
        void CreateForceSlider()
        {
            // 创建滑块容器
            GameObject sliderGO = new GameObject("Force Slider");
            sliderGO.transform.SetParent(uiCanvas.transform, false);
            
            RectTransform sliderRect = sliderGO.AddComponent<RectTransform>();
            sliderRect.anchorMin = new Vector2(0.5f, 0f);
            sliderRect.anchorMax = new Vector2(0.5f, 0f);
            sliderRect.anchoredPosition = new Vector2(0, 100);
            sliderRect.sizeDelta = new Vector2(300, 20);
            
            forceSlider = sliderGO.AddComponent<Slider>();
            forceSlider.minValue = 100f;
            forceSlider.maxValue = 5000f;
            forceSlider.value = 1000f;
            
            // 创建背景
            GameObject background = new GameObject("Background");
            background.transform.SetParent(sliderGO.transform, false);
            RectTransform bgRect = background.AddComponent<RectTransform>();
            bgRect.anchorMin = Vector2.zero;
            bgRect.anchorMax = Vector2.one;
            bgRect.sizeDelta = Vector2.zero;
            bgRect.anchoredPosition = Vector2.zero;
            Image bgImage = background.AddComponent<Image>();
            bgImage.color = new Color(0.2f, 0.2f, 0.2f, 0.8f);
            forceSlider.targetGraphic = bgImage;
            
            // 创建填充区域
            GameObject fillArea = new GameObject("Fill Area");
            fillArea.transform.SetParent(sliderGO.transform, false);
            RectTransform fillAreaRect = fillArea.AddComponent<RectTransform>();
            fillAreaRect.anchorMin = Vector2.zero;
            fillAreaRect.anchorMax = Vector2.one;
            fillAreaRect.sizeDelta = Vector2.zero;
            fillAreaRect.anchoredPosition = Vector2.zero;
            
            GameObject fill = new GameObject("Fill");
            fill.transform.SetParent(fillArea.transform, false);
            RectTransform fillRect = fill.AddComponent<RectTransform>();
            fillRect.anchorMin = Vector2.zero;
            fillRect.anchorMax = Vector2.one;
            fillRect.sizeDelta = Vector2.zero;
            fillRect.anchoredPosition = Vector2.zero;
            Image fillImage = fill.AddComponent<Image>();
            fillImage.color = new Color(0.2f, 0.8f, 0.2f, 0.8f);
            forceSlider.fillRect = fillRect;
            
            // 创建滑块手柄
            GameObject handleArea = new GameObject("Handle Slide Area");
            handleArea.transform.SetParent(sliderGO.transform, false);
            RectTransform handleAreaRect = handleArea.AddComponent<RectTransform>();
            handleAreaRect.anchorMin = Vector2.zero;
            handleAreaRect.anchorMax = Vector2.one;
            handleAreaRect.sizeDelta = Vector2.zero;
            handleAreaRect.anchoredPosition = Vector2.zero;
            
            GameObject handle = new GameObject("Handle");
            handle.transform.SetParent(handleArea.transform, false);
            RectTransform handleRect = handle.AddComponent<RectTransform>();
            handleRect.anchorMin = new Vector2(0.5f, 0.5f);
            handleRect.anchorMax = new Vector2(0.5f, 0.5f);
            handleRect.sizeDelta = new Vector2(20, 20);
            handleRect.anchoredPosition = Vector2.zero;
            Image handleImage = handle.AddComponent<Image>();
            handleImage.color = Color.white;
            forceSlider.handleRect = handleRect;
            
            // 创建力度显示文本
            forceText = CreateTextElement("Force Text", new Vector2(0, 130), new Vector2(200, 30));
            forceText.alignment = TextAnchor.MiddleCenter;
            forceText.fontSize = 14;
            
            // 绑定事件
            forceSlider.onValueChanged.AddListener(OnForceChanged);
        }
        
        void CreateResetButton()
        {
            GameObject buttonGO = new GameObject("Reset Button");
            buttonGO.transform.SetParent(uiCanvas.transform, false);
            
            RectTransform buttonRect = buttonGO.AddComponent<RectTransform>();
            buttonRect.anchorMin = new Vector2(1f, 1f);
            buttonRect.anchorMax = new Vector2(1f, 1f);
            buttonRect.anchoredPosition = new Vector2(-100, -50);
            buttonRect.sizeDelta = new Vector2(80, 30);
            
            resetButton = buttonGO.AddComponent<Button>();
            Image buttonImage = buttonGO.AddComponent<Image>();
            buttonImage.color = new Color(0.8f, 0.2f, 0.2f, 0.8f);
            
            // 按钮文本
            GameObject textGO = new GameObject("Text");
            textGO.transform.SetParent(buttonGO.transform, false);
            RectTransform textRect = textGO.AddComponent<RectTransform>();
            textRect.anchorMin = Vector2.zero;
            textRect.anchorMax = Vector2.one;
            textRect.sizeDelta = Vector2.zero;
            textRect.anchoredPosition = Vector2.zero;
            
            Text buttonText = textGO.AddComponent<Text>();
            buttonText.text = "重置";
            buttonText.font = Resources.GetBuiltinResource<Font>("Arial.ttf");
            buttonText.fontSize = 14;
            buttonText.color = Color.white;
            buttonText.alignment = TextAnchor.MiddleCenter;
            
            resetButton.targetGraphic = buttonImage;
            resetButton.onClick.AddListener(ResetScene);
        }
        
        void OnForceChanged(float value)
        {
            if (demoGun != null)
            {
                demoGun.impactForce = value;
            }
            
            if (forceText != null)
            {
                forceText.text = $"撞击力度: {value:F0}";
            }
        }
        
        void CountGlassObjects()
        {
            BaseGlass[] glasses = FindObjectsOfType<BaseGlass>();
            totalGlassCount = glasses.Length;
        }
        
        void UpdateStats()
        {
            if (statsText == null) return;
            
            // 计算破碎的玻璃数量
            BaseGlass[] glasses = FindObjectsOfType<BaseGlass>();
            int currentIntact = 0;
            
            foreach (BaseGlass glass in glasses)
            {
                if (glass != null && glass.GetComponent<Renderer>() != null && glass.GetComponent<Renderer>().enabled)
                {
                    currentIntact++;
                }
            }
            
            brokenGlassCount = totalGlassCount - currentIntact;
            
            statsText.text = $"玻璃统计:\n" +
                           $"总数: {totalGlassCount}\n" +
                           $"完整: {currentIntact}\n" +
                           $"已破碎: {brokenGlassCount}\n" +
                           $"FPS: {(1f / Time.deltaTime):F0}";
        }
        
        void ResetScene()
        {
            UnityEngine.SceneManagement.SceneManager.LoadScene(
                UnityEngine.SceneManagement.SceneManager.GetActiveScene().name);
        }
        
        void Update()
        {
            // 切换UI显示
            if (Input.GetKeyDown(KeyCode.Tab))
            {
                if (uiCanvas != null)
                {
                    uiCanvas.gameObject.SetActive(!uiCanvas.gameObject.activeSelf);
                }
            }
        }
    }
}
