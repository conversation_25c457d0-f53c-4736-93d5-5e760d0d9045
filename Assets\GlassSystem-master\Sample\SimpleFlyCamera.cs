using UnityEngine;

namespace GlassSystem.Sample
{
    /// <summary>
    /// 简化版飞行相机 - 专门解决鼠标控制问题
    /// </summary>
    [RequireComponent(typeof(Camera))]
    public class SimpleFlyCamera : MonoBehaviour
    {
        [Header("移动设置")]
        public float moveSpeed = 5f;
        public float fastMoveSpeed = 15f;
        public float slowMoveSpeed = 1f;
        
        [Header("鼠标控制")]
        public float mouseSensitivity = 2f;
        public bool invertY = false;
        
        [Header("自动锁定鼠标")]
        public bool autoLockMouse = true;
        
        private float rotationX = 0f;
        private Vector3 initialPosition;
        private Quaternion initialRotation;
        
        void Start()
        {
            // 保存初始位置
            initialPosition = transform.position;
            initialRotation = transform.rotation;
            
            // 自动锁定鼠标
            if (autoLockMouse)
            {
                LockMouse();
            }
            
            // 获取当前X轴旋转
            rotationX = transform.eulerAngles.x;
            if (rotationX > 180f)
                rotationX -= 360f;
        }
        
        void Update()
        {
            HandleMovement();
            HandleMouseLook();
            HandleInput();
        }
        
        void HandleMovement()
        {
            Vector3 moveDirection = Vector3.zero;
            
            // 获取移动输入
            if (Input.GetKey(KeyCode.W))
                moveDirection += transform.forward;
            if (Input.GetKey(KeyCode.S))
                moveDirection -= transform.forward;
            if (Input.GetKey(KeyCode.A))
                moveDirection -= transform.right;
            if (Input.GetKey(KeyCode.D))
                moveDirection += transform.right;
            if (Input.GetKey(KeyCode.Space))
                moveDirection += Vector3.up;
            if (Input.GetKey(KeyCode.C))
                moveDirection -= Vector3.up;
            
            // 确定移动速度
            float currentSpeed = moveSpeed;
            if (Input.GetKey(KeyCode.LeftShift))
                currentSpeed = fastMoveSpeed;
            else if (Input.GetKey(KeyCode.LeftControl))
                currentSpeed = slowMoveSpeed;
            
            // 应用移动
            if (moveDirection.magnitude > 0.1f)
            {
                moveDirection = moveDirection.normalized;
                transform.position += moveDirection * currentSpeed * Time.deltaTime;
            }
        }
        
        void HandleMouseLook()
        {
            // 只有在鼠标锁定时才处理视角控制
            if (Cursor.lockState == CursorLockMode.Locked)
            {
                // 获取鼠标输入
                float mouseX = Input.GetAxis("Mouse X") * mouseSensitivity;
                float mouseY = Input.GetAxis("Mouse Y") * mouseSensitivity;
                
                if (invertY)
                    mouseY = -mouseY;
                
                // 水平旋转
                transform.Rotate(0, mouseX, 0, Space.World);
                
                // 垂直旋转
                rotationX -= mouseY;
                rotationX = Mathf.Clamp(rotationX, -90f, 90f);
                
                // 应用垂直旋转
                Vector3 eulerAngles = transform.eulerAngles;
                eulerAngles.x = rotationX;
                transform.eulerAngles = eulerAngles;
            }
        }
        
        void HandleInput()
        {
            // ESC键解锁鼠标
            if (Input.GetKeyDown(KeyCode.Escape))
            {
                UnlockMouse();
            }
            
            // L键切换鼠标锁定状态
            if (Input.GetKeyDown(KeyCode.L))
            {
                if (Cursor.lockState == CursorLockMode.Locked)
                    UnlockMouse();
                else
                    LockMouse();
            }
            
            // 右键锁定鼠标
            if (Input.GetMouseButtonDown(1))
            {
                LockMouse();
            }
            
            // R键重置位置
            if (Input.GetKeyDown(KeyCode.R))
            {
                ResetPosition();
            }
        }
        
        void LockMouse()
        {
            Cursor.lockState = CursorLockMode.Locked;
            Cursor.visible = false;
            Debug.Log("鼠标已锁定 - 现在可以控制视角了！");
        }
        
        void UnlockMouse()
        {
            Cursor.lockState = CursorLockMode.None;
            Cursor.visible = true;
            Debug.Log("鼠标已解锁");
        }
        
        void ResetPosition()
        {
            transform.position = initialPosition;
            transform.rotation = initialRotation;
            rotationX = initialRotation.eulerAngles.x;
            if (rotationX > 180f)
                rotationX -= 360f;
            Debug.Log("相机位置已重置");
        }
        
        void OnGUI()
        {
            // 显示控制说明
            float panelWidth = 300f;
            float panelHeight = 200f;
            float margin = 10f;
            
            Rect panelRect = new Rect(margin, margin, panelWidth, panelHeight);
            GUI.Box(panelRect, "简化飞行相机控制");
            
            float yOffset = 30f;
            float lineHeight = 18f;
            
            GUI.Label(new Rect(margin + 10, margin + yOffset, panelWidth - 20, lineHeight), "移动: WASD + 空格/C");
            yOffset += lineHeight;
            GUI.Label(new Rect(margin + 10, margin + yOffset, panelWidth - 20, lineHeight), "速度: Shift(快) / Ctrl(慢)");
            yOffset += lineHeight;
            GUI.Label(new Rect(margin + 10, margin + yOffset, panelWidth - 20, lineHeight), "视角: 鼠标移动（需锁定）");
            yOffset += lineHeight;
            GUI.Label(new Rect(margin + 10, margin + yOffset, panelWidth - 20, lineHeight), "锁定: 右键 或 L键");
            yOffset += lineHeight;
            GUI.Label(new Rect(margin + 10, margin + yOffset, panelWidth - 20, lineHeight), "解锁: ESC键");
            yOffset += lineHeight;
            GUI.Label(new Rect(margin + 10, margin + yOffset, panelWidth - 20, lineHeight), "重置: R键");
            yOffset += lineHeight;
            
            yOffset += 10;
            string lockStatus = Cursor.lockState == CursorLockMode.Locked ? "已锁定" : "未锁定";
            Color originalColor = GUI.color;
            GUI.color = Cursor.lockState == CursorLockMode.Locked ? Color.green : Color.red;
            GUI.Label(new Rect(margin + 10, margin + yOffset, panelWidth - 20, lineHeight), $"鼠标状态: {lockStatus}");
            GUI.color = originalColor;
            
            yOffset += lineHeight;
            GUI.Label(new Rect(margin + 10, margin + yOffset, panelWidth - 20, lineHeight), $"当前速度: {(Input.GetKey(KeyCode.LeftShift) ? fastMoveSpeed : Input.GetKey(KeyCode.LeftControl) ? slowMoveSpeed : moveSpeed)}m/s");
            
            // 如果鼠标未锁定，显示提示
            if (Cursor.lockState != CursorLockMode.Locked)
            {
                yOffset += lineHeight + 10;
                GUI.color = Color.yellow;
                GUI.Label(new Rect(margin + 10, margin + yOffset, panelWidth - 20, lineHeight * 2), "提示: 按右键或L键锁定鼠标\n才能控制视角！");
                GUI.color = originalColor;
            }
        }
        
        // 公共方法
        public void SetMouseSensitivity(float sensitivity)
        {
            mouseSensitivity = sensitivity;
        }
        
        public void SetMoveSpeed(float normal, float fast, float slow)
        {
            moveSpeed = normal;
            fastMoveSpeed = fast;
            slowMoveSpeed = slow;
        }
        
        public bool IsMouseLocked()
        {
            return Cursor.lockState == CursorLockMode.Locked;
        }
    }
}
