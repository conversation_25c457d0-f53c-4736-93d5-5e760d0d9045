# Glass System Master 演示场景设置指南

## 快速开始

### 方法一：自动设置（推荐）

1. 在Unity中创建一个新的空场景
2. 创建一个空的GameObject
3. 添加 `CompleteDemoSetup` 脚本到该GameObject
4. 在Inspector中确保 `Setup On Start` 已勾选
5. 运行场景，演示场景将自动创建

### 方法二：手动设置

1. 创建一个空的GameObject，添加 `DemoSceneSetup` 脚本
2. 创建另一个GameObject，添加 `DemoUIManager` 脚本
3. 在相机上添加 `FreeFlyCamera` 和 `DemoGun` 脚本
4. 运行场景

### 方法三：URP材质设置

1. 如果使用URP渲染管线，添加 `URPMaterialCreator` 脚本
2. 在Inspector中配置材质参数
3. 点击"创建所有URP材质"按钮
4. 然后按照方法一或方法二设置场景

## 演示场景包含的内容

### 环境
- 地面平台
- 背景墙和侧墙
- 适当的光照设置

### 玻璃对象
- 大型窗户（生命值：3）
- 中型窗户（生命值：2）
- 小型窗户（生命值：1）
- 侧面玻璃板
- 悬浮玻璃板
- 倾斜玻璃板

### 交互系统
- 鼠标点击破碎玻璃
- 力度调节滑块
- 实时统计显示
- 场景重置功能

## 控制说明

### 基本控制
- **左键点击**：破碎玻璃
- **WASD**：移动相机
- **鼠标移动**：旋转视角
- **鼠标滚轮**：缩放视角

### 快捷键
- **1-5键**：快速调整撞击力度
  - 1键：500力度
  - 2键：1000力度
  - 3键：2000力度
  - 4键：3000力度
  - 5键：5000力度
- **R键**：重置场景
- **C键**：切换相机视角（如果有多个预设位置）
- **Tab键**：切换UI显示/隐藏
- **ESC键**：解锁/锁定鼠标

### UI界面
- **左上角**：操作说明
- **右上角**：统计信息（玻璃数量、FPS等）
- **底部**：力度调节滑块
- **右上角**：重置按钮

## 脚本说明

### CompleteDemoSetup.cs
- 一键创建完整演示场景的主脚本
- 包含环境、玻璃、相机、UI、光照的完整设置
- 可通过Inspector面板自定义各个组件的创建
- 自动检测并创建URP材质

### DemoSceneSetup.cs
- 场景环境和玻璃对象的创建脚本
- 负责生成地面、墙壁和各种玻璃窗户
- 支持URP材质自动创建和回退机制

### DemoUIManager.cs
- UI界面管理脚本
- 创建和管理所有UI元素
- 实时更新统计信息

### DemoGun.cs（已增强）
- 玻璃破碎交互脚本
- 支持力度调节、音效播放、重试机制
- 包含键盘快捷键支持

### FreeFlyCamera.cs
- 自由飞行相机控制脚本
- 支持WASD移动和鼠标视角控制

### URPMaterialCreator.cs（新增）
- 专门用于创建和配置URP材质的脚本
- 支持透明玻璃材质和不透明环境材质
- 包含URP着色器检测和回退机制
- 提供编辑器工具用于创建材质资源文件

## 自定义设置

### 修改玻璃属性
在 `CreateGlassWindow` 方法中可以调整：
- 位置和大小
- 生命值（health）
- 使用的破碎图案

### 修改环境
在 `CreateEnvironment` 方法中可以调整：
- 地面和墙壁的大小
- 材质和颜色
- 布局和位置

### 修改UI
在 `DemoUIManager` 中可以调整：
- UI元素的位置和大小
- 颜色和字体
- 显示的信息内容

## 故障排除

### 常见问题

1. **玻璃不能破碎**
   - 检查是否正确加载了破碎图案（GlassPattern1.mesh, GlassPattern2.mesh）
   - 确保玻璃对象有正确的碰撞器

2. **材质显示不正确**
   - 检查Resources文件夹中的材质文件（Glass.mat, GlassSide.mat）
   - 确保材质正确分配给了玻璃对象

3. **UI不显示**
   - 检查Canvas是否正确创建
   - 确保UI元素的层级关系正确

4. **相机控制不响应**
   - 检查FreeFlyCamera脚本是否正确添加
   - 确保鼠标锁定状态正确

### 性能优化建议

1. 限制同时存在的玻璃碎片数量
2. 调整碎片的生存时间
3. 使用对象池管理碎片
4. 根据距离调整碎片的细节级别

## 扩展功能建议

1. 添加音效系统
2. 添加粒子特效
3. 添加更多玻璃类型
4. 添加物理交互（如球体撞击）
5. 添加VR支持

## 技术要求

- Unity 2019.4 或更高版本
- Glass System Master 插件
- 标准渲染管线或URP支持

## 联系和支持

如果遇到问题或需要更多功能，请参考Glass System Master的官方文档或GitHub仓库。
