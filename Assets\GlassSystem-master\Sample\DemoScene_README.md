# Glass System Master 演示场景设置指南

## 快速开始

### 方法一：自动设置（推荐）

1. 在Unity中创建一个新的空场景
2. 创建一个空的GameObject
3. 添加 `CompleteDemoSetup` 脚本到该GameObject
4. 在Inspector中确保 `Setup On Start` 已勾选
5. 运行场景，演示场景将自动创建

### 方法二：手动设置

1. 创建一个空的GameObject，添加 `DemoSceneSetup` 脚本
2. 创建另一个GameObject，添加 `DemoUIManager` 脚本
3. 在相机上添加 `FreeFlyCamera` 和 `DemoGun` 脚本
4. 运行场景

### 方法三：URP材质设置

1. 如果使用URP渲染管线，添加 `URPMaterialCreator` 脚本
2. 在Inspector中配置材质参数
3. 点击"创建所有URP材质"按钮
4. 然后按照方法一或方法二设置场景

## 演示场景包含的内容

### 环境
- 地面平台
- 背景墙和侧墙
- 适当的光照设置

### 玻璃对象
- 大型窗户（生命值：3，递归深度：3）
- 中型窗户（生命值：2，递归深度：3）
- 小型窗户（生命值：1，递归深度：3）
- 侧面玻璃板（支持递归破碎）
- 悬浮玻璃板（支持递归破碎）
- 倾斜玻璃板（支持递归破碎）

### 递归破碎功能
- **碎片可重复破碎**：破碎后的碎片可以再次被击中破碎
- **多层级破碎**：支持最多3层递归破碎
- **智能尺寸控制**：太小的碎片不会继续破碎
- **视觉深度指示**：不同深度的碎片显示不同颜色标记

### 交互系统
- 鼠标点击破碎玻璃
- 力度调节滑块
- 实时统计显示
- 场景重置功能

## 控制说明

### 基本控制
- **左键点击**：破碎玻璃
- **WASD**：移动相机（前进/后退/左移/右移）
- **空格键/C键**：上升/下降
- **鼠标移动**：旋转视角（需要锁定鼠标）

### 快捷键
- **1-5键**：快速调整撞击力度
  - 1键：500力度
  - 2键：1000力度
  - 3键：2000力度
  - 4键：3000力度
  - 5键：5000力度
- **Shift键**：快速移动（15m/s）
- **Ctrl键**：慢速移动（1m/s）
- **R键**：重置相机位置
- **H键**：切换控制帮助显示
- **Tab键**：切换UI显示/隐藏
- **ESC键**：解锁鼠标
- **右键/L键**：锁定鼠标（用于控制视角）

### UI界面
- **左上角**：操作说明
- **右上角**：统计信息（玻璃数量、FPS等）
- **底部**：力度调节滑块
- **右上角**：重置按钮

## 脚本说明

### CompleteDemoSetup.cs
- 一键创建完整演示场景的主脚本
- 包含环境、玻璃、相机、UI、光照的完整设置
- 可通过Inspector面板自定义各个组件的创建
- 自动检测并创建URP材质

### DemoSceneSetup.cs
- 场景环境和玻璃对象的创建脚本
- 负责生成地面、墙壁和各种玻璃窗户
- 支持URP材质自动创建和回退机制

### DemoUIManager.cs
- UI界面管理脚本
- 创建和管理所有UI元素
- 实时更新统计信息

### DemoGun.cs（已增强）
- 玻璃破碎交互脚本
- 支持力度调节、音效播放、重试机制
- 包含键盘快捷键支持

### FreeFlyCamera.cs（原版）
- 原始的自由飞行相机控制脚本
- 使用AZERTY键盘布局（已修正为QWERTY）

### ImprovedFlyCamera.cs（高级版）
- 改进的飞行相机控制脚本
- 标准FPS游戏键位（WASD移动）
- 平滑加速/减速系统
- 可调节的移动速度（普通/快速/慢速）
- 内置控制帮助显示
- 更好的鼠标锁定控制

### SimpleFlyCamera.cs（推荐）
- 简化版飞行相机，专门解决鼠标控制问题
- 标准FPS键位，简单易用
- 自动鼠标锁定功能
- 清晰的状态提示
- 与DemoGun脚本无冲突

### RecursiveGlassPanel.cs（新增）
- 支持递归破碎的玻璃面板
- 碎片可以重复破碎多次
- 可配置的递归深度和最小碎片尺寸
- 智能的碎片管理系统

### RecursiveShard.cs（新增）
- 可重复破碎的玻璃碎片
- 支持多层级递归破碎
- 自动深度管理和颜色标识
- 物理效果递减系统

### URPMaterialCreator.cs（新增）
- 专门用于创建和配置URP材质的脚本
- 支持透明玻璃材质和不透明环境材质
- 包含URP着色器检测和回退机制
- 提供编辑器工具用于创建材质资源文件

## 递归破碎使用指南

### 基本使用
1. **破碎玻璃**：左键点击玻璃进行初次破碎
2. **破碎碎片**：继续左键点击生成的碎片进行二次破碎
3. **观察深度**：注意碎片上的深度标记（D0, D1, D2等）
4. **多层破碎**：可以连续破碎同一个碎片多次

### 配置参数
- **递归深度**：最大破碎层级（默认3层）
- **最小尺寸**：碎片的最小可破碎尺寸
- **自动销毁**：是否自动销毁小碎片
- **力度递减**：每层破碎的力度衰减

### 视觉指示
- **深度标记**：碎片显示D0、D1、D2等深度标识
- **颜色编码**：不同深度的碎片在Scene视图中显示不同颜色
- **状态面板**：左下角显示递归破碎的实时状态

## 自定义设置

### 修改玻璃属性
在 `CreateGlassWindow` 方法中可以调整：
- 位置和大小
- 生命值（health）
- 使用的破碎图案
- 递归破碎参数

### 修改环境
在 `CreateEnvironment` 方法中可以调整：
- 地面和墙壁的大小
- 材质和颜色
- 布局和位置

### 修改UI
在 `DemoUIManager` 中可以调整：
- UI元素的位置和大小
- 颜色和字体
- 显示的信息内容

### 修改递归破碎设置
```csharp
RecursiveGlassPanel glassPanel = GetComponent<RecursiveGlassPanel>();
glassPanel.SetRecursiveBreaking(true);        // 启用递归破碎
glassPanel.SetMaxRecursionDepth(5);           // 设置最大深度
glassPanel.SetMinShardSize(0.03f);            // 设置最小碎片尺寸
glassPanel.SetAutoDestroy(false);             // 禁用自动销毁
```

## 故障排除

### 常见问题

1. **玻璃不能破碎**
   - 检查是否正确加载了破碎图案（GlassPattern1.mesh, GlassPattern2.mesh）
   - 确保玻璃对象有正确的碰撞器

2. **材质显示不正确**
   - 检查Resources文件夹中的材质文件（Glass.mat, GlassSide.mat）
   - 确保材质正确分配给了玻璃对象

3. **UI不显示**
   - 检查Canvas是否正确创建
   - 确保UI元素的层级关系正确

4. **鼠标无法控制视角**
   - **首先确保鼠标已锁定**：按右键或L键锁定鼠标
   - 检查Console是否显示"鼠标已锁定"消息
   - 确保使用SimpleFlyCamera脚本（推荐）
   - 如果仍有问题，按ESC解锁后重新锁定

5. **相机控制不响应**
   - 检查是否有多个相机控制脚本冲突
   - 确保只使用一个飞行相机脚本
   - 重启场景或重置相机位置（R键）

### 性能优化建议

1. 限制同时存在的玻璃碎片数量
2. 调整碎片的生存时间
3. 使用对象池管理碎片
4. 根据距离调整碎片的细节级别

## 扩展功能建议

1. 添加音效系统
2. 添加粒子特效
3. 添加更多玻璃类型
4. 添加物理交互（如球体撞击）
5. 添加VR支持

## URP支持说明

### URP材质特性
- 自动检测URP渲染管线
- 创建透明玻璃材质（支持Alpha混合）
- 优化的环境材质（地面、墙壁）
- 回退机制：如果URP不可用，自动使用标准着色器

### URP设置步骤
1. 确保项目使用URP渲染管线
2. 使用 `URPMaterialCreator` 脚本创建URP材质
3. 或者让 `CompleteDemoSetup` 自动检测并创建URP材质

### URP着色器支持
- Universal Render Pipeline/Lit（主要）
- Universal Render Pipeline/Simple Lit（备选）
- URP/Lit（备选）
- Shader Graphs/Lit（备选）

## 技术要求

- Unity 2019.4 或更高版本
- Glass System Master 插件
- 支持标准渲染管线和URP渲染管线
- 推荐使用URP以获得更好的透明效果

## 联系和支持

如果遇到问题或需要更多功能，请参考Glass System Master的官方文档或GitHub仓库。
