using UnityEngine;
using UnityEngine.UI;

namespace GlassSystem.Sample
{
    /// <summary>
    /// 递归破碎演示脚本 - 展示和控制递归破碎功能
    /// </summary>
    public class RecursiveBreakingDemo : MonoBehaviour
    {
        [Header("演示设置")]
        public bool showDemoUI = true;
        public bool enableDebugInfo = true;
        
        [Header("递归破碎控制")]
        [SerializeField] private bool globalRecursiveBreaking = true;
        [SerializeField] private int globalMaxDepth = 3;
        [SerializeField] private float globalMinShardSize = 0.05f;
        [SerializeField] private bool globalAutoDestroy = false;
        
        private RecursiveGlassPanel[] allGlassPanels;
        private SimpleRecursiveGlass[] allSimpleGlasses;
        private int totalShards = 0;
        private int totalBreakEvents = 0;
        
        void Start()
        {
            // 查找所有递归玻璃面板
            RefreshGlassPanels();
            
            // 应用全局设置
            ApplyGlobalSettings();
        }
        
        void Update()
        {
            // 快捷键控制
            HandleKeyboardInput();
            
            // 更新统计信息
            if (enableDebugInfo)
            {
                UpdateStatistics();
            }
        }
        
        void HandleKeyboardInput()
        {
            // F1: 切换递归破碎
            if (Input.GetKeyDown(KeyCode.F1))
            {
                ToggleRecursiveBreaking();
            }
            
            // F2: 增加最大深度
            if (Input.GetKeyDown(KeyCode.F2))
            {
                ChangeMaxDepth(1);
            }
            
            // F3: 减少最大深度
            if (Input.GetKeyDown(KeyCode.F3))
            {
                ChangeMaxDepth(-1);
            }
            
            // F4: 切换自动销毁
            if (Input.GetKeyDown(KeyCode.F4))
            {
                ToggleAutoDestroy();
            }
            
            // F5: 重置所有玻璃
            if (Input.GetKeyDown(KeyCode.F5))
            {
                ResetAllGlass();
            }
        }
        
        void RefreshGlassPanels()
        {
            allGlassPanels = FindObjectsOfType<RecursiveGlassPanel>();
            allSimpleGlasses = FindObjectsOfType<SimpleRecursiveGlass>();
            Debug.Log($"找到 {allGlassPanels.Length} 个递归玻璃面板和 {allSimpleGlasses.Length} 个简单递归玻璃");
        }
        
        void ApplyGlobalSettings()
        {
            foreach (var panel in allGlassPanels)
            {
                if (panel != null)
                {
                    panel.SetRecursiveBreaking(globalRecursiveBreaking);
                    panel.SetMaxRecursionDepth(globalMaxDepth);
                    panel.SetMinShardSize(globalMinShardSize);
                    panel.SetAutoDestroy(globalAutoDestroy);
                }
            }

            foreach (var glass in allSimpleGlasses)
            {
                if (glass != null)
                {
                    glass.SetRecursiveBreaking(globalRecursiveBreaking);
                    glass.SetMaxRecursionDepth(globalMaxDepth);
                    glass.SetMinShardSize(globalMinShardSize);
                    glass.SetAutoDestroy(globalAutoDestroy);
                }
            }

            Debug.Log($"已应用全局设置到 {allGlassPanels.Length} 个递归玻璃面板和 {allSimpleGlasses.Length} 个简单递归玻璃");
        }
        
        void ToggleRecursiveBreaking()
        {
            globalRecursiveBreaking = !globalRecursiveBreaking;
            ApplyGlobalSettings();
            Debug.Log($"递归破碎: {(globalRecursiveBreaking ? "启用" : "禁用")}");
        }
        
        void ChangeMaxDepth(int delta)
        {
            globalMaxDepth = Mathf.Clamp(globalMaxDepth + delta, 1, 10);
            ApplyGlobalSettings();
            Debug.Log($"最大递归深度: {globalMaxDepth}");
        }
        
        void ToggleAutoDestroy()
        {
            globalAutoDestroy = !globalAutoDestroy;
            ApplyGlobalSettings();
            Debug.Log($"自动销毁: {(globalAutoDestroy ? "启用" : "禁用")}");
        }
        
        void ResetAllGlass()
        {
            // 重新加载场景来重置所有玻璃
            UnityEngine.SceneManagement.SceneManager.LoadScene(
                UnityEngine.SceneManagement.SceneManager.GetActiveScene().name);
        }
        
        void UpdateStatistics()
        {
            // 统计当前场景中的碎片数量
            RecursiveShard[] allShards = FindObjectsOfType<RecursiveShard>();
            SimpleRecursiveShardComponent[] allSimpleShards = FindObjectsOfType<SimpleRecursiveShardComponent>();
            totalShards = allShards.Length + allSimpleShards.Length;
        }
        
        void OnGUI()
        {
            if (!showDemoUI) return;
            
            // 主控制面板
            float panelWidth = 320f;
            float panelHeight = 280f;
            float margin = 10f;
            
            Rect panelRect = new Rect(Screen.width - panelWidth - margin, margin, panelWidth, panelHeight);
            GUI.Box(panelRect, "递归破碎控制面板");
            
            float yOffset = 30f;
            float lineHeight = 20f;
            float buttonWidth = 80f;
            float labelWidth = 200f;
            
            // 递归破碎开关
            GUI.Label(new Rect(panelRect.x + 10, panelRect.y + yOffset, labelWidth, lineHeight), 
                     $"递归破碎: {(globalRecursiveBreaking ? "启用" : "禁用")}");
            if (GUI.Button(new Rect(panelRect.x + labelWidth + 10, panelRect.y + yOffset, buttonWidth, lineHeight), 
                          globalRecursiveBreaking ? "禁用" : "启用"))
            {
                ToggleRecursiveBreaking();
            }
            yOffset += lineHeight + 5;
            
            // 最大深度控制
            GUI.Label(new Rect(panelRect.x + 10, panelRect.y + yOffset, labelWidth, lineHeight), 
                     $"最大深度: {globalMaxDepth}");
            if (GUI.Button(new Rect(panelRect.x + labelWidth + 10, panelRect.y + yOffset, 35, lineHeight), "-"))
            {
                ChangeMaxDepth(-1);
            }
            if (GUI.Button(new Rect(panelRect.x + labelWidth + 50, panelRect.y + yOffset, 35, lineHeight), "+"))
            {
                ChangeMaxDepth(1);
            }
            yOffset += lineHeight + 5;
            
            // 最小碎片尺寸
            GUI.Label(new Rect(panelRect.x + 10, panelRect.y + yOffset, labelWidth, lineHeight), 
                     $"最小尺寸: {globalMinShardSize:F3}");
            globalMinShardSize = GUI.HorizontalSlider(
                new Rect(panelRect.x + 10, panelRect.y + yOffset + 20, labelWidth, 20), 
                globalMinShardSize, 0.01f, 0.2f);
            if (GUI.Button(new Rect(panelRect.x + labelWidth + 10, panelRect.y + yOffset, buttonWidth, lineHeight), "应用"))
            {
                ApplyGlobalSettings();
            }
            yOffset += lineHeight + 25;
            
            // 自动销毁开关
            GUI.Label(new Rect(panelRect.x + 10, panelRect.y + yOffset, labelWidth, lineHeight), 
                     $"自动销毁: {(globalAutoDestroy ? "启用" : "禁用")}");
            if (GUI.Button(new Rect(panelRect.x + labelWidth + 10, panelRect.y + yOffset, buttonWidth, lineHeight), 
                          globalAutoDestroy ? "禁用" : "启用"))
            {
                ToggleAutoDestroy();
            }
            yOffset += lineHeight + 10;
            
            // 分隔线
            GUI.Box(new Rect(panelRect.x + 10, panelRect.y + yOffset, panelWidth - 20, 1), "");
            yOffset += 10;
            
            // 统计信息
            GUI.Label(new Rect(panelRect.x + 10, panelRect.y + yOffset, panelWidth - 20, lineHeight), 
                     "=== 统计信息 ===");
            yOffset += lineHeight;
            
            GUI.Label(new Rect(panelRect.x + 10, panelRect.y + yOffset, panelWidth - 20, lineHeight), 
                     $"玻璃面板: {allGlassPanels?.Length ?? 0}");
            yOffset += lineHeight;
            
            GUI.Label(new Rect(panelRect.x + 10, panelRect.y + yOffset, panelWidth - 20, lineHeight), 
                     $"当前碎片: {totalShards}");
            yOffset += lineHeight;
            
            // 控制按钮
            yOffset += 10;
            if (GUI.Button(new Rect(panelRect.x + 10, panelRect.y + yOffset, 90, 25), "重置场景"))
            {
                ResetAllGlass();
            }
            
            if (GUI.Button(new Rect(panelRect.x + 110, panelRect.y + yOffset, 90, 25), "刷新面板"))
            {
                RefreshGlassPanels();
                ApplyGlobalSettings();
            }
            
            // 快捷键说明
            float helpY = Screen.height - 120;
            GUI.Box(new Rect(Screen.width - panelWidth - margin, helpY, panelWidth, 110), "快捷键说明");
            
            yOffset = 20;
            GUI.Label(new Rect(Screen.width - panelWidth - margin + 10, helpY + yOffset, panelWidth - 20, lineHeight), 
                     "F1: 切换递归破碎");
            yOffset += lineHeight;
            GUI.Label(new Rect(Screen.width - panelWidth - margin + 10, helpY + yOffset, panelWidth - 20, lineHeight), 
                     "F2/F3: 增加/减少最大深度");
            yOffset += lineHeight;
            GUI.Label(new Rect(Screen.width - panelWidth - margin + 10, helpY + yOffset, panelWidth - 20, lineHeight), 
                     "F4: 切换自动销毁");
            yOffset += lineHeight;
            GUI.Label(new Rect(Screen.width - panelWidth - margin + 10, helpY + yOffset, panelWidth - 20, lineHeight), 
                     "F5: 重置场景");
        }
        
        // 公共方法供其他脚本调用
        public void SetGlobalRecursiveBreaking(bool enabled)
        {
            globalRecursiveBreaking = enabled;
            ApplyGlobalSettings();
        }
        
        public void SetGlobalMaxDepth(int depth)
        {
            globalMaxDepth = Mathf.Clamp(depth, 1, 10);
            ApplyGlobalSettings();
        }
        
        public void SetGlobalMinShardSize(float size)
        {
            globalMinShardSize = Mathf.Clamp(size, 0.01f, 0.5f);
            ApplyGlobalSettings();
        }
        
        public int GetTotalShards()
        {
            return totalShards;
        }
        
        public int GetTotalGlassPanels()
        {
            return (allGlassPanels?.Length ?? 0) + (allSimpleGlasses?.Length ?? 0);
        }
    }
}
