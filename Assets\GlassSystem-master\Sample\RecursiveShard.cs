using System;
using System.Collections.Generic;
using System.Linq;
using MathNet.Spatial.Euclidean;
using UnityEngine;
using GlassSystem.Scripts;

namespace GlassSystem.Sample
{
    /// <summary>
    /// 递归碎片 - 可以重复破碎的玻璃碎片
    /// </summary>
    public class RecursiveShard : BaseGlass
    {
        [Header("递归设置")]
        public RecursiveGlassPanel parentPanel;
        public int recursionDepth = 0;
        public int maxRecursionDepth = 3;
        public float minShardSizeForBreaking = 0.05f;
        public float recursiveBreakForceMultiplier = 0.8f;
        public bool enableRecursiveBreaking = true;
        
        private bool hasBeenBroken = false;

        public void InitializeShard(RecursiveGlassPanel parentPanel, Polygon2D polygon, Vector2[] uvs, float thickness)
        {
            this.parentPanel = parentPanel;
            _polygon = polygon;
            _uvs = uvs;
            _thickness = thickness;
        }

        public override void Break(Vector3 breakPosition, Vector3 originVector, int patternIndex = -1, float rotation = float.NaN)
        {
            if (hasBeenBroken)
            {
                Debug.Log($"碎片已经破碎过了 (深度: {recursionDepth})");
                return;
            }
            
            if (!enableRecursiveBreaking)
            {
                Debug.Log("递归破碎已禁用");
                return;
            }
            
            if (recursionDepth >= maxRecursionDepth)
            {
                Debug.Log($"已达到最大递归深度 ({maxRecursionDepth})");
                return;
            }
            
            // 检查碎片大小
            float shardSurface = GetShardSurface();
            if (shardSurface < minShardSizeForBreaking)
            {
                Debug.Log($"碎片太小无法破碎 (表面积: {shardSurface:F3}, 最小: {minShardSizeForBreaking:F3})");
                return;
            }
            
            Debug.Log($"递归破碎碎片 - 深度: {recursionDepth}, 表面积: {shardSurface:F3}");
            
            hasBeenBroken = true;
            
            // 执行递归破碎
            PerformRecursiveBreak(breakPosition, originVector, patternIndex, rotation);
            
            // 通知父面板
            if (parentPanel != null)
            {
                parentPanel.OnRecursiveShardDestroyed(this);
            }
            
            // 销毁当前碎片
            Destroy(gameObject);
        }
        
        private void PerformRecursiveBreak(Vector3 breakPosition, Vector3 originVector, int patternIndex, float rotation)
        {
            Vector3 localPosition = transform.InverseTransformPoint(breakPosition);
            var scale = transform.lossyScale;
            localPosition.x *= scale.x;
            localPosition.y *= scale.y;
            
            if (_polygon == null)
            {
                Debug.LogWarning("碎片多边形为空，无法进行递归破碎");
                return;
            }
            
            if (patternIndex == -1)
                patternIndex = UnityEngine.Random.Range(0, Patterns.Length);
            if (float.IsNaN(rotation))
                rotation = UnityEngine.Random.Range(0, 360f);
            
            var lines = ClipPattern.Clip(Patterns[patternIndex], _polygon, localPosition, rotation);
            List<Polygon2D> shardPolygons = ShardPolygonBuilder.Build(lines, 0.001f);
            
            var materials = GetComponent<Renderer>().sharedMaterials;
            
            foreach (Polygon2D shardPolygon in shardPolygons)
            {
                var center = Point2D.Centroid(shardPolygon.Vertices);
                var centeredShardPolygon = shardPolygon.TranslateBy(-center.ToVector2D());
                Vector2[] uvs = null;
                if (_uvs != null)
                    uvs = shardPolygon.Vertices.Select(InterpolateUv).ToArray();
                
                var shardMesh = CreateRecursiveMesh(centeredShardPolygon, uvs, _thickness);
                var newShard = SpawnChildShard(shardMesh, originVector, new Vector3((float)center.X, (float)center.Y, 0), materials);
                
                if (newShard != null)
                {
                    newShard.InitializeShard(parentPanel, centeredShardPolygon, uvs, _thickness);
                    if (parentPanel != null)
                    {
                        parentPanel.OnNewRecursiveShard(newShard, recursionDepth + 1);
                    }
                }
            }
        }
        
        private RecursiveShard SpawnChildShard(Mesh mesh, Vector3 originVector, Vector3 offset, Material[] materials)
        {
            float shardSurface = mesh.bounds.size.x * mesh.bounds.size.y;
            
            var go = new GameObject($"SubShard_D{recursionDepth + 1}_{mesh.name}");
            go.tag = gameObject.tag;
            
            var rotation = transform.rotation;
            go.transform.position = transform.position + rotation * offset;
            go.transform.rotation = rotation;
            go.transform.parent = transform.parent;
            
            // 添加网格组件
            var meshFilter = go.AddComponent<MeshFilter>();
            meshFilter.sharedMesh = mesh;
            
            var meshRenderer = go.AddComponent<MeshRenderer>();
            meshRenderer.sharedMaterials = materials;
            
            var meshCollider = go.AddComponent<MeshCollider>();
            meshCollider.convex = true;
            meshCollider.sharedMesh = mesh;
            
            RecursiveShard shard = null;
            
            // 决定是否可以继续破碎
            bool canBreakFurther = enableRecursiveBreaking && 
                                 shardSurface >= minShardSizeForBreaking && 
                                 (recursionDepth + 1) < maxRecursionDepth;
            
            if (canBreakFurther)
            {
                shard = go.AddComponent<RecursiveShard>();
                shard.Patterns = Patterns;
                shard.parentPanel = parentPanel;
                shard.recursionDepth = recursionDepth + 1;
                shard.maxRecursionDepth = maxRecursionDepth;
                shard.minShardSizeForBreaking = minShardSizeForBreaking;
                shard.recursiveBreakForceMultiplier = recursiveBreakForceMultiplier;
                shard.enableRecursiveBreaking = enableRecursiveBreaking;
            }
            
            // 添加物理效果
            var shardRigidbody = go.AddComponent<Rigidbody>();
            shardRigidbody.mass = shardSurface;
            shardRigidbody.collisionDetectionMode = CollisionDetectionMode.Continuous;
            
            // 递归破碎时力度递减
            float forceMultiplier = Mathf.Pow(recursiveBreakForceMultiplier, recursionDepth + 1);
            shardRigidbody.AddForce(originVector * forceMultiplier);
            
            // 只有无法继续破碎的碎片才会自动销毁
            if (!canBreakFurther)
            {
                float destroyTime = shardSurface > 0.07f ? 8f : 4f;
                Destroy(go, destroyTime);
            }
            
            return shard;
        }
        
        private Mesh CreateRecursiveMesh(Polygon2D polygon, Vector2[] uvs, float thickness)
        {
            Mesh mesh = new Mesh();
            mesh.name = "Recursive Sub-Shard Mesh";
            
            var vertices = polygon.Vertices.ToArray();
            int vertexCount = vertices.Length;
            
            if (vertexCount < 3)
            {
                Debug.LogWarning("多边形顶点数量不足，无法创建网格");
                return mesh;
            }
            
            // 创建前后两个面的顶点
            Vector3[] meshVertices = new Vector3[vertexCount * 2];
            Vector2[] meshUVs = new Vector2[vertexCount * 2];
            
            for (int i = 0; i < vertexCount; i++)
            {
                // 前面
                meshVertices[i] = new Vector3((float)vertices[i].X, (float)vertices[i].Y, thickness / 2);
                // 后面
                meshVertices[i + vertexCount] = new Vector3((float)vertices[i].X, (float)vertices[i].Y, -thickness / 2);
                
                // UV坐标
                if (uvs != null && i < uvs.Length)
                {
                    meshUVs[i] = uvs[i];
                    meshUVs[i + vertexCount] = uvs[i];
                }
                else
                {
                    // 使用顶点位置作为UV
                    meshUVs[i] = new Vector2((float)vertices[i].X, (float)vertices[i].Y);
                    meshUVs[i + vertexCount] = new Vector2((float)vertices[i].X, (float)vertices[i].Y);
                }
            }
            
            mesh.vertices = meshVertices;
            mesh.uv = meshUVs;
            
            // 创建三角形
            List<int> triangles = new List<int>();
            
            // 前面三角形（扇形三角化）
            for (int i = 1; i < vertexCount - 1; i++)
            {
                triangles.Add(0);
                triangles.Add(i);
                triangles.Add(i + 1);
            }
            
            // 后面三角形（反向）
            for (int i = 1; i < vertexCount - 1; i++)
            {
                triangles.Add(vertexCount);
                triangles.Add(vertexCount + i + 1);
                triangles.Add(vertexCount + i);
            }
            
            mesh.triangles = triangles.ToArray();
            mesh.RecalculateNormals();
            mesh.RecalculateBounds();
            
            return mesh;
        }
        
        private Vector2 InterpolateUv(Point2D point)
        {
            // 简化的UV插值
            return new Vector2((float)point.X, (float)point.Y);
        }
        
        private float GetShardSurface()
        {
            var meshFilter = GetComponent<MeshFilter>();
            if (meshFilter != null && meshFilter.sharedMesh != null)
            {
                var bounds = meshFilter.sharedMesh.bounds;
                return bounds.size.x * bounds.size.y;
            }
            return 0f;
        }
        
        public new void Fall()
        {
            var rb = GetComponent<Rigidbody>();
            if (rb != null)
            {
                rb.isKinematic = false;
                rb.AddForce(Vector3.down * 5f, ForceMode.Impulse);
            }
        }
        
        // 在场景视图中显示递归深度
        void OnDrawGizmos()
        {
            if (Application.isPlaying)
            {
                // 根据递归深度设置不同颜色
                Color[] depthColors = { Color.white, Color.yellow, Color.orange, Color.red, Color.magenta };
                Gizmos.color = depthColors[Mathf.Min(recursionDepth, depthColors.Length - 1)];
                
                Gizmos.DrawWireCube(transform.position, transform.localScale * 0.1f);
            }
        }
        
        // GUI显示递归信息
        void OnGUI()
        {
            if (Application.isPlaying && Camera.main != null)
            {
                Vector3 screenPos = Camera.main.WorldToScreenPoint(transform.position);
                if (screenPos.z > 0 && screenPos.x > 0 && screenPos.x < Screen.width && 
                    screenPos.y > 0 && screenPos.y < Screen.height)
                {
                    float distance = Vector3.Distance(Camera.main.transform.position, transform.position);
                    if (distance < 10f) // 只在近距离显示
                    {
                        GUI.color = Color.yellow;
                        GUI.Label(new Rect(screenPos.x - 20, Screen.height - screenPos.y - 10, 40, 20), 
                                 $"D{recursionDepth}");
                        GUI.color = Color.white;
                    }
                }
            }
        }
    }
}
