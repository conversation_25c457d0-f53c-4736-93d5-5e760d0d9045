using System.Collections.Generic;
using System.Linq;
using MathNet.Numerics.LinearAlgebra.Double;
using MathNet.Spatial.Euclidean;
using UnityEngine;
using GlassSystem.Scripts;
using static GlassSystem.Scripts.MathNetUtils;

namespace GlassSystem.Sample
{
    /// <summary>
    /// 递归玻璃面板 - 简单修改原系统以支持碎片重复破碎
    /// </summary>
    public class RecursiveGlassPanel : GlassPanel
    {
        [Header("递归破碎设置")]
        [SerializeField] private bool enableRecursiveBreaking = true;
        [SerializeField] private float minShardSizeForBreaking = 0.05f; // 降低最小尺寸限制
        [SerializeField] private int maxRecursionDepth = 3;
        [SerializeField] private bool disableAutoDestroy = false; // 禁用自动销毁
        [SerializeField] private float recursiveBreakForceMultiplier = 0.8f;
        
        private Dictionary<GameObject, int> shardDepthMap = new Dictionary<GameObject, int>();
        
        public override void Break(Vector3 breakPosition, Vector3 originVector, int patternIndex = -1, float rotation = float.NaN)
        {
            if (_shards is not null)
            {
                Debug.LogWarning("RecursiveGlassPanel已经破碎过了");
                return;
            }
            
            _shards = new();
            PerformRecursiveBreak(breakPosition, originVector, patternIndex, rotation, 0);
            
            // 隐藏原始渲染器
            var renderer = GetComponent<MeshRenderer>();
            if (renderer != null)
                renderer.enabled = false;
                
            var collider = GetComponent<MeshCollider>();
            if (collider != null)
                collider.enabled = false;
        }
        
        private void PerformRecursiveBreak(Vector3 breakPosition, Vector3 originVector, int patternIndex, float rotation, int depth)
        {
            Vector3 localPosition = transform.InverseTransformPoint(breakPosition);
            var scale = transform.lossyScale;
            localPosition.x *= scale.x;
            localPosition.y *= scale.y;

            _polygon ??= BuildPolygon(localPosition.z);
            if (_polygon is null)
                return;

            if (patternIndex == -1)
                patternIndex = Random.Range(0, Patterns.Length);
            if (float.IsNaN(rotation))
                rotation = Random.Range(0, 360f);
                
            var lines = ClipPattern.Clip(Patterns[patternIndex], _polygon, localPosition, rotation);
            List<Polygon2D> shardPolygons = ShardPolygonBuilder.Build(lines, Tolerance);
            
            var materials = GetComponent<Renderer>().sharedMaterials;
            foreach (Polygon2D shardPolygon in shardPolygons)
            {
                var center = Point2D.Centroid(shardPolygon.Vertices);
                var centeredShardPolygon = shardPolygon.TranslateBy(-center.ToVector2D());
                Vector2[] uvs = null;
                if (_uvs is not null)
                    uvs = shardPolygon.Vertices.Select(InterpolateUv).ToArray();
                    
                var shardMesh = CreateMesh(centeredShardPolygon, uvs, _thickness);
                var glassShard = SpawnRecursiveShard(shardMesh, originVector, new Vector3((float)center.X, (float)center.Y, 0), materials, depth);
                
                if (glassShard is not null)
                {
                    glassShard.InitializeShard(this, centeredShardPolygon, uvs, _thickness);
                    _shards.Add(glassShard);
                    
                    // 记录碎片深度
                    shardDepthMap[glassShard.gameObject] = depth;
                }
            }
        }
        
        private RecursiveShard SpawnRecursiveShard(Mesh mesh, Vector3 originVector, Vector3 offset, Material[] materials, int depth)
        {
            float shardSurface = mesh.bounds.size.x * mesh.bounds.size.y;

            var go = new GameObject($"RecursiveShard_D{depth}_{mesh.name}");
            go.tag = gameObject.tag;
            
            var rotation = transform.rotation;
            go.transform.position = transform.position + rotation * offset;
            go.transform.rotation = rotation;
            go.transform.parent = transform.parent;

            var meshFilter = go.AddComponent<MeshFilter>();
            meshFilter.sharedMesh = mesh;

            var meshRenderer = go.AddComponent<MeshRenderer>();
            meshRenderer.sharedMaterials = materials;

            var meshCollider = go.AddComponent<MeshCollider>();
            meshCollider.convex = true;
            meshCollider.sharedMesh = mesh;

            RecursiveShard shard = null;
            
            // 决定是否可以继续破碎
            bool canBreakFurther = enableRecursiveBreaking && 
                                 shardSurface >= minShardSizeForBreaking && 
                                 depth < maxRecursionDepth;

            if (canBreakFurther)
            {
                shard = go.AddComponent<RecursiveShard>();
                shard.Patterns = Patterns;
                shard.parentPanel = this;
                shard.recursionDepth = depth;
                shard.maxRecursionDepth = maxRecursionDepth;
                shard.minShardSizeForBreaking = minShardSizeForBreaking;
                shard.recursiveBreakForceMultiplier = recursiveBreakForceMultiplier;
                shard.enableRecursiveBreaking = enableRecursiveBreaking;
            }

            // 添加物理效果
            var shardRigidbody = go.AddComponent<Rigidbody>();
            shardRigidbody.mass = shardSurface;
            shardRigidbody.collisionDetectionMode = CollisionDetectionMode.Continuous;
            shardRigidbody.AddForce(originVector * (depth == 0 ? 1f : recursiveBreakForceMultiplier));

            // 只有在禁用递归破碎或达到最大深度时才自动销毁
            if (!disableAutoDestroy && (!canBreakFurther || !enableRecursiveBreaking))
            {
                float destroyTime = shardSurface > 0.07f ? 8f : 4f;
                Destroy(go, destroyTime);
            }

            return shard;
        }
        
        // 重写CreateMesh方法以支持更好的网格生成
        private Mesh CreateMesh(Polygon2D polygon, Vector2[] uvs, float thickness)
        {
            Mesh mesh = new Mesh();
            mesh.name = "Recursive Shard Mesh";
            
            var vertices = polygon.Vertices.ToArray();
            int vertexCount = vertices.Length;
            
            // 创建前后两个面的顶点
            Vector3[] meshVertices = new Vector3[vertexCount * 2];
            Vector2[] meshUVs = new Vector2[vertexCount * 2];
            
            for (int i = 0; i < vertexCount; i++)
            {
                // 前面
                meshVertices[i] = new Vector3((float)vertices[i].X, (float)vertices[i].Y, thickness / 2);
                // 后面
                meshVertices[i + vertexCount] = new Vector3((float)vertices[i].X, (float)vertices[i].Y, -thickness / 2);
                
                // UV坐标
                if (uvs != null && i < uvs.Length)
                {
                    meshUVs[i] = uvs[i];
                    meshUVs[i + vertexCount] = uvs[i];
                }
                else
                {
                    meshUVs[i] = new Vector2((float)vertices[i].X, (float)vertices[i].Y);
                    meshUVs[i + vertexCount] = new Vector2((float)vertices[i].X, (float)vertices[i].Y);
                }
            }
            
            mesh.vertices = meshVertices;
            mesh.uv = meshUVs;
            
            // 创建三角形
            List<int> triangles = new List<int>();
            
            // 前面三角形
            for (int i = 1; i < vertexCount - 1; i++)
            {
                triangles.Add(0);
                triangles.Add(i);
                triangles.Add(i + 1);
            }
            
            // 后面三角形（反向）
            for (int i = 1; i < vertexCount - 1; i++)
            {
                triangles.Add(vertexCount);
                triangles.Add(vertexCount + i + 1);
                triangles.Add(vertexCount + i);
            }
            
            mesh.triangles = triangles.ToArray();
            mesh.RecalculateNormals();
            mesh.RecalculateBounds();
            
            return mesh;
        }
        
        private Vector2 InterpolateUv(Point2D point)
        {
            // 简化的UV插值实现
            return new Vector2((float)point.X, (float)point.Y);
        }
        
        public void OnRecursiveShardDestroyed(RecursiveShard shard)
        {
            if (_shards != null)
            {
                _shards.Remove(shard);
                shardDepthMap.Remove(shard.gameObject);
                
                if (health > 0)
                {
                    health -= 1;
                    if (health == 0)
                    {
                        foreach (Shard s in _shards)
                        {
                            s.Fall();
                        }
                    }
                }
            }
        }
        
        public void OnNewRecursiveShard(RecursiveShard shard, int depth)
        {
            if (_shards != null)
            {
                _shards.Add(shard);
                shardDepthMap[shard.gameObject] = depth;
            }
        }

        public void OnNewRecursiveShard(SimpleRecursiveShard shard, int depth)
        {
            if (_shards != null)
            {
                // 为简化版碎片创建一个包装器
                GameObject wrapperGO = new GameObject($"ShardWrapper_{depth}");
                wrapperGO.transform.position = shard.transform.position;
                wrapperGO.transform.rotation = shard.transform.rotation;
                wrapperGO.transform.parent = shard.transform.parent;

                // 这里可以添加更多逻辑来管理简化版碎片
                shardDepthMap[shard.gameObject] = depth;
            }
        }
        
        // 公共方法用于运行时配置
        public void SetRecursiveBreaking(bool enabled)
        {
            enableRecursiveBreaking = enabled;
        }
        
        public void SetMaxRecursionDepth(int depth)
        {
            maxRecursionDepth = depth;
        }
        
        public void SetMinShardSize(float size)
        {
            minShardSizeForBreaking = size;
        }
        
        public void SetAutoDestroy(bool enabled)
        {
            disableAutoDestroy = !enabled;
        }
        
        // 调试信息
        void OnGUI()
        {
            if (Application.isPlaying && _shards != null)
            {
                GUI.Box(new Rect(10, Screen.height - 120, 200, 100), "递归玻璃状态");
                GUI.Label(new Rect(20, Screen.height - 100, 180, 20), $"总碎片数: {_shards.Count}");
                GUI.Label(new Rect(20, Screen.height - 80, 180, 20), $"递归破碎: {(enableRecursiveBreaking ? "启用" : "禁用")}");
                GUI.Label(new Rect(20, Screen.height - 60, 180, 20), $"最大深度: {maxRecursionDepth}");
                GUI.Label(new Rect(20, Screen.height - 40, 180, 20), $"最小尺寸: {minShardSizeForBreaking:F3}");
            }
        }
    }
}
