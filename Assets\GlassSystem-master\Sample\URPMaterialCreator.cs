using UnityEngine;

namespace GlassSystem.Sample
{
    /// <summary>
    /// URP材质创建器 - 专门用于创建和配置URP材质
    /// </summary>
    public class URPMaterialCreator : MonoBehaviour
    {
        [Header("URP材质设置")]
        public bool createMaterialsOnStart = false;
        
        [Header("玻璃材质配置")]
        [SerializeField] private Color glassBaseColor = new Color(0.8f, 0.9f, 1f, 0.1f);
        [SerializeField] private float glassMetallic = 0.0f;
        [SerializeField] private float glassSmoothness = 0.95f;
        [SerializeField] private float glassAlpha = 0.1f;
        
        [Header("环境材质配置")]
        [SerializeField] private Color floorColor = new Color(0.7f, 0.7f, 0.7f, 1f);
        [SerializeField] private Color wallColor = new Color(0.9f, 0.9f, 0.8f, 1f);
        [SerializeField] private float environmentMetallic = 0.1f;
        [SerializeField] private float environmentSmoothness = 0.3f;
        
        void Start()
        {
            if (createMaterialsOnStart)
            {
                CreateAllURPMaterials();
            }
        }
        
        [ContextMenu("创建所有URP材质")]
        public void CreateAllURPMaterials()
        {
            CreateURPGlassMaterials();
            CreateURPEnvironmentMaterials();
            Debug.Log("所有URP材质创建完成！");
        }
        
        [ContextMenu("创建URP玻璃材质")]
        public void CreateURPGlassMaterials()
        {
            // 创建主玻璃材质
            Material glassMaterial = CreateURPGlassMaterial("URP_Glass_Main");
            SaveMaterialToResources(glassMaterial, "URP_Glass");
            
            // 创建玻璃边缘材质
            Material glassSideMaterial = CreateURPGlassSideMaterial("URP_Glass_Side");
            SaveMaterialToResources(glassSideMaterial, "URP_GlassSide");
            
            Debug.Log("URP玻璃材质创建完成");
        }
        
        [ContextMenu("创建URP环境材质")]
        public void CreateURPEnvironmentMaterials()
        {
            // 创建地面材质
            Material floorMaterial = CreateURPEnvironmentMaterial("URP_Floor", floorColor, environmentMetallic, environmentSmoothness);
            SaveMaterialToResources(floorMaterial, "URP_Floor");
            
            // 创建墙壁材质
            Material wallMaterial = CreateURPEnvironmentMaterial("URP_Wall", wallColor, 0.0f, 0.2f);
            SaveMaterialToResources(wallMaterial, "URP_Wall");
            
            Debug.Log("URP环境材质创建完成");
        }
        
        public Material CreateURPMaterial(string materialName)
        {
            // 尝试不同的URP着色器名称
            string[] urpShaderNames = {
                "Universal Render Pipeline/Lit",
                "URP/Lit",
                "Shader Graphs/Lit",
                "Universal Render Pipeline/Simple Lit"
            };
            
            Material mat = null;
            foreach (string shaderName in urpShaderNames)
            {
                Shader shader = Shader.Find(shaderName);
                if (shader != null)
                {
                    mat = new Material(shader);
                    mat.name = materialName;
                    Debug.Log($"使用URP着色器创建材质: {shaderName}");
                    break;
                }
            }
            
            // 如果找不到URP着色器，回退到标准着色器
            if (mat == null)
            {
                mat = new Material(Shader.Find("Standard"));
                mat.name = materialName + " (Standard Fallback)";
                Debug.LogWarning($"URP着色器未找到，使用标准着色器作为后备: {materialName}");
            }
            
            return mat;
        }
        
        public Material CreateURPGlassMaterial(string materialName)
        {
            Material glassMat = CreateURPMaterial(materialName);
            
            // 设置基础颜色
            glassMat.SetColor("_BaseColor", glassBaseColor);
            glassMat.SetFloat("_Metallic", glassMetallic);
            glassMat.SetFloat("_Smoothness", glassSmoothness);
            
            // 配置透明度设置（URP特有）
            ConfigureURPTransparency(glassMat);
            
            return glassMat;
        }
        
        public Material CreateURPGlassSideMaterial(string materialName)
        {
            Material glassSideMat = CreateURPMaterial(materialName);
            
            // 边缘材质通常不透明但有玻璃质感
            Color sideColor = new Color(glassBaseColor.r, glassBaseColor.g, glassBaseColor.b, 0.8f);
            glassSideMat.SetColor("_BaseColor", sideColor);
            glassSideMat.SetFloat("_Metallic", 0.1f);
            glassSideMat.SetFloat("_Smoothness", 0.9f);
            
            // 半透明设置
            if (glassSideMat.HasProperty("_Surface"))
            {
                glassSideMat.SetFloat("_Surface", 1); // 透明表面
                glassSideMat.SetFloat("_Blend", 0); // Alpha混合
            }
            
            glassSideMat.renderQueue = 3000;
            
            return glassSideMat;
        }
        
        public Material CreateURPEnvironmentMaterial(string materialName, Color baseColor, float metallic, float smoothness)
        {
            Material envMat = CreateURPMaterial(materialName);
            
            envMat.SetColor("_BaseColor", baseColor);
            envMat.SetFloat("_Metallic", metallic);
            envMat.SetFloat("_Smoothness", smoothness);
            
            return envMat;
        }
        
        void ConfigureURPTransparency(Material material)
        {
            // URP透明度配置
            if (material.HasProperty("_Surface"))
            {
                material.SetFloat("_Surface", 1); // 1 = Transparent
                material.SetFloat("_Blend", 0); // 0 = Alpha, 1 = Premultiply, 2 = Additive, 3 = Multiply
                material.SetFloat("_AlphaClip", 0); // 禁用Alpha裁剪
                material.SetFloat("_SrcBlend", 5); // SrcAlpha
                material.SetFloat("_DstBlend", 10); // OneMinusSrcAlpha
                material.SetFloat("_ZWrite", 0); // 禁用深度写入
                material.SetFloat("_Cull", 2); // 背面剔除
            }
            
            // 设置渲染队列
            material.renderQueue = 3000; // Transparent queue
            
            // 启用关键字
            material.EnableKeyword("_SURFACE_TYPE_TRANSPARENT");
            material.EnableKeyword("_ALPHAPREMULTIPLY_ON");
            
            // 禁用不需要的关键字
            material.DisableKeyword("_ALPHATEST_ON");
            material.DisableKeyword("_SURFACE_TYPE_OPAQUE");
        }
        
        void SaveMaterialToResources(Material material, string fileName)
        {
            // 这里可以添加保存材质到Resources文件夹的逻辑
            // 注意：在运行时无法直接保存到Assets文件夹，这通常在编辑器中完成
            Debug.Log($"材质已创建: {material.name}");
        }
        
        [ContextMenu("测试URP着色器可用性")]
        public void TestURPShaderAvailability()
        {
            string[] urpShaderNames = {
                "Universal Render Pipeline/Lit",
                "Universal Render Pipeline/Simple Lit",
                "Universal Render Pipeline/Unlit",
                "URP/Lit",
                "Shader Graphs/Lit"
            };
            
            Debug.Log("=== URP着色器可用性测试 ===");
            foreach (string shaderName in urpShaderNames)
            {
                Shader shader = Shader.Find(shaderName);
                if (shader != null)
                {
                    Debug.Log($"✓ 可用: {shaderName}");
                }
                else
                {
                    Debug.Log($"✗ 不可用: {shaderName}");
                }
            }
            Debug.Log("=== 测试完成 ===");
        }
        
        // 公共方法供其他脚本调用
        public static Material CreateQuickURPGlass()
        {
            URPMaterialCreator creator = new URPMaterialCreator();
            return creator.CreateURPGlassMaterial("Quick_URP_Glass");
        }
        
        public static Material CreateQuickURPOpaque(Color color, float metallic = 0.0f, float smoothness = 0.5f)
        {
            URPMaterialCreator creator = new URPMaterialCreator();
            return creator.CreateURPEnvironmentMaterial("Quick_URP_Opaque", color, metallic, smoothness);
        }
        
        // 编辑器辅助方法
        #if UNITY_EDITOR
        [ContextMenu("在编辑器中创建材质资源")]
        public void CreateMaterialAssets()
        {
            if (!Application.isPlaying)
            {
                // 创建材质
                Material glassMat = CreateURPGlassMaterial("URP_Glass_Asset");
                Material glassSideMat = CreateURPGlassSideMaterial("URP_GlassSide_Asset");
                Material floorMat = CreateURPEnvironmentMaterial("URP_Floor_Asset", floorColor, environmentMetallic, environmentSmoothness);
                Material wallMat = CreateURPEnvironmentMaterial("URP_Wall_Asset", wallColor, 0.0f, 0.2f);
                
                // 保存为资源文件
                string resourcePath = "Assets/GlassSystem-master/Resources/";
                UnityEditor.AssetDatabase.CreateAsset(glassMat, resourcePath + "URP_Glass.mat");
                UnityEditor.AssetDatabase.CreateAsset(glassSideMat, resourcePath + "URP_GlassSide.mat");
                UnityEditor.AssetDatabase.CreateAsset(floorMat, resourcePath + "URP_Floor.mat");
                UnityEditor.AssetDatabase.CreateAsset(wallMat, resourcePath + "URP_Wall.mat");
                
                UnityEditor.AssetDatabase.SaveAssets();
                UnityEditor.AssetDatabase.Refresh();
                
                Debug.Log("URP材质资源文件已创建并保存到Resources文件夹");
            }
            else
            {
                Debug.LogWarning("请在编辑器模式下运行此功能");
            }
        }
        #endif
    }
}
