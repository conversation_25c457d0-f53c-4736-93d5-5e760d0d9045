using GlassSystem.Scripts;
using UnityEngine;
using UnityEngine.UI;

namespace GlassSystem.Sample
{
    public class DemoGun : MonoBehaviour
    {
        [Header("Glass Breaking Settings")]
        public float impactForce = 1000f;
        public int Retry = 3;

        [Header("UI Elements")]
        public Text instructionText;
        public Text forceText;
        public Slider forceSlider;

        [Header("Audio")]
        public AudioSource audioSource;
        public AudioClip glassBreakSound;

        private Camera playerCamera;
        private int glassCount = 0;

        private void Start()
        {
            Cursor.lockState = CursorLockMode.Confined;
            playerCamera = Camera.main;
            if (playerCamera == null)
                playerCamera = FindObjectOfType<Camera>();

            // 初始化UI
            UpdateUI();

            // 设置力度滑块
            if (forceSlider != null)
            {
                forceSlider.minValue = 100f;
                forceSlider.maxValue = 5000f;
                forceSlider.value = impactForce;
                forceSlider.onValueChanged.AddListener(OnForceChanged);
            }

            // 计算场景中的玻璃数量
            CountGlassObjects();
        }

        void Update()
        {
            // 鼠标左键射击
            if (Input.GetMouseButtonDown(0))
            {
                ShootFromCamera();
            }

            // 键盘数字键调整力度
            if (Input.GetKeyDown(KeyCode.Alpha1)) SetForce(500f);
            if (Input.GetKeyDown(KeyCode.Alpha2)) SetForce(1000f);
            if (Input.GetKeyDown(KeyCode.Alpha3)) SetForce(2000f);
            if (Input.GetKeyDown(KeyCode.Alpha4)) SetForce(3000f);
            if (Input.GetKeyDown(KeyCode.Alpha5)) SetForce(5000f);

            // R键重置场景
            if (Input.GetKeyDown(KeyCode.R))
            {
                ResetScene();
            }
        }

        void ShootFromCamera()
        {
            RaycastHit hit;
            Ray ray = playerCamera.ScreenPointToRay(Input.mousePosition);

            if (Physics.Raycast(ray, out hit, Mathf.Infinity))
            {
                Debug.DrawRay(ray.origin, ray.direction * hit.distance, Color.yellow, 2f);

                var glass = hit.collider.gameObject.GetComponent<BaseGlass>();
                if (glass != null)
                {
                    int failBreak = 0;
                    while (true)
                        try
                        {
                            glass.Break(hit.point, ray.direction * impactForce);

                            // 播放破碎音效
                            PlayBreakSound();

                            // 更新UI
                            UpdateUI();
                            return;
                        }
                        catch (InternalGlassException e)
                        {
                            if (++failBreak >= Retry)
                                throw;
                            Debug.LogWarning($"Failed to break glass (retry {failBreak}): {e}");
                        }
                }
                else
                {
                    // 击中其他物体，显示击中效果
                    ShowHitEffect(hit.point);
                }
            }
        }

        void SetForce(float force)
        {
            impactForce = force;
            if (forceSlider != null)
                forceSlider.value = force;
            UpdateUI();
        }

        void OnForceChanged(float value)
        {
            impactForce = value;
            UpdateUI();
        }

        void UpdateUI()
        {
            if (instructionText != null)
            {
                instructionText.text = "左键点击破碎玻璃\n" +
                                     "1-5键调整力度\n" +
                                     "R键重置场景\n" +
                                     "ESC解锁鼠标";
            }

            if (forceText != null)
            {
                forceText.text = $"撞击力度: {impactForce:F0}";
            }
        }

        void CountGlassObjects()
        {
            BaseGlass[] glasses = FindObjectsOfType<BaseGlass>();
            glassCount = glasses.Length;
            Debug.Log($"场景中发现 {glassCount} 个玻璃对象");
        }

        void PlayBreakSound()
        {
            if (audioSource != null && glassBreakSound != null)
            {
                audioSource.PlayOneShot(glassBreakSound);
            }
        }

        void ShowHitEffect(Vector3 position)
        {
            // 可以在这里添加击中特效
            Debug.Log($"击中位置: {position}");
        }

        void ResetScene()
        {
            // 重新加载当前场景
            UnityEngine.SceneManagement.SceneManager.LoadScene(
                UnityEngine.SceneManagement.SceneManager.GetActiveScene().name);
        }
    }
}
