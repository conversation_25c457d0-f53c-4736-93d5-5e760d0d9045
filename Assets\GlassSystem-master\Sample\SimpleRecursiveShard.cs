using UnityEngine;
using GlassSystem.Scripts;

namespace GlassSystem.Sample
{
    /// <summary>
    /// 简化版递归碎片 - 避免复杂继承问题的简单实现
    /// </summary>
    public class SimpleRecursiveShard : MonoBehaviour
    {
        [Header("递归设置")]
        public Mesh[] Patterns;
        public int recursionDepth = 0;
        public int maxRecursionDepth = 3;
        public float minShardSizeForBreaking = 0.05f;
        public bool enableRecursiveBreaking = true;
        
        private RecursiveGlassPanel parentPanel;
        private bool hasBeenBroken = false;
        
        public void InitializeShard(RecursiveGlassPanel parent, Mesh[] patterns, int depth)
        {
            parentPanel = parent;
            Patterns = patterns;
            recursionDepth = depth;
        }
        
        public void Break(Vector3 breakPosition, Vector3 originVector)
        {
            if (hasBeenBroken)
            {
                Debug.Log($"碎片已经破碎过了 (深度: {recursionDepth})");
                return;
            }
            
            if (!enableRecursiveBreaking)
            {
                Debug.Log("递归破碎已禁用");
                return;
            }
            
            if (recursionDepth >= maxRecursionDepth)
            {
                Debug.Log($"已达到最大递归深度 ({maxRecursionDepth})");
                return;
            }
            
            // 检查碎片大小
            float shardSurface = GetShardSurface();
            if (shardSurface < minShardSizeForBreaking)
            {
                Debug.Log($"碎片太小无法破碎 (表面积: {shardSurface:F3}, 最小: {minShardSizeForBreaking:F3})");
                return;
            }
            
            Debug.Log($"递归破碎碎片 - 深度: {recursionDepth}, 表面积: {shardSurface:F3}");
            
            hasBeenBroken = true;
            
            // 简单的递归破碎：创建几个子碎片
            CreateSubShards(breakPosition, originVector);
            
            // 通知父面板
            if (parentPanel != null)
            {
                parentPanel.OnRecursiveShardDestroyed(this);
            }
            
            // 销毁当前碎片
            Destroy(gameObject);
        }
        
        void CreateSubShards(Vector3 breakPosition, Vector3 originVector)
        {
            // 简化的子碎片创建逻辑
            int subShardCount = Random.Range(3, 6);
            var materials = GetComponent<Renderer>().sharedMaterials;
            
            for (int i = 0; i < subShardCount; i++)
            {
                // 创建子碎片
                GameObject subShardGO = GameObject.CreatePrimitive(PrimitiveType.Cube);
                subShardGO.name = $"SubShard_D{recursionDepth + 1}_{i}";
                subShardGO.tag = gameObject.tag;
                
                // 随机位置和大小
                Vector3 randomOffset = Random.insideUnitSphere * 0.5f;
                subShardGO.transform.position = transform.position + randomOffset;
                subShardGO.transform.rotation = Random.rotation;
                
                float sizeReduction = Random.Range(0.3f, 0.7f);
                subShardGO.transform.localScale = transform.localScale * sizeReduction;
                subShardGO.transform.parent = transform.parent;
                
                // 设置材质
                var renderer = subShardGO.GetComponent<Renderer>();
                if (renderer != null && materials != null && materials.Length > 0)
                {
                    renderer.material = materials[0];
                }
                
                // 添加物理效果
                var rb = subShardGO.AddComponent<Rigidbody>();
                rb.mass = GetShardSurface() * sizeReduction;
                rb.collisionDetectionMode = CollisionDetectionMode.Continuous;
                
                // 添加随机力
                Vector3 randomForce = (originVector + Random.insideUnitSphere).normalized;
                rb.AddForce(randomForce * Random.Range(50f, 200f));
                
                // 决定是否可以继续破碎
                float subShardSurface = subShardGO.transform.localScale.x * subShardGO.transform.localScale.y;
                bool canBreakFurther = enableRecursiveBreaking && 
                                     subShardSurface >= minShardSizeForBreaking && 
                                     (recursionDepth + 1) < maxRecursionDepth;
                
                if (canBreakFurther)
                {
                    // 添加递归碎片组件
                    SimpleRecursiveShard subShard = subShardGO.AddComponent<SimpleRecursiveShard>();
                    subShard.InitializeShard(parentPanel, Patterns, recursionDepth + 1);
                    subShard.maxRecursionDepth = maxRecursionDepth;
                    subShard.minShardSizeForBreaking = minShardSizeForBreaking;
                    subShard.enableRecursiveBreaking = enableRecursiveBreaking;
                    
                    if (parentPanel != null)
                    {
                        parentPanel.OnNewRecursiveShard(subShard, recursionDepth + 1);
                    }
                }
                else
                {
                    // 自动销毁小碎片
                    Destroy(subShardGO, Random.Range(4f, 8f));
                }
            }
        }
        
        float GetShardSurface()
        {
            var bounds = GetComponent<Renderer>().bounds;
            return bounds.size.x * bounds.size.y;
        }
        
        public void Fall()
        {
            var rb = GetComponent<Rigidbody>();
            if (rb == null)
            {
                rb = gameObject.AddComponent<Rigidbody>();
                rb.mass = GetShardSurface();
            }
            rb.isKinematic = false;
            rb.AddForce(Vector3.down * 5f, ForceMode.Impulse);
        }
        
        // 鼠标点击检测
        void OnMouseDown()
        {
            // 获取点击位置
            Vector3 mousePos = Input.mousePosition;
            Ray ray = Camera.main.ScreenPointToRay(mousePos);
            
            if (Physics.Raycast(ray, out RaycastHit hit))
            {
                if (hit.collider.gameObject == gameObject)
                {
                    // 模拟撞击
                    Vector3 impactDirection = ray.direction;
                    Break(hit.point, impactDirection * 100f);
                }
            }
        }
        
        // 可视化调试
        void OnDrawGizmos()
        {
            if (Application.isPlaying)
            {
                // 根据递归深度设置不同颜色
                Color[] depthColors = { Color.white, Color.yellow, Color.orange, Color.red, Color.magenta };
                Gizmos.color = depthColors[Mathf.Min(recursionDepth, depthColors.Length - 1)];
                
                Gizmos.DrawWireCube(transform.position, transform.localScale * 0.1f);
            }
        }
        
        // GUI显示递归信息
        void OnGUI()
        {
            if (Application.isPlaying && Camera.main != null)
            {
                Vector3 screenPos = Camera.main.WorldToScreenPoint(transform.position);
                if (screenPos.z > 0 && screenPos.x > 0 && screenPos.x < Screen.width && 
                    screenPos.y > 0 && screenPos.y < Screen.height)
                {
                    float distance = Vector3.Distance(Camera.main.transform.position, transform.position);
                    if (distance < 10f) // 只在近距离显示
                    {
                        GUI.color = Color.yellow;
                        GUI.Label(new Rect(screenPos.x - 20, Screen.height - screenPos.y - 10, 40, 20), 
                                 $"D{recursionDepth}");
                        GUI.color = Color.white;
                    }
                }
            }
        }
    }
}
