fileFormatVersion: 2
guid: 7a4cfc2e38798624c8eb4ec989fc31be
timeCreated: **********
licenseType: Store
ModelImporter:
  serializedVersion: 19
  fileIDToRecycleName:
    100000: Jar2
    100002: Jar2_Broken_01
    100004: Jar2_Broken_01_Shard
    100006: Jar2_Broken_01_Shard.001
    100008: Jar2_Broken_01_Shard.002
    100010: Jar2_Broken_02
    100012: Jar2_Broken_02_Shard
    100014: Jar2_Broken_02_Shard.001
    100016: Jar2_Broken_02_Shard.002
    100018: Jar2_Lid
    100020: Jar2_Lid.002
    100022: Jar2_Lid.003
    100024: //RootNode
    400000: Jar2
    400002: Jar2_Broken_01
    400004: Jar2_Broken_01_Shard
    400006: Jar2_Broken_01_Shard.001
    400008: Jar2_Broken_01_Shard.002
    400010: Jar2_Broken_02
    400012: Jar2_Broken_02_Shard
    400014: Jar2_Broken_02_Shard.001
    400016: Jar2_Broken_02_Shard.002
    400018: Jar2_Lid
    400020: Jar2_Lid.002
    400022: Jar2_Lid.003
    400024: //RootNode
    2300000: Jar2
    2300002: Jar2_Broken_01_Shard
    2300004: Jar2_Broken_01_Shard.001
    2300006: Jar2_Broken_01_Shard.002
    2300008: Jar2_Broken_02_Shard
    2300010: Jar2_Broken_02_Shard.001
    2300012: Jar2_Broken_02_Shard.002
    2300014: Jar2_Lid
    2300016: Jar2_Lid.002
    2300018: Jar2_Lid.003
    3300000: Jar2
    3300002: Jar2_Broken_01_Shard
    3300004: Jar2_Broken_01_Shard.001
    3300006: Jar2_Broken_01_Shard.002
    3300008: Jar2_Broken_02_Shard
    3300010: Jar2_Broken_02_Shard.001
    3300012: Jar2_Broken_02_Shard.002
    3300014: Jar2_Lid
    3300016: Jar2_Lid.002
    3300018: Jar2_Lid.003
    4300000: Jar2_Broken_02_Shard.002
    4300002: Jar2_Broken_02_Shard.001
    4300004: Jar2_Broken_02_Shard
    4300006: Jar2_Lid.002
    4300008: Jar2_Broken_01_Shard.002
    4300010: Jar2_Broken_01_Shard.001
    4300012: Jar2_Broken_01_Shard
    4300014: Jar2_Lid.003
    4300016: Jar2
    4300018: Jar2_Lid
  materials:
    importMaterials: 1
    materialName: 0
    materialSearch: 1
  animations:
    legacyGenerateAnimations: 4
    bakeSimulation: 0
    resampleCurves: 1
    optimizeGameObjects: 0
    motionNodeName: 
    animationImportErrors: 
    animationImportWarnings: 
    animationRetargetingWarnings: 
    animationDoRetargetingWarnings: 0
    animationCompression: 1
    animationRotationError: 0.5
    animationPositionError: 0.5
    animationScaleError: 0.5
    animationWrapMode: 0
    extraExposedTransformPaths: []
    clipAnimations: []
    isReadable: 1
  meshes:
    lODScreenPercentages: []
    globalScale: 1
    meshCompression: 0
    addColliders: 0
    importBlendShapes: 1
    swapUVChannels: 0
    generateSecondaryUV: 0
    useFileUnits: 1
    optimizeMeshForGPU: 1
    keepQuads: 0
    weldVertices: 1
    secondaryUVAngleDistortion: 8
    secondaryUVAreaDistortion: 15.000001
    secondaryUVHardAngle: 88
    secondaryUVPackMargin: 4
    useFileScale: 1
  tangentSpace:
    normalSmoothAngle: 60
    normalImportMode: 0
    tangentImportMode: 3
  importAnimation: 1
  copyAvatar: 0
  humanDescription:
    human: []
    skeleton: []
    armTwist: 0.5
    foreArmTwist: 0.5
    upperLegTwist: 0.5
    legTwist: 0.5
    armStretch: 0.05
    legStretch: 0.05
    feetSpacing: 0
    rootMotionBoneName: 
    hasTranslationDoF: 0
  lastHumanDescriptionAvatarSource: {instanceID: 0}
  animationType: 0
  humanoidOversampling: 1
  additionalBone: 0
  userData: 
  assetBundleName: 
  assetBundleVariant: 
