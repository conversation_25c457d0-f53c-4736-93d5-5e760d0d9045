using System;
using MathNet.Spatial.Euclidean;
using UnityEngine;

namespace GlassSystem.Sample
{
    /// <summary>
    /// 增强版碎片 - 支持递归破碎
    /// </summary>
    public class EnhancedShard : MonoBehaviour
    {
        [Header("破碎图案")]
        public Mesh[] Patterns;
        
        [Header("碎片设置")]
        public bool allowShardBreaking = true;
        public float minShardSize = 0.05f;
        public int maxBreakDepth = 3;
        public bool autoDestroySmallShards = true;
        public float smallShardLifetime = 8f;
        public float microShardLifetime = 4f;
        
        [Header("物理设置")]
        public bool addPhysicsToShards = true;
        public float shardMassMultiplier = 1f;
        public float breakForceMultiplier = 1f;
        
        // 私有变量
        private EnhancedGlassPanel _parentPanel;
        private Polygon2D _polygon;
        private Vector2[] _uvs;
        private float _thickness;
        private int _breakDepth;
        private bool _hasBeenBroken = false;
        
        protected const float Tolerance = 0.001f;
        
        public void InitializeShard(EnhancedGlassPanel parentPanel, Polygon2D polygon, Vector2[] uvs, float thickness, int breakDepth)
        {
            _parentPanel = parentPanel;
            _polygon = polygon;
            _uvs = uvs;
            _thickness = thickness;
            _breakDepth = breakDepth;
        }
        
        /// <summary>
        /// 破碎这个碎片
        /// </summary>
        public virtual void Break(Vector3 breakPosition, Vector3 originVector, int patternIndex = -1, float rotation = float.NaN)
        {
            if (_hasBeenBroken)
            {
                Debug.Log("这个碎片已经被破碎过了");
                return;
            }
            
            if (!allowShardBreaking)
            {
                Debug.Log("碎片破碎功能已禁用");
                return;
            }
            
            if (_breakDepth >= maxBreakDepth)
            {
                Debug.Log($"已达到最大破碎深度 ({maxBreakDepth})");
                return;
            }
            
            // 检查碎片大小
            float shardSurface = GetShardSurface();
            if (shardSurface < minShardSize)
            {
                Debug.Log($"碎片太小无法继续破碎 (表面积: {shardSurface:F3}, 最小值: {minShardSize:F3})");
                return;
            }
            
            Debug.Log($"破碎碎片 - 深度: {_breakDepth}, 表面积: {shardSurface:F3}");
            
            _hasBeenBroken = true;
            
            // 执行破碎
            PerformShardBreak(breakPosition, originVector, patternIndex, rotation);
            
            // 通知父面板
            if (_parentPanel != null)
            {
                _parentPanel.OnShardDestroyed(this);
            }
            
            // 销毁当前碎片
            Destroy(gameObject);
        }
        
        protected virtual void PerformShardBreak(Vector3 breakPosition, Vector3 originVector, int patternIndex, float rotation)
        {
            Vector3 localPosition = transform.InverseTransformPoint(breakPosition);
            var scale = transform.lossyScale;
            localPosition.x *= scale.x;
            localPosition.y *= scale.y;
            
            if (_polygon == null)
            {
                Debug.LogWarning("碎片多边形为空，无法破碎");
                return;
            }
            
            if (patternIndex == -1)
                patternIndex = UnityEngine.Random.Range(0, Patterns.Length);
            if (float.IsNaN(rotation))
                rotation = UnityEngine.Random.Range(0, 360f);
            
            var lines = GlassSystem.Scripts.ClipPattern.Clip(Patterns[patternIndex], _polygon, localPosition, rotation);
            var shardPolygons = GlassSystem.Scripts.ShardPolygonBuilder.Build(lines, Tolerance);
            
            var materials = GetComponent<Renderer>().sharedMaterials;
            
            foreach (var shardPolygon in shardPolygons)
            {
                var center = Point2D.Centroid(shardPolygon.Vertices);
                var centeredShardPolygon = shardPolygon.TranslateBy(-center.ToVector2D());
                Vector2[] uvs = null;
                if (_uvs != null)
                    uvs = shardPolygon.Vertices.Select(InterpolateUv).ToArray();
                
                var shardMesh = CreateMesh(centeredShardPolygon, uvs, _thickness);
                var newShard = SpawnChildShard(shardMesh, originVector, new Vector3((float)center.X, (float)center.Y, 0), materials);
                
                if (newShard != null)
                {
                    newShard.InitializeShard(_parentPanel, centeredShardPolygon, uvs, _thickness, _breakDepth + 1);
                    if (_parentPanel != null)
                    {
                        _parentPanel.OnNewShard(newShard);
                    }
                }
            }
        }
        
        EnhancedShard SpawnChildShard(Mesh mesh, Vector3 originVector, Vector3 offset, Material[] materials)
        {
            float shardSurface = mesh.bounds.size.x * mesh.bounds.size.y;
            
            var go = new GameObject($"SubShard_Depth{_breakDepth + 1}_{mesh.name}");
            go.tag = gameObject.tag;
            
            var rotation = transform.rotation;
            go.transform.position = transform.position + rotation * offset;
            go.transform.rotation = rotation;
            go.transform.parent = transform.parent;
            
            // 添加网格组件
            var meshFilter = go.AddComponent<MeshFilter>();
            meshFilter.sharedMesh = mesh;
            
            var meshRenderer = go.AddComponent<MeshRenderer>();
            meshRenderer.sharedMaterials = materials;
            
            var meshCollider = go.AddComponent<MeshCollider>();
            meshCollider.convex = true;
            meshCollider.sharedMesh = mesh;
            
            EnhancedShard shard = null;
            
            // 决定是否可以继续破碎
            bool canBreakFurther = allowShardBreaking && 
                                 shardSurface >= minShardSize && 
                                 (_breakDepth + 1) < maxBreakDepth;
            
            if (canBreakFurther)
            {
                // 添加增强碎片组件
                shard = go.AddComponent<EnhancedShard>();
                shard.Patterns = Patterns;
                shard.allowShardBreaking = allowShardBreaking;
                shard.minShardSize = minShardSize;
                shard.maxBreakDepth = maxBreakDepth;
                shard.autoDestroySmallShards = autoDestroySmallShards;
                shard.smallShardLifetime = smallShardLifetime;
                shard.microShardLifetime = microShardLifetime;
                shard.addPhysicsToShards = addPhysicsToShards;
                shard.shardMassMultiplier = shardMassMultiplier;
                shard.breakForceMultiplier = breakForceMultiplier;
            }
            
            // 添加物理效果
            if (addPhysicsToShards)
            {
                var shardRigidbody = go.AddComponent<Rigidbody>();
                shardRigidbody.mass = shardSurface * shardMassMultiplier;
                shardRigidbody.collisionDetectionMode = CollisionDetectionMode.Continuous;
                shardRigidbody.AddForce(originVector * breakForceMultiplier);
            }
            
            // 自动销毁小碎片
            if (autoDestroySmallShards && !canBreakFurther)
            {
                float lifetime = shardSurface < 0.07f ? microShardLifetime : smallShardLifetime;
                Destroy(go, lifetime);
            }
            
            return shard;
        }
        
        Mesh CreateMesh(Polygon2D polygon, Vector2[] uvs, float thickness)
        {
            // 简化的网格创建实现
            Mesh mesh = new Mesh();
            mesh.name = "Enhanced Sub-Shard Mesh";
            
            var vertices = polygon.Vertices.ToArray();
            Vector3[] meshVertices = new Vector3[vertices.Length * 2];
            
            for (int i = 0; i < vertices.Length; i++)
            {
                meshVertices[i] = new Vector3((float)vertices[i].X, (float)vertices[i].Y, thickness / 2);
                meshVertices[i + vertices.Length] = new Vector3((float)vertices[i].X, (float)vertices[i].Y, -thickness / 2);
            }
            
            mesh.vertices = meshVertices;
            
            // 简化的三角形创建
            System.Collections.Generic.List<int> triangles = new System.Collections.Generic.List<int>();
            for (int i = 0; i < vertices.Length - 2; i++)
            {
                triangles.Add(0);
                triangles.Add(i + 1);
                triangles.Add(i + 2);
            }
            
            mesh.triangles = triangles.ToArray();
            mesh.RecalculateNormals();
            mesh.RecalculateBounds();
            
            return mesh;
        }
        
        Vector2 InterpolateUv(Point2D point)
        {
            // 简化的UV插值
            return new Vector2((float)point.X, (float)point.Y);
        }
        
        float GetShardSurface()
        {
            var meshFilter = GetComponent<MeshFilter>();
            if (meshFilter != null && meshFilter.sharedMesh != null)
            {
                var bounds = meshFilter.sharedMesh.bounds;
                return bounds.size.x * bounds.size.y;
            }
            return 0f;
        }
        
        public void Fall()
        {
            // 让碎片开始下落
            var rb = GetComponent<Rigidbody>();
            if (rb == null)
            {
                rb = gameObject.AddComponent<Rigidbody>();
                rb.mass = GetShardSurface() * shardMassMultiplier;
            }
            rb.isKinematic = false;
            rb.AddForce(Vector3.down * 5f, ForceMode.Impulse);
        }
        
        // 调试信息
        void OnGUI()
        {
            if (Application.isPlaying && _hasBeenBroken)
            {
                Vector3 screenPos = Camera.main.WorldToScreenPoint(transform.position);
                if (screenPos.z > 0 && screenPos.x > 0 && screenPos.x < Screen.width && screenPos.y > 0 && screenPos.y < Screen.height)
                {
                    GUI.Label(new Rect(screenPos.x, Screen.height - screenPos.y, 100, 20), $"深度: {_breakDepth}");
                }
            }
        }
    }
}
