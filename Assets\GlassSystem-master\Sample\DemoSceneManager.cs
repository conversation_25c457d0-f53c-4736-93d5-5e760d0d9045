using System.Collections;
using System.Collections.Generic;
using UnityEngine;
using UnityEngine.UI;
using GlassSystem.Scripts;

namespace GlassSystem.Sample
{
    public class DemoSceneManager : MonoBehaviour
    {
        [Header("Demo Settings")]
        public Transform[] glassSpawnPoints;
        public GameObject[] glassPrefabs;
        public Material[] glassMaterials;
        
        [Header("Environment")]
        public Light mainLight;
        public Transform lightPivot;
        public bool rotateLighting = true;
        public float lightRotationSpeed = 10f;
        
        [Header("UI")]
        public Canvas demoUI;
        public Text statsText;
        public Button resetButton;
        public Button nextSceneButton;
        public Dropdown materialDropdown;
        
        [Header("Camera Positions")]
        public Transform[] cameraPositions;
        public float cameraTransitionSpeed = 2f;
        
        private Camera mainCamera;
        private int currentCameraIndex = 0;
        private int totalGlassCount = 0;
        private int brokenGlassCount = 0;
        private List<GameObject> spawnedGlasses = new List<GameObject>();
        
        void Start()
        {
            mainCamera = Camera.main;
            if (mainCamera == null)
                mainCamera = FindObjectOfType<Camera>();
                
            InitializeUI();
            SetupScene();
            StartCoroutine(UpdateStats());
        }
        
        void Update()
        {
            // 旋转光照
            if (rotateLighting && lightPivot != null)
            {
                lightPivot.Rotate(Vector3.up, lightRotationSpeed * Time.deltaTime);
            }
            
            // 相机位置切换
            if (Input.GetKeyDown(KeyCode.C))
            {
                SwitchCameraPosition();
            }
            
            // 切换UI显示
            if (Input.GetKeyDown(KeyCode.Tab))
            {
                ToggleUI();
            }
        }
        
        void InitializeUI()
        {
            if (resetButton != null)
                resetButton.onClick.AddListener(ResetScene);
                
            if (nextSceneButton != null)
                nextSceneButton.onClick.AddListener(LoadNextScene);
                
            if (materialDropdown != null)
            {
                materialDropdown.ClearOptions();
                List<string> materialNames = new List<string>();
                for (int i = 0; i < glassMaterials.Length; i++)
                {
                    materialNames.Add(glassMaterials[i].name);
                }
                materialDropdown.AddOptions(materialNames);
                materialDropdown.onValueChanged.AddListener(OnMaterialChanged);
            }
        }
        
        void SetupScene()
        {
            // 清理现有玻璃
            ClearSpawnedGlasses();
            
            // 在指定位置生成玻璃
            if (glassSpawnPoints != null && glassPrefabs != null)
            {
                for (int i = 0; i < glassSpawnPoints.Length; i++)
                {
                    if (i < glassPrefabs.Length)
                    {
                        GameObject glassPrefab = glassPrefabs[i % glassPrefabs.Length];
                        GameObject glass = Instantiate(glassPrefab, glassSpawnPoints[i].position, 
                                                     glassSpawnPoints[i].rotation);
                        spawnedGlasses.Add(glass);
                        
                        // 添加破碎监听
                        BaseGlass glassComponent = glass.GetComponent<BaseGlass>();
                        if (glassComponent != null)
                        {
                            // 这里可以添加破碎事件监听
                        }
                    }
                }
            }
            
            totalGlassCount = spawnedGlasses.Count;
            brokenGlassCount = 0;
        }
        
        void ClearSpawnedGlasses()
        {
            foreach (GameObject glass in spawnedGlasses)
            {
                if (glass != null)
                    DestroyImmediate(glass);
            }
            spawnedGlasses.Clear();
        }
        
        void SwitchCameraPosition()
        {
            if (cameraPositions != null && cameraPositions.Length > 0)
            {
                currentCameraIndex = (currentCameraIndex + 1) % cameraPositions.Length;
                StartCoroutine(MoveCameraToPosition(cameraPositions[currentCameraIndex]));
            }
        }
        
        IEnumerator MoveCameraToPosition(Transform targetTransform)
        {
            Vector3 startPos = mainCamera.transform.position;
            Quaternion startRot = mainCamera.transform.rotation;
            Vector3 targetPos = targetTransform.position;
            Quaternion targetRot = targetTransform.rotation;
            
            float elapsed = 0f;
            float duration = 1f / cameraTransitionSpeed;
            
            while (elapsed < duration)
            {
                elapsed += Time.deltaTime;
                float t = elapsed / duration;
                t = Mathf.SmoothStep(0f, 1f, t);
                
                mainCamera.transform.position = Vector3.Lerp(startPos, targetPos, t);
                mainCamera.transform.rotation = Quaternion.Lerp(startRot, targetRot, t);
                
                yield return null;
            }
            
            mainCamera.transform.position = targetPos;
            mainCamera.transform.rotation = targetRot;
        }
        
        void ToggleUI()
        {
            if (demoUI != null)
            {
                demoUI.gameObject.SetActive(!demoUI.gameObject.activeSelf);
            }
        }
        
        IEnumerator UpdateStats()
        {
            while (true)
            {
                if (statsText != null)
                {
                    // 计算破碎的玻璃数量
                    int currentBroken = 0;
                    foreach (GameObject glass in spawnedGlasses)
                    {
                        if (glass != null)
                        {
                            BaseGlass glassComponent = glass.GetComponent<BaseGlass>();
                            if (glassComponent == null || !glass.GetComponent<Renderer>().enabled)
                            {
                                currentBroken++;
                            }
                        }
                        else
                        {
                            currentBroken++;
                        }
                    }
                    
                    brokenGlassCount = currentBroken;
                    
                    statsText.text = $"玻璃统计:\n" +
                                   $"总数: {totalGlassCount}\n" +
                                   $"已破碎: {brokenGlassCount}\n" +
                                   $"完整: {totalGlassCount - brokenGlassCount}\n" +
                                   $"FPS: {(1f / Time.deltaTime):F0}";
                }
                
                yield return new WaitForSeconds(0.5f);
            }
        }
        
        void OnMaterialChanged(int index)
        {
            if (index < glassMaterials.Length)
            {
                Material selectedMaterial = glassMaterials[index];
                
                // 应用材质到所有玻璃
                foreach (GameObject glass in spawnedGlasses)
                {
                    if (glass != null)
                    {
                        Renderer renderer = glass.GetComponent<Renderer>();
                        if (renderer != null)
                        {
                            Material[] materials = renderer.materials;
                            materials[0] = selectedMaterial; // 假设第一个材质是主材质
                            renderer.materials = materials;
                        }
                    }
                }
            }
        }
        
        public void ResetScene()
        {
            SetupScene();
        }
        
        public void LoadNextScene()
        {
            // 这里可以加载下一个演示场景
            Debug.Log("加载下一个场景");
        }
        
        void OnGUI()
        {
            // 显示控制提示
            GUI.Box(new Rect(10, 10, 200, 120), "控制说明");
            GUI.Label(new Rect(20, 35, 180, 20), "左键: 破碎玻璃");
            GUI.Label(new Rect(20, 55, 180, 20), "C键: 切换相机视角");
            GUI.Label(new Rect(20, 75, 180, 20), "Tab键: 切换UI显示");
            GUI.Label(new Rect(20, 95, 180, 20), "R键: 重置场景");
            GUI.Label(new Rect(20, 115, 180, 20), "ESC键: 解锁鼠标");
        }
    }
}
