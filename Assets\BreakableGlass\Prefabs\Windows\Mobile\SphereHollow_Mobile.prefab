%YAML 1.1
%TAG !u! tag:unity3d.com,2011:
--- !u!1 &103512
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 463808}
  - component: {fileID: 11480760}
  - component: {fileID: 3301672}
  - component: {fileID: 2303692}
  - component: {fileID: 13567564}
  - component: {fileID: 8283952}
  m_Layer: 0
  m_Name: SphereHollow_Mobile
  m_TagString: BreakableGlass
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 4294967295
  m_IsActive: 1
--- !u!4 &463808
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 103512}
  serializedVersion: 2
  m_LocalRotation: {x: -4.32978e-17, y: 0.7071067, z: 0.7071068, w: -4.3297806e-17}
  m_LocalPosition: {x: 0, y: 1, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 0}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!114 &11480760
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 103512}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 8072940124c296a4385eb9e137a79e05, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  Replacements:
  - {fileID: 112114, guid: 010d9d9ea4f4bd345b13277b86e26170, type: 3}
  - {fileID: 100178, guid: d2d27746bec507a47aa9e1a9fdb110cd, type: 3}
  - {fileID: 167126, guid: cb483557634a8f34b81878e7d81f94d9, type: 3}
  BreakOnCollision: 1
  SpeedRequiredToBreak: 0
  CleanUpShards: 1
  MinShardLife: 8
  MaxShardLife: 10
  Repair: 1
  RepairTime: 11
  replaceMaterialOnBreak: 0
  ReplacementMaterial: {fileID: 2100000, guid: 2e39d8a8163aa3545a6d62bf3ea64dcf, type: 2}
  ShardMassChangesWithScale: 1
  CrackBeforeShattering: 0
  PlaySound: 1
  ShatterSoundClips:
  - {fileID: 8300000, guid: 2c2da1c25a62c9c47968f8748db76182, type: 3}
  - {fileID: 8300000, guid: f0de69fe7299151439da49df3edf688d, type: 3}
  - {fileID: 8300000, guid: 1f41d6f832a5e324b8680672a2e221fb, type: 3}
  - {fileID: 8300000, guid: 67f70ddaf13330247a99ca579c05a5ea, type: 3}
  - {fileID: 8300000, guid: 507d2a4cd4f3ffc48bc3b8f699b2ca20, type: 3}
  - {fileID: 8300000, guid: f7df8aeb1d5c17d4f897d5eaa85042cd, type: 3}
  - {fileID: 8300000, guid: da266b16da9b40a42a784f200ec7c0da, type: 3}
  - {fileID: 8300000, guid: caca9ad3b45e98041870eafdc5465553, type: 3}
  - {fileID: 8300000, guid: ed6181c840006094bb7799283ab55cd0, type: 3}
  CrackSoundClips:
  - {fileID: 8300000, guid: bf3fdd0b322e9994b8dc0c6431586e56, type: 3}
  - {fileID: 8300000, guid: 0e23a4b937836614691523456b9ff603, type: 3}
  - {fileID: 8300000, guid: 79d60e4a925b465419ca3d2e4805603b, type: 3}
  - {fileID: 8300000, guid: 5b040562d07399046aa608067313b419, type: 3}
  - {fileID: 8300000, guid: 412372b29d23f5c4ea0ba9a4584b4fc5, type: 3}
  - {fileID: 8300000, guid: f808a28f5df68e24e8277f740a7e3252, type: 3}
  - {fileID: 8300000, guid: efd3d80f1cd3f0e41951173fdb813824, type: 3}
  - {fileID: 8300000, guid: 00a49445529a718459fadfdb7681b43c, type: 3}
  - {fileID: 8300000, guid: 3b897c59f1d17c44689e726969db2348, type: 3}
  Cracked: 0
--- !u!33 &3301672
MeshFilter:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 103512}
  m_Mesh: {fileID: 4307284, guid: ca9b4b500c4010242a47988b470c81ff, type: 3}
--- !u!23 &2303692
MeshRenderer:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 103512}
  m_Enabled: 1
  m_CastShadows: 1
  m_ReceiveShadows: 1
  m_DynamicOccludee: 1
  m_StaticShadowCaster: 0
  m_MotionVectors: 1
  m_LightProbeUsage: 1
  m_ReflectionProbeUsage: 1
  m_RayTracingMode: 2
  m_RayTraceProcedural: 0
  m_RenderingLayerMask: 1
  m_RendererPriority: 0
  m_Materials:
  - {fileID: 2100000, guid: 234068967f3b73d40b0f989429d5fb13, type: 2}
  m_StaticBatchInfo:
    firstSubMesh: 0
    subMeshCount: 0
  m_StaticBatchRoot: {fileID: 0}
  m_ProbeAnchor: {fileID: 0}
  m_LightProbeVolumeOverride: {fileID: 0}
  m_ScaleInLightmap: 1
  m_ReceiveGI: 1
  m_PreserveUVs: 0
  m_IgnoreNormalsForChartDetection: 0
  m_ImportantGI: 0
  m_StitchLightmapSeams: 1
  m_SelectedEditorRenderState: 3
  m_MinimumChartSize: 4
  m_AutoUVMaxDistance: 0.5
  m_AutoUVMaxAngle: 89
  m_LightmapParameters: {fileID: 0}
  m_SortingLayerID: 0
  m_SortingLayer: 0
  m_SortingOrder: 0
  m_AdditionalVertexStreams: {fileID: 0}
--- !u!135 &13567564
SphereCollider:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 103512}
  m_Material: {fileID: 0}
  m_IncludeLayers:
    serializedVersion: 2
    m_Bits: 0
  m_ExcludeLayers:
    serializedVersion: 2
    m_Bits: 0
  m_LayerOverridePriority: 0
  m_IsTrigger: 0
  m_ProvidesContacts: 0
  m_Enabled: 1
  serializedVersion: 3
  m_Radius: 1
  m_Center: {x: 0, y: 0, z: 0}
--- !u!82 &8283952
AudioSource:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 103512}
  m_Enabled: 1
  serializedVersion: 4
  OutputAudioMixerGroup: {fileID: 0}
  m_audioClip: {fileID: 8300000, guid: 2c2da1c25a62c9c47968f8748db76182, type: 3}
  m_PlayOnAwake: 0
  m_Volume: 1
  m_Pitch: 1
  Loop: 0
  Mute: 0
  Spatialize: 0
  SpatializePostEffects: 0
  Priority: 128
  DopplerLevel: 1
  MinDistance: 1
  MaxDistance: 500
  Pan2D: 0
  rolloffMode: 0
  BypassEffects: 0
  BypassListenerEffects: 0
  BypassReverbZones: 0
  rolloffCustomCurve:
    serializedVersion: 2
    m_Curve:
    - serializedVersion: 3
      time: 0
      value: 1
      inSlope: 0
      outSlope: 0
      tangentMode: 0
      weightedMode: 0
      inWeight: 0.33333334
      outWeight: 0.33333334
    - serializedVersion: 3
      time: 1
      value: 0
      inSlope: 0
      outSlope: 0
      tangentMode: 0
      weightedMode: 0
      inWeight: 0.33333334
      outWeight: 0.33333334
    m_PreInfinity: 2
    m_PostInfinity: 2
    m_RotationOrder: 4
  panLevelCustomCurve:
    serializedVersion: 2
    m_Curve:
    - serializedVersion: 3
      time: 0
      value: 0
      inSlope: 0
      outSlope: 0
      tangentMode: 0
      weightedMode: 0
      inWeight: 0.33333334
      outWeight: 0.33333334
    m_PreInfinity: 2
    m_PostInfinity: 2
    m_RotationOrder: 4
  spreadCustomCurve:
    serializedVersion: 2
    m_Curve:
    - serializedVersion: 3
      time: 0
      value: 0
      inSlope: 0
      outSlope: 0
      tangentMode: 0
      weightedMode: 0
      inWeight: 0.33333334
      outWeight: 0.33333334
    m_PreInfinity: 2
    m_PostInfinity: 2
    m_RotationOrder: 4
  reverbZoneMixCustomCurve:
    serializedVersion: 2
    m_Curve:
    - serializedVersion: 3
      time: 0
      value: 1
      inSlope: 0
      outSlope: 0
      tangentMode: 0
      weightedMode: 0
      inWeight: 0.33333334
      outWeight: 0.33333334
    m_PreInfinity: 2
    m_PostInfinity: 2
    m_RotationOrder: 4
