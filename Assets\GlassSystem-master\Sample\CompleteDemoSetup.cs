using UnityEngine;
using GlassSystem.Scripts;

namespace GlassSystem.Sample
{
    /// <summary>
    /// 完整的演示场景设置器 - 一键创建完整的Glass System演示场景
    /// </summary>
    public class CompleteDemoSetup : MonoBehaviour
    {
        [Header("一键设置")]
        [SerializeField] private bool setupOnStart = true;
        
        [Header("场景组件")]
        [SerializeField] private bool createEnvironment = true;
        [SerializeField] private bool createGlassObjects = true;
        [SerializeField] private bool setupCamera = true;
        [SerializeField] private bool setupUI = true;
        [SerializeField] private bool setupLighting = true;
        
        void Start()
        {
            if (setupOnStart)
            {
                CreateCompleteDemo();
            }
        }
        
        [ContextMenu("创建完整演示场景")]
        public void CreateCompleteDemo()
        {
            Debug.Log("开始创建Glass System演示场景...");
            
            // 1. 创建环境
            if (createEnvironment)
            {
                CreateEnvironment();
                Debug.Log("✓ 环境创建完成");
            }
            
            // 2. 创建玻璃对象
            if (createGlassObjects)
            {
                CreateGlassObjects();
                Debug.Log("✓ 玻璃对象创建完成");
            }
            
            // 3. 设置相机
            if (setupCamera)
            {
                SetupCameraSystem();
                Debug.Log("✓ 相机系统设置完成");
            }
            
            // 4. 设置UI
            if (setupUI)
            {
                SetupUISystem();
                Debug.Log("✓ UI系统设置完成");
            }
            
            // 5. 设置光照
            if (setupLighting)
            {
                SetupLightingSystem();
                Debug.Log("✓ 光照系统设置完成");
            }
            
            Debug.Log("Glass System演示场景创建完成！");
            Debug.Log("控制说明：左键破碎玻璃，WASD移动，鼠标旋转视角，1-5调整力度，R重置场景");
        }
        
        void CreateEnvironment()
        {
            // 创建地面
            GameObject floor = GameObject.CreatePrimitive(PrimitiveType.Plane);
            floor.name = "Floor";
            floor.transform.position = Vector3.zero;
            floor.transform.localScale = new Vector3(3, 1, 3);
            
            Material floorMat = new Material(Shader.Find("Standard"));
            floorMat.color = new Color(0.7f, 0.7f, 0.7f);
            floorMat.metallic = 0.1f;
            floorMat.smoothness = 0.3f;
            floor.GetComponent<Renderer>().material = floorMat;
            
            // 创建背景墙
            GameObject backWall = GameObject.CreatePrimitive(PrimitiveType.Cube);
            backWall.name = "BackWall";
            backWall.transform.position = new Vector3(0, 3, -8);
            backWall.transform.localScale = new Vector3(25, 6, 1);
            
            Material wallMat = new Material(Shader.Find("Standard"));
            wallMat.color = new Color(0.9f, 0.9f, 0.8f);
            backWall.GetComponent<Renderer>().material = wallMat;
            
            // 创建侧墙
            GameObject leftWall = GameObject.CreatePrimitive(PrimitiveType.Cube);
            leftWall.name = "LeftWall";
            leftWall.transform.position = new Vector3(-12, 3, 0);
            leftWall.transform.localScale = new Vector3(1, 6, 16);
            leftWall.GetComponent<Renderer>().material = wallMat;
            
            GameObject rightWall = GameObject.CreatePrimitive(PrimitiveType.Cube);
            rightWall.name = "RightWall";
            rightWall.transform.position = new Vector3(12, 3, 0);
            rightWall.transform.localScale = new Vector3(1, 6, 16);
            rightWall.GetComponent<Renderer>().material = wallMat;
        }
        
        void CreateGlassObjects()
        {
            // 加载默认资源
            Material glassMat = Resources.Load<Material>("Glass");
            Material glassSideMat = Resources.Load<Material>("GlassSide");
            Mesh[] patterns = new Mesh[]
            {
                Resources.Load<Mesh>("GlassPattern1"),
                Resources.Load<Mesh>("GlassPattern2")
            };
            
            // 创建不同类型的玻璃窗户
            CreateGlassWindow("大窗户", new Vector3(-4, 3, -7.5f), new Vector3(4, 3, 0.1f), glassMat, glassSideMat, patterns, 3);
            CreateGlassWindow("中窗户", new Vector3(2, 2.5f, -7.5f), new Vector3(2.5f, 2, 0.1f), glassMat, glassSideMat, patterns, 2);
            CreateGlassWindow("小窗户", new Vector3(6, 1.5f, -7.5f), new Vector3(1.5f, 1.5f, 0.1f), glassMat, glassSideMat, patterns, 1);
            
            // 侧面玻璃
            CreateGlassWindow("左侧玻璃", new Vector3(-11.5f, 2.5f, -3), new Vector3(0.1f, 2.5f, 3), glassMat, glassSideMat, patterns, 2);
            CreateGlassWindow("右侧玻璃", new Vector3(11.5f, 2.5f, 2), new Vector3(0.1f, 2.5f, 3), glassMat, glassSideMat, patterns, 2);
            
            // 悬浮玻璃板（用于展示不同角度的破碎效果）
            CreateGlassWindow("悬浮玻璃1", new Vector3(-2, 4, -2), new Vector3(2, 2, 0.1f), glassMat, glassSideMat, patterns, 1);
            CreateGlassWindow("悬浮玻璃2", new Vector3(4, 3.5f, 0), new Vector3(1.5f, 1.5f, 0.1f), glassMat, glassSideMat, patterns, 1);
            
            // 倾斜玻璃
            GameObject tiltedGlass = CreateGlassWindow("倾斜玻璃", new Vector3(0, 2, 2), new Vector3(2, 2, 0.1f), glassMat, glassSideMat, patterns, 2);
            tiltedGlass.transform.rotation = Quaternion.Euler(0, 45, 15);
        }
        
        GameObject CreateGlassWindow(string name, Vector3 position, Vector3 size, Material glassMat, Material sideMat, Mesh[] patterns, int health)
        {
            GameObject glassWindow = new GameObject(name);
            glassWindow.transform.position = position;
            
            // 添加组件
            MeshFilter meshFilter = glassWindow.AddComponent<MeshFilter>();
            MeshRenderer meshRenderer = glassWindow.AddComponent<MeshRenderer>();
            BoxCollider collider = glassWindow.AddComponent<BoxCollider>();
            
            // 创建网格
            meshFilter.mesh = CreateGlassMesh(size);
            
            // 设置材质
            Material[] materials = new Material[2];
            materials[0] = glassMat;
            materials[1] = sideMat;
            meshRenderer.materials = materials;
            
            // 设置碰撞器
            collider.size = size;
            
            // 添加玻璃组件
            GlassPanel glassPanel = glassWindow.AddComponent<GlassPanel>();
            glassPanel.Patterns = patterns;
            glassPanel.health = health;
            
            return glassWindow;
        }
        
        Mesh CreateGlassMesh(Vector3 size)
        {
            Mesh mesh = new Mesh();
            mesh.name = "Glass Mesh";
            
            float hx = size.x / 2, hy = size.y / 2, hz = size.z / 2;
            
            Vector3[] vertices = new Vector3[]
            {
                new Vector3(-hx, -hy, hz), new Vector3(hx, -hy, hz), new Vector3(hx, hy, hz), new Vector3(-hx, hy, hz),
                new Vector3(-hx, -hy, -hz), new Vector3(hx, -hy, -hz), new Vector3(hx, hy, -hz), new Vector3(-hx, hy, -hz)
            };
            
            Vector2[] uvs = new Vector2[]
            {
                new Vector2(0, 0), new Vector2(1, 0), new Vector2(1, 1), new Vector2(0, 1),
                new Vector2(1, 0), new Vector2(0, 0), new Vector2(0, 1), new Vector2(1, 1)
            };
            
            mesh.vertices = vertices;
            mesh.uv = uvs;
            mesh.subMeshCount = 2;
            
            // 主表面
            mesh.SetTriangles(new int[] { 0, 2, 1, 0, 3, 2, 4, 5, 6, 4, 6, 7 }, 0);
            
            // 边缘
            mesh.SetTriangles(new int[] 
            {
                0, 1, 5, 0, 5, 4, 1, 2, 6, 1, 6, 5,
                2, 3, 7, 2, 7, 6, 3, 0, 4, 3, 4, 7
            }, 1);
            
            mesh.RecalculateNormals();
            return mesh;
        }
        
        void SetupCameraSystem()
        {
            Camera mainCamera = Camera.main;
            if (mainCamera == null)
                mainCamera = FindObjectOfType<Camera>();
            
            if (mainCamera != null)
            {
                mainCamera.transform.position = new Vector3(0, 2, 8);
                mainCamera.transform.rotation = Quaternion.identity;
                
                // 添加自由飞行相机
                if (mainCamera.GetComponent<FreeFlyCamera>() == null)
                    mainCamera.gameObject.AddComponent<FreeFlyCamera>();
                
                // 添加演示枪
                DemoGun demoGun = mainCamera.GetComponent<DemoGun>();
                if (demoGun == null)
                    demoGun = mainCamera.gameObject.AddComponent<DemoGun>();
                
                demoGun.impactForce = 1000f;
            }
        }
        
        void SetupUISystem()
        {
            // 创建UI管理器
            GameObject uiManager = new GameObject("Demo UI Manager");
            DemoUIManager uiComponent = uiManager.AddComponent<DemoUIManager>();
            uiComponent.createUIOnStart = true;
            uiComponent.showInstructions = true;
        }
        
        void SetupLightingSystem()
        {
            // 设置主光源
            Light directionalLight = FindObjectOfType<Light>();
            if (directionalLight == null)
            {
                GameObject lightGO = new GameObject("Directional Light");
                directionalLight = lightGO.AddComponent<Light>();
            }
            
            directionalLight.type = LightType.Directional;
            directionalLight.intensity = 1.2f;
            directionalLight.shadows = LightShadows.Soft;
            directionalLight.transform.rotation = Quaternion.Euler(45, -30, 0);
            directionalLight.color = new Color(1f, 0.95f, 0.8f);
            
            // 添加环境光
            RenderSettings.ambientMode = UnityEngine.Rendering.AmbientMode.Trilight;
            RenderSettings.ambientSkyColor = new Color(0.5f, 0.7f, 1f);
            RenderSettings.ambientEquatorColor = new Color(0.4f, 0.4f, 0.4f);
            RenderSettings.ambientGroundColor = new Color(0.2f, 0.2f, 0.2f);
        }
        
        [ContextMenu("清理场景")]
        public void ClearScene()
        {
            // 清理所有创建的对象
            GameObject[] objectsToDestroy = GameObject.FindGameObjectsWithTag("Untagged");
            foreach (GameObject obj in objectsToDestroy)
            {
                if (obj.name.Contains("Floor") || obj.name.Contains("Wall") || obj.name.Contains("Glass") || obj.name.Contains("玻璃"))
                {
                    DestroyImmediate(obj);
                }
            }
            
            Debug.Log("场景已清理");
        }
    }
}
