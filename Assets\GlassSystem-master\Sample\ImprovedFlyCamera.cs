using UnityEngine;

namespace GlassSystem.Sample
{
    /// <summary>
    /// 改进的飞行相机控制器 - 使用标准FPS键位和更好的控制体验
    /// </summary>
    [RequireComponent(typeof(Camera))]
    public class ImprovedFlyCamera : MonoBehaviour
    {
        [Header("移动设置")]
        [SerializeField] private bool enableMovement = true;
        [SerializeField] private float normalSpeed = 5f;
        [SerializeField] private float fastSpeed = 15f;
        [SerializeField] private float slowSpeed = 1f;
        [SerializeField] private float acceleration = 2f;
        [SerializeField] private float deceleration = 5f;
        
        [Header("鼠标控制")]
        [SerializeField] private bool enableMouseLook = true;
        [SerializeField] private float mouseSensitivity = 2f;
        [SerializeField] private bool invertY = false;
        [SerializeField] private float maxLookAngle = 90f;
        
        [Header("键位设置")]
        [SerializeField] private KeyCode forwardKey = KeyCode.W;
        [SerializeField] private KeyCode backwardKey = KeyCode.S;
        [SerializeField] private KeyCode leftKey = KeyCode.A;
        [SerializeField] private KeyCode rightKey = KeyCode.D;
        [SerializeField] private KeyCode upKey = KeyCode.Space;
        [SerializeField] private KeyCode downKey = KeyCode.C;
        [SerializeField] private KeyCode fastKey = KeyCode.LeftShift;
        [SerializeField] private KeyCode slowKey = KeyCode.LeftControl;
        [SerializeField] private KeyCode resetKey = KeyCode.R;
        
        [Header("其他设置")]
        [SerializeField] private bool lockCursorOnStart = true;
        [SerializeField] private bool showControlsGUI = true;
        
        // 私有变量
        private Vector3 currentVelocity = Vector3.zero;
        private float currentRotationX = 0f;
        private Vector3 initialPosition;
        private Quaternion initialRotation;
        private bool isControlsVisible = true;
        
        void Start()
        {
            // 保存初始位置和旋转
            initialPosition = transform.position;
            initialRotation = transform.rotation;
            
            // 设置鼠标锁定
            if (lockCursorOnStart)
            {
                Cursor.lockState = CursorLockMode.Locked;
                Cursor.visible = false;
            }
            
            // 获取当前X轴旋转
            currentRotationX = transform.eulerAngles.x;
            if (currentRotationX > 180f)
                currentRotationX -= 360f;
        }
        
        void Update()
        {
            HandleInput();
            HandleMovement();
            HandleMouseLook();
            HandleCursorControl();
        }
        
        void HandleInput()
        {
            // 重置位置
            if (Input.GetKeyDown(resetKey))
            {
                ResetPosition();
            }
            
            // 切换控制说明显示
            if (Input.GetKeyDown(KeyCode.H))
            {
                isControlsVisible = !isControlsVisible;
            }
        }
        
        void HandleMovement()
        {
            if (!enableMovement) return;
            
            Vector3 inputDirection = Vector3.zero;
            
            // 获取输入
            if (Input.GetKey(forwardKey))
                inputDirection += transform.forward;
            if (Input.GetKey(backwardKey))
                inputDirection -= transform.forward;
            if (Input.GetKey(leftKey))
                inputDirection -= transform.right;
            if (Input.GetKey(rightKey))
                inputDirection += transform.right;
            if (Input.GetKey(upKey))
                inputDirection += Vector3.up;
            if (Input.GetKey(downKey))
                inputDirection -= Vector3.up;
            
            // 标准化输入方向
            inputDirection = inputDirection.normalized;
            
            // 确定当前速度
            float targetSpeed = normalSpeed;
            if (Input.GetKey(fastKey))
                targetSpeed = fastSpeed;
            else if (Input.GetKey(slowKey))
                targetSpeed = slowSpeed;
            
            // 计算目标速度
            Vector3 targetVelocity = inputDirection * targetSpeed;
            
            // 平滑加速/减速
            if (inputDirection.magnitude > 0.1f)
            {
                currentVelocity = Vector3.Lerp(currentVelocity, targetVelocity, acceleration * Time.deltaTime);
            }
            else
            {
                currentVelocity = Vector3.Lerp(currentVelocity, Vector3.zero, deceleration * Time.deltaTime);
            }
            
            // 应用移动
            transform.position += currentVelocity * Time.deltaTime;
        }
        
        void HandleMouseLook()
        {
            if (!enableMouseLook || Cursor.lockState != CursorLockMode.Locked) return;
            
            // 获取鼠标输入
            float mouseX = Input.GetAxis("Mouse X") * mouseSensitivity;
            float mouseY = Input.GetAxis("Mouse Y") * mouseSensitivity;
            
            if (invertY)
                mouseY = -mouseY;
            
            // 水平旋转（Y轴）
            transform.Rotate(0, mouseX, 0, Space.World);
            
            // 垂直旋转（X轴）
            currentRotationX -= mouseY;
            currentRotationX = Mathf.Clamp(currentRotationX, -maxLookAngle, maxLookAngle);
            
            // 应用垂直旋转
            Vector3 eulerAngles = transform.eulerAngles;
            eulerAngles.x = currentRotationX;
            transform.eulerAngles = eulerAngles;
        }
        
        void HandleCursorControl()
        {
            // ESC键解锁鼠标
            if (Input.GetKeyDown(KeyCode.Escape))
            {
                if (Cursor.lockState == CursorLockMode.Locked)
                {
                    Cursor.lockState = CursorLockMode.None;
                    Cursor.visible = true;
                }
            }
            
            // 点击锁定鼠标
            if (Input.GetMouseButtonDown(0) && Cursor.lockState == CursorLockMode.None)
            {
                Cursor.lockState = CursorLockMode.Locked;
                Cursor.visible = false;
            }
        }
        
        void ResetPosition()
        {
            transform.position = initialPosition;
            transform.rotation = initialRotation;
            currentVelocity = Vector3.zero;
            currentRotationX = initialRotation.eulerAngles.x;
            if (currentRotationX > 180f)
                currentRotationX -= 360f;
        }
        
        void OnGUI()
        {
            if (!showControlsGUI || !isControlsVisible) return;
            
            // 控制说明面板
            float panelWidth = 280f;
            float panelHeight = 320f;
            float margin = 10f;
            
            Rect panelRect = new Rect(margin, margin, panelWidth, panelHeight);
            GUI.Box(panelRect, "飞行相机控制");
            
            float yOffset = 30f;
            float lineHeight = 18f;
            
            GUI.Label(new Rect(margin + 10, margin + yOffset, panelWidth - 20, lineHeight), "=== 移动控制 ===");
            yOffset += lineHeight + 5;
            
            GUI.Label(new Rect(margin + 10, margin + yOffset, panelWidth - 20, lineHeight), $"W/S: 前进/后退");
            yOffset += lineHeight;
            GUI.Label(new Rect(margin + 10, margin + yOffset, panelWidth - 20, lineHeight), $"A/D: 左移/右移");
            yOffset += lineHeight;
            GUI.Label(new Rect(margin + 10, margin + yOffset, panelWidth - 20, lineHeight), $"空格/C: 上升/下降");
            yOffset += lineHeight;
            
            yOffset += 5;
            GUI.Label(new Rect(margin + 10, margin + yOffset, panelWidth - 20, lineHeight), "=== 速度控制 ===");
            yOffset += lineHeight + 5;
            
            GUI.Label(new Rect(margin + 10, margin + yOffset, panelWidth - 20, lineHeight), $"Shift: 快速移动 ({fastSpeed}m/s)");
            yOffset += lineHeight;
            GUI.Label(new Rect(margin + 10, margin + yOffset, panelWidth - 20, lineHeight), $"Ctrl: 慢速移动 ({slowSpeed}m/s)");
            yOffset += lineHeight;
            GUI.Label(new Rect(margin + 10, margin + yOffset, panelWidth - 20, lineHeight), $"普通速度: {normalSpeed}m/s");
            yOffset += lineHeight;
            
            yOffset += 5;
            GUI.Label(new Rect(margin + 10, margin + yOffset, panelWidth - 20, lineHeight), "=== 视角控制 ===");
            yOffset += lineHeight + 5;
            
            GUI.Label(new Rect(margin + 10, margin + yOffset, panelWidth - 20, lineHeight), "鼠标: 旋转视角");
            yOffset += lineHeight;
            GUI.Label(new Rect(margin + 10, margin + yOffset, panelWidth - 20, lineHeight), "ESC: 解锁鼠标");
            yOffset += lineHeight;
            GUI.Label(new Rect(margin + 10, margin + yOffset, panelWidth - 20, lineHeight), "左键: 锁定鼠标");
            yOffset += lineHeight;
            
            yOffset += 5;
            GUI.Label(new Rect(margin + 10, margin + yOffset, panelWidth - 20, lineHeight), "=== 其他 ===");
            yOffset += lineHeight + 5;
            
            GUI.Label(new Rect(margin + 10, margin + yOffset, panelWidth - 20, lineHeight), "R: 重置位置");
            yOffset += lineHeight;
            GUI.Label(new Rect(margin + 10, margin + yOffset, panelWidth - 20, lineHeight), "H: 切换帮助显示");
            yOffset += lineHeight;
            
            // 状态信息
            yOffset += 10;
            string cursorStatus = Cursor.lockState == CursorLockMode.Locked ? "已锁定" : "未锁定";
            GUI.Label(new Rect(margin + 10, margin + yOffset, panelWidth - 20, lineHeight), $"鼠标状态: {cursorStatus}");
            yOffset += lineHeight;
            GUI.Label(new Rect(margin + 10, margin + yOffset, panelWidth - 20, lineHeight), $"当前速度: {currentVelocity.magnitude:F1}m/s");
        }
        
        // 公共方法
        public void SetPosition(Vector3 position)
        {
            transform.position = position;
            currentVelocity = Vector3.zero;
        }
        
        public void SetRotation(Quaternion rotation)
        {
            transform.rotation = rotation;
            currentRotationX = rotation.eulerAngles.x;
            if (currentRotationX > 180f)
                currentRotationX -= 360f;
        }
        
        public void SetSpeed(float normal, float fast, float slow)
        {
            normalSpeed = normal;
            fastSpeed = fast;
            slowSpeed = slow;
        }
        
        public void SetMouseSensitivity(float sensitivity)
        {
            mouseSensitivity = sensitivity;
        }
    }
}
