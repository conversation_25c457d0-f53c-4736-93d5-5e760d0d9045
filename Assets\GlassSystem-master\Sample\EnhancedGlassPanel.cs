using System;
using System.Collections.Generic;
using System.Linq;
using MathNet.Numerics.LinearAlgebra.Double;
using MathNet.Spatial.Euclidean;
using UnityEngine;
using static GlassSystem.Scripts.MathNetUtils;

namespace GlassSystem.Sample
{
    /// <summary>
    /// 增强版玻璃面板 - 支持碎片的递归破碎和更好的控制
    /// </summary>
    public class EnhancedGlassPanel : MonoBehaviour
    {
        [Header("破碎设置")]
        public Mesh[] Patterns;
        public int health = 2;
        
        [Header("碎片设置")]
        [SerializeField] private bool allowShardBreaking = true;
        [SerializeField] private float minShardSize = 0.05f; // 最小可破碎碎片大小
        [SerializeField] private int maxBreakDepth = 3; // 最大破碎层级
        [SerializeField] private bool autoDestroySmallShards = true;
        [SerializeField] private float smallShardLifetime = 8f;
        [SerializeField] private float microShardLifetime = 4f;
        
        [Header("物理设置")]
        [SerializeField] private bool addPhysicsToShards = true;
        [SerializeField] private float shardMassMultiplier = 1f;
        [SerializeField] private float breakForceMultiplier = 1f;
        
        // 私有变量
        protected List<EnhancedShard> _shards;
        protected Transform _transform;
        protected float _thickness;
        protected Polygon2D _polygon;
        protected Vector2[] _uvs;
        protected const float Tolerance = 0.001f;
        private int _currentBreakDepth = 0;
        
        protected void Start()
        {
            _transform = transform;
            _shards = new List<EnhancedShard>();
        }
        
        /// <summary>
        /// 破碎玻璃的主要方法
        /// </summary>
        public virtual void Break(Vector3 breakPosition, Vector3 originVector, int patternIndex = -1, float rotation = float.NaN)
        {
            if (_shards != null && _shards.Count > 0)
            {
                Debug.LogWarning("玻璃已经破碎过了");
                return;
            }
            
            _shards = new List<EnhancedShard>();
            PerformBreak(breakPosition, originVector, patternIndex, rotation, 0);
            
            // 隐藏原始网格
            var meshRenderer = GetComponent<MeshRenderer>();
            if (meshRenderer != null)
                meshRenderer.enabled = false;
            
            var meshCollider = GetComponent<MeshCollider>();
            if (meshCollider != null)
                meshCollider.enabled = false;
        }
        
        protected virtual void PerformBreak(Vector3 breakPosition, Vector3 originVector, int patternIndex, float rotation, int breakDepth)
        {
            Vector3 localPosition = transform.InverseTransformPoint(breakPosition);
            var scale = _transform.lossyScale;
            localPosition.x *= scale.x;
            localPosition.y *= scale.y;
            
            _polygon ??= BuildPolygon(localPosition.z);
            if (_polygon == null)
                return;
            
            if (patternIndex == -1)
                patternIndex = UnityEngine.Random.Range(0, Patterns.Length);
            if (float.IsNaN(rotation))
                rotation = UnityEngine.Random.Range(0, 360f);
                
            var lines = GlassSystem.Scripts.ClipPattern.Clip(Patterns[patternIndex], _polygon, localPosition, rotation);
            List<Polygon2D> shardPolygons = GlassSystem.Scripts.ShardPolygonBuilder.Build(lines, Tolerance);
            
            var materials = GetComponent<Renderer>().sharedMaterials;
            foreach (Polygon2D shardPolygon in shardPolygons)
            {
                var center = Point2D.Centroid(shardPolygon.Vertices);
                var centeredShardPolygon = shardPolygon.TranslateBy(-center.ToVector2D());
                Vector2[] uvs = null;
                if (_uvs != null)
                    uvs = shardPolygon.Vertices.Select(InterpolateUv).ToArray();
                    
                var shardMesh = CreateMesh(centeredShardPolygon, uvs, _thickness);
                var glassShard = SpawnEnhancedShard(shardMesh, originVector, new Vector3((float)center.X, (float)center.Y, 0), materials, breakDepth);
                
                if (glassShard != null)
                {
                    glassShard.InitializeShard(this, centeredShardPolygon, uvs, _thickness, breakDepth);
                    _shards.Add(glassShard);
                }
            }
        }
        
        protected virtual Polygon2D BuildPolygon(float side)
        {
            var targetMeshFilter = GetComponent<MeshFilter>();
            if (targetMeshFilter == null)
                return null;
            
            var targetMesh = targetMeshFilter.sharedMesh;
            var targetVertices = targetMesh.vertices;
            if (targetVertices.Length is > 100 or < 3)
            {
                Debug.LogWarning($"Invalid mesh ({targetVertices.Length})");
                return null;
            }
            
            // Scale
            var scale = _transform.lossyScale;
            var scalingMatrix = new DiagonalMatrix(2, 2, new double[] { scale.x, scale.y });
            
            // Thickness
            var verticesZ = targetVertices.Select(p => p.z).ToList();
            _thickness = (verticesZ.Max() + Mathf.Abs(verticesZ.Min())) * scale.z;
            
            // Vertices to polygon
            var targetPoints = targetVertices.Select((p, i) => new IndexedPoint(p, i)).ToList();
            targetPoints.RemoveAll(p => Mathf.Abs(p.Z - side) > Tolerance);
            targetPoints = targetPoints.Distinct(new Point2DComparer(Tolerance)).ToList();
            foreach (var point in targetPoints)
                point.TransformBy(scalingMatrix);
            
            // Build convex polygon
            targetPoints.Sort((a, b) => CompareVectorAngle(new Point2D(0, 0), a, b));
            Polygon2D targetPolygon = new Polygon2D(targetPoints.Select(p => p.Point2D));
            
            // UVs
            var uvs = targetMesh.uv;
            if (uvs != null && uvs.Length > 0)
            {
                _uvs = new Vector2[targetPoints.Count];
                for (int i = 0; i < targetPoints.Count; i++)
                    _uvs[i] = uvs[targetPoints[i].Index];
            }
            
            return targetPolygon;
        }
        
        EnhancedShard SpawnEnhancedShard(Mesh mesh, Vector3 originVector, Vector3 offset, Material[] materials, int breakDepth)
        {
            float shardSurface = mesh.bounds.size.x * mesh.bounds.size.y;
            
            var go = new GameObject($"Shard_Depth{breakDepth}_{mesh.name}");
            go.tag = gameObject.tag;
            
            var rotation = _transform.rotation;
            go.transform.position = _transform.position + rotation * offset;
            go.transform.rotation = rotation;
            go.transform.parent = transform.parent;
            
            // 添加网格组件
            var meshFilter = go.AddComponent<MeshFilter>();
            meshFilter.sharedMesh = mesh;
            
            var meshRenderer = go.AddComponent<MeshRenderer>();
            meshRenderer.sharedMaterials = materials;
            
            var meshCollider = go.AddComponent<MeshCollider>();
            meshCollider.convex = true;
            meshCollider.sharedMesh = mesh;
            
            EnhancedShard shard = null;
            
            // 决定是否可以继续破碎
            bool canBreakFurther = allowShardBreaking && 
                                 shardSurface >= minShardSize && 
                                 breakDepth < maxBreakDepth;
            
            if (canBreakFurther)
            {
                // 添加增强碎片组件
                shard = go.AddComponent<EnhancedShard>();
                shard.Patterns = Patterns;
                shard.allowShardBreaking = allowShardBreaking;
                shard.minShardSize = minShardSize;
                shard.maxBreakDepth = maxBreakDepth;
                shard.autoDestroySmallShards = autoDestroySmallShards;
                shard.smallShardLifetime = smallShardLifetime;
                shard.microShardLifetime = microShardLifetime;
                shard.addPhysicsToShards = addPhysicsToShards;
                shard.shardMassMultiplier = shardMassMultiplier;
                shard.breakForceMultiplier = breakForceMultiplier;
            }
            
            // 添加物理效果
            if (addPhysicsToShards)
            {
                var shardRigidbody = go.AddComponent<Rigidbody>();
                shardRigidbody.mass = shardSurface * shardMassMultiplier;
                shardRigidbody.collisionDetectionMode = CollisionDetectionMode.Continuous;
                shardRigidbody.AddForce(originVector * breakForceMultiplier);
            }
            
            // 自动销毁小碎片
            if (autoDestroySmallShards && !canBreakFurther)
            {
                float lifetime = shardSurface < 0.07f ? microShardLifetime : smallShardLifetime;
                Destroy(go, lifetime);
            }
            
            return shard;
        }
        
        Mesh CreateMesh(Polygon2D polygon, Vector2[] uvs, float thickness)
        {
            // 这里应该实现网格创建逻辑
            // 为了简化，我们使用一个基本的实现
            Mesh mesh = new Mesh();
            mesh.name = "Enhanced Shard Mesh";
            
            // 简化的网格创建 - 在实际使用中应该使用更复杂的逻辑
            var vertices = polygon.Vertices.ToArray();
            Vector3[] meshVertices = new Vector3[vertices.Length * 2];
            
            for (int i = 0; i < vertices.Length; i++)
            {
                meshVertices[i] = new Vector3((float)vertices[i].X, (float)vertices[i].Y, thickness / 2);
                meshVertices[i + vertices.Length] = new Vector3((float)vertices[i].X, (float)vertices[i].Y, -thickness / 2);
            }
            
            mesh.vertices = meshVertices;
            
            // 简化的三角形创建
            List<int> triangles = new List<int>();
            for (int i = 0; i < vertices.Length - 2; i++)
            {
                triangles.Add(0);
                triangles.Add(i + 1);
                triangles.Add(i + 2);
            }
            
            mesh.triangles = triangles.ToArray();
            mesh.RecalculateNormals();
            mesh.RecalculateBounds();
            
            return mesh;
        }
        
        Vector2 InterpolateUv(Point2D point)
        {
            // 简化的UV插值
            return new Vector2((float)point.X, (float)point.Y);
        }
        
        public void OnShardDestroyed(EnhancedShard shard)
        {
            if (_shards != null)
            {
                _shards.Remove(shard);
                if (health > 0)
                {
                    health -= 1;
                    if (health == 0)
                    {
                        foreach (EnhancedShard s in _shards)
                        {
                            s.Fall();
                        }
                    }
                }
            }
        }
        
        public void OnNewShard(EnhancedShard shard)
        {
            if (_shards != null)
            {
                _shards.Add(shard);
            }
        }
        
        // 公共方法用于配置
        public void SetShardBreakingEnabled(bool enabled)
        {
            allowShardBreaking = enabled;
        }
        
        public void SetMaxBreakDepth(int depth)
        {
            maxBreakDepth = depth;
        }
        
        public void SetMinShardSize(float size)
        {
            minShardSize = size;
        }
    }
}
