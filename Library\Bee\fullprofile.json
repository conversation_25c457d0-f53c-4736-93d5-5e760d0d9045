{"Instructions Readme": "1) Open Chrome or Edge, 2) go to chrome://tracing, 3) click Load, 4) navigate to this file.", "processInfo": {}, "traceEvents": [{"pid": 3100, "tid": -1, "ph": "M", "name": "process_name", "args": {"name": "BeeDriver"}}, {"pid": 3100, "tid": -1, "ph": "M", "name": "process_sort_index", "args": {"sort_index": "-2"}}, {"pid": 3100, "tid": 24784, "ph": "M", "name": "thread_name", "args": {"name": "Thread Pool Worker"}}, {"pid": 3100, "tid": 24784, "ts": 1754736894303866, "dur": 8, "ph": "X", "name": "ChromeTraceHeader", "args": {}}, {"pid": 3100, "tid": 24784, "ts": 1754736894303886, "dur": 3, "ph": "X", "name": "Thread Pool Worker", "args": {}}, {"pid": 3100, "tid": 42949672960, "ph": "M", "name": "thread_name", "args": {"name": "ReadEntireBinlogFromIpcAsync"}}, {"pid": 3100, "tid": 42949672960, "ts": 1754736893692174, "dur": 142809, "ph": "X", "name": "WaitForConnectionAsync", "args": {}}, {"pid": 3100, "tid": 42949672960, "ts": 1754736893834984, "dur": 468297, "ph": "X", "name": "UpdateFromStreamAsync", "args": {}}, {"pid": 3100, "tid": 42949672960, "ts": 1754736893835002, "dur": 49, "ph": "X", "name": "ReadAsync 0", "args": {}}, {"pid": 3100, "tid": 42949672960, "ts": 1754736893835058, "dur": 2, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 3100, "tid": 42949672960, "ts": 1754736893835065, "dur": 102570, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 3100, "tid": 42949672960, "ts": 1754736893937647, "dur": 3, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 3100, "tid": 42949672960, "ts": 1754736893937652, "dur": 66, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3100, "tid": 42949672960, "ts": 1754736893937724, "dur": 9, "ph": "X", "name": "ProcessMessages 41", "args": {}}, {"pid": 3100, "tid": 42949672960, "ts": 1754736893937734, "dur": 2243, "ph": "X", "name": "ReadAsync 41", "args": {}}, {"pid": 3100, "tid": 42949672960, "ts": 1754736893939986, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 3100, "tid": 42949672960, "ts": 1754736893939989, "dur": 45, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3100, "tid": 42949672960, "ts": 1754736893940035, "dur": 1, "ph": "X", "name": "ProcessMessages 1088", "args": {}}, {"pid": 3100, "tid": 42949672960, "ts": 1754736893940037, "dur": 33, "ph": "X", "name": "ReadAsync 1088", "args": {}}, {"pid": 3100, "tid": 42949672960, "ts": 1754736893940072, "dur": 31, "ph": "X", "name": "ReadAsync 261", "args": {}}, {"pid": 3100, "tid": 42949672960, "ts": 1754736893940106, "dur": 28, "ph": "X", "name": "ReadAsync 521", "args": {}}, {"pid": 3100, "tid": 42949672960, "ts": 1754736893940137, "dur": 26, "ph": "X", "name": "ReadAsync 350", "args": {}}, {"pid": 3100, "tid": 42949672960, "ts": 1754736893940165, "dur": 22, "ph": "X", "name": "ReadAsync 454", "args": {}}, {"pid": 3100, "tid": 42949672960, "ts": 1754736893940189, "dur": 30, "ph": "X", "name": "ReadAsync 450", "args": {}}, {"pid": 3100, "tid": 42949672960, "ts": 1754736893940222, "dur": 28, "ph": "X", "name": "ReadAsync 354", "args": {}}, {"pid": 3100, "tid": 42949672960, "ts": 1754736893940252, "dur": 24, "ph": "X", "name": "ReadAsync 605", "args": {}}, {"pid": 3100, "tid": 42949672960, "ts": 1754736893940278, "dur": 27, "ph": "X", "name": "ReadAsync 373", "args": {}}, {"pid": 3100, "tid": 42949672960, "ts": 1754736893940307, "dur": 27, "ph": "X", "name": "ReadAsync 444", "args": {}}, {"pid": 3100, "tid": 42949672960, "ts": 1754736893940336, "dur": 43, "ph": "X", "name": "ReadAsync 345", "args": {}}, {"pid": 3100, "tid": 42949672960, "ts": 1754736893940382, "dur": 26, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3100, "tid": 42949672960, "ts": 1754736893940410, "dur": 24, "ph": "X", "name": "ReadAsync 398", "args": {}}, {"pid": 3100, "tid": 42949672960, "ts": 1754736893940436, "dur": 18, "ph": "X", "name": "ReadAsync 559", "args": {}}, {"pid": 3100, "tid": 42949672960, "ts": 1754736893940457, "dur": 27, "ph": "X", "name": "ReadAsync 337", "args": {}}, {"pid": 3100, "tid": 42949672960, "ts": 1754736893940486, "dur": 28, "ph": "X", "name": "ReadAsync 338", "args": {}}, {"pid": 3100, "tid": 42949672960, "ts": 1754736893940516, "dur": 29, "ph": "X", "name": "ReadAsync 475", "args": {}}, {"pid": 3100, "tid": 42949672960, "ts": 1754736893940547, "dur": 26, "ph": "X", "name": "ReadAsync 529", "args": {}}, {"pid": 3100, "tid": 42949672960, "ts": 1754736893940574, "dur": 31, "ph": "X", "name": "ReadAsync 223", "args": {}}, {"pid": 3100, "tid": 42949672960, "ts": 1754736893940609, "dur": 1, "ph": "X", "name": "ProcessMessages 340", "args": {}}, {"pid": 3100, "tid": 42949672960, "ts": 1754736893940612, "dur": 32, "ph": "X", "name": "ReadAsync 340", "args": {}}, {"pid": 3100, "tid": 42949672960, "ts": 1754736893940647, "dur": 27, "ph": "X", "name": "ReadAsync 691", "args": {}}, {"pid": 3100, "tid": 42949672960, "ts": 1754736893940675, "dur": 27, "ph": "X", "name": "ReadAsync 460", "args": {}}, {"pid": 3100, "tid": 42949672960, "ts": 1754736893940705, "dur": 25, "ph": "X", "name": "ReadAsync 411", "args": {}}, {"pid": 3100, "tid": 42949672960, "ts": 1754736893940732, "dur": 25, "ph": "X", "name": "ReadAsync 357", "args": {}}, {"pid": 3100, "tid": 42949672960, "ts": 1754736893940758, "dur": 24, "ph": "X", "name": "ReadAsync 360", "args": {}}, {"pid": 3100, "tid": 42949672960, "ts": 1754736893940784, "dur": 35, "ph": "X", "name": "ReadAsync 294", "args": {}}, {"pid": 3100, "tid": 42949672960, "ts": 1754736893940821, "dur": 26, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3100, "tid": 42949672960, "ts": 1754736893940850, "dur": 25, "ph": "X", "name": "ReadAsync 455", "args": {}}, {"pid": 3100, "tid": 42949672960, "ts": 1754736893940876, "dur": 28, "ph": "X", "name": "ReadAsync 118", "args": {}}, {"pid": 3100, "tid": 42949672960, "ts": 1754736893940907, "dur": 26, "ph": "X", "name": "ReadAsync 400", "args": {}}, {"pid": 3100, "tid": 42949672960, "ts": 1754736893940935, "dur": 25, "ph": "X", "name": "ReadAsync 264", "args": {}}, {"pid": 3100, "tid": 42949672960, "ts": 1754736893940962, "dur": 27, "ph": "X", "name": "ReadAsync 425", "args": {}}, {"pid": 3100, "tid": 42949672960, "ts": 1754736893940991, "dur": 17, "ph": "X", "name": "ReadAsync 484", "args": {}}, {"pid": 3100, "tid": 42949672960, "ts": 1754736893941010, "dur": 18, "ph": "X", "name": "ReadAsync 335", "args": {}}, {"pid": 3100, "tid": 42949672960, "ts": 1754736893941030, "dur": 21, "ph": "X", "name": "ReadAsync 259", "args": {}}, {"pid": 3100, "tid": 42949672960, "ts": 1754736893941053, "dur": 24, "ph": "X", "name": "ReadAsync 290", "args": {}}, {"pid": 3100, "tid": 42949672960, "ts": 1754736893941079, "dur": 38, "ph": "X", "name": "ReadAsync 394", "args": {}}, {"pid": 3100, "tid": 42949672960, "ts": 1754736893941118, "dur": 27, "ph": "X", "name": "ReadAsync 323", "args": {}}, {"pid": 3100, "tid": 42949672960, "ts": 1754736893941147, "dur": 26, "ph": "X", "name": "ReadAsync 493", "args": {}}, {"pid": 3100, "tid": 42949672960, "ts": 1754736893941174, "dur": 42, "ph": "X", "name": "ReadAsync 355", "args": {}}, {"pid": 3100, "tid": 42949672960, "ts": 1754736893941219, "dur": 29, "ph": "X", "name": "ReadAsync 254", "args": {}}, {"pid": 3100, "tid": 42949672960, "ts": 1754736893941249, "dur": 24, "ph": "X", "name": "ReadAsync 534", "args": {}}, {"pid": 3100, "tid": 42949672960, "ts": 1754736893941276, "dur": 25, "ph": "X", "name": "ReadAsync 343", "args": {}}, {"pid": 3100, "tid": 42949672960, "ts": 1754736893941302, "dur": 26, "ph": "X", "name": "ReadAsync 143", "args": {}}, {"pid": 3100, "tid": 42949672960, "ts": 1754736893941330, "dur": 26, "ph": "X", "name": "ReadAsync 534", "args": {}}, {"pid": 3100, "tid": 42949672960, "ts": 1754736893941358, "dur": 25, "ph": "X", "name": "ReadAsync 356", "args": {}}, {"pid": 3100, "tid": 42949672960, "ts": 1754736893941384, "dur": 27, "ph": "X", "name": "ReadAsync 469", "args": {}}, {"pid": 3100, "tid": 42949672960, "ts": 1754736893941413, "dur": 26, "ph": "X", "name": "ReadAsync 438", "args": {}}, {"pid": 3100, "tid": 42949672960, "ts": 1754736893941442, "dur": 26, "ph": "X", "name": "ReadAsync 350", "args": {}}, {"pid": 3100, "tid": 42949672960, "ts": 1754736893941469, "dur": 26, "ph": "X", "name": "ReadAsync 471", "args": {}}, {"pid": 3100, "tid": 42949672960, "ts": 1754736893941497, "dur": 26, "ph": "X", "name": "ReadAsync 347", "args": {}}, {"pid": 3100, "tid": 42949672960, "ts": 1754736893941526, "dur": 26, "ph": "X", "name": "ReadAsync 465", "args": {}}, {"pid": 3100, "tid": 42949672960, "ts": 1754736893941553, "dur": 25, "ph": "X", "name": "ReadAsync 458", "args": {}}, {"pid": 3100, "tid": 42949672960, "ts": 1754736893941580, "dur": 26, "ph": "X", "name": "ReadAsync 509", "args": {}}, {"pid": 3100, "tid": 42949672960, "ts": 1754736893941607, "dur": 27, "ph": "X", "name": "ReadAsync 348", "args": {}}, {"pid": 3100, "tid": 42949672960, "ts": 1754736893941636, "dur": 26, "ph": "X", "name": "ReadAsync 452", "args": {}}, {"pid": 3100, "tid": 42949672960, "ts": 1754736893941662, "dur": 3, "ph": "X", "name": "ProcessMessages 419", "args": {}}, {"pid": 3100, "tid": 42949672960, "ts": 1754736893941666, "dur": 23, "ph": "X", "name": "ReadAsync 419", "args": {}}, {"pid": 3100, "tid": 42949672960, "ts": 1754736893941691, "dur": 26, "ph": "X", "name": "ReadAsync 8", "args": {}}, {"pid": 3100, "tid": 42949672960, "ts": 1754736893941719, "dur": 25, "ph": "X", "name": "ReadAsync 321", "args": {}}, {"pid": 3100, "tid": 42949672960, "ts": 1754736893941746, "dur": 24, "ph": "X", "name": "ReadAsync 591", "args": {}}, {"pid": 3100, "tid": 42949672960, "ts": 1754736893941772, "dur": 24, "ph": "X", "name": "ReadAsync 378", "args": {}}, {"pid": 3100, "tid": 42949672960, "ts": 1754736893941799, "dur": 23, "ph": "X", "name": "ReadAsync 456", "args": {}}, {"pid": 3100, "tid": 42949672960, "ts": 1754736893941824, "dur": 25, "ph": "X", "name": "ReadAsync 475", "args": {}}, {"pid": 3100, "tid": 42949672960, "ts": 1754736893941851, "dur": 27, "ph": "X", "name": "ReadAsync 505", "args": {}}, {"pid": 3100, "tid": 42949672960, "ts": 1754736893941880, "dur": 26, "ph": "X", "name": "ReadAsync 496", "args": {}}, {"pid": 3100, "tid": 42949672960, "ts": 1754736893941908, "dur": 25, "ph": "X", "name": "ReadAsync 613", "args": {}}, {"pid": 3100, "tid": 42949672960, "ts": 1754736893941934, "dur": 27, "ph": "X", "name": "ReadAsync 464", "args": {}}, {"pid": 3100, "tid": 42949672960, "ts": 1754736893941964, "dur": 26, "ph": "X", "name": "ReadAsync 481", "args": {}}, {"pid": 3100, "tid": 42949672960, "ts": 1754736893941992, "dur": 28, "ph": "X", "name": "ReadAsync 326", "args": {}}, {"pid": 3100, "tid": 42949672960, "ts": 1754736893942022, "dur": 26, "ph": "X", "name": "ReadAsync 549", "args": {}}, {"pid": 3100, "tid": 42949672960, "ts": 1754736893942050, "dur": 27, "ph": "X", "name": "ReadAsync 342", "args": {}}, {"pid": 3100, "tid": 42949672960, "ts": 1754736893942079, "dur": 27, "ph": "X", "name": "ReadAsync 453", "args": {}}, {"pid": 3100, "tid": 42949672960, "ts": 1754736893942108, "dur": 26, "ph": "X", "name": "ReadAsync 461", "args": {}}, {"pid": 3100, "tid": 42949672960, "ts": 1754736893942136, "dur": 24, "ph": "X", "name": "ReadAsync 361", "args": {}}, {"pid": 3100, "tid": 42949672960, "ts": 1754736893942163, "dur": 25, "ph": "X", "name": "ReadAsync 353", "args": {}}, {"pid": 3100, "tid": 42949672960, "ts": 1754736893942189, "dur": 25, "ph": "X", "name": "ReadAsync 442", "args": {}}, {"pid": 3100, "tid": 42949672960, "ts": 1754736893942216, "dur": 26, "ph": "X", "name": "ReadAsync 372", "args": {}}, {"pid": 3100, "tid": 42949672960, "ts": 1754736893942244, "dur": 21, "ph": "X", "name": "ReadAsync 497", "args": {}}, {"pid": 3100, "tid": 42949672960, "ts": 1754736893942267, "dur": 24, "ph": "X", "name": "ReadAsync 226", "args": {}}, {"pid": 3100, "tid": 42949672960, "ts": 1754736893942292, "dur": 26, "ph": "X", "name": "ReadAsync 413", "args": {}}, {"pid": 3100, "tid": 42949672960, "ts": 1754736893942321, "dur": 25, "ph": "X", "name": "ReadAsync 401", "args": {}}, {"pid": 3100, "tid": 42949672960, "ts": 1754736893942347, "dur": 1, "ph": "X", "name": "ProcessMessages 440", "args": {}}, {"pid": 3100, "tid": 42949672960, "ts": 1754736893942348, "dur": 25, "ph": "X", "name": "ReadAsync 440", "args": {}}, {"pid": 3100, "tid": 42949672960, "ts": 1754736893942376, "dur": 25, "ph": "X", "name": "ReadAsync 333", "args": {}}, {"pid": 3100, "tid": 42949672960, "ts": 1754736893942402, "dur": 15, "ph": "X", "name": "ReadAsync 379", "args": {}}, {"pid": 3100, "tid": 42949672960, "ts": 1754736893942419, "dur": 23, "ph": "X", "name": "ReadAsync 190", "args": {}}, {"pid": 3100, "tid": 42949672960, "ts": 1754736893942443, "dur": 21, "ph": "X", "name": "ReadAsync 265", "args": {}}, {"pid": 3100, "tid": 42949672960, "ts": 1754736893942467, "dur": 28, "ph": "X", "name": "ReadAsync 21", "args": {}}, {"pid": 3100, "tid": 42949672960, "ts": 1754736893942497, "dur": 33, "ph": "X", "name": "ReadAsync 325", "args": {}}, {"pid": 3100, "tid": 42949672960, "ts": 1754736893942531, "dur": 1, "ph": "X", "name": "ProcessMessages 452", "args": {}}, {"pid": 3100, "tid": 42949672960, "ts": 1754736893942533, "dur": 51, "ph": "X", "name": "ReadAsync 452", "args": {}}, {"pid": 3100, "tid": 42949672960, "ts": 1754736893942586, "dur": 1, "ph": "X", "name": "ProcessMessages 566", "args": {}}, {"pid": 3100, "tid": 42949672960, "ts": 1754736893942588, "dur": 51, "ph": "X", "name": "ReadAsync 566", "args": {}}, {"pid": 3100, "tid": 42949672960, "ts": 1754736893942641, "dur": 1, "ph": "X", "name": "ProcessMessages 591", "args": {}}, {"pid": 3100, "tid": 42949672960, "ts": 1754736893942642, "dur": 51, "ph": "X", "name": "ReadAsync 591", "args": {}}, {"pid": 3100, "tid": 42949672960, "ts": 1754736893942695, "dur": 1, "ph": "X", "name": "ProcessMessages 595", "args": {}}, {"pid": 3100, "tid": 42949672960, "ts": 1754736893942696, "dur": 52, "ph": "X", "name": "ReadAsync 595", "args": {}}, {"pid": 3100, "tid": 42949672960, "ts": 1754736893942750, "dur": 1, "ph": "X", "name": "ProcessMessages 602", "args": {}}, {"pid": 3100, "tid": 42949672960, "ts": 1754736893942752, "dur": 50, "ph": "X", "name": "ReadAsync 602", "args": {}}, {"pid": 3100, "tid": 42949672960, "ts": 1754736893942804, "dur": 1, "ph": "X", "name": "ProcessMessages 570", "args": {}}, {"pid": 3100, "tid": 42949672960, "ts": 1754736893942806, "dur": 49, "ph": "X", "name": "ReadAsync 570", "args": {}}, {"pid": 3100, "tid": 42949672960, "ts": 1754736893942857, "dur": 1, "ph": "X", "name": "ProcessMessages 452", "args": {}}, {"pid": 3100, "tid": 42949672960, "ts": 1754736893942859, "dur": 47, "ph": "X", "name": "ReadAsync 452", "args": {}}, {"pid": 3100, "tid": 42949672960, "ts": 1754736893942910, "dur": 54, "ph": "X", "name": "ReadAsync 150", "args": {}}, {"pid": 3100, "tid": 42949672960, "ts": 1754736893942965, "dur": 1, "ph": "X", "name": "ProcessMessages 355", "args": {}}, {"pid": 3100, "tid": 42949672960, "ts": 1754736893942967, "dur": 52, "ph": "X", "name": "ReadAsync 355", "args": {}}, {"pid": 3100, "tid": 42949672960, "ts": 1754736893943021, "dur": 1, "ph": "X", "name": "ProcessMessages 705", "args": {}}, {"pid": 3100, "tid": 42949672960, "ts": 1754736893943022, "dur": 52, "ph": "X", "name": "ReadAsync 705", "args": {}}, {"pid": 3100, "tid": 42949672960, "ts": 1754736893943076, "dur": 1, "ph": "X", "name": "ProcessMessages 510", "args": {}}, {"pid": 3100, "tid": 42949672960, "ts": 1754736893943078, "dur": 48, "ph": "X", "name": "ReadAsync 510", "args": {}}, {"pid": 3100, "tid": 42949672960, "ts": 1754736893943128, "dur": 1, "ph": "X", "name": "ProcessMessages 451", "args": {}}, {"pid": 3100, "tid": 42949672960, "ts": 1754736893943130, "dur": 51, "ph": "X", "name": "ReadAsync 451", "args": {}}, {"pid": 3100, "tid": 42949672960, "ts": 1754736893943182, "dur": 1, "ph": "X", "name": "ProcessMessages 668", "args": {}}, {"pid": 3100, "tid": 42949672960, "ts": 1754736893943185, "dur": 50, "ph": "X", "name": "ReadAsync 668", "args": {}}, {"pid": 3100, "tid": 42949672960, "ts": 1754736893943237, "dur": 1, "ph": "X", "name": "ProcessMessages 553", "args": {}}, {"pid": 3100, "tid": 42949672960, "ts": 1754736893943239, "dur": 51, "ph": "X", "name": "ReadAsync 553", "args": {}}, {"pid": 3100, "tid": 42949672960, "ts": 1754736893943291, "dur": 1, "ph": "X", "name": "ProcessMessages 548", "args": {}}, {"pid": 3100, "tid": 42949672960, "ts": 1754736893943293, "dur": 48, "ph": "X", "name": "ReadAsync 548", "args": {}}, {"pid": 3100, "tid": 42949672960, "ts": 1754736893943344, "dur": 48, "ph": "X", "name": "ReadAsync 390", "args": {}}, {"pid": 3100, "tid": 42949672960, "ts": 1754736893943395, "dur": 50, "ph": "X", "name": "ReadAsync 454", "args": {}}, {"pid": 3100, "tid": 42949672960, "ts": 1754736893943447, "dur": 1, "ph": "X", "name": "ProcessMessages 706", "args": {}}, {"pid": 3100, "tid": 42949672960, "ts": 1754736893943449, "dur": 30, "ph": "X", "name": "ReadAsync 706", "args": {}}, {"pid": 3100, "tid": 42949672960, "ts": 1754736893943481, "dur": 22, "ph": "X", "name": "ReadAsync 692", "args": {}}, {"pid": 3100, "tid": 42949672960, "ts": 1754736893943505, "dur": 25, "ph": "X", "name": "ReadAsync 359", "args": {}}, {"pid": 3100, "tid": 42949672960, "ts": 1754736893943532, "dur": 25, "ph": "X", "name": "ReadAsync 395", "args": {}}, {"pid": 3100, "tid": 42949672960, "ts": 1754736893943559, "dur": 24, "ph": "X", "name": "ReadAsync 343", "args": {}}, {"pid": 3100, "tid": 42949672960, "ts": 1754736893943585, "dur": 24, "ph": "X", "name": "ReadAsync 293", "args": {}}, {"pid": 3100, "tid": 42949672960, "ts": 1754736893943611, "dur": 23, "ph": "X", "name": "ReadAsync 363", "args": {}}, {"pid": 3100, "tid": 42949672960, "ts": 1754736893943636, "dur": 27, "ph": "X", "name": "ReadAsync 232", "args": {}}, {"pid": 3100, "tid": 42949672960, "ts": 1754736893943665, "dur": 30, "ph": "X", "name": "ReadAsync 387", "args": {}}, {"pid": 3100, "tid": 42949672960, "ts": 1754736893943697, "dur": 25, "ph": "X", "name": "ReadAsync 301", "args": {}}, {"pid": 3100, "tid": 42949672960, "ts": 1754736893943724, "dur": 25, "ph": "X", "name": "ReadAsync 343", "args": {}}, {"pid": 3100, "tid": 42949672960, "ts": 1754736893943751, "dur": 29, "ph": "X", "name": "ReadAsync 352", "args": {}}, {"pid": 3100, "tid": 42949672960, "ts": 1754736893943781, "dur": 53, "ph": "X", "name": "ReadAsync 348", "args": {}}, {"pid": 3100, "tid": 42949672960, "ts": 1754736893943837, "dur": 1, "ph": "X", "name": "ProcessMessages 424", "args": {}}, {"pid": 3100, "tid": 42949672960, "ts": 1754736893943839, "dur": 50, "ph": "X", "name": "ReadAsync 424", "args": {}}, {"pid": 3100, "tid": 42949672960, "ts": 1754736893943892, "dur": 1, "ph": "X", "name": "ProcessMessages 737", "args": {}}, {"pid": 3100, "tid": 42949672960, "ts": 1754736893943894, "dur": 43, "ph": "X", "name": "ReadAsync 737", "args": {}}, {"pid": 3100, "tid": 42949672960, "ts": 1754736893943940, "dur": 1, "ph": "X", "name": "ProcessMessages 288", "args": {}}, {"pid": 3100, "tid": 42949672960, "ts": 1754736893943942, "dur": 37, "ph": "X", "name": "ReadAsync 288", "args": {}}, {"pid": 3100, "tid": 42949672960, "ts": 1754736893943981, "dur": 25, "ph": "X", "name": "ReadAsync 514", "args": {}}, {"pid": 3100, "tid": 42949672960, "ts": 1754736893944008, "dur": 22, "ph": "X", "name": "ReadAsync 336", "args": {}}, {"pid": 3100, "tid": 42949672960, "ts": 1754736893944033, "dur": 25, "ph": "X", "name": "ReadAsync 283", "args": {}}, {"pid": 3100, "tid": 42949672960, "ts": 1754736893944059, "dur": 29, "ph": "X", "name": "ReadAsync 301", "args": {}}, {"pid": 3100, "tid": 42949672960, "ts": 1754736893944091, "dur": 25, "ph": "X", "name": "ReadAsync 420", "args": {}}, {"pid": 3100, "tid": 42949672960, "ts": 1754736893944119, "dur": 24, "ph": "X", "name": "ReadAsync 9", "args": {}}, {"pid": 3100, "tid": 42949672960, "ts": 1754736893944144, "dur": 25, "ph": "X", "name": "ReadAsync 96", "args": {}}, {"pid": 3100, "tid": 42949672960, "ts": 1754736893944171, "dur": 25, "ph": "X", "name": "ReadAsync 179", "args": {}}, {"pid": 3100, "tid": 42949672960, "ts": 1754736893944198, "dur": 24, "ph": "X", "name": "ReadAsync 184", "args": {}}, {"pid": 3100, "tid": 42949672960, "ts": 1754736893944225, "dur": 24, "ph": "X", "name": "ReadAsync 155", "args": {}}, {"pid": 3100, "tid": 42949672960, "ts": 1754736893944251, "dur": 25, "ph": "X", "name": "ReadAsync 40", "args": {}}, {"pid": 3100, "tid": 42949672960, "ts": 1754736893944278, "dur": 20, "ph": "X", "name": "ReadAsync 254", "args": {}}, {"pid": 3100, "tid": 42949672960, "ts": 1754736893944299, "dur": 1, "ph": "X", "name": "ProcessMessages 21", "args": {}}, {"pid": 3100, "tid": 42949672960, "ts": 1754736893944301, "dur": 278, "ph": "X", "name": "ReadAsync 21", "args": {}}, {"pid": 3100, "tid": 42949672960, "ts": 1754736893944581, "dur": 25, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3100, "tid": 42949672960, "ts": 1754736893944608, "dur": 27, "ph": "X", "name": "ReadAsync 271", "args": {}}, {"pid": 3100, "tid": 42949672960, "ts": 1754736893944637, "dur": 25, "ph": "X", "name": "ReadAsync 389", "args": {}}, {"pid": 3100, "tid": 42949672960, "ts": 1754736893944663, "dur": 26, "ph": "X", "name": "ReadAsync 135", "args": {}}, {"pid": 3100, "tid": 42949672960, "ts": 1754736893944691, "dur": 28, "ph": "X", "name": "ReadAsync 348", "args": {}}, {"pid": 3100, "tid": 42949672960, "ts": 1754736893944721, "dur": 21, "ph": "X", "name": "ReadAsync 370", "args": {}}, {"pid": 3100, "tid": 42949672960, "ts": 1754736893944744, "dur": 25, "ph": "X", "name": "ReadAsync 57", "args": {}}, {"pid": 3100, "tid": 42949672960, "ts": 1754736893944771, "dur": 25, "ph": "X", "name": "ReadAsync 316", "args": {}}, {"pid": 3100, "tid": 42949672960, "ts": 1754736893944799, "dur": 24, "ph": "X", "name": "ReadAsync 343", "args": {}}, {"pid": 3100, "tid": 42949672960, "ts": 1754736893944825, "dur": 27, "ph": "X", "name": "ReadAsync 261", "args": {}}, {"pid": 3100, "tid": 42949672960, "ts": 1754736893944853, "dur": 25, "ph": "X", "name": "ReadAsync 347", "args": {}}, {"pid": 3100, "tid": 42949672960, "ts": 1754736893944880, "dur": 26, "ph": "X", "name": "ReadAsync 244", "args": {}}, {"pid": 3100, "tid": 42949672960, "ts": 1754736893944907, "dur": 23, "ph": "X", "name": "ReadAsync 383", "args": {}}, {"pid": 3100, "tid": 42949672960, "ts": 1754736893944933, "dur": 25, "ph": "X", "name": "ReadAsync 207", "args": {}}, {"pid": 3100, "tid": 42949672960, "ts": 1754736893944960, "dur": 26, "ph": "X", "name": "ReadAsync 458", "args": {}}, {"pid": 3100, "tid": 42949672960, "ts": 1754736893944987, "dur": 24, "ph": "X", "name": "ReadAsync 473", "args": {}}, {"pid": 3100, "tid": 42949672960, "ts": 1754736893945014, "dur": 24, "ph": "X", "name": "ReadAsync 353", "args": {}}, {"pid": 3100, "tid": 42949672960, "ts": 1754736893945039, "dur": 26, "ph": "X", "name": "ReadAsync 255", "args": {}}, {"pid": 3100, "tid": 42949672960, "ts": 1754736893945068, "dur": 21, "ph": "X", "name": "ReadAsync 470", "args": {}}, {"pid": 3100, "tid": 42949672960, "ts": 1754736893945091, "dur": 20, "ph": "X", "name": "ReadAsync 190", "args": {}}, {"pid": 3100, "tid": 42949672960, "ts": 1754736893945112, "dur": 25, "ph": "X", "name": "ReadAsync 136", "args": {}}, {"pid": 3100, "tid": 42949672960, "ts": 1754736893945139, "dur": 29, "ph": "X", "name": "ReadAsync 334", "args": {}}, {"pid": 3100, "tid": 42949672960, "ts": 1754736893945169, "dur": 20, "ph": "X", "name": "ReadAsync 457", "args": {}}, {"pid": 3100, "tid": 42949672960, "ts": 1754736893945191, "dur": 25, "ph": "X", "name": "ReadAsync 95", "args": {}}, {"pid": 3100, "tid": 42949672960, "ts": 1754736893945218, "dur": 25, "ph": "X", "name": "ReadAsync 366", "args": {}}, {"pid": 3100, "tid": 42949672960, "ts": 1754736893945245, "dur": 24, "ph": "X", "name": "ReadAsync 384", "args": {}}, {"pid": 3100, "tid": 42949672960, "ts": 1754736893945271, "dur": 33, "ph": "X", "name": "ReadAsync 368", "args": {}}, {"pid": 3100, "tid": 42949672960, "ts": 1754736893945305, "dur": 28, "ph": "X", "name": "ReadAsync 302", "args": {}}, {"pid": 3100, "tid": 42949672960, "ts": 1754736893945336, "dur": 25, "ph": "X", "name": "ReadAsync 527", "args": {}}, {"pid": 3100, "tid": 42949672960, "ts": 1754736893945363, "dur": 29, "ph": "X", "name": "ReadAsync 519", "args": {}}, {"pid": 3100, "tid": 42949672960, "ts": 1754736893945394, "dur": 26, "ph": "X", "name": "ReadAsync 490", "args": {}}, {"pid": 3100, "tid": 42949672960, "ts": 1754736893945421, "dur": 25, "ph": "X", "name": "ReadAsync 388", "args": {}}, {"pid": 3100, "tid": 42949672960, "ts": 1754736893945448, "dur": 25, "ph": "X", "name": "ReadAsync 364", "args": {}}, {"pid": 3100, "tid": 42949672960, "ts": 1754736893945474, "dur": 24, "ph": "X", "name": "ReadAsync 538", "args": {}}, {"pid": 3100, "tid": 42949672960, "ts": 1754736893945501, "dur": 26, "ph": "X", "name": "ReadAsync 426", "args": {}}, {"pid": 3100, "tid": 42949672960, "ts": 1754736893945529, "dur": 25, "ph": "X", "name": "ReadAsync 474", "args": {}}, {"pid": 3100, "tid": 42949672960, "ts": 1754736893945556, "dur": 25, "ph": "X", "name": "ReadAsync 398", "args": {}}, {"pid": 3100, "tid": 42949672960, "ts": 1754736893945582, "dur": 22, "ph": "X", "name": "ReadAsync 375", "args": {}}, {"pid": 3100, "tid": 42949672960, "ts": 1754736893945606, "dur": 25, "ph": "X", "name": "ReadAsync 378", "args": {}}, {"pid": 3100, "tid": 42949672960, "ts": 1754736893945633, "dur": 27, "ph": "X", "name": "ReadAsync 372", "args": {}}, {"pid": 3100, "tid": 42949672960, "ts": 1754736893945662, "dur": 23, "ph": "X", "name": "ReadAsync 500", "args": {}}, {"pid": 3100, "tid": 42949672960, "ts": 1754736893945687, "dur": 25, "ph": "X", "name": "ReadAsync 208", "args": {}}, {"pid": 3100, "tid": 42949672960, "ts": 1754736893945714, "dur": 31, "ph": "X", "name": "ReadAsync 411", "args": {}}, {"pid": 3100, "tid": 42949672960, "ts": 1754736893945747, "dur": 28, "ph": "X", "name": "ReadAsync 485", "args": {}}, {"pid": 3100, "tid": 42949672960, "ts": 1754736893945777, "dur": 27, "ph": "X", "name": "ReadAsync 318", "args": {}}, {"pid": 3100, "tid": 42949672960, "ts": 1754736893945805, "dur": 24, "ph": "X", "name": "ReadAsync 337", "args": {}}, {"pid": 3100, "tid": 42949672960, "ts": 1754736893945831, "dur": 27, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 3100, "tid": 42949672960, "ts": 1754736893945860, "dur": 26, "ph": "X", "name": "ReadAsync 401", "args": {}}, {"pid": 3100, "tid": 42949672960, "ts": 1754736893945888, "dur": 25, "ph": "X", "name": "ReadAsync 368", "args": {}}, {"pid": 3100, "tid": 42949672960, "ts": 1754736893945914, "dur": 28, "ph": "X", "name": "ReadAsync 320", "args": {}}, {"pid": 3100, "tid": 42949672960, "ts": 1754736893945944, "dur": 24, "ph": "X", "name": "ReadAsync 419", "args": {}}, {"pid": 3100, "tid": 42949672960, "ts": 1754736893945971, "dur": 25, "ph": "X", "name": "ReadAsync 294", "args": {}}, {"pid": 3100, "tid": 42949672960, "ts": 1754736893945998, "dur": 25, "ph": "X", "name": "ReadAsync 435", "args": {}}, {"pid": 3100, "tid": 42949672960, "ts": 1754736893946025, "dur": 27, "ph": "X", "name": "ReadAsync 370", "args": {}}, {"pid": 3100, "tid": 42949672960, "ts": 1754736893946054, "dur": 25, "ph": "X", "name": "ReadAsync 395", "args": {}}, {"pid": 3100, "tid": 42949672960, "ts": 1754736893946081, "dur": 24, "ph": "X", "name": "ReadAsync 351", "args": {}}, {"pid": 3100, "tid": 42949672960, "ts": 1754736893946107, "dur": 21, "ph": "X", "name": "ReadAsync 422", "args": {}}, {"pid": 3100, "tid": 42949672960, "ts": 1754736893946130, "dur": 25, "ph": "X", "name": "ReadAsync 216", "args": {}}, {"pid": 3100, "tid": 42949672960, "ts": 1754736893946156, "dur": 1, "ph": "X", "name": "ProcessMessages 275", "args": {}}, {"pid": 3100, "tid": 42949672960, "ts": 1754736893946157, "dur": 27, "ph": "X", "name": "ReadAsync 275", "args": {}}, {"pid": 3100, "tid": 42949672960, "ts": 1754736893946186, "dur": 26, "ph": "X", "name": "ReadAsync 572", "args": {}}, {"pid": 3100, "tid": 42949672960, "ts": 1754736893946215, "dur": 24, "ph": "X", "name": "ReadAsync 365", "args": {}}, {"pid": 3100, "tid": 42949672960, "ts": 1754736893946240, "dur": 25, "ph": "X", "name": "ReadAsync 235", "args": {}}, {"pid": 3100, "tid": 42949672960, "ts": 1754736893946267, "dur": 25, "ph": "X", "name": "ReadAsync 404", "args": {}}, {"pid": 3100, "tid": 42949672960, "ts": 1754736893946294, "dur": 150, "ph": "X", "name": "ReadAsync 327", "args": {}}, {"pid": 3100, "tid": 42949672960, "ts": 1754736893946445, "dur": 65, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3100, "tid": 42949672960, "ts": 1754736893946516, "dur": 2, "ph": "X", "name": "ProcessMessages 448", "args": {}}, {"pid": 3100, "tid": 42949672960, "ts": 1754736893946519, "dur": 87, "ph": "X", "name": "ReadAsync 448", "args": {}}, {"pid": 3100, "tid": 42949672960, "ts": 1754736893946608, "dur": 2, "ph": "X", "name": "ProcessMessages 1403", "args": {}}, {"pid": 3100, "tid": 42949672960, "ts": 1754736893946613, "dur": 58, "ph": "X", "name": "ReadAsync 1403", "args": {}}, {"pid": 3100, "tid": 42949672960, "ts": 1754736893946672, "dur": 1, "ph": "X", "name": "ProcessMessages 816", "args": {}}, {"pid": 3100, "tid": 42949672960, "ts": 1754736893946675, "dur": 59, "ph": "X", "name": "ReadAsync 816", "args": {}}, {"pid": 3100, "tid": 42949672960, "ts": 1754736893946735, "dur": 1, "ph": "X", "name": "ProcessMessages 908", "args": {}}, {"pid": 3100, "tid": 42949672960, "ts": 1754736893946737, "dur": 58, "ph": "X", "name": "ReadAsync 908", "args": {}}, {"pid": 3100, "tid": 42949672960, "ts": 1754736893946796, "dur": 1, "ph": "X", "name": "ProcessMessages 822", "args": {}}, {"pid": 3100, "tid": 42949672960, "ts": 1754736893946798, "dur": 53, "ph": "X", "name": "ReadAsync 822", "args": {}}, {"pid": 3100, "tid": 42949672960, "ts": 1754736893946853, "dur": 1, "ph": "X", "name": "ProcessMessages 566", "args": {}}, {"pid": 3100, "tid": 42949672960, "ts": 1754736893946855, "dur": 56, "ph": "X", "name": "ReadAsync 566", "args": {}}, {"pid": 3100, "tid": 42949672960, "ts": 1754736893946912, "dur": 1, "ph": "X", "name": "ProcessMessages 741", "args": {}}, {"pid": 3100, "tid": 42949672960, "ts": 1754736893946914, "dur": 54, "ph": "X", "name": "ReadAsync 741", "args": {}}, {"pid": 3100, "tid": 42949672960, "ts": 1754736893946970, "dur": 1, "ph": "X", "name": "ProcessMessages 583", "args": {}}, {"pid": 3100, "tid": 42949672960, "ts": 1754736893946972, "dur": 52, "ph": "X", "name": "ReadAsync 583", "args": {}}, {"pid": 3100, "tid": 42949672960, "ts": 1754736893947026, "dur": 1, "ph": "X", "name": "ProcessMessages 535", "args": {}}, {"pid": 3100, "tid": 42949672960, "ts": 1754736893947027, "dur": 59, "ph": "X", "name": "ReadAsync 535", "args": {}}, {"pid": 3100, "tid": 42949672960, "ts": 1754736893947088, "dur": 1, "ph": "X", "name": "ProcessMessages 663", "args": {}}, {"pid": 3100, "tid": 42949672960, "ts": 1754736893947090, "dur": 56, "ph": "X", "name": "ReadAsync 663", "args": {}}, {"pid": 3100, "tid": 42949672960, "ts": 1754736893947148, "dur": 1, "ph": "X", "name": "ProcessMessages 731", "args": {}}, {"pid": 3100, "tid": 42949672960, "ts": 1754736893947151, "dur": 53, "ph": "X", "name": "ReadAsync 731", "args": {}}, {"pid": 3100, "tid": 42949672960, "ts": 1754736893947205, "dur": 1, "ph": "X", "name": "ProcessMessages 494", "args": {}}, {"pid": 3100, "tid": 42949672960, "ts": 1754736893947207, "dur": 93, "ph": "X", "name": "ReadAsync 494", "args": {}}, {"pid": 3100, "tid": 42949672960, "ts": 1754736893947302, "dur": 28, "ph": "X", "name": "ReadAsync 602", "args": {}}, {"pid": 3100, "tid": 42949672960, "ts": 1754736893947332, "dur": 27, "ph": "X", "name": "ReadAsync 420", "args": {}}, {"pid": 3100, "tid": 42949672960, "ts": 1754736893947361, "dur": 26, "ph": "X", "name": "ReadAsync 336", "args": {}}, {"pid": 3100, "tid": 42949672960, "ts": 1754736893947389, "dur": 51, "ph": "X", "name": "ReadAsync 366", "args": {}}, {"pid": 3100, "tid": 42949672960, "ts": 1754736893947442, "dur": 36, "ph": "X", "name": "ReadAsync 361", "args": {}}, {"pid": 3100, "tid": 42949672960, "ts": 1754736893947482, "dur": 47, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 3100, "tid": 42949672960, "ts": 1754736893947532, "dur": 27, "ph": "X", "name": "ReadAsync 877", "args": {}}, {"pid": 3100, "tid": 42949672960, "ts": 1754736893947560, "dur": 26, "ph": "X", "name": "ReadAsync 471", "args": {}}, {"pid": 3100, "tid": 42949672960, "ts": 1754736893947588, "dur": 26, "ph": "X", "name": "ReadAsync 313", "args": {}}, {"pid": 3100, "tid": 42949672960, "ts": 1754736893947616, "dur": 25, "ph": "X", "name": "ReadAsync 462", "args": {}}, {"pid": 3100, "tid": 42949672960, "ts": 1754736893947643, "dur": 25, "ph": "X", "name": "ReadAsync 236", "args": {}}, {"pid": 3100, "tid": 42949672960, "ts": 1754736893947670, "dur": 25, "ph": "X", "name": "ReadAsync 513", "args": {}}, {"pid": 3100, "tid": 42949672960, "ts": 1754736893947697, "dur": 27, "ph": "X", "name": "ReadAsync 383", "args": {}}, {"pid": 3100, "tid": 42949672960, "ts": 1754736893947726, "dur": 25, "ph": "X", "name": "ReadAsync 363", "args": {}}, {"pid": 3100, "tid": 42949672960, "ts": 1754736893947753, "dur": 26, "ph": "X", "name": "ReadAsync 379", "args": {}}, {"pid": 3100, "tid": 42949672960, "ts": 1754736893947781, "dur": 21, "ph": "X", "name": "ReadAsync 422", "args": {}}, {"pid": 3100, "tid": 42949672960, "ts": 1754736893947803, "dur": 27, "ph": "X", "name": "ReadAsync 91", "args": {}}, {"pid": 3100, "tid": 42949672960, "ts": 1754736893947832, "dur": 27, "ph": "X", "name": "ReadAsync 501", "args": {}}, {"pid": 3100, "tid": 42949672960, "ts": 1754736893947861, "dur": 28, "ph": "X", "name": "ReadAsync 400", "args": {}}, {"pid": 3100, "tid": 42949672960, "ts": 1754736893947891, "dur": 25, "ph": "X", "name": "ReadAsync 440", "args": {}}, {"pid": 3100, "tid": 42949672960, "ts": 1754736893947919, "dur": 25, "ph": "X", "name": "ReadAsync 380", "args": {}}, {"pid": 3100, "tid": 42949672960, "ts": 1754736893947945, "dur": 24, "ph": "X", "name": "ReadAsync 489", "args": {}}, {"pid": 3100, "tid": 42949672960, "ts": 1754736893947971, "dur": 24, "ph": "X", "name": "ReadAsync 203", "args": {}}, {"pid": 3100, "tid": 42949672960, "ts": 1754736893947997, "dur": 26, "ph": "X", "name": "ReadAsync 387", "args": {}}, {"pid": 3100, "tid": 42949672960, "ts": 1754736893948025, "dur": 27, "ph": "X", "name": "ReadAsync 494", "args": {}}, {"pid": 3100, "tid": 42949672960, "ts": 1754736893948054, "dur": 27, "ph": "X", "name": "ReadAsync 419", "args": {}}, {"pid": 3100, "tid": 42949672960, "ts": 1754736893948083, "dur": 25, "ph": "X", "name": "ReadAsync 357", "args": {}}, {"pid": 3100, "tid": 42949672960, "ts": 1754736893948110, "dur": 26, "ph": "X", "name": "ReadAsync 350", "args": {}}, {"pid": 3100, "tid": 42949672960, "ts": 1754736893948138, "dur": 25, "ph": "X", "name": "ReadAsync 309", "args": {}}, {"pid": 3100, "tid": 42949672960, "ts": 1754736893948165, "dur": 24, "ph": "X", "name": "ReadAsync 266", "args": {}}, {"pid": 3100, "tid": 42949672960, "ts": 1754736893948191, "dur": 26, "ph": "X", "name": "ReadAsync 352", "args": {}}, {"pid": 3100, "tid": 42949672960, "ts": 1754736893948219, "dur": 24, "ph": "X", "name": "ReadAsync 403", "args": {}}, {"pid": 3100, "tid": 42949672960, "ts": 1754736893948246, "dur": 26, "ph": "X", "name": "ReadAsync 404", "args": {}}, {"pid": 3100, "tid": 42949672960, "ts": 1754736893948273, "dur": 25, "ph": "X", "name": "ReadAsync 364", "args": {}}, {"pid": 3100, "tid": 42949672960, "ts": 1754736893948300, "dur": 26, "ph": "X", "name": "ReadAsync 283", "args": {}}, {"pid": 3100, "tid": 42949672960, "ts": 1754736893948328, "dur": 25, "ph": "X", "name": "ReadAsync 541", "args": {}}, {"pid": 3100, "tid": 42949672960, "ts": 1754736893948354, "dur": 27, "ph": "X", "name": "ReadAsync 349", "args": {}}, {"pid": 3100, "tid": 42949672960, "ts": 1754736893948383, "dur": 25, "ph": "X", "name": "ReadAsync 329", "args": {}}, {"pid": 3100, "tid": 42949672960, "ts": 1754736893948410, "dur": 28, "ph": "X", "name": "ReadAsync 282", "args": {}}, {"pid": 3100, "tid": 42949672960, "ts": 1754736893948440, "dur": 25, "ph": "X", "name": "ReadAsync 345", "args": {}}, {"pid": 3100, "tid": 42949672960, "ts": 1754736893948467, "dur": 25, "ph": "X", "name": "ReadAsync 249", "args": {}}, {"pid": 3100, "tid": 42949672960, "ts": 1754736893948494, "dur": 25, "ph": "X", "name": "ReadAsync 393", "args": {}}, {"pid": 3100, "tid": 42949672960, "ts": 1754736893948521, "dur": 26, "ph": "X", "name": "ReadAsync 535", "args": {}}, {"pid": 3100, "tid": 42949672960, "ts": 1754736893948549, "dur": 27, "ph": "X", "name": "ReadAsync 511", "args": {}}, {"pid": 3100, "tid": 42949672960, "ts": 1754736893948578, "dur": 25, "ph": "X", "name": "ReadAsync 346", "args": {}}, {"pid": 3100, "tid": 42949672960, "ts": 1754736893948605, "dur": 24, "ph": "X", "name": "ReadAsync 384", "args": {}}, {"pid": 3100, "tid": 42949672960, "ts": 1754736893948630, "dur": 25, "ph": "X", "name": "ReadAsync 300", "args": {}}, {"pid": 3100, "tid": 42949672960, "ts": 1754736893948657, "dur": 26, "ph": "X", "name": "ReadAsync 564", "args": {}}, {"pid": 3100, "tid": 42949672960, "ts": 1754736893948685, "dur": 45, "ph": "X", "name": "ReadAsync 591", "args": {}}, {"pid": 3100, "tid": 42949672960, "ts": 1754736893948732, "dur": 24, "ph": "X", "name": "ReadAsync 361", "args": {}}, {"pid": 3100, "tid": 42949672960, "ts": 1754736893948758, "dur": 25, "ph": "X", "name": "ReadAsync 348", "args": {}}, {"pid": 3100, "tid": 42949672960, "ts": 1754736893948784, "dur": 35, "ph": "X", "name": "ReadAsync 248", "args": {}}, {"pid": 3100, "tid": 42949672960, "ts": 1754736893948822, "dur": 25, "ph": "X", "name": "ReadAsync 522", "args": {}}, {"pid": 3100, "tid": 42949672960, "ts": 1754736893948848, "dur": 26, "ph": "X", "name": "ReadAsync 380", "args": {}}, {"pid": 3100, "tid": 42949672960, "ts": 1754736893948876, "dur": 28, "ph": "X", "name": "ReadAsync 492", "args": {}}, {"pid": 3100, "tid": 42949672960, "ts": 1754736893948906, "dur": 25, "ph": "X", "name": "ReadAsync 588", "args": {}}, {"pid": 3100, "tid": 42949672960, "ts": 1754736893948933, "dur": 26, "ph": "X", "name": "ReadAsync 314", "args": {}}, {"pid": 3100, "tid": 42949672960, "ts": 1754736893948961, "dur": 25, "ph": "X", "name": "ReadAsync 415", "args": {}}, {"pid": 3100, "tid": 42949672960, "ts": 1754736893948987, "dur": 24, "ph": "X", "name": "ReadAsync 357", "args": {}}, {"pid": 3100, "tid": 42949672960, "ts": 1754736893949014, "dur": 26, "ph": "X", "name": "ReadAsync 367", "args": {}}, {"pid": 3100, "tid": 42949672960, "ts": 1754736893949042, "dur": 25, "ph": "X", "name": "ReadAsync 477", "args": {}}, {"pid": 3100, "tid": 42949672960, "ts": 1754736893949068, "dur": 17, "ph": "X", "name": "ReadAsync 270", "args": {}}, {"pid": 3100, "tid": 42949672960, "ts": 1754736893949087, "dur": 21, "ph": "X", "name": "ReadAsync 241", "args": {}}, {"pid": 3100, "tid": 42949672960, "ts": 1754736893949110, "dur": 23, "ph": "X", "name": "ReadAsync 272", "args": {}}, {"pid": 3100, "tid": 42949672960, "ts": 1754736893949134, "dur": 25, "ph": "X", "name": "ReadAsync 293", "args": {}}, {"pid": 3100, "tid": 42949672960, "ts": 1754736893949161, "dur": 25, "ph": "X", "name": "ReadAsync 311", "args": {}}, {"pid": 3100, "tid": 42949672960, "ts": 1754736893949188, "dur": 25, "ph": "X", "name": "ReadAsync 414", "args": {}}, {"pid": 3100, "tid": 42949672960, "ts": 1754736893949214, "dur": 26, "ph": "X", "name": "ReadAsync 253", "args": {}}, {"pid": 3100, "tid": 42949672960, "ts": 1754736893949242, "dur": 30, "ph": "X", "name": "ReadAsync 339", "args": {}}, {"pid": 3100, "tid": 42949672960, "ts": 1754736893949274, "dur": 25, "ph": "X", "name": "ReadAsync 488", "args": {}}, {"pid": 3100, "tid": 42949672960, "ts": 1754736893949300, "dur": 25, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 3100, "tid": 42949672960, "ts": 1754736893949327, "dur": 27, "ph": "X", "name": "ReadAsync 313", "args": {}}, {"pid": 3100, "tid": 42949672960, "ts": 1754736893949356, "dur": 26, "ph": "X", "name": "ReadAsync 437", "args": {}}, {"pid": 3100, "tid": 42949672960, "ts": 1754736893949383, "dur": 25, "ph": "X", "name": "ReadAsync 422", "args": {}}, {"pid": 3100, "tid": 42949672960, "ts": 1754736893949410, "dur": 24, "ph": "X", "name": "ReadAsync 307", "args": {}}, {"pid": 3100, "tid": 42949672960, "ts": 1754736893949437, "dur": 24, "ph": "X", "name": "ReadAsync 263", "args": {}}, {"pid": 3100, "tid": 42949672960, "ts": 1754736893949463, "dur": 25, "ph": "X", "name": "ReadAsync 345", "args": {}}, {"pid": 3100, "tid": 42949672960, "ts": 1754736893949489, "dur": 24, "ph": "X", "name": "ReadAsync 382", "args": {}}, {"pid": 3100, "tid": 42949672960, "ts": 1754736893949515, "dur": 25, "ph": "X", "name": "ReadAsync 498", "args": {}}, {"pid": 3100, "tid": 42949672960, "ts": 1754736893949542, "dur": 25, "ph": "X", "name": "ReadAsync 455", "args": {}}, {"pid": 3100, "tid": 42949672960, "ts": 1754736893949568, "dur": 27, "ph": "X", "name": "ReadAsync 423", "args": {}}, {"pid": 3100, "tid": 42949672960, "ts": 1754736893949597, "dur": 24, "ph": "X", "name": "ReadAsync 468", "args": {}}, {"pid": 3100, "tid": 42949672960, "ts": 1754736893949623, "dur": 33, "ph": "X", "name": "ReadAsync 231", "args": {}}, {"pid": 3100, "tid": 42949672960, "ts": 1754736893949659, "dur": 1, "ph": "X", "name": "ProcessMessages 355", "args": {}}, {"pid": 3100, "tid": 42949672960, "ts": 1754736893949660, "dur": 50, "ph": "X", "name": "ReadAsync 355", "args": {}}, {"pid": 3100, "tid": 42949672960, "ts": 1754736893949713, "dur": 1, "ph": "X", "name": "ProcessMessages 724", "args": {}}, {"pid": 3100, "tid": 42949672960, "ts": 1754736893949715, "dur": 42, "ph": "X", "name": "ReadAsync 724", "args": {}}, {"pid": 3100, "tid": 42949672960, "ts": 1754736893949760, "dur": 1, "ph": "X", "name": "ProcessMessages 376", "args": {}}, {"pid": 3100, "tid": 42949672960, "ts": 1754736893949762, "dur": 41, "ph": "X", "name": "ReadAsync 376", "args": {}}, {"pid": 3100, "tid": 42949672960, "ts": 1754736893949807, "dur": 32, "ph": "X", "name": "ReadAsync 414", "args": {}}, {"pid": 3100, "tid": 42949672960, "ts": 1754736893949842, "dur": 26, "ph": "X", "name": "ReadAsync 313", "args": {}}, {"pid": 3100, "tid": 42949672960, "ts": 1754736893949870, "dur": 17, "ph": "X", "name": "ReadAsync 341", "args": {}}, {"pid": 3100, "tid": 42949672960, "ts": 1754736893949889, "dur": 17, "ph": "X", "name": "ReadAsync 231", "args": {}}, {"pid": 3100, "tid": 42949672960, "ts": 1754736893949908, "dur": 47, "ph": "X", "name": "ReadAsync 320", "args": {}}, {"pid": 3100, "tid": 42949672960, "ts": 1754736893949961, "dur": 1, "ph": "X", "name": "ProcessMessages 295", "args": {}}, {"pid": 3100, "tid": 42949672960, "ts": 1754736893949964, "dur": 80, "ph": "X", "name": "ReadAsync 295", "args": {}}, {"pid": 3100, "tid": 42949672960, "ts": 1754736893950046, "dur": 2, "ph": "X", "name": "ProcessMessages 1103", "args": {}}, {"pid": 3100, "tid": 42949672960, "ts": 1754736893950048, "dur": 78, "ph": "X", "name": "ReadAsync 1103", "args": {}}, {"pid": 3100, "tid": 42949672960, "ts": 1754736893950130, "dur": 1, "ph": "X", "name": "ProcessMessages 629", "args": {}}, {"pid": 3100, "tid": 42949672960, "ts": 1754736893950132, "dur": 182, "ph": "X", "name": "ReadAsync 629", "args": {}}, {"pid": 3100, "tid": 42949672960, "ts": 1754736893950320, "dur": 2, "ph": "X", "name": "ProcessMessages 696", "args": {}}, {"pid": 3100, "tid": 42949672960, "ts": 1754736893950324, "dur": 84, "ph": "X", "name": "ReadAsync 696", "args": {}}, {"pid": 3100, "tid": 42949672960, "ts": 1754736893950413, "dur": 4, "ph": "X", "name": "ProcessMessages 1929", "args": {}}, {"pid": 3100, "tid": 42949672960, "ts": 1754736893950420, "dur": 61, "ph": "X", "name": "ReadAsync 1929", "args": {}}, {"pid": 3100, "tid": 42949672960, "ts": 1754736893950484, "dur": 1, "ph": "X", "name": "ProcessMessages 529", "args": {}}, {"pid": 3100, "tid": 42949672960, "ts": 1754736893950486, "dur": 45, "ph": "X", "name": "ReadAsync 529", "args": {}}, {"pid": 3100, "tid": 42949672960, "ts": 1754736893950534, "dur": 30, "ph": "X", "name": "ReadAsync 686", "args": {}}, {"pid": 3100, "tid": 42949672960, "ts": 1754736893950565, "dur": 31, "ph": "X", "name": "ReadAsync 185", "args": {}}, {"pid": 3100, "tid": 42949672960, "ts": 1754736893950598, "dur": 29, "ph": "X", "name": "ReadAsync 277", "args": {}}, {"pid": 3100, "tid": 42949672960, "ts": 1754736893950630, "dur": 26, "ph": "X", "name": "ReadAsync 388", "args": {}}, {"pid": 3100, "tid": 42949672960, "ts": 1754736893950658, "dur": 32, "ph": "X", "name": "ReadAsync 138", "args": {}}, {"pid": 3100, "tid": 42949672960, "ts": 1754736893950694, "dur": 33, "ph": "X", "name": "ReadAsync 95", "args": {}}, {"pid": 3100, "tid": 42949672960, "ts": 1754736893950729, "dur": 37, "ph": "X", "name": "ReadAsync 157", "args": {}}, {"pid": 3100, "tid": 42949672960, "ts": 1754736893950770, "dur": 1, "ph": "X", "name": "ProcessMessages 297", "args": {}}, {"pid": 3100, "tid": 42949672960, "ts": 1754736893950772, "dur": 40, "ph": "X", "name": "ReadAsync 297", "args": {}}, {"pid": 3100, "tid": 42949672960, "ts": 1754736893950814, "dur": 30, "ph": "X", "name": "ReadAsync 419", "args": {}}, {"pid": 3100, "tid": 42949672960, "ts": 1754736893950846, "dur": 21, "ph": "X", "name": "ReadAsync 240", "args": {}}, {"pid": 3100, "tid": 42949672960, "ts": 1754736893950869, "dur": 21, "ph": "X", "name": "ReadAsync 21", "args": {}}, {"pid": 3100, "tid": 42949672960, "ts": 1754736893950892, "dur": 25, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3100, "tid": 42949672960, "ts": 1754736893950919, "dur": 25, "ph": "X", "name": "ReadAsync 212", "args": {}}, {"pid": 3100, "tid": 42949672960, "ts": 1754736893950946, "dur": 29, "ph": "X", "name": "ReadAsync 345", "args": {}}, {"pid": 3100, "tid": 42949672960, "ts": 1754736893950977, "dur": 25, "ph": "X", "name": "ReadAsync 419", "args": {}}, {"pid": 3100, "tid": 42949672960, "ts": 1754736893951004, "dur": 25, "ph": "X", "name": "ReadAsync 349", "args": {}}, {"pid": 3100, "tid": 42949672960, "ts": 1754736893951031, "dur": 25, "ph": "X", "name": "ReadAsync 291", "args": {}}, {"pid": 3100, "tid": 42949672960, "ts": 1754736893951058, "dur": 24, "ph": "X", "name": "ReadAsync 314", "args": {}}, {"pid": 3100, "tid": 42949672960, "ts": 1754736893951084, "dur": 24, "ph": "X", "name": "ReadAsync 418", "args": {}}, {"pid": 3100, "tid": 42949672960, "ts": 1754736893951110, "dur": 20, "ph": "X", "name": "ReadAsync 263", "args": {}}, {"pid": 3100, "tid": 42949672960, "ts": 1754736893951132, "dur": 21, "ph": "X", "name": "ReadAsync 144", "args": {}}, {"pid": 3100, "tid": 42949672960, "ts": 1754736893951155, "dur": 24, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3100, "tid": 42949672960, "ts": 1754736893951181, "dur": 25, "ph": "X", "name": "ReadAsync 224", "args": {}}, {"pid": 3100, "tid": 42949672960, "ts": 1754736893951208, "dur": 25, "ph": "X", "name": "ReadAsync 312", "args": {}}, {"pid": 3100, "tid": 42949672960, "ts": 1754736893951235, "dur": 24, "ph": "X", "name": "ReadAsync 198", "args": {}}, {"pid": 3100, "tid": 42949672960, "ts": 1754736893951260, "dur": 1, "ph": "X", "name": "ProcessMessages 393", "args": {}}, {"pid": 3100, "tid": 42949672960, "ts": 1754736893951262, "dur": 25, "ph": "X", "name": "ReadAsync 393", "args": {}}, {"pid": 3100, "tid": 42949672960, "ts": 1754736893951289, "dur": 24, "ph": "X", "name": "ReadAsync 334", "args": {}}, {"pid": 3100, "tid": 42949672960, "ts": 1754736893951316, "dur": 25, "ph": "X", "name": "ReadAsync 284", "args": {}}, {"pid": 3100, "tid": 42949672960, "ts": 1754736893951343, "dur": 25, "ph": "X", "name": "ReadAsync 379", "args": {}}, {"pid": 3100, "tid": 42949672960, "ts": 1754736893951384, "dur": 23, "ph": "X", "name": "ReadAsync 232", "args": {}}, {"pid": 3100, "tid": 42949672960, "ts": 1754736893951409, "dur": 26, "ph": "X", "name": "ReadAsync 188", "args": {}}, {"pid": 3100, "tid": 42949672960, "ts": 1754736893951437, "dur": 28, "ph": "X", "name": "ReadAsync 300", "args": {}}, {"pid": 3100, "tid": 42949672960, "ts": 1754736893951468, "dur": 25, "ph": "X", "name": "ReadAsync 337", "args": {}}, {"pid": 3100, "tid": 42949672960, "ts": 1754736893951496, "dur": 24, "ph": "X", "name": "ReadAsync 369", "args": {}}, {"pid": 3100, "tid": 42949672960, "ts": 1754736893951522, "dur": 24, "ph": "X", "name": "ReadAsync 314", "args": {}}, {"pid": 3100, "tid": 42949672960, "ts": 1754736893951548, "dur": 26, "ph": "X", "name": "ReadAsync 250", "args": {}}, {"pid": 3100, "tid": 42949672960, "ts": 1754736893951576, "dur": 25, "ph": "X", "name": "ReadAsync 349", "args": {}}, {"pid": 3100, "tid": 42949672960, "ts": 1754736893951603, "dur": 21, "ph": "X", "name": "ReadAsync 236", "args": {}}, {"pid": 3100, "tid": 42949672960, "ts": 1754736893951626, "dur": 25, "ph": "X", "name": "ReadAsync 138", "args": {}}, {"pid": 3100, "tid": 42949672960, "ts": 1754736893951652, "dur": 25, "ph": "X", "name": "ReadAsync 165", "args": {}}, {"pid": 3100, "tid": 42949672960, "ts": 1754736893951679, "dur": 26, "ph": "X", "name": "ReadAsync 332", "args": {}}, {"pid": 3100, "tid": 42949672960, "ts": 1754736893951707, "dur": 21, "ph": "X", "name": "ReadAsync 381", "args": {}}, {"pid": 3100, "tid": 42949672960, "ts": 1754736893951729, "dur": 24, "ph": "X", "name": "ReadAsync 62", "args": {}}, {"pid": 3100, "tid": 42949672960, "ts": 1754736893951756, "dur": 25, "ph": "X", "name": "ReadAsync 215", "args": {}}, {"pid": 3100, "tid": 42949672960, "ts": 1754736893951783, "dur": 25, "ph": "X", "name": "ReadAsync 334", "args": {}}, {"pid": 3100, "tid": 42949672960, "ts": 1754736893951809, "dur": 16, "ph": "X", "name": "ReadAsync 392", "args": {}}, {"pid": 3100, "tid": 42949672960, "ts": 1754736893951827, "dur": 25, "ph": "X", "name": "ReadAsync 57", "args": {}}, {"pid": 3100, "tid": 42949672960, "ts": 1754736893951854, "dur": 26, "ph": "X", "name": "ReadAsync 213", "args": {}}, {"pid": 3100, "tid": 42949672960, "ts": 1754736893951881, "dur": 27, "ph": "X", "name": "ReadAsync 345", "args": {}}, {"pid": 3100, "tid": 42949672960, "ts": 1754736893951910, "dur": 21, "ph": "X", "name": "ReadAsync 398", "args": {}}, {"pid": 3100, "tid": 42949672960, "ts": 1754736893951933, "dur": 25, "ph": "X", "name": "ReadAsync 100", "args": {}}, {"pid": 3100, "tid": 42949672960, "ts": 1754736893951960, "dur": 28, "ph": "X", "name": "ReadAsync 279", "args": {}}, {"pid": 3100, "tid": 42949672960, "ts": 1754736893951989, "dur": 1, "ph": "X", "name": "ProcessMessages 371", "args": {}}, {"pid": 3100, "tid": 42949672960, "ts": 1754736893951991, "dur": 25, "ph": "X", "name": "ReadAsync 371", "args": {}}, {"pid": 3100, "tid": 42949672960, "ts": 1754736893952018, "dur": 26, "ph": "X", "name": "ReadAsync 354", "args": {}}, {"pid": 3100, "tid": 42949672960, "ts": 1754736893952046, "dur": 25, "ph": "X", "name": "ReadAsync 174", "args": {}}, {"pid": 3100, "tid": 42949672960, "ts": 1754736893952072, "dur": 25, "ph": "X", "name": "ReadAsync 347", "args": {}}, {"pid": 3100, "tid": 42949672960, "ts": 1754736893952100, "dur": 16, "ph": "X", "name": "ReadAsync 450", "args": {}}, {"pid": 3100, "tid": 42949672960, "ts": 1754736893952119, "dur": 20, "ph": "X", "name": "ReadAsync 197", "args": {}}, {"pid": 3100, "tid": 42949672960, "ts": 1754736893952140, "dur": 30, "ph": "X", "name": "ReadAsync 208", "args": {}}, {"pid": 3100, "tid": 42949672960, "ts": 1754736893952173, "dur": 1, "ph": "X", "name": "ProcessMessages 319", "args": {}}, {"pid": 3100, "tid": 42949672960, "ts": 1754736893952175, "dur": 28, "ph": "X", "name": "ReadAsync 319", "args": {}}, {"pid": 3100, "tid": 42949672960, "ts": 1754736893952206, "dur": 24, "ph": "X", "name": "ReadAsync 528", "args": {}}, {"pid": 3100, "tid": 42949672960, "ts": 1754736893952233, "dur": 24, "ph": "X", "name": "ReadAsync 240", "args": {}}, {"pid": 3100, "tid": 42949672960, "ts": 1754736893952259, "dur": 26, "ph": "X", "name": "ReadAsync 237", "args": {}}, {"pid": 3100, "tid": 42949672960, "ts": 1754736893952287, "dur": 25, "ph": "X", "name": "ReadAsync 416", "args": {}}, {"pid": 3100, "tid": 42949672960, "ts": 1754736893952313, "dur": 22, "ph": "X", "name": "ReadAsync 462", "args": {}}, {"pid": 3100, "tid": 42949672960, "ts": 1754736893952337, "dur": 24, "ph": "X", "name": "ReadAsync 134", "args": {}}, {"pid": 3100, "tid": 42949672960, "ts": 1754736893952363, "dur": 23, "ph": "X", "name": "ReadAsync 287", "args": {}}, {"pid": 3100, "tid": 42949672960, "ts": 1754736893952388, "dur": 24, "ph": "X", "name": "ReadAsync 233", "args": {}}, {"pid": 3100, "tid": 42949672960, "ts": 1754736893952413, "dur": 22, "ph": "X", "name": "ReadAsync 441", "args": {}}, {"pid": 3100, "tid": 42949672960, "ts": 1754736893952437, "dur": 26, "ph": "X", "name": "ReadAsync 145", "args": {}}, {"pid": 3100, "tid": 42949672960, "ts": 1754736893952465, "dur": 28, "ph": "X", "name": "ReadAsync 233", "args": {}}, {"pid": 3100, "tid": 42949672960, "ts": 1754736893952494, "dur": 24, "ph": "X", "name": "ReadAsync 488", "args": {}}, {"pid": 3100, "tid": 42949672960, "ts": 1754736893952520, "dur": 21, "ph": "X", "name": "ReadAsync 311", "args": {}}, {"pid": 3100, "tid": 42949672960, "ts": 1754736893952543, "dur": 24, "ph": "X", "name": "ReadAsync 52", "args": {}}, {"pid": 3100, "tid": 42949672960, "ts": 1754736893952569, "dur": 24, "ph": "X", "name": "ReadAsync 347", "args": {}}, {"pid": 3100, "tid": 42949672960, "ts": 1754736893952596, "dur": 24, "ph": "X", "name": "ReadAsync 352", "args": {}}, {"pid": 3100, "tid": 42949672960, "ts": 1754736893952621, "dur": 25, "ph": "X", "name": "ReadAsync 343", "args": {}}, {"pid": 3100, "tid": 42949672960, "ts": 1754736893952648, "dur": 24, "ph": "X", "name": "ReadAsync 100", "args": {}}, {"pid": 3100, "tid": 42949672960, "ts": 1754736893952674, "dur": 23, "ph": "X", "name": "ReadAsync 286", "args": {}}, {"pid": 3100, "tid": 42949672960, "ts": 1754736893952699, "dur": 24, "ph": "X", "name": "ReadAsync 371", "args": {}}, {"pid": 3100, "tid": 42949672960, "ts": 1754736893952726, "dur": 22, "ph": "X", "name": "ReadAsync 237", "args": {}}, {"pid": 3100, "tid": 42949672960, "ts": 1754736893952750, "dur": 23, "ph": "X", "name": "ReadAsync 150", "args": {}}, {"pid": 3100, "tid": 42949672960, "ts": 1754736893952775, "dur": 22, "ph": "X", "name": "ReadAsync 249", "args": {}}, {"pid": 3100, "tid": 42949672960, "ts": 1754736893952799, "dur": 24, "ph": "X", "name": "ReadAsync 58", "args": {}}, {"pid": 3100, "tid": 42949672960, "ts": 1754736893952824, "dur": 26, "ph": "X", "name": "ReadAsync 234", "args": {}}, {"pid": 3100, "tid": 42949672960, "ts": 1754736893952852, "dur": 25, "ph": "X", "name": "ReadAsync 394", "args": {}}, {"pid": 3100, "tid": 42949672960, "ts": 1754736893952879, "dur": 23, "ph": "X", "name": "ReadAsync 291", "args": {}}, {"pid": 3100, "tid": 42949672960, "ts": 1754736893952904, "dur": 25, "ph": "X", "name": "ReadAsync 290", "args": {}}, {"pid": 3100, "tid": 42949672960, "ts": 1754736893952930, "dur": 24, "ph": "X", "name": "ReadAsync 310", "args": {}}, {"pid": 3100, "tid": 42949672960, "ts": 1754736893952956, "dur": 40, "ph": "X", "name": "ReadAsync 324", "args": {}}, {"pid": 3100, "tid": 42949672960, "ts": 1754736893952999, "dur": 26, "ph": "X", "name": "ReadAsync 303", "args": {}}, {"pid": 3100, "tid": 42949672960, "ts": 1754736893953027, "dur": 21, "ph": "X", "name": "ReadAsync 231", "args": {}}, {"pid": 3100, "tid": 42949672960, "ts": 1754736893953050, "dur": 26, "ph": "X", "name": "ReadAsync 40", "args": {}}, {"pid": 3100, "tid": 42949672960, "ts": 1754736893953078, "dur": 26, "ph": "X", "name": "ReadAsync 304", "args": {}}, {"pid": 3100, "tid": 42949672960, "ts": 1754736893953105, "dur": 26, "ph": "X", "name": "ReadAsync 360", "args": {}}, {"pid": 3100, "tid": 42949672960, "ts": 1754736893953133, "dur": 28, "ph": "X", "name": "ReadAsync 320", "args": {}}, {"pid": 3100, "tid": 42949672960, "ts": 1754736893953163, "dur": 25, "ph": "X", "name": "ReadAsync 416", "args": {}}, {"pid": 3100, "tid": 42949672960, "ts": 1754736893953190, "dur": 25, "ph": "X", "name": "ReadAsync 261", "args": {}}, {"pid": 3100, "tid": 42949672960, "ts": 1754736893953217, "dur": 25, "ph": "X", "name": "ReadAsync 268", "args": {}}, {"pid": 3100, "tid": 42949672960, "ts": 1754736893953244, "dur": 25, "ph": "X", "name": "ReadAsync 406", "args": {}}, {"pid": 3100, "tid": 42949672960, "ts": 1754736893953271, "dur": 24, "ph": "X", "name": "ReadAsync 264", "args": {}}, {"pid": 3100, "tid": 42949672960, "ts": 1754736893953297, "dur": 25, "ph": "X", "name": "ReadAsync 140", "args": {}}, {"pid": 3100, "tid": 42949672960, "ts": 1754736893953323, "dur": 25, "ph": "X", "name": "ReadAsync 277", "args": {}}, {"pid": 3100, "tid": 42949672960, "ts": 1754736893953351, "dur": 26, "ph": "X", "name": "ReadAsync 344", "args": {}}, {"pid": 3100, "tid": 42949672960, "ts": 1754736893953378, "dur": 26, "ph": "X", "name": "ReadAsync 290", "args": {}}, {"pid": 3100, "tid": 42949672960, "ts": 1754736893953406, "dur": 46, "ph": "X", "name": "ReadAsync 509", "args": {}}, {"pid": 3100, "tid": 42949672960, "ts": 1754736893953454, "dur": 28, "ph": "X", "name": "ReadAsync 68", "args": {}}, {"pid": 3100, "tid": 42949672960, "ts": 1754736893953485, "dur": 31, "ph": "X", "name": "ReadAsync 510", "args": {}}, {"pid": 3100, "tid": 42949672960, "ts": 1754736893953518, "dur": 26, "ph": "X", "name": "ReadAsync 310", "args": {}}, {"pid": 3100, "tid": 42949672960, "ts": 1754736893953546, "dur": 26, "ph": "X", "name": "ReadAsync 451", "args": {}}, {"pid": 3100, "tid": 42949672960, "ts": 1754736893953574, "dur": 20, "ph": "X", "name": "ReadAsync 395", "args": {}}, {"pid": 3100, "tid": 42949672960, "ts": 1754736893953596, "dur": 26, "ph": "X", "name": "ReadAsync 61", "args": {}}, {"pid": 3100, "tid": 42949672960, "ts": 1754736893953624, "dur": 25, "ph": "X", "name": "ReadAsync 257", "args": {}}, {"pid": 3100, "tid": 42949672960, "ts": 1754736893953651, "dur": 25, "ph": "X", "name": "ReadAsync 438", "args": {}}, {"pid": 3100, "tid": 42949672960, "ts": 1754736893953678, "dur": 24, "ph": "X", "name": "ReadAsync 263", "args": {}}, {"pid": 3100, "tid": 42949672960, "ts": 1754736893953704, "dur": 19, "ph": "X", "name": "ReadAsync 144", "args": {}}, {"pid": 3100, "tid": 42949672960, "ts": 1754736893953724, "dur": 25, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3100, "tid": 42949672960, "ts": 1754736893953752, "dur": 26, "ph": "X", "name": "ReadAsync 274", "args": {}}, {"pid": 3100, "tid": 42949672960, "ts": 1754736893953779, "dur": 25, "ph": "X", "name": "ReadAsync 399", "args": {}}, {"pid": 3100, "tid": 42949672960, "ts": 1754736893953806, "dur": 20, "ph": "X", "name": "ReadAsync 348", "args": {}}, {"pid": 3100, "tid": 42949672960, "ts": 1754736893953828, "dur": 26, "ph": "X", "name": "ReadAsync 91", "args": {}}, {"pid": 3100, "tid": 42949672960, "ts": 1754736893953856, "dur": 25, "ph": "X", "name": "ReadAsync 303", "args": {}}, {"pid": 3100, "tid": 42949672960, "ts": 1754736893953882, "dur": 24, "ph": "X", "name": "ReadAsync 397", "args": {}}, {"pid": 3100, "tid": 42949672960, "ts": 1754736893953908, "dur": 21, "ph": "X", "name": "ReadAsync 257", "args": {}}, {"pid": 3100, "tid": 42949672960, "ts": 1754736893953931, "dur": 25, "ph": "X", "name": "ReadAsync 97", "args": {}}, {"pid": 3100, "tid": 42949672960, "ts": 1754736893953959, "dur": 28, "ph": "X", "name": "ReadAsync 247", "args": {}}, {"pid": 3100, "tid": 42949672960, "ts": 1754736893953989, "dur": 24, "ph": "X", "name": "ReadAsync 464", "args": {}}, {"pid": 3100, "tid": 42949672960, "ts": 1754736893954014, "dur": 1, "ph": "X", "name": "ProcessMessages 421", "args": {}}, {"pid": 3100, "tid": 42949672960, "ts": 1754736893954016, "dur": 25, "ph": "X", "name": "ReadAsync 421", "args": {}}, {"pid": 3100, "tid": 42949672960, "ts": 1754736893954042, "dur": 25, "ph": "X", "name": "ReadAsync 458", "args": {}}, {"pid": 3100, "tid": 42949672960, "ts": 1754736893954069, "dur": 24, "ph": "X", "name": "ReadAsync 380", "args": {}}, {"pid": 3100, "tid": 42949672960, "ts": 1754736893954096, "dur": 17, "ph": "X", "name": "ReadAsync 403", "args": {}}, {"pid": 3100, "tid": 42949672960, "ts": 1754736893954114, "dur": 21, "ph": "X", "name": "ReadAsync 313", "args": {}}, {"pid": 3100, "tid": 42949672960, "ts": 1754736893954137, "dur": 24, "ph": "X", "name": "ReadAsync 240", "args": {}}, {"pid": 3100, "tid": 42949672960, "ts": 1754736893954163, "dur": 25, "ph": "X", "name": "ReadAsync 40", "args": {}}, {"pid": 3100, "tid": 42949672960, "ts": 1754736893954190, "dur": 25, "ph": "X", "name": "ReadAsync 394", "args": {}}, {"pid": 3100, "tid": 42949672960, "ts": 1754736893954217, "dur": 26, "ph": "X", "name": "ReadAsync 406", "args": {}}, {"pid": 3100, "tid": 42949672960, "ts": 1754736893954245, "dur": 16, "ph": "X", "name": "ReadAsync 260", "args": {}}, {"pid": 3100, "tid": 42949672960, "ts": 1754736893954263, "dur": 21, "ph": "X", "name": "ReadAsync 181", "args": {}}, {"pid": 3100, "tid": 42949672960, "ts": 1754736893954285, "dur": 49, "ph": "X", "name": "ReadAsync 261", "args": {}}, {"pid": 3100, "tid": 42949672960, "ts": 1754736893954340, "dur": 2, "ph": "X", "name": "ProcessMessages 209", "args": {}}, {"pid": 3100, "tid": 42949672960, "ts": 1754736893954343, "dur": 76, "ph": "X", "name": "ReadAsync 209", "args": {}}, {"pid": 3100, "tid": 42949672960, "ts": 1754736893954422, "dur": 1, "ph": "X", "name": "ProcessMessages 689", "args": {}}, {"pid": 3100, "tid": 42949672960, "ts": 1754736893954424, "dur": 54, "ph": "X", "name": "ReadAsync 689", "args": {}}, {"pid": 3100, "tid": 42949672960, "ts": 1754736893954480, "dur": 1, "ph": "X", "name": "ProcessMessages 708", "args": {}}, {"pid": 3100, "tid": 42949672960, "ts": 1754736893954482, "dur": 35, "ph": "X", "name": "ReadAsync 708", "args": {}}, {"pid": 3100, "tid": 42949672960, "ts": 1754736893954518, "dur": 26, "ph": "X", "name": "ReadAsync 303", "args": {}}, {"pid": 3100, "tid": 42949672960, "ts": 1754736893954547, "dur": 26, "ph": "X", "name": "ReadAsync 309", "args": {}}, {"pid": 3100, "tid": 42949672960, "ts": 1754736893954575, "dur": 23, "ph": "X", "name": "ReadAsync 404", "args": {}}, {"pid": 3100, "tid": 42949672960, "ts": 1754736893954601, "dur": 27, "ph": "X", "name": "ReadAsync 237", "args": {}}, {"pid": 3100, "tid": 42949672960, "ts": 1754736893954630, "dur": 24, "ph": "X", "name": "ReadAsync 225", "args": {}}, {"pid": 3100, "tid": 42949672960, "ts": 1754736893954657, "dur": 24, "ph": "X", "name": "ReadAsync 381", "args": {}}, {"pid": 3100, "tid": 42949672960, "ts": 1754736893954683, "dur": 22, "ph": "X", "name": "ReadAsync 413", "args": {}}, {"pid": 3100, "tid": 42949672960, "ts": 1754736893954706, "dur": 23, "ph": "X", "name": "ReadAsync 63", "args": {}}, {"pid": 3100, "tid": 42949672960, "ts": 1754736893954731, "dur": 23, "ph": "X", "name": "ReadAsync 248", "args": {}}, {"pid": 3100, "tid": 42949672960, "ts": 1754736893954756, "dur": 23, "ph": "X", "name": "ReadAsync 373", "args": {}}, {"pid": 3100, "tid": 42949672960, "ts": 1754736893954782, "dur": 24, "ph": "X", "name": "ReadAsync 432", "args": {}}, {"pid": 3100, "tid": 42949672960, "ts": 1754736893954807, "dur": 23, "ph": "X", "name": "ReadAsync 371", "args": {}}, {"pid": 3100, "tid": 42949672960, "ts": 1754736893954832, "dur": 23, "ph": "X", "name": "ReadAsync 281", "args": {}}, {"pid": 3100, "tid": 42949672960, "ts": 1754736893954857, "dur": 23, "ph": "X", "name": "ReadAsync 330", "args": {}}, {"pid": 3100, "tid": 42949672960, "ts": 1754736893954883, "dur": 23, "ph": "X", "name": "ReadAsync 430", "args": {}}, {"pid": 3100, "tid": 42949672960, "ts": 1754736893954907, "dur": 27, "ph": "X", "name": "ReadAsync 274", "args": {}}, {"pid": 3100, "tid": 42949672960, "ts": 1754736893954936, "dur": 19, "ph": "X", "name": "ReadAsync 139", "args": {}}, {"pid": 3100, "tid": 42949672960, "ts": 1754736893954957, "dur": 23, "ph": "X", "name": "ReadAsync 357", "args": {}}, {"pid": 3100, "tid": 42949672960, "ts": 1754736893954982, "dur": 23, "ph": "X", "name": "ReadAsync 215", "args": {}}, {"pid": 3100, "tid": 42949672960, "ts": 1754736893955007, "dur": 23, "ph": "X", "name": "ReadAsync 488", "args": {}}, {"pid": 3100, "tid": 42949672960, "ts": 1754736893955033, "dur": 24, "ph": "X", "name": "ReadAsync 520", "args": {}}, {"pid": 3100, "tid": 42949672960, "ts": 1754736893955059, "dur": 23, "ph": "X", "name": "ReadAsync 401", "args": {}}, {"pid": 3100, "tid": 42949672960, "ts": 1754736893955084, "dur": 24, "ph": "X", "name": "ReadAsync 362", "args": {}}, {"pid": 3100, "tid": 42949672960, "ts": 1754736893955110, "dur": 22, "ph": "X", "name": "ReadAsync 549", "args": {}}, {"pid": 3100, "tid": 42949672960, "ts": 1754736893955134, "dur": 24, "ph": "X", "name": "ReadAsync 137", "args": {}}, {"pid": 3100, "tid": 42949672960, "ts": 1754736893955159, "dur": 22, "ph": "X", "name": "ReadAsync 200", "args": {}}, {"pid": 3100, "tid": 42949672960, "ts": 1754736893955183, "dur": 23, "ph": "X", "name": "ReadAsync 118", "args": {}}, {"pid": 3100, "tid": 42949672960, "ts": 1754736893955208, "dur": 24, "ph": "X", "name": "ReadAsync 365", "args": {}}, {"pid": 3100, "tid": 42949672960, "ts": 1754736893955235, "dur": 23, "ph": "X", "name": "ReadAsync 428", "args": {}}, {"pid": 3100, "tid": 42949672960, "ts": 1754736893955260, "dur": 41, "ph": "X", "name": "ReadAsync 375", "args": {}}, {"pid": 3100, "tid": 42949672960, "ts": 1754736893955303, "dur": 26, "ph": "X", "name": "ReadAsync 552", "args": {}}, {"pid": 3100, "tid": 42949672960, "ts": 1754736893955331, "dur": 26, "ph": "X", "name": "ReadAsync 369", "args": {}}, {"pid": 3100, "tid": 42949672960, "ts": 1754736893955359, "dur": 28, "ph": "X", "name": "ReadAsync 467", "args": {}}, {"pid": 3100, "tid": 42949672960, "ts": 1754736893955389, "dur": 21, "ph": "X", "name": "ReadAsync 362", "args": {}}, {"pid": 3100, "tid": 42949672960, "ts": 1754736893955411, "dur": 24, "ph": "X", "name": "ReadAsync 56", "args": {}}, {"pid": 3100, "tid": 42949672960, "ts": 1754736893955438, "dur": 25, "ph": "X", "name": "ReadAsync 383", "args": {}}, {"pid": 3100, "tid": 42949672960, "ts": 1754736893955464, "dur": 22, "ph": "X", "name": "ReadAsync 457", "args": {}}, {"pid": 3100, "tid": 42949672960, "ts": 1754736893955489, "dur": 20, "ph": "X", "name": "ReadAsync 315", "args": {}}, {"pid": 3100, "tid": 42949672960, "ts": 1754736893955511, "dur": 40, "ph": "X", "name": "ReadAsync 68", "args": {}}, {"pid": 3100, "tid": 42949672960, "ts": 1754736893955557, "dur": 51, "ph": "X", "name": "ReadAsync 294", "args": {}}, {"pid": 3100, "tid": 42949672960, "ts": 1754736893955611, "dur": 1, "ph": "X", "name": "ProcessMessages 751", "args": {}}, {"pid": 3100, "tid": 42949672960, "ts": 1754736893955613, "dur": 46, "ph": "X", "name": "ReadAsync 751", "args": {}}, {"pid": 3100, "tid": 42949672960, "ts": 1754736893955662, "dur": 1, "ph": "X", "name": "ProcessMessages 144", "args": {}}, {"pid": 3100, "tid": 42949672960, "ts": 1754736893955664, "dur": 64, "ph": "X", "name": "ReadAsync 144", "args": {}}, {"pid": 3100, "tid": 42949672960, "ts": 1754736893955731, "dur": 1, "ph": "X", "name": "ProcessMessages 577", "args": {}}, {"pid": 3100, "tid": 42949672960, "ts": 1754736893955733, "dur": 45, "ph": "X", "name": "ReadAsync 577", "args": {}}, {"pid": 3100, "tid": 42949672960, "ts": 1754736893955780, "dur": 1, "ph": "X", "name": "ProcessMessages 661", "args": {}}, {"pid": 3100, "tid": 42949672960, "ts": 1754736893955781, "dur": 28, "ph": "X", "name": "ReadAsync 661", "args": {}}, {"pid": 3100, "tid": 42949672960, "ts": 1754736893955812, "dur": 26, "ph": "X", "name": "ReadAsync 308", "args": {}}, {"pid": 3100, "tid": 42949672960, "ts": 1754736893955841, "dur": 26, "ph": "X", "name": "ReadAsync 401", "args": {}}, {"pid": 3100, "tid": 42949672960, "ts": 1754736893955869, "dur": 22, "ph": "X", "name": "ReadAsync 249", "args": {}}, {"pid": 3100, "tid": 42949672960, "ts": 1754736893955893, "dur": 21, "ph": "X", "name": "ReadAsync 237", "args": {}}, {"pid": 3100, "tid": 42949672960, "ts": 1754736893955915, "dur": 24, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3100, "tid": 42949672960, "ts": 1754736893955942, "dur": 21, "ph": "X", "name": "ReadAsync 357", "args": {}}, {"pid": 3100, "tid": 42949672960, "ts": 1754736893955965, "dur": 26, "ph": "X", "name": "ReadAsync 148", "args": {}}, {"pid": 3100, "tid": 42949672960, "ts": 1754736893955994, "dur": 26, "ph": "X", "name": "ReadAsync 372", "args": {}}, {"pid": 3100, "tid": 42949672960, "ts": 1754736893956021, "dur": 35, "ph": "X", "name": "ReadAsync 205", "args": {}}, {"pid": 3100, "tid": 42949672960, "ts": 1754736893956059, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 3100, "tid": 42949672960, "ts": 1754736893956061, "dur": 35, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 3100, "tid": 42949672960, "ts": 1754736893956098, "dur": 26, "ph": "X", "name": "ReadAsync 350", "args": {}}, {"pid": 3100, "tid": 42949672960, "ts": 1754736893956127, "dur": 27, "ph": "X", "name": "ReadAsync 262", "args": {}}, {"pid": 3100, "tid": 42949672960, "ts": 1754736893956156, "dur": 35, "ph": "X", "name": "ReadAsync 434", "args": {}}, {"pid": 3100, "tid": 42949672960, "ts": 1754736893956193, "dur": 28, "ph": "X", "name": "ReadAsync 362", "args": {}}, {"pid": 3100, "tid": 42949672960, "ts": 1754736893956222, "dur": 1, "ph": "X", "name": "ProcessMessages 355", "args": {}}, {"pid": 3100, "tid": 42949672960, "ts": 1754736893956224, "dur": 25, "ph": "X", "name": "ReadAsync 355", "args": {}}, {"pid": 3100, "tid": 42949672960, "ts": 1754736893956251, "dur": 24, "ph": "X", "name": "ReadAsync 297", "args": {}}, {"pid": 3100, "tid": 42949672960, "ts": 1754736893956277, "dur": 21, "ph": "X", "name": "ReadAsync 474", "args": {}}, {"pid": 3100, "tid": 42949672960, "ts": 1754736893956301, "dur": 24, "ph": "X", "name": "ReadAsync 126", "args": {}}, {"pid": 3100, "tid": 42949672960, "ts": 1754736893956327, "dur": 20, "ph": "X", "name": "ReadAsync 193", "args": {}}, {"pid": 3100, "tid": 42949672960, "ts": 1754736893956349, "dur": 25, "ph": "X", "name": "ReadAsync 44", "args": {}}, {"pid": 3100, "tid": 42949672960, "ts": 1754736893956376, "dur": 26, "ph": "X", "name": "ReadAsync 320", "args": {}}, {"pid": 3100, "tid": 42949672960, "ts": 1754736893956403, "dur": 25, "ph": "X", "name": "ReadAsync 486", "args": {}}, {"pid": 3100, "tid": 42949672960, "ts": 1754736893956430, "dur": 21, "ph": "X", "name": "ReadAsync 174", "args": {}}, {"pid": 3100, "tid": 42949672960, "ts": 1754736893956453, "dur": 21, "ph": "X", "name": "ReadAsync 145", "args": {}}, {"pid": 3100, "tid": 42949672960, "ts": 1754736893956478, "dur": 27, "ph": "X", "name": "ReadAsync 206", "args": {}}, {"pid": 3100, "tid": 42949672960, "ts": 1754736893956507, "dur": 25, "ph": "X", "name": "ReadAsync 323", "args": {}}, {"pid": 3100, "tid": 42949672960, "ts": 1754736893956534, "dur": 16, "ph": "X", "name": "ReadAsync 452", "args": {}}, {"pid": 3100, "tid": 42949672960, "ts": 1754736893956552, "dur": 31, "ph": "X", "name": "ReadAsync 139", "args": {}}, {"pid": 3100, "tid": 42949672960, "ts": 1754736893956584, "dur": 25, "ph": "X", "name": "ReadAsync 221", "args": {}}, {"pid": 3100, "tid": 42949672960, "ts": 1754736893956611, "dur": 24, "ph": "X", "name": "ReadAsync 422", "args": {}}, {"pid": 3100, "tid": 42949672960, "ts": 1754736893956637, "dur": 21, "ph": "X", "name": "ReadAsync 423", "args": {}}, {"pid": 3100, "tid": 42949672960, "ts": 1754736893956659, "dur": 25, "ph": "X", "name": "ReadAsync 67", "args": {}}, {"pid": 3100, "tid": 42949672960, "ts": 1754736893956686, "dur": 27, "ph": "X", "name": "ReadAsync 281", "args": {}}, {"pid": 3100, "tid": 42949672960, "ts": 1754736893956715, "dur": 26, "ph": "X", "name": "ReadAsync 482", "args": {}}, {"pid": 3100, "tid": 42949672960, "ts": 1754736893956743, "dur": 25, "ph": "X", "name": "ReadAsync 357", "args": {}}, {"pid": 3100, "tid": 42949672960, "ts": 1754736893956770, "dur": 25, "ph": "X", "name": "ReadAsync 182", "args": {}}, {"pid": 3100, "tid": 42949672960, "ts": 1754736893956797, "dur": 24, "ph": "X", "name": "ReadAsync 272", "args": {}}, {"pid": 3100, "tid": 42949672960, "ts": 1754736893956823, "dur": 24, "ph": "X", "name": "ReadAsync 315", "args": {}}, {"pid": 3100, "tid": 42949672960, "ts": 1754736893956849, "dur": 16, "ph": "X", "name": "ReadAsync 386", "args": {}}, {"pid": 3100, "tid": 42949672960, "ts": 1754736893956868, "dur": 21, "ph": "X", "name": "ReadAsync 212", "args": {}}, {"pid": 3100, "tid": 42949672960, "ts": 1754736893956890, "dur": 20, "ph": "X", "name": "ReadAsync 219", "args": {}}, {"pid": 3100, "tid": 42949672960, "ts": 1754736893956912, "dur": 21, "ph": "X", "name": "ReadAsync 378", "args": {}}, {"pid": 3100, "tid": 42949672960, "ts": 1754736893956935, "dur": 24, "ph": "X", "name": "ReadAsync 186", "args": {}}, {"pid": 3100, "tid": 42949672960, "ts": 1754736893956961, "dur": 21, "ph": "X", "name": "ReadAsync 237", "args": {}}, {"pid": 3100, "tid": 42949672960, "ts": 1754736893956984, "dur": 27, "ph": "X", "name": "ReadAsync 144", "args": {}}, {"pid": 3100, "tid": 42949672960, "ts": 1754736893957013, "dur": 25, "ph": "X", "name": "ReadAsync 195", "args": {}}, {"pid": 3100, "tid": 42949672960, "ts": 1754736893957040, "dur": 25, "ph": "X", "name": "ReadAsync 368", "args": {}}, {"pid": 3100, "tid": 42949672960, "ts": 1754736893957066, "dur": 26, "ph": "X", "name": "ReadAsync 426", "args": {}}, {"pid": 3100, "tid": 42949672960, "ts": 1754736893957093, "dur": 27, "ph": "X", "name": "ReadAsync 422", "args": {}}, {"pid": 3100, "tid": 42949672960, "ts": 1754736893957122, "dur": 24, "ph": "X", "name": "ReadAsync 324", "args": {}}, {"pid": 3100, "tid": 42949672960, "ts": 1754736893957147, "dur": 25, "ph": "X", "name": "ReadAsync 218", "args": {}}, {"pid": 3100, "tid": 42949672960, "ts": 1754736893957175, "dur": 26, "ph": "X", "name": "ReadAsync 305", "args": {}}, {"pid": 3100, "tid": 42949672960, "ts": 1754736893957202, "dur": 24, "ph": "X", "name": "ReadAsync 289", "args": {}}, {"pid": 3100, "tid": 42949672960, "ts": 1754736893957228, "dur": 28, "ph": "X", "name": "ReadAsync 240", "args": {}}, {"pid": 3100, "tid": 42949672960, "ts": 1754736893957258, "dur": 26, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3100, "tid": 42949672960, "ts": 1754736893957286, "dur": 24, "ph": "X", "name": "ReadAsync 310", "args": {}}, {"pid": 3100, "tid": 42949672960, "ts": 1754736893957312, "dur": 27, "ph": "X", "name": "ReadAsync 227", "args": {}}, {"pid": 3100, "tid": 42949672960, "ts": 1754736893957341, "dur": 24, "ph": "X", "name": "ReadAsync 373", "args": {}}, {"pid": 3100, "tid": 42949672960, "ts": 1754736893957367, "dur": 26, "ph": "X", "name": "ReadAsync 293", "args": {}}, {"pid": 3100, "tid": 42949672960, "ts": 1754736893957395, "dur": 26, "ph": "X", "name": "ReadAsync 288", "args": {}}, {"pid": 3100, "tid": 42949672960, "ts": 1754736893957423, "dur": 25, "ph": "X", "name": "ReadAsync 408", "args": {}}, {"pid": 3100, "tid": 42949672960, "ts": 1754736893957450, "dur": 24, "ph": "X", "name": "ReadAsync 302", "args": {}}, {"pid": 3100, "tid": 42949672960, "ts": 1754736893957476, "dur": 21, "ph": "X", "name": "ReadAsync 252", "args": {}}, {"pid": 3100, "tid": 42949672960, "ts": 1754736893957498, "dur": 25, "ph": "X", "name": "ReadAsync 90", "args": {}}, {"pid": 3100, "tid": 42949672960, "ts": 1754736893957525, "dur": 25, "ph": "X", "name": "ReadAsync 232", "args": {}}, {"pid": 3100, "tid": 42949672960, "ts": 1754736893957552, "dur": 24, "ph": "X", "name": "ReadAsync 487", "args": {}}, {"pid": 3100, "tid": 42949672960, "ts": 1754736893957578, "dur": 20, "ph": "X", "name": "ReadAsync 317", "args": {}}, {"pid": 3100, "tid": 42949672960, "ts": 1754736893957600, "dur": 25, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3100, "tid": 42949672960, "ts": 1754736893957627, "dur": 25, "ph": "X", "name": "ReadAsync 391", "args": {}}, {"pid": 3100, "tid": 42949672960, "ts": 1754736893957654, "dur": 25, "ph": "X", "name": "ReadAsync 469", "args": {}}, {"pid": 3100, "tid": 42949672960, "ts": 1754736893957681, "dur": 24, "ph": "X", "name": "ReadAsync 371", "args": {}}, {"pid": 3100, "tid": 42949672960, "ts": 1754736893957707, "dur": 25, "ph": "X", "name": "ReadAsync 217", "args": {}}, {"pid": 3100, "tid": 42949672960, "ts": 1754736893957734, "dur": 25, "ph": "X", "name": "ReadAsync 360", "args": {}}, {"pid": 3100, "tid": 42949672960, "ts": 1754736893957762, "dur": 20, "ph": "X", "name": "ReadAsync 482", "args": {}}, {"pid": 3100, "tid": 42949672960, "ts": 1754736893957784, "dur": 24, "ph": "X", "name": "ReadAsync 139", "args": {}}, {"pid": 3100, "tid": 42949672960, "ts": 1754736893957810, "dur": 24, "ph": "X", "name": "ReadAsync 272", "args": {}}, {"pid": 3100, "tid": 42949672960, "ts": 1754736893957836, "dur": 25, "ph": "X", "name": "ReadAsync 413", "args": {}}, {"pid": 3100, "tid": 42949672960, "ts": 1754736893957863, "dur": 27, "ph": "X", "name": "ReadAsync 476", "args": {}}, {"pid": 3100, "tid": 42949672960, "ts": 1754736893957892, "dur": 25, "ph": "X", "name": "ReadAsync 508", "args": {}}, {"pid": 3100, "tid": 42949672960, "ts": 1754736893957919, "dur": 25, "ph": "X", "name": "ReadAsync 424", "args": {}}, {"pid": 3100, "tid": 42949672960, "ts": 1754736893957946, "dur": 25, "ph": "X", "name": "ReadAsync 310", "args": {}}, {"pid": 3100, "tid": 42949672960, "ts": 1754736893957973, "dur": 26, "ph": "X", "name": "ReadAsync 458", "args": {}}, {"pid": 3100, "tid": 42949672960, "ts": 1754736893958000, "dur": 20, "ph": "X", "name": "ReadAsync 295", "args": {}}, {"pid": 3100, "tid": 42949672960, "ts": 1754736893958022, "dur": 27, "ph": "X", "name": "ReadAsync 21", "args": {}}, {"pid": 3100, "tid": 42949672960, "ts": 1754736893958051, "dur": 26, "ph": "X", "name": "ReadAsync 278", "args": {}}, {"pid": 3100, "tid": 42949672960, "ts": 1754736893958078, "dur": 24, "ph": "X", "name": "ReadAsync 523", "args": {}}, {"pid": 3100, "tid": 42949672960, "ts": 1754736893958104, "dur": 25, "ph": "X", "name": "ReadAsync 360", "args": {}}, {"pid": 3100, "tid": 42949672960, "ts": 1754736893958131, "dur": 24, "ph": "X", "name": "ReadAsync 489", "args": {}}, {"pid": 3100, "tid": 42949672960, "ts": 1754736893958157, "dur": 29, "ph": "X", "name": "ReadAsync 416", "args": {}}, {"pid": 3100, "tid": 42949672960, "ts": 1754736893958188, "dur": 24, "ph": "X", "name": "ReadAsync 489", "args": {}}, {"pid": 3100, "tid": 42949672960, "ts": 1754736893958214, "dur": 24, "ph": "X", "name": "ReadAsync 342", "args": {}}, {"pid": 3100, "tid": 42949672960, "ts": 1754736893958240, "dur": 21, "ph": "X", "name": "ReadAsync 240", "args": {}}, {"pid": 3100, "tid": 42949672960, "ts": 1754736893958262, "dur": 25, "ph": "X", "name": "ReadAsync 117", "args": {}}, {"pid": 3100, "tid": 42949672960, "ts": 1754736893958289, "dur": 25, "ph": "X", "name": "ReadAsync 357", "args": {}}, {"pid": 3100, "tid": 42949672960, "ts": 1754736893958317, "dur": 25, "ph": "X", "name": "ReadAsync 464", "args": {}}, {"pid": 3100, "tid": 42949672960, "ts": 1754736893958344, "dur": 25, "ph": "X", "name": "ReadAsync 523", "args": {}}, {"pid": 3100, "tid": 42949672960, "ts": 1754736893958371, "dur": 25, "ph": "X", "name": "ReadAsync 438", "args": {}}, {"pid": 3100, "tid": 42949672960, "ts": 1754736893958398, "dur": 24, "ph": "X", "name": "ReadAsync 380", "args": {}}, {"pid": 3100, "tid": 42949672960, "ts": 1754736893958424, "dur": 24, "ph": "X", "name": "ReadAsync 399", "args": {}}, {"pid": 3100, "tid": 42949672960, "ts": 1754736893958450, "dur": 21, "ph": "X", "name": "ReadAsync 331", "args": {}}, {"pid": 3100, "tid": 42949672960, "ts": 1754736893958473, "dur": 24, "ph": "X", "name": "ReadAsync 193", "args": {}}, {"pid": 3100, "tid": 42949672960, "ts": 1754736893958499, "dur": 25, "ph": "X", "name": "ReadAsync 281", "args": {}}, {"pid": 3100, "tid": 42949672960, "ts": 1754736893958527, "dur": 25, "ph": "X", "name": "ReadAsync 438", "args": {}}, {"pid": 3100, "tid": 42949672960, "ts": 1754736893958553, "dur": 24, "ph": "X", "name": "ReadAsync 308", "args": {}}, {"pid": 3100, "tid": 42949672960, "ts": 1754736893958580, "dur": 24, "ph": "X", "name": "ReadAsync 404", "args": {}}, {"pid": 3100, "tid": 42949672960, "ts": 1754736893958606, "dur": 24, "ph": "X", "name": "ReadAsync 285", "args": {}}, {"pid": 3100, "tid": 42949672960, "ts": 1754736893958632, "dur": 24, "ph": "X", "name": "ReadAsync 404", "args": {}}, {"pid": 3100, "tid": 42949672960, "ts": 1754736893958658, "dur": 24, "ph": "X", "name": "ReadAsync 333", "args": {}}, {"pid": 3100, "tid": 42949672960, "ts": 1754736893958684, "dur": 20, "ph": "X", "name": "ReadAsync 238", "args": {}}, {"pid": 3100, "tid": 42949672960, "ts": 1754736893958706, "dur": 25, "ph": "X", "name": "ReadAsync 60", "args": {}}, {"pid": 3100, "tid": 42949672960, "ts": 1754736893958734, "dur": 26, "ph": "X", "name": "ReadAsync 294", "args": {}}, {"pid": 3100, "tid": 42949672960, "ts": 1754736893958761, "dur": 25, "ph": "X", "name": "ReadAsync 475", "args": {}}, {"pid": 3100, "tid": 42949672960, "ts": 1754736893958788, "dur": 24, "ph": "X", "name": "ReadAsync 311", "args": {}}, {"pid": 3100, "tid": 42949672960, "ts": 1754736893958813, "dur": 25, "ph": "X", "name": "ReadAsync 198", "args": {}}, {"pid": 3100, "tid": 42949672960, "ts": 1754736893958841, "dur": 27, "ph": "X", "name": "ReadAsync 346", "args": {}}, {"pid": 3100, "tid": 42949672960, "ts": 1754736893958870, "dur": 16, "ph": "X", "name": "ReadAsync 507", "args": {}}, {"pid": 3100, "tid": 42949672960, "ts": 1754736893958888, "dur": 22, "ph": "X", "name": "ReadAsync 233", "args": {}}, {"pid": 3100, "tid": 42949672960, "ts": 1754736893958911, "dur": 41, "ph": "X", "name": "ReadAsync 349", "args": {}}, {"pid": 3100, "tid": 42949672960, "ts": 1754736893958954, "dur": 25, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3100, "tid": 42949672960, "ts": 1754736893958981, "dur": 26, "ph": "X", "name": "ReadAsync 395", "args": {}}, {"pid": 3100, "tid": 42949672960, "ts": 1754736893959009, "dur": 25, "ph": "X", "name": "ReadAsync 416", "args": {}}, {"pid": 3100, "tid": 42949672960, "ts": 1754736893959035, "dur": 24, "ph": "X", "name": "ReadAsync 331", "args": {}}, {"pid": 3100, "tid": 42949672960, "ts": 1754736893959061, "dur": 21, "ph": "X", "name": "ReadAsync 257", "args": {}}, {"pid": 3100, "tid": 42949672960, "ts": 1754736893959084, "dur": 24, "ph": "X", "name": "ReadAsync 61", "args": {}}, {"pid": 3100, "tid": 42949672960, "ts": 1754736893959109, "dur": 25, "ph": "X", "name": "ReadAsync 199", "args": {}}, {"pid": 3100, "tid": 42949672960, "ts": 1754736893959136, "dur": 25, "ph": "X", "name": "ReadAsync 324", "args": {}}, {"pid": 3100, "tid": 42949672960, "ts": 1754736893959163, "dur": 28, "ph": "X", "name": "ReadAsync 478", "args": {}}, {"pid": 3100, "tid": 42949672960, "ts": 1754736893959192, "dur": 26, "ph": "X", "name": "ReadAsync 476", "args": {}}, {"pid": 3100, "tid": 42949672960, "ts": 1754736893959221, "dur": 24, "ph": "X", "name": "ReadAsync 399", "args": {}}, {"pid": 3100, "tid": 42949672960, "ts": 1754736893959247, "dur": 28, "ph": "X", "name": "ReadAsync 364", "args": {}}, {"pid": 3100, "tid": 42949672960, "ts": 1754736893959277, "dur": 26, "ph": "X", "name": "ReadAsync 497", "args": {}}, {"pid": 3100, "tid": 42949672960, "ts": 1754736893959305, "dur": 20, "ph": "X", "name": "ReadAsync 237", "args": {}}, {"pid": 3100, "tid": 42949672960, "ts": 1754736893959327, "dur": 25, "ph": "X", "name": "ReadAsync 111", "args": {}}, {"pid": 3100, "tid": 42949672960, "ts": 1754736893959353, "dur": 26, "ph": "X", "name": "ReadAsync 296", "args": {}}, {"pid": 3100, "tid": 42949672960, "ts": 1754736893959381, "dur": 24, "ph": "X", "name": "ReadAsync 370", "args": {}}, {"pid": 3100, "tid": 42949672960, "ts": 1754736893959407, "dur": 24, "ph": "X", "name": "ReadAsync 351", "args": {}}, {"pid": 3100, "tid": 42949672960, "ts": 1754736893959434, "dur": 25, "ph": "X", "name": "ReadAsync 423", "args": {}}, {"pid": 3100, "tid": 42949672960, "ts": 1754736893959460, "dur": 24, "ph": "X", "name": "ReadAsync 281", "args": {}}, {"pid": 3100, "tid": 42949672960, "ts": 1754736893959486, "dur": 25, "ph": "X", "name": "ReadAsync 341", "args": {}}, {"pid": 3100, "tid": 42949672960, "ts": 1754736893959513, "dur": 17, "ph": "X", "name": "ReadAsync 319", "args": {}}, {"pid": 3100, "tid": 42949672960, "ts": 1754736893959531, "dur": 22, "ph": "X", "name": "ReadAsync 146", "args": {}}, {"pid": 3100, "tid": 42949672960, "ts": 1754736893959555, "dur": 68, "ph": "X", "name": "ReadAsync 153", "args": {}}, {"pid": 3100, "tid": 42949672960, "ts": 1754736893959629, "dur": 2, "ph": "X", "name": "ProcessMessages 239", "args": {}}, {"pid": 3100, "tid": 42949672960, "ts": 1754736893959633, "dur": 91, "ph": "X", "name": "ReadAsync 239", "args": {}}, {"pid": 3100, "tid": 42949672960, "ts": 1754736893959726, "dur": 2, "ph": "X", "name": "ProcessMessages 1488", "args": {}}, {"pid": 3100, "tid": 42949672960, "ts": 1754736893959729, "dur": 58, "ph": "X", "name": "ReadAsync 1488", "args": {}}, {"pid": 3100, "tid": 42949672960, "ts": 1754736893959788, "dur": 1, "ph": "X", "name": "ProcessMessages 697", "args": {}}, {"pid": 3100, "tid": 42949672960, "ts": 1754736893959790, "dur": 50, "ph": "X", "name": "ReadAsync 697", "args": {}}, {"pid": 3100, "tid": 42949672960, "ts": 1754736893959844, "dur": 42, "ph": "X", "name": "ReadAsync 359", "args": {}}, {"pid": 3100, "tid": 42949672960, "ts": 1754736893959889, "dur": 49, "ph": "X", "name": "ReadAsync 70", "args": {}}, {"pid": 3100, "tid": 42949672960, "ts": 1754736893959942, "dur": 28, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3100, "tid": 42949672960, "ts": 1754736893959973, "dur": 22, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 3100, "tid": 42949672960, "ts": 1754736893959996, "dur": 20, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 3100, "tid": 42949672960, "ts": 1754736893960018, "dur": 20, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 3100, "tid": 42949672960, "ts": 1754736893960040, "dur": 24, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 3100, "tid": 42949672960, "ts": 1754736893960067, "dur": 23, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 3100, "tid": 42949672960, "ts": 1754736893960092, "dur": 23, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 3100, "tid": 42949672960, "ts": 1754736893960119, "dur": 43, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 3100, "tid": 42949672960, "ts": 1754736893960164, "dur": 1, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 3100, "tid": 42949672960, "ts": 1754736893960166, "dur": 53, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 3100, "tid": 42949672960, "ts": 1754736893960222, "dur": 30, "ph": "X", "name": "ReadAsync 144", "args": {}}, {"pid": 3100, "tid": 42949672960, "ts": 1754736893960254, "dur": 23, "ph": "X", "name": "ReadAsync 144", "args": {}}, {"pid": 3100, "tid": 42949672960, "ts": 1754736893960280, "dur": 40, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 3100, "tid": 42949672960, "ts": 1754736893960322, "dur": 26, "ph": "X", "name": "ReadAsync 96", "args": {}}, {"pid": 3100, "tid": 42949672960, "ts": 1754736893960351, "dur": 25, "ph": "X", "name": "ReadAsync 128", "args": {}}, {"pid": 3100, "tid": 42949672960, "ts": 1754736893960378, "dur": 24, "ph": "X", "name": "ReadAsync 108", "args": {}}, {"pid": 3100, "tid": 42949672960, "ts": 1754736893960404, "dur": 23, "ph": "X", "name": "ReadAsync 68", "args": {}}, {"pid": 3100, "tid": 42949672960, "ts": 1754736893960430, "dur": 24, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 3100, "tid": 42949672960, "ts": 1754736893960456, "dur": 25, "ph": "X", "name": "ReadAsync 60", "args": {}}, {"pid": 3100, "tid": 42949672960, "ts": 1754736893960483, "dur": 76, "ph": "X", "name": "ReadAsync 68", "args": {}}, {"pid": 3100, "tid": 42949672960, "ts": 1754736893960564, "dur": 39, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3100, "tid": 42949672960, "ts": 1754736893960604, "dur": 1, "ph": "X", "name": "ProcessMessages 180", "args": {}}, {"pid": 3100, "tid": 42949672960, "ts": 1754736893960605, "dur": 28, "ph": "X", "name": "ReadAsync 180", "args": {}}, {"pid": 3100, "tid": 42949672960, "ts": 1754736893960636, "dur": 31, "ph": "X", "name": "ReadAsync 128", "args": {}}, {"pid": 3100, "tid": 42949672960, "ts": 1754736893960669, "dur": 1, "ph": "X", "name": "ProcessMessages 140", "args": {}}, {"pid": 3100, "tid": 42949672960, "ts": 1754736893960671, "dur": 28, "ph": "X", "name": "ReadAsync 140", "args": {}}, {"pid": 3100, "tid": 42949672960, "ts": 1754736893960701, "dur": 23, "ph": "X", "name": "ReadAsync 148", "args": {}}, {"pid": 3100, "tid": 42949672960, "ts": 1754736893960726, "dur": 24, "ph": "X", "name": "ReadAsync 124", "args": {}}, {"pid": 3100, "tid": 42949672960, "ts": 1754736893960753, "dur": 28, "ph": "X", "name": "ReadAsync 116", "args": {}}, {"pid": 3100, "tid": 42949672960, "ts": 1754736893960783, "dur": 1, "ph": "X", "name": "ProcessMessages 128", "args": {}}, {"pid": 3100, "tid": 42949672960, "ts": 1754736893960785, "dur": 28, "ph": "X", "name": "ReadAsync 128", "args": {}}, {"pid": 3100, "tid": 42949672960, "ts": 1754736893960815, "dur": 1, "ph": "X", "name": "ProcessMessages 176", "args": {}}, {"pid": 3100, "tid": 42949672960, "ts": 1754736893960816, "dur": 27, "ph": "X", "name": "ReadAsync 176", "args": {}}, {"pid": 3100, "tid": 42949672960, "ts": 1754736893960847, "dur": 1, "ph": "X", "name": "ProcessMessages 124", "args": {}}, {"pid": 3100, "tid": 42949672960, "ts": 1754736893960848, "dur": 30, "ph": "X", "name": "ReadAsync 124", "args": {}}, {"pid": 3100, "tid": 42949672960, "ts": 1754736893960880, "dur": 1, "ph": "X", "name": "ProcessMessages 176", "args": {}}, {"pid": 3100, "tid": 42949672960, "ts": 1754736893960882, "dur": 28, "ph": "X", "name": "ReadAsync 176", "args": {}}, {"pid": 3100, "tid": 42949672960, "ts": 1754736893960912, "dur": 24, "ph": "X", "name": "ReadAsync 112", "args": {}}, {"pid": 3100, "tid": 42949672960, "ts": 1754736893960937, "dur": 2, "ph": "X", "name": "ProcessMessages 132", "args": {}}, {"pid": 3100, "tid": 42949672960, "ts": 1754736893960940, "dur": 29, "ph": "X", "name": "ReadAsync 132", "args": {}}, {"pid": 3100, "tid": 42949672960, "ts": 1754736893960973, "dur": 29, "ph": "X", "name": "ReadAsync 160", "args": {}}, {"pid": 3100, "tid": 42949672960, "ts": 1754736893961005, "dur": 32, "ph": "X", "name": "ReadAsync 92", "args": {}}, {"pid": 3100, "tid": 42949672960, "ts": 1754736893961039, "dur": 1, "ph": "X", "name": "ProcessMessages 132", "args": {}}, {"pid": 3100, "tid": 42949672960, "ts": 1754736893961042, "dur": 29, "ph": "X", "name": "ReadAsync 132", "args": {}}, {"pid": 3100, "tid": 42949672960, "ts": 1754736893961073, "dur": 1, "ph": "X", "name": "ProcessMessages 192", "args": {}}, {"pid": 3100, "tid": 42949672960, "ts": 1754736893961074, "dur": 22, "ph": "X", "name": "ReadAsync 192", "args": {}}, {"pid": 3100, "tid": 42949672960, "ts": 1754736893961098, "dur": 1, "ph": "X", "name": "ProcessMessages 128", "args": {}}, {"pid": 3100, "tid": 42949672960, "ts": 1754736893961101, "dur": 30, "ph": "X", "name": "ReadAsync 128", "args": {}}, {"pid": 3100, "tid": 42949672960, "ts": 1754736893961134, "dur": 26, "ph": "X", "name": "ReadAsync 128", "args": {}}, {"pid": 3100, "tid": 42949672960, "ts": 1754736893961163, "dur": 27, "ph": "X", "name": "ReadAsync 160", "args": {}}, {"pid": 3100, "tid": 42949672960, "ts": 1754736893961191, "dur": 27, "ph": "X", "name": "ReadAsync 140", "args": {}}, {"pid": 3100, "tid": 42949672960, "ts": 1754736893961219, "dur": 1, "ph": "X", "name": "ProcessMessages 112", "args": {}}, {"pid": 3100, "tid": 42949672960, "ts": 1754736893961221, "dur": 28, "ph": "X", "name": "ReadAsync 112", "args": {}}, {"pid": 3100, "tid": 42949672960, "ts": 1754736893961252, "dur": 28, "ph": "X", "name": "ReadAsync 144", "args": {}}, {"pid": 3100, "tid": 42949672960, "ts": 1754736893961281, "dur": 1, "ph": "X", "name": "ProcessMessages 128", "args": {}}, {"pid": 3100, "tid": 42949672960, "ts": 1754736893961283, "dur": 31, "ph": "X", "name": "ReadAsync 128", "args": {}}, {"pid": 3100, "tid": 42949672960, "ts": 1754736893961316, "dur": 1, "ph": "X", "name": "ProcessMessages 164", "args": {}}, {"pid": 3100, "tid": 42949672960, "ts": 1754736893961317, "dur": 28, "ph": "X", "name": "ReadAsync 164", "args": {}}, {"pid": 3100, "tid": 42949672960, "ts": 1754736893961347, "dur": 28, "ph": "X", "name": "ReadAsync 172", "args": {}}, {"pid": 3100, "tid": 42949672960, "ts": 1754736893961378, "dur": 1, "ph": "X", "name": "ProcessMessages 100", "args": {}}, {"pid": 3100, "tid": 42949672960, "ts": 1754736893961380, "dur": 50, "ph": "X", "name": "ReadAsync 100", "args": {}}, {"pid": 3100, "tid": 42949672960, "ts": 1754736893961433, "dur": 3, "ph": "X", "name": "ProcessMessages 128", "args": {}}, {"pid": 3100, "tid": 42949672960, "ts": 1754736893961437, "dur": 87, "ph": "X", "name": "ReadAsync 128", "args": {}}, {"pid": 3100, "tid": 42949672960, "ts": 1754736893961527, "dur": 1, "ph": "X", "name": "ProcessMessages 240", "args": {}}, {"pid": 3100, "tid": 42949672960, "ts": 1754736893961529, "dur": 279, "ph": "X", "name": "ReadAsync 240", "args": {}}, {"pid": 3100, "tid": 42949672960, "ts": 1754736893961812, "dur": 1, "ph": "X", "name": "ProcessMessages 156", "args": {}}, {"pid": 3100, "tid": 42949672960, "ts": 1754736893961814, "dur": 54, "ph": "X", "name": "ReadAsync 156", "args": {}}, {"pid": 3100, "tid": 42949672960, "ts": 1754736893961871, "dur": 3, "ph": "X", "name": "ProcessMessages 916", "args": {}}, {"pid": 3100, "tid": 42949672960, "ts": 1754736893961875, "dur": 88, "ph": "X", "name": "ReadAsync 916", "args": {}}, {"pid": 3100, "tid": 42949672960, "ts": 1754736893961966, "dur": 235, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3100, "tid": 42949672960, "ts": 1754736893962203, "dur": 1, "ph": "X", "name": "ProcessMessages 52", "args": {}}, {"pid": 3100, "tid": 42949672960, "ts": 1754736893962205, "dur": 39, "ph": "X", "name": "ReadAsync 52", "args": {}}, {"pid": 3100, "tid": 42949672960, "ts": 1754736893962247, "dur": 3, "ph": "X", "name": "ProcessMessages 68", "args": {}}, {"pid": 3100, "tid": 42949672960, "ts": 1754736893962251, "dur": 814, "ph": "X", "name": "ReadAsync 68", "args": {}}, {"pid": 3100, "tid": 42949672960, "ts": 1754736893963071, "dur": 112, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3100, "tid": 42949672960, "ts": 1754736893963186, "dur": 30, "ph": "X", "name": "ProcessMessages 34", "args": {}}, {"pid": 3100, "tid": 42949672960, "ts": 1754736893963217, "dur": 252, "ph": "X", "name": "ReadAsync 34", "args": {}}, {"pid": 3100, "tid": 42949672960, "ts": 1754736893963474, "dur": 35, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3100, "tid": 42949672960, "ts": 1754736893963513, "dur": 482, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 3100, "tid": 42949672960, "ts": 1754736893963997, "dur": 3, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 3100, "tid": 42949672960, "ts": 1754736893964001, "dur": 435, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3100, "tid": 42949672960, "ts": 1754736893964439, "dur": 406, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 3100, "tid": 42949672960, "ts": 1754736893964849, "dur": 2384, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 3100, "tid": 42949672960, "ts": 1754736893967239, "dur": 39, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3100, "tid": 42949672960, "ts": 1754736893967283, "dur": 27, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 3100, "tid": 42949672960, "ts": 1754736893967312, "dur": 40, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 3100, "tid": 42949672960, "ts": 1754736893967356, "dur": 36, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3100, "tid": 42949672960, "ts": 1754736893967395, "dur": 183, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 3100, "tid": 42949672960, "ts": 1754736893967582, "dur": 510, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3100, "tid": 42949672960, "ts": 1754736893968096, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 3100, "tid": 42949672960, "ts": 1754736893968098, "dur": 54, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 3100, "tid": 42949672960, "ts": 1754736893968155, "dur": 2, "ph": "X", "name": "ProcessMessages 304", "args": {}}, {"pid": 3100, "tid": 42949672960, "ts": 1754736893968158, "dur": 275, "ph": "X", "name": "ReadAsync 304", "args": {}}, {"pid": 3100, "tid": 42949672960, "ts": 1754736893968436, "dur": 1, "ph": "X", "name": "ProcessMessages 144", "args": {}}, {"pid": 3100, "tid": 42949672960, "ts": 1754736893968438, "dur": 27, "ph": "X", "name": "ReadAsync 144", "args": {}}, {"pid": 3100, "tid": 42949672960, "ts": 1754736893968467, "dur": 35, "ph": "X", "name": "ReadAsync 112", "args": {}}, {"pid": 3100, "tid": 42949672960, "ts": 1754736893968506, "dur": 124, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3100, "tid": 42949672960, "ts": 1754736893968633, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 3100, "tid": 42949672960, "ts": 1754736893968635, "dur": 125, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 3100, "tid": 42949672960, "ts": 1754736893968764, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 3100, "tid": 42949672960, "ts": 1754736893968765, "dur": 46, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 3100, "tid": 42949672960, "ts": 1754736893968813, "dur": 1, "ph": "X", "name": "ProcessMessages 224", "args": {}}, {"pid": 3100, "tid": 42949672960, "ts": 1754736893968815, "dur": 73, "ph": "X", "name": "ReadAsync 224", "args": {}}, {"pid": 3100, "tid": 42949672960, "ts": 1754736893968891, "dur": 1, "ph": "X", "name": "ProcessMessages 64", "args": {}}, {"pid": 3100, "tid": 42949672960, "ts": 1754736893968893, "dur": 46, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 3100, "tid": 42949672960, "ts": 1754736893968943, "dur": 60, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 3100, "tid": 42949672960, "ts": 1754736893969006, "dur": 76, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 3100, "tid": 42949672960, "ts": 1754736893969086, "dur": 1, "ph": "X", "name": "ProcessMessages 160", "args": {}}, {"pid": 3100, "tid": 42949672960, "ts": 1754736893969088, "dur": 28, "ph": "X", "name": "ReadAsync 160", "args": {}}, {"pid": 3100, "tid": 42949672960, "ts": 1754736893969118, "dur": 29, "ph": "X", "name": "ReadAsync 28", "args": {}}, {"pid": 3100, "tid": 42949672960, "ts": 1754736893969148, "dur": 27, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 3100, "tid": 42949672960, "ts": 1754736893969179, "dur": 37, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 3100, "tid": 42949672960, "ts": 1754736893969219, "dur": 29, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 3100, "tid": 42949672960, "ts": 1754736893969251, "dur": 29, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 3100, "tid": 42949672960, "ts": 1754736893969283, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 3100, "tid": 42949672960, "ts": 1754736893969285, "dur": 46, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 3100, "tid": 42949672960, "ts": 1754736893969334, "dur": 1, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 3100, "tid": 42949672960, "ts": 1754736893969336, "dur": 23, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 3100, "tid": 42949672960, "ts": 1754736893969362, "dur": 31, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 3100, "tid": 42949672960, "ts": 1754736893969397, "dur": 29, "ph": "X", "name": "ReadAsync 28", "args": {}}, {"pid": 3100, "tid": 42949672960, "ts": 1754736893969444, "dur": 39, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 3100, "tid": 42949672960, "ts": 1754736893969486, "dur": 31, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 3100, "tid": 42949672960, "ts": 1754736893969519, "dur": 41, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 3100, "tid": 42949672960, "ts": 1754736893969564, "dur": 35, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3100, "tid": 42949672960, "ts": 1754736893969601, "dur": 2, "ph": "X", "name": "ProcessMessages 8", "args": {}}, {"pid": 3100, "tid": 42949672960, "ts": 1754736893969605, "dur": 62, "ph": "X", "name": "ReadAsync 8", "args": {}}, {"pid": 3100, "tid": 42949672960, "ts": 1754736893969669, "dur": 24, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3100, "tid": 42949672960, "ts": 1754736893969695, "dur": 33, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 3100, "tid": 42949672960, "ts": 1754736893969731, "dur": 29, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3100, "tid": 42949672960, "ts": 1754736893969762, "dur": 27, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 3100, "tid": 42949672960, "ts": 1754736893969793, "dur": 80, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 3100, "tid": 42949672960, "ts": 1754736893969875, "dur": 18, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3100, "tid": 42949672960, "ts": 1754736893969895, "dur": 88, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 3100, "tid": 42949672960, "ts": 1754736893969986, "dur": 31, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3100, "tid": 42949672960, "ts": 1754736893970020, "dur": 72, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 3100, "tid": 42949672960, "ts": 1754736893970094, "dur": 27, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3100, "tid": 42949672960, "ts": 1754736893970125, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 3100, "tid": 42949672960, "ts": 1754736893970126, "dur": 40, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 3100, "tid": 42949672960, "ts": 1754736893970169, "dur": 25, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 3100, "tid": 42949672960, "ts": 1754736893970194, "dur": 2, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 3100, "tid": 42949672960, "ts": 1754736893970197, "dur": 68, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 3100, "tid": 42949672960, "ts": 1754736893970269, "dur": 29, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3100, "tid": 42949672960, "ts": 1754736893970301, "dur": 17, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 3100, "tid": 42949672960, "ts": 1754736893970320, "dur": 42, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 3100, "tid": 42949672960, "ts": 1754736893970365, "dur": 26, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3100, "tid": 42949672960, "ts": 1754736893970393, "dur": 57, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 3100, "tid": 42949672960, "ts": 1754736893970452, "dur": 27, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3100, "tid": 42949672960, "ts": 1754736893970482, "dur": 28, "ph": "X", "name": "ReadAsync 52", "args": {}}, {"pid": 3100, "tid": 42949672960, "ts": 1754736893970513, "dur": 1, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 3100, "tid": 42949672960, "ts": 1754736893970514, "dur": 36, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 3100, "tid": 42949672960, "ts": 1754736893970553, "dur": 20, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3100, "tid": 42949672960, "ts": 1754736893970576, "dur": 114, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 3100, "tid": 42949672960, "ts": 1754736893970695, "dur": 26, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3100, "tid": 42949672960, "ts": 1754736893970724, "dur": 160, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 3100, "tid": 42949672960, "ts": 1754736893970886, "dur": 34, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3100, "tid": 42949672960, "ts": 1754736893970922, "dur": 1, "ph": "X", "name": "ProcessMessages 52", "args": {}}, {"pid": 3100, "tid": 42949672960, "ts": 1754736893970924, "dur": 32, "ph": "X", "name": "ReadAsync 52", "args": {}}, {"pid": 3100, "tid": 42949672960, "ts": 1754736893970960, "dur": 32, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 3100, "tid": 42949672960, "ts": 1754736893970994, "dur": 96, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 3100, "tid": 42949672960, "ts": 1754736893971093, "dur": 24, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3100, "tid": 42949672960, "ts": 1754736893971119, "dur": 72, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 3100, "tid": 42949672960, "ts": 1754736893971195, "dur": 42, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3100, "tid": 42949672960, "ts": 1754736893971240, "dur": 1, "ph": "X", "name": "ProcessMessages 52", "args": {}}, {"pid": 3100, "tid": 42949672960, "ts": 1754736893971242, "dur": 31, "ph": "X", "name": "ReadAsync 52", "args": {}}, {"pid": 3100, "tid": 42949672960, "ts": 1754736893971275, "dur": 23, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 3100, "tid": 42949672960, "ts": 1754736893971300, "dur": 22, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 3100, "tid": 42949672960, "ts": 1754736893971325, "dur": 25, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 3100, "tid": 42949672960, "ts": 1754736893971352, "dur": 26, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3100, "tid": 42949672960, "ts": 1754736893971380, "dur": 147, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 3100, "tid": 42949672960, "ts": 1754736893971532, "dur": 27, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3100, "tid": 42949672960, "ts": 1754736893971562, "dur": 29, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 3100, "tid": 42949672960, "ts": 1754736893971593, "dur": 21, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3100, "tid": 42949672960, "ts": 1754736893971616, "dur": 84, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 3100, "tid": 42949672960, "ts": 1754736893971702, "dur": 21, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3100, "tid": 42949672960, "ts": 1754736893971725, "dur": 51, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 3100, "tid": 42949672960, "ts": 1754736893971778, "dur": 21, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3100, "tid": 42949672960, "ts": 1754736893971800, "dur": 1, "ph": "X", "name": "ProcessMessages 36", "args": {}}, {"pid": 3100, "tid": 42949672960, "ts": 1754736893971802, "dur": 17, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 3100, "tid": 42949672960, "ts": 1754736893971821, "dur": 21, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 3100, "tid": 42949672960, "ts": 1754736893971844, "dur": 55, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 3100, "tid": 42949672960, "ts": 1754736893971900, "dur": 24, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3100, "tid": 42949672960, "ts": 1754736893971926, "dur": 51, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 3100, "tid": 42949672960, "ts": 1754736893971979, "dur": 21, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3100, "tid": 42949672960, "ts": 1754736893972002, "dur": 21, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 3100, "tid": 42949672960, "ts": 1754736893972024, "dur": 58, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 3100, "tid": 42949672960, "ts": 1754736893972084, "dur": 21, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3100, "tid": 42949672960, "ts": 1754736893972107, "dur": 45, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 3100, "tid": 42949672960, "ts": 1754736893972153, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 3100, "tid": 42949672960, "ts": 1754736893972154, "dur": 23, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3100, "tid": 42949672960, "ts": 1754736893972179, "dur": 2683, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 3100, "tid": 42949672960, "ts": 1754736893974867, "dur": 33, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3100, "tid": 42949672960, "ts": 1754736893974902, "dur": 2, "ph": "X", "name": "ProcessMessages 8", "args": {}}, {"pid": 3100, "tid": 42949672960, "ts": 1754736893974905, "dur": 270825, "ph": "X", "name": "ReadAsync 8", "args": {}}, {"pid": 3100, "tid": 42949672960, "ts": 1754736894245742, "dur": 3, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 3100, "tid": 42949672960, "ts": 1754736894245746, "dur": 48, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3100, "tid": 42949672960, "ts": 1754736894245796, "dur": 25, "ph": "X", "name": "ProcessMessages 192", "args": {}}, {"pid": 3100, "tid": 42949672960, "ts": 1754736894245823, "dur": 3940, "ph": "X", "name": "ReadAsync 192", "args": {}}, {"pid": 3100, "tid": 42949672960, "ts": 1754736894249769, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 3100, "tid": 42949672960, "ts": 1754736894249772, "dur": 119, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3100, "tid": 42949672960, "ts": 1754736894249896, "dur": 1, "ph": "X", "name": "ProcessMessages 68", "args": {}}, {"pid": 3100, "tid": 42949672960, "ts": 1754736894249898, "dur": 34, "ph": "X", "name": "ReadAsync 68", "args": {}}, {"pid": 3100, "tid": 42949672960, "ts": 1754736894249936, "dur": 222, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 3100, "tid": 42949672960, "ts": 1754736894250164, "dur": 981, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3100, "tid": 42949672960, "ts": 1754736894251147, "dur": 1, "ph": "X", "name": "ProcessMessages 36", "args": {}}, {"pid": 3100, "tid": 42949672960, "ts": 1754736894251151, "dur": 2014, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 3100, "tid": 42949672960, "ts": 1754736894253169, "dur": 2, "ph": "X", "name": "ProcessMessages 144", "args": {}}, {"pid": 3100, "tid": 42949672960, "ts": 1754736894253172, "dur": 462, "ph": "X", "name": "ReadAsync 144", "args": {}}, {"pid": 3100, "tid": 42949672960, "ts": 1754736894253638, "dur": 3, "ph": "X", "name": "ProcessMessages 556", "args": {}}, {"pid": 3100, "tid": 42949672960, "ts": 1754736894253642, "dur": 57, "ph": "X", "name": "ReadAsync 556", "args": {}}, {"pid": 3100, "tid": 42949672960, "ts": 1754736894253702, "dur": 5, "ph": "X", "name": "ProcessMessages 1220", "args": {}}, {"pid": 3100, "tid": 42949672960, "ts": 1754736894253709, "dur": 116, "ph": "X", "name": "ReadAsync 1220", "args": {}}, {"pid": 3100, "tid": 42949672960, "ts": 1754736894253830, "dur": 50, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3100, "tid": 42949672960, "ts": 1754736894253884, "dur": 1, "ph": "X", "name": "ProcessMessages 64", "args": {}}, {"pid": 3100, "tid": 42949672960, "ts": 1754736894253886, "dur": 35, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 3100, "tid": 42949672960, "ts": 1754736894253923, "dur": 2, "ph": "X", "name": "ProcessMessages 36", "args": {}}, {"pid": 3100, "tid": 42949672960, "ts": 1754736894253926, "dur": 53, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 3100, "tid": 42949672960, "ts": 1754736894253983, "dur": 53, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3100, "tid": 42949672960, "ts": 1754736894254040, "dur": 1, "ph": "X", "name": "ProcessMessages 52", "args": {}}, {"pid": 3100, "tid": 42949672960, "ts": 1754736894254042, "dur": 37, "ph": "X", "name": "ReadAsync 52", "args": {}}, {"pid": 3100, "tid": 42949672960, "ts": 1754736894254083, "dur": 31, "ph": "X", "name": "ReadAsync 60", "args": {}}, {"pid": 3100, "tid": 42949672960, "ts": 1754736894254115, "dur": 5, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 3100, "tid": 42949672960, "ts": 1754736894254121, "dur": 28, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 3100, "tid": 42949672960, "ts": 1754736894254151, "dur": 25, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 3100, "tid": 42949672960, "ts": 1754736894254178, "dur": 61, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 3100, "tid": 42949672960, "ts": 1754736894254242, "dur": 31, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3100, "tid": 42949672960, "ts": 1754736894254276, "dur": 1, "ph": "X", "name": "ProcessMessages 36", "args": {}}, {"pid": 3100, "tid": 42949672960, "ts": 1754736894254277, "dur": 33, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 3100, "tid": 42949672960, "ts": 1754736894254313, "dur": 38, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 3100, "tid": 42949672960, "ts": 1754736894254353, "dur": 27, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3100, "tid": 42949672960, "ts": 1754736894254383, "dur": 22, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 3100, "tid": 42949672960, "ts": 1754736894254407, "dur": 39255, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 3100, "tid": 42949672960, "ts": 1754736894293673, "dur": 3, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 3100, "tid": 42949672960, "ts": 1754736894293677, "dur": 55, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3100, "tid": 42949672960, "ts": 1754736894293737, "dur": 135, "ph": "X", "name": "ProcessMessages 1025", "args": {}}, {"pid": 3100, "tid": 42949672960, "ts": 1754736894293873, "dur": 2675, "ph": "X", "name": "ReadAsync 1025", "args": {}}, {"pid": 3100, "tid": 42949672960, "ts": 1754736894296553, "dur": 31, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3100, "tid": 42949672960, "ts": 1754736894296586, "dur": 6690, "ph": "X", "name": "ReadAsync 13", "args": {}}, {"pid": 3100, "tid": 24784, "ts": 1754736894303890, "dur": 786, "ph": "X", "name": "ReadEntireBinlogFromIpcAsync", "args": {}}, {"pid": 3100, "tid": 38654705664, "ph": "M", "name": "thread_name", "args": {"name": "WaitForBuildProgramInputDataBeingWrittenAndSendDagReadyMessageAsync"}}, {"pid": 3100, "tid": 38654705664, "ts": 1754736893692140, "dur": 6, "ph": "X", "name": "await writeBuildProgramInputDataTask", "args": {}}, {"pid": 3100, "tid": 38654705664, "ts": 1754736893692147, "dur": 142836, "ph": "X", "name": "WritePipe.WaitConnectionAsync", "args": {}}, {"pid": 3100, "tid": 38654705664, "ts": 1754736893834984, "dur": 47, "ph": "X", "name": "WriteDagReadyMessage", "args": {}}, {"pid": 3100, "tid": 24784, "ts": 1754736894304677, "dur": 4, "ph": "X", "name": "WaitForBuildProgramInputDataBeingWrittenAndSendDagReadyMessageAsync", "args": {}}, {"pid": 3100, "tid": 1, "ph": "M", "name": "thread_name", "args": {"name": ""}}, {"pid": 3100, "tid": 1, "ts": 1754736892738955, "dur": 1012, "ph": "X", "name": "<Add>b__0", "args": {}}, {"pid": 3100, "tid": 1, "ts": 1754736892739970, "dur": 17915, "ph": "X", "name": "<Add>b__0", "args": {}}, {"pid": 3100, "tid": 1, "ts": 1754736892757887, "dur": 28786, "ph": "X", "name": "WriteJson", "args": {}}, {"pid": 3100, "tid": 24784, "ts": 1754736894304682, "dur": 3, "ph": "X", "name": "", "args": {}}, {"pid": 3100, "tid": 34359738368, "ph": "M", "name": "thread_name", "args": {"name": "ReadEntireBinlogFromIpcAsync"}}, {"pid": 3100, "tid": 34359738368, "ts": 1754736892738929, "dur": 144520, "ph": "X", "name": "WaitForConnectionAsync", "args": {}}, {"pid": 3100, "tid": 34359738368, "ts": 1754736892883451, "dur": 33890, "ph": "X", "name": "UpdateFromStreamAsync", "args": {}}, {"pid": 3100, "tid": 34359738368, "ts": 1754736892883467, "dur": 49, "ph": "X", "name": "ReadAsync 0", "args": {}}, {"pid": 3100, "tid": 34359738368, "ts": 1754736892883520, "dur": 231, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 3100, "tid": 34359738368, "ts": 1754736892883755, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 3100, "tid": 34359738368, "ts": 1754736892883758, "dur": 53, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3100, "tid": 34359738368, "ts": 1754736892883817, "dur": 8, "ph": "X", "name": "ProcessMessages 41", "args": {}}, {"pid": 3100, "tid": 34359738368, "ts": 1754736892883827, "dur": 2573, "ph": "X", "name": "ReadAsync 41", "args": {}}, {"pid": 3100, "tid": 34359738368, "ts": 1754736892886406, "dur": 57, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3100, "tid": 34359738368, "ts": 1754736892886467, "dur": 1, "ph": "X", "name": "ProcessMessages 648", "args": {}}, {"pid": 3100, "tid": 34359738368, "ts": 1754736892886469, "dur": 41, "ph": "X", "name": "ReadAsync 648", "args": {}}, {"pid": 3100, "tid": 34359738368, "ts": 1754736892886513, "dur": 30, "ph": "X", "name": "ReadAsync 788", "args": {}}, {"pid": 3100, "tid": 34359738368, "ts": 1754736892886546, "dur": 31, "ph": "X", "name": "ReadAsync 302", "args": {}}, {"pid": 3100, "tid": 34359738368, "ts": 1754736892886580, "dur": 1, "ph": "X", "name": "ProcessMessages 394", "args": {}}, {"pid": 3100, "tid": 34359738368, "ts": 1754736892886582, "dur": 31, "ph": "X", "name": "ReadAsync 394", "args": {}}, {"pid": 3100, "tid": 34359738368, "ts": 1754736892886616, "dur": 28, "ph": "X", "name": "ReadAsync 644", "args": {}}, {"pid": 3100, "tid": 34359738368, "ts": 1754736892886646, "dur": 28, "ph": "X", "name": "ReadAsync 392", "args": {}}, {"pid": 3100, "tid": 34359738368, "ts": 1754736892886676, "dur": 27, "ph": "X", "name": "ReadAsync 554", "args": {}}, {"pid": 3100, "tid": 34359738368, "ts": 1754736892886706, "dur": 26, "ph": "X", "name": "ReadAsync 138", "args": {}}, {"pid": 3100, "tid": 34359738368, "ts": 1754736892886735, "dur": 26, "ph": "X", "name": "ReadAsync 471", "args": {}}, {"pid": 3100, "tid": 34359738368, "ts": 1754736892886763, "dur": 28, "ph": "X", "name": "ReadAsync 227", "args": {}}, {"pid": 3100, "tid": 34359738368, "ts": 1754736892886793, "dur": 22, "ph": "X", "name": "ReadAsync 476", "args": {}}, {"pid": 3100, "tid": 34359738368, "ts": 1754736892886817, "dur": 71, "ph": "X", "name": "ReadAsync 211", "args": {}}, {"pid": 3100, "tid": 34359738368, "ts": 1754736892886892, "dur": 32, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3100, "tid": 34359738368, "ts": 1754736892886927, "dur": 25, "ph": "X", "name": "ReadAsync 705", "args": {}}, {"pid": 3100, "tid": 34359738368, "ts": 1754736892886955, "dur": 37, "ph": "X", "name": "ReadAsync 262", "args": {}}, {"pid": 3100, "tid": 34359738368, "ts": 1754736892886995, "dur": 1, "ph": "X", "name": "ProcessMessages 405", "args": {}}, {"pid": 3100, "tid": 34359738368, "ts": 1754736892886997, "dur": 38, "ph": "X", "name": "ReadAsync 405", "args": {}}, {"pid": 3100, "tid": 34359738368, "ts": 1754736892887037, "dur": 23, "ph": "X", "name": "ReadAsync 739", "args": {}}, {"pid": 3100, "tid": 34359738368, "ts": 1754736892887061, "dur": 59, "ph": "X", "name": "ReadAsync 277", "args": {}}, {"pid": 3100, "tid": 34359738368, "ts": 1754736892887123, "dur": 1, "ph": "X", "name": "ProcessMessages 477", "args": {}}, {"pid": 3100, "tid": 34359738368, "ts": 1754736892887125, "dur": 39, "ph": "X", "name": "ReadAsync 477", "args": {}}, {"pid": 3100, "tid": 34359738368, "ts": 1754736892887167, "dur": 33, "ph": "X", "name": "ReadAsync 571", "args": {}}, {"pid": 3100, "tid": 34359738368, "ts": 1754736892887202, "dur": 34, "ph": "X", "name": "ReadAsync 577", "args": {}}, {"pid": 3100, "tid": 34359738368, "ts": 1754736892887238, "dur": 35, "ph": "X", "name": "ReadAsync 564", "args": {}}, {"pid": 3100, "tid": 34359738368, "ts": 1754736892887276, "dur": 40, "ph": "X", "name": "ReadAsync 338", "args": {}}, {"pid": 3100, "tid": 34359738368, "ts": 1754736892887320, "dur": 1, "ph": "X", "name": "ProcessMessages 675", "args": {}}, {"pid": 3100, "tid": 34359738368, "ts": 1754736892887322, "dur": 30, "ph": "X", "name": "ReadAsync 675", "args": {}}, {"pid": 3100, "tid": 34359738368, "ts": 1754736892887353, "dur": 1, "ph": "X", "name": "ProcessMessages 182", "args": {}}, {"pid": 3100, "tid": 34359738368, "ts": 1754736892887355, "dur": 26, "ph": "X", "name": "ReadAsync 182", "args": {}}, {"pid": 3100, "tid": 34359738368, "ts": 1754736892887383, "dur": 21, "ph": "X", "name": "ReadAsync 238", "args": {}}, {"pid": 3100, "tid": 34359738368, "ts": 1754736892887406, "dur": 39, "ph": "X", "name": "ReadAsync 352", "args": {}}, {"pid": 3100, "tid": 34359738368, "ts": 1754736892887449, "dur": 1, "ph": "X", "name": "ProcessMessages 334", "args": {}}, {"pid": 3100, "tid": 34359738368, "ts": 1754736892887451, "dur": 39, "ph": "X", "name": "ReadAsync 334", "args": {}}, {"pid": 3100, "tid": 34359738368, "ts": 1754736892887493, "dur": 26, "ph": "X", "name": "ReadAsync 742", "args": {}}, {"pid": 3100, "tid": 34359738368, "ts": 1754736892887521, "dur": 26, "ph": "X", "name": "ReadAsync 368", "args": {}}, {"pid": 3100, "tid": 34359738368, "ts": 1754736892887549, "dur": 29, "ph": "X", "name": "ReadAsync 464", "args": {}}, {"pid": 3100, "tid": 34359738368, "ts": 1754736892887581, "dur": 1, "ph": "X", "name": "ProcessMessages 455", "args": {}}, {"pid": 3100, "tid": 34359738368, "ts": 1754736892887583, "dur": 37, "ph": "X", "name": "ReadAsync 455", "args": {}}, {"pid": 3100, "tid": 34359738368, "ts": 1754736892887622, "dur": 40, "ph": "X", "name": "ReadAsync 694", "args": {}}, {"pid": 3100, "tid": 34359738368, "ts": 1754736892887664, "dur": 27, "ph": "X", "name": "ReadAsync 359", "args": {}}, {"pid": 3100, "tid": 34359738368, "ts": 1754736892887694, "dur": 27, "ph": "X", "name": "ReadAsync 473", "args": {}}, {"pid": 3100, "tid": 34359738368, "ts": 1754736892887723, "dur": 26, "ph": "X", "name": "ReadAsync 382", "args": {}}, {"pid": 3100, "tid": 34359738368, "ts": 1754736892887751, "dur": 29, "ph": "X", "name": "ReadAsync 340", "args": {}}, {"pid": 3100, "tid": 34359738368, "ts": 1754736892887782, "dur": 28, "ph": "X", "name": "ReadAsync 542", "args": {}}, {"pid": 3100, "tid": 34359738368, "ts": 1754736892887811, "dur": 27, "ph": "X", "name": "ReadAsync 401", "args": {}}, {"pid": 3100, "tid": 34359738368, "ts": 1754736892887840, "dur": 28, "ph": "X", "name": "ReadAsync 266", "args": {}}, {"pid": 3100, "tid": 34359738368, "ts": 1754736892887870, "dur": 29, "ph": "X", "name": "ReadAsync 474", "args": {}}, {"pid": 3100, "tid": 34359738368, "ts": 1754736892887901, "dur": 23, "ph": "X", "name": "ReadAsync 356", "args": {}}, {"pid": 3100, "tid": 34359738368, "ts": 1754736892887926, "dur": 25, "ph": "X", "name": "ReadAsync 351", "args": {}}, {"pid": 3100, "tid": 34359738368, "ts": 1754736892887953, "dur": 26, "ph": "X", "name": "ReadAsync 339", "args": {}}, {"pid": 3100, "tid": 34359738368, "ts": 1754736892887981, "dur": 27, "ph": "X", "name": "ReadAsync 352", "args": {}}, {"pid": 3100, "tid": 34359738368, "ts": 1754736892888010, "dur": 24, "ph": "X", "name": "ReadAsync 433", "args": {}}, {"pid": 3100, "tid": 34359738368, "ts": 1754736892888036, "dur": 25, "ph": "X", "name": "ReadAsync 469", "args": {}}, {"pid": 3100, "tid": 34359738368, "ts": 1754736892888063, "dur": 28, "ph": "X", "name": "ReadAsync 224", "args": {}}, {"pid": 3100, "tid": 34359738368, "ts": 1754736892888093, "dur": 26, "ph": "X", "name": "ReadAsync 515", "args": {}}, {"pid": 3100, "tid": 34359738368, "ts": 1754736892888121, "dur": 27, "ph": "X", "name": "ReadAsync 364", "args": {}}, {"pid": 3100, "tid": 34359738368, "ts": 1754736892888150, "dur": 28, "ph": "X", "name": "ReadAsync 458", "args": {}}, {"pid": 3100, "tid": 34359738368, "ts": 1754736892888181, "dur": 25, "ph": "X", "name": "ReadAsync 427", "args": {}}, {"pid": 3100, "tid": 34359738368, "ts": 1754736892888208, "dur": 37, "ph": "X", "name": "ReadAsync 333", "args": {}}, {"pid": 3100, "tid": 34359738368, "ts": 1754736892888246, "dur": 20, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3100, "tid": 34359738368, "ts": 1754736892888268, "dur": 28, "ph": "X", "name": "ReadAsync 438", "args": {}}, {"pid": 3100, "tid": 34359738368, "ts": 1754736892888299, "dur": 37, "ph": "X", "name": "ReadAsync 345", "args": {}}, {"pid": 3100, "tid": 34359738368, "ts": 1754736892888339, "dur": 26, "ph": "X", "name": "ReadAsync 838", "args": {}}, {"pid": 3100, "tid": 34359738368, "ts": 1754736892888366, "dur": 28, "ph": "X", "name": "ReadAsync 351", "args": {}}, {"pid": 3100, "tid": 34359738368, "ts": 1754736892888397, "dur": 25, "ph": "X", "name": "ReadAsync 498", "args": {}}, {"pid": 3100, "tid": 34359738368, "ts": 1754736892888424, "dur": 28, "ph": "X", "name": "ReadAsync 393", "args": {}}, {"pid": 3100, "tid": 34359738368, "ts": 1754736892888454, "dur": 31, "ph": "X", "name": "ReadAsync 474", "args": {}}, {"pid": 3100, "tid": 34359738368, "ts": 1754736892888487, "dur": 25, "ph": "X", "name": "ReadAsync 497", "args": {}}, {"pid": 3100, "tid": 34359738368, "ts": 1754736892888514, "dur": 26, "ph": "X", "name": "ReadAsync 377", "args": {}}, {"pid": 3100, "tid": 34359738368, "ts": 1754736892888542, "dur": 36, "ph": "X", "name": "ReadAsync 476", "args": {}}, {"pid": 3100, "tid": 34359738368, "ts": 1754736892888579, "dur": 25, "ph": "X", "name": "ReadAsync 525", "args": {}}, {"pid": 3100, "tid": 34359738368, "ts": 1754736892888607, "dur": 26, "ph": "X", "name": "ReadAsync 357", "args": {}}, {"pid": 3100, "tid": 34359738368, "ts": 1754736892888635, "dur": 28, "ph": "X", "name": "ReadAsync 416", "args": {}}, {"pid": 3100, "tid": 34359738368, "ts": 1754736892888664, "dur": 26, "ph": "X", "name": "ReadAsync 448", "args": {}}, {"pid": 3100, "tid": 34359738368, "ts": 1754736892888692, "dur": 28, "ph": "X", "name": "ReadAsync 345", "args": {}}, {"pid": 3100, "tid": 34359738368, "ts": 1754736892888722, "dur": 25, "ph": "X", "name": "ReadAsync 560", "args": {}}, {"pid": 3100, "tid": 34359738368, "ts": 1754736892888750, "dur": 26, "ph": "X", "name": "ReadAsync 349", "args": {}}, {"pid": 3100, "tid": 34359738368, "ts": 1754736892888778, "dur": 27, "ph": "X", "name": "ReadAsync 519", "args": {}}, {"pid": 3100, "tid": 34359738368, "ts": 1754736892888807, "dur": 27, "ph": "X", "name": "ReadAsync 470", "args": {}}, {"pid": 3100, "tid": 34359738368, "ts": 1754736892888836, "dur": 27, "ph": "X", "name": "ReadAsync 374", "args": {}}, {"pid": 3100, "tid": 34359738368, "ts": 1754736892888865, "dur": 26, "ph": "X", "name": "ReadAsync 449", "args": {}}, {"pid": 3100, "tid": 34359738368, "ts": 1754736892888893, "dur": 26, "ph": "X", "name": "ReadAsync 371", "args": {}}, {"pid": 3100, "tid": 34359738368, "ts": 1754736892888921, "dur": 27, "ph": "X", "name": "ReadAsync 429", "args": {}}, {"pid": 3100, "tid": 34359738368, "ts": 1754736892888950, "dur": 27, "ph": "X", "name": "ReadAsync 382", "args": {}}, {"pid": 3100, "tid": 34359738368, "ts": 1754736892888979, "dur": 25, "ph": "X", "name": "ReadAsync 303", "args": {}}, {"pid": 3100, "tid": 34359738368, "ts": 1754736892889006, "dur": 25, "ph": "X", "name": "ReadAsync 322", "args": {}}, {"pid": 3100, "tid": 34359738368, "ts": 1754736892889033, "dur": 24, "ph": "X", "name": "ReadAsync 286", "args": {}}, {"pid": 3100, "tid": 34359738368, "ts": 1754736892889060, "dur": 27, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3100, "tid": 34359738368, "ts": 1754736892889089, "dur": 26, "ph": "X", "name": "ReadAsync 457", "args": {}}, {"pid": 3100, "tid": 34359738368, "ts": 1754736892889119, "dur": 27, "ph": "X", "name": "ReadAsync 400", "args": {}}, {"pid": 3100, "tid": 34359738368, "ts": 1754736892889147, "dur": 25, "ph": "X", "name": "ReadAsync 434", "args": {}}, {"pid": 3100, "tid": 34359738368, "ts": 1754736892889174, "dur": 28, "ph": "X", "name": "ReadAsync 236", "args": {}}, {"pid": 3100, "tid": 34359738368, "ts": 1754736892889204, "dur": 28, "ph": "X", "name": "ReadAsync 319", "args": {}}, {"pid": 3100, "tid": 34359738368, "ts": 1754736892889233, "dur": 25, "ph": "X", "name": "ReadAsync 468", "args": {}}, {"pid": 3100, "tid": 34359738368, "ts": 1754736892889260, "dur": 28, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 3100, "tid": 34359738368, "ts": 1754736892889290, "dur": 27, "ph": "X", "name": "ReadAsync 418", "args": {}}, {"pid": 3100, "tid": 34359738368, "ts": 1754736892889319, "dur": 35, "ph": "X", "name": "ReadAsync 391", "args": {}}, {"pid": 3100, "tid": 34359738368, "ts": 1754736892889357, "dur": 1, "ph": "X", "name": "ProcessMessages 313", "args": {}}, {"pid": 3100, "tid": 34359738368, "ts": 1754736892889359, "dur": 36, "ph": "X", "name": "ReadAsync 313", "args": {}}, {"pid": 3100, "tid": 34359738368, "ts": 1754736892889397, "dur": 46, "ph": "X", "name": "ReadAsync 338", "args": {}}, {"pid": 3100, "tid": 34359738368, "ts": 1754736892889446, "dur": 23, "ph": "X", "name": "ReadAsync 429", "args": {}}, {"pid": 3100, "tid": 34359738368, "ts": 1754736892889471, "dur": 78, "ph": "X", "name": "ReadAsync 8", "args": {}}, {"pid": 3100, "tid": 34359738368, "ts": 1754736892889551, "dur": 22, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3100, "tid": 34359738368, "ts": 1754736892889576, "dur": 25, "ph": "X", "name": "ReadAsync 410", "args": {}}, {"pid": 3100, "tid": 34359738368, "ts": 1754736892889604, "dur": 28, "ph": "X", "name": "ReadAsync 176", "args": {}}, {"pid": 3100, "tid": 34359738368, "ts": 1754736892889634, "dur": 25, "ph": "X", "name": "ReadAsync 468", "args": {}}, {"pid": 3100, "tid": 34359738368, "ts": 1754736892889661, "dur": 25, "ph": "X", "name": "ReadAsync 239", "args": {}}, {"pid": 3100, "tid": 34359738368, "ts": 1754736892889688, "dur": 25, "ph": "X", "name": "ReadAsync 330", "args": {}}, {"pid": 3100, "tid": 34359738368, "ts": 1754736892889714, "dur": 1, "ph": "X", "name": "ProcessMessages 262", "args": {}}, {"pid": 3100, "tid": 34359738368, "ts": 1754736892889716, "dur": 25, "ph": "X", "name": "ReadAsync 262", "args": {}}, {"pid": 3100, "tid": 34359738368, "ts": 1754736892889743, "dur": 25, "ph": "X", "name": "ReadAsync 294", "args": {}}, {"pid": 3100, "tid": 34359738368, "ts": 1754736892889770, "dur": 26, "ph": "X", "name": "ReadAsync 327", "args": {}}, {"pid": 3100, "tid": 34359738368, "ts": 1754736892889798, "dur": 25, "ph": "X", "name": "ReadAsync 390", "args": {}}, {"pid": 3100, "tid": 34359738368, "ts": 1754736892889824, "dur": 26, "ph": "X", "name": "ReadAsync 259", "args": {}}, {"pid": 3100, "tid": 34359738368, "ts": 1754736892889852, "dur": 25, "ph": "X", "name": "ReadAsync 300", "args": {}}, {"pid": 3100, "tid": 34359738368, "ts": 1754736892889879, "dur": 28, "ph": "X", "name": "ReadAsync 323", "args": {}}, {"pid": 3100, "tid": 34359738368, "ts": 1754736892889909, "dur": 24, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3100, "tid": 34359738368, "ts": 1754736892889935, "dur": 20, "ph": "X", "name": "ReadAsync 241", "args": {}}, {"pid": 3100, "tid": 34359738368, "ts": 1754736892889957, "dur": 25, "ph": "X", "name": "ReadAsync 142", "args": {}}, {"pid": 3100, "tid": 34359738368, "ts": 1754736892889985, "dur": 27, "ph": "X", "name": "ReadAsync 439", "args": {}}, {"pid": 3100, "tid": 34359738368, "ts": 1754736892890013, "dur": 40, "ph": "X", "name": "ReadAsync 412", "args": {}}, {"pid": 3100, "tid": 34359738368, "ts": 1754736892890056, "dur": 32, "ph": "X", "name": "ReadAsync 634", "args": {}}, {"pid": 3100, "tid": 34359738368, "ts": 1754736892890090, "dur": 27, "ph": "X", "name": "ReadAsync 473", "args": {}}, {"pid": 3100, "tid": 34359738368, "ts": 1754736892890119, "dur": 27, "ph": "X", "name": "ReadAsync 446", "args": {}}, {"pid": 3100, "tid": 34359738368, "ts": 1754736892890148, "dur": 25, "ph": "X", "name": "ReadAsync 332", "args": {}}, {"pid": 3100, "tid": 34359738368, "ts": 1754736892890175, "dur": 1, "ph": "X", "name": "ProcessMessages 270", "args": {}}, {"pid": 3100, "tid": 34359738368, "ts": 1754736892890176, "dur": 27, "ph": "X", "name": "ReadAsync 270", "args": {}}, {"pid": 3100, "tid": 34359738368, "ts": 1754736892890205, "dur": 21, "ph": "X", "name": "ReadAsync 427", "args": {}}, {"pid": 3100, "tid": 34359738368, "ts": 1754736892890228, "dur": 22, "ph": "X", "name": "ReadAsync 167", "args": {}}, {"pid": 3100, "tid": 34359738368, "ts": 1754736892890252, "dur": 26, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3100, "tid": 34359738368, "ts": 1754736892890280, "dur": 25, "ph": "X", "name": "ReadAsync 352", "args": {}}, {"pid": 3100, "tid": 34359738368, "ts": 1754736892890307, "dur": 26, "ph": "X", "name": "ReadAsync 255", "args": {}}, {"pid": 3100, "tid": 34359738368, "ts": 1754736892890335, "dur": 26, "ph": "X", "name": "ReadAsync 406", "args": {}}, {"pid": 3100, "tid": 34359738368, "ts": 1754736892890362, "dur": 25, "ph": "X", "name": "ReadAsync 319", "args": {}}, {"pid": 3100, "tid": 34359738368, "ts": 1754736892890390, "dur": 25, "ph": "X", "name": "ReadAsync 348", "args": {}}, {"pid": 3100, "tid": 34359738368, "ts": 1754736892890417, "dur": 34, "ph": "X", "name": "ReadAsync 271", "args": {}}, {"pid": 3100, "tid": 34359738368, "ts": 1754736892890457, "dur": 43, "ph": "X", "name": "ReadAsync 303", "args": {}}, {"pid": 3100, "tid": 34359738368, "ts": 1754736892890503, "dur": 1, "ph": "X", "name": "ProcessMessages 538", "args": {}}, {"pid": 3100, "tid": 34359738368, "ts": 1754736892890504, "dur": 30, "ph": "X", "name": "ReadAsync 538", "args": {}}, {"pid": 3100, "tid": 34359738368, "ts": 1754736892890536, "dur": 46, "ph": "X", "name": "ReadAsync 265", "args": {}}, {"pid": 3100, "tid": 34359738368, "ts": 1754736892890586, "dur": 1, "ph": "X", "name": "ProcessMessages 243", "args": {}}, {"pid": 3100, "tid": 34359738368, "ts": 1754736892890588, "dur": 35, "ph": "X", "name": "ReadAsync 243", "args": {}}, {"pid": 3100, "tid": 34359738368, "ts": 1754736892890625, "dur": 294, "ph": "X", "name": "ReadAsync 271", "args": {}}, {"pid": 3100, "tid": 34359738368, "ts": 1754736892890923, "dur": 34, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 3100, "tid": 34359738368, "ts": 1754736892890960, "dur": 30, "ph": "X", "name": "ReadAsync 339", "args": {}}, {"pid": 3100, "tid": 34359738368, "ts": 1754736892890993, "dur": 25, "ph": "X", "name": "ReadAsync 425", "args": {}}, {"pid": 3100, "tid": 34359738368, "ts": 1754736892891020, "dur": 32, "ph": "X", "name": "ReadAsync 194", "args": {}}, {"pid": 3100, "tid": 34359738368, "ts": 1754736892891053, "dur": 43, "ph": "X", "name": "ReadAsync 222", "args": {}}, {"pid": 3100, "tid": 34359738368, "ts": 1754736892891100, "dur": 2, "ph": "X", "name": "ProcessMessages 431", "args": {}}, {"pid": 3100, "tid": 34359738368, "ts": 1754736892891103, "dur": 59, "ph": "X", "name": "ReadAsync 431", "args": {}}, {"pid": 3100, "tid": 34359738368, "ts": 1754736892891165, "dur": 1, "ph": "X", "name": "ProcessMessages 671", "args": {}}, {"pid": 3100, "tid": 34359738368, "ts": 1754736892891167, "dur": 32, "ph": "X", "name": "ReadAsync 671", "args": {}}, {"pid": 3100, "tid": 34359738368, "ts": 1754736892891201, "dur": 45, "ph": "X", "name": "ReadAsync 242", "args": {}}, {"pid": 3100, "tid": 34359738368, "ts": 1754736892891249, "dur": 1, "ph": "X", "name": "ProcessMessages 376", "args": {}}, {"pid": 3100, "tid": 34359738368, "ts": 1754736892891251, "dur": 50, "ph": "X", "name": "ReadAsync 376", "args": {}}, {"pid": 3100, "tid": 34359738368, "ts": 1754736892891304, "dur": 26, "ph": "X", "name": "ReadAsync 759", "args": {}}, {"pid": 3100, "tid": 34359738368, "ts": 1754736892891332, "dur": 30, "ph": "X", "name": "ReadAsync 540", "args": {}}, {"pid": 3100, "tid": 34359738368, "ts": 1754736892891366, "dur": 51, "ph": "X", "name": "ReadAsync 237", "args": {}}, {"pid": 3100, "tid": 34359738368, "ts": 1754736892891421, "dur": 1, "ph": "X", "name": "ProcessMessages 422", "args": {}}, {"pid": 3100, "tid": 34359738368, "ts": 1754736892891424, "dur": 43, "ph": "X", "name": "ReadAsync 422", "args": {}}, {"pid": 3100, "tid": 34359738368, "ts": 1754736892891470, "dur": 1, "ph": "X", "name": "ProcessMessages 371", "args": {}}, {"pid": 3100, "tid": 34359738368, "ts": 1754736892891472, "dur": 55, "ph": "X", "name": "ReadAsync 371", "args": {}}, {"pid": 3100, "tid": 34359738368, "ts": 1754736892891530, "dur": 1, "ph": "X", "name": "ProcessMessages 594", "args": {}}, {"pid": 3100, "tid": 34359738368, "ts": 1754736892891532, "dur": 38, "ph": "X", "name": "ReadAsync 594", "args": {}}, {"pid": 3100, "tid": 34359738368, "ts": 1754736892891573, "dur": 1, "ph": "X", "name": "ProcessMessages 379", "args": {}}, {"pid": 3100, "tid": 34359738368, "ts": 1754736892891574, "dur": 34, "ph": "X", "name": "ReadAsync 379", "args": {}}, {"pid": 3100, "tid": 34359738368, "ts": 1754736892891612, "dur": 1, "ph": "X", "name": "ProcessMessages 639", "args": {}}, {"pid": 3100, "tid": 34359738368, "ts": 1754736892891614, "dur": 31, "ph": "X", "name": "ReadAsync 639", "args": {}}, {"pid": 3100, "tid": 34359738368, "ts": 1754736892891647, "dur": 27, "ph": "X", "name": "ReadAsync 534", "args": {}}, {"pid": 3100, "tid": 34359738368, "ts": 1754736892891676, "dur": 35, "ph": "X", "name": "ReadAsync 169", "args": {}}, {"pid": 3100, "tid": 34359738368, "ts": 1754736892891714, "dur": 33, "ph": "X", "name": "ReadAsync 339", "args": {}}, {"pid": 3100, "tid": 34359738368, "ts": 1754736892891751, "dur": 1, "ph": "X", "name": "ProcessMessages 529", "args": {}}, {"pid": 3100, "tid": 34359738368, "ts": 1754736892891753, "dur": 35, "ph": "X", "name": "ReadAsync 529", "args": {}}, {"pid": 3100, "tid": 34359738368, "ts": 1754736892891790, "dur": 27, "ph": "X", "name": "ReadAsync 87", "args": {}}, {"pid": 3100, "tid": 34359738368, "ts": 1754736892891819, "dur": 1, "ph": "X", "name": "ProcessMessages 557", "args": {}}, {"pid": 3100, "tid": 34359738368, "ts": 1754736892891820, "dur": 21, "ph": "X", "name": "ReadAsync 557", "args": {}}, {"pid": 3100, "tid": 34359738368, "ts": 1754736892891844, "dur": 25, "ph": "X", "name": "ReadAsync 125", "args": {}}, {"pid": 3100, "tid": 34359738368, "ts": 1754736892891872, "dur": 26, "ph": "X", "name": "ReadAsync 328", "args": {}}, {"pid": 3100, "tid": 34359738368, "ts": 1754736892891899, "dur": 29, "ph": "X", "name": "ReadAsync 268", "args": {}}, {"pid": 3100, "tid": 34359738368, "ts": 1754736892891930, "dur": 25, "ph": "X", "name": "ReadAsync 396", "args": {}}, {"pid": 3100, "tid": 34359738368, "ts": 1754736892891958, "dur": 24, "ph": "X", "name": "ReadAsync 318", "args": {}}, {"pid": 3100, "tid": 34359738368, "ts": 1754736892891984, "dur": 28, "ph": "X", "name": "ReadAsync 169", "args": {}}, {"pid": 3100, "tid": 34359738368, "ts": 1754736892892014, "dur": 29, "ph": "X", "name": "ReadAsync 405", "args": {}}, {"pid": 3100, "tid": 34359738368, "ts": 1754736892892045, "dur": 25, "ph": "X", "name": "ReadAsync 673", "args": {}}, {"pid": 3100, "tid": 34359738368, "ts": 1754736892892072, "dur": 27, "ph": "X", "name": "ReadAsync 343", "args": {}}, {"pid": 3100, "tid": 34359738368, "ts": 1754736892892101, "dur": 38, "ph": "X", "name": "ReadAsync 408", "args": {}}, {"pid": 3100, "tid": 34359738368, "ts": 1754736892892142, "dur": 26, "ph": "X", "name": "ReadAsync 376", "args": {}}, {"pid": 3100, "tid": 34359738368, "ts": 1754736892892170, "dur": 27, "ph": "X", "name": "ReadAsync 352", "args": {}}, {"pid": 3100, "tid": 34359738368, "ts": 1754736892892199, "dur": 20, "ph": "X", "name": "ReadAsync 546", "args": {}}, {"pid": 3100, "tid": 34359738368, "ts": 1754736892892221, "dur": 26, "ph": "X", "name": "ReadAsync 276", "args": {}}, {"pid": 3100, "tid": 34359738368, "ts": 1754736892892249, "dur": 28, "ph": "X", "name": "ReadAsync 517", "args": {}}, {"pid": 3100, "tid": 34359738368, "ts": 1754736892892279, "dur": 27, "ph": "X", "name": "ReadAsync 446", "args": {}}, {"pid": 3100, "tid": 34359738368, "ts": 1754736892892308, "dur": 24, "ph": "X", "name": "ReadAsync 450", "args": {}}, {"pid": 3100, "tid": 34359738368, "ts": 1754736892892334, "dur": 22, "ph": "X", "name": "ReadAsync 366", "args": {}}, {"pid": 3100, "tid": 34359738368, "ts": 1754736892892357, "dur": 26, "ph": "X", "name": "ReadAsync 260", "args": {}}, {"pid": 3100, "tid": 34359738368, "ts": 1754736892892385, "dur": 25, "ph": "X", "name": "ReadAsync 413", "args": {}}, {"pid": 3100, "tid": 34359738368, "ts": 1754736892892412, "dur": 27, "ph": "X", "name": "ReadAsync 419", "args": {}}, {"pid": 3100, "tid": 34359738368, "ts": 1754736892892441, "dur": 25, "ph": "X", "name": "ReadAsync 404", "args": {}}, {"pid": 3100, "tid": 34359738368, "ts": 1754736892892468, "dur": 27, "ph": "X", "name": "ReadAsync 209", "args": {}}, {"pid": 3100, "tid": 34359738368, "ts": 1754736892892498, "dur": 24, "ph": "X", "name": "ReadAsync 519", "args": {}}, {"pid": 3100, "tid": 34359738368, "ts": 1754736892892523, "dur": 18, "ph": "X", "name": "ReadAsync 91", "args": {}}, {"pid": 3100, "tid": 34359738368, "ts": 1754736892892544, "dur": 28, "ph": "X", "name": "ReadAsync 250", "args": {}}, {"pid": 3100, "tid": 34359738368, "ts": 1754736892892573, "dur": 26, "ph": "X", "name": "ReadAsync 276", "args": {}}, {"pid": 3100, "tid": 34359738368, "ts": 1754736892892601, "dur": 25, "ph": "X", "name": "ReadAsync 434", "args": {}}, {"pid": 3100, "tid": 34359738368, "ts": 1754736892892628, "dur": 24, "ph": "X", "name": "ReadAsync 297", "args": {}}, {"pid": 3100, "tid": 34359738368, "ts": 1754736892892654, "dur": 17, "ph": "X", "name": "ReadAsync 425", "args": {}}, {"pid": 3100, "tid": 34359738368, "ts": 1754736892892674, "dur": 16, "ph": "X", "name": "ReadAsync 291", "args": {}}, {"pid": 3100, "tid": 34359738368, "ts": 1754736892892692, "dur": 21, "ph": "X", "name": "ReadAsync 78", "args": {}}, {"pid": 3100, "tid": 34359738368, "ts": 1754736892892715, "dur": 33, "ph": "X", "name": "ReadAsync 248", "args": {}}, {"pid": 3100, "tid": 34359738368, "ts": 1754736892892751, "dur": 1, "ph": "X", "name": "ProcessMessages 314", "args": {}}, {"pid": 3100, "tid": 34359738368, "ts": 1754736892892752, "dur": 32, "ph": "X", "name": "ReadAsync 314", "args": {}}, {"pid": 3100, "tid": 34359738368, "ts": 1754736892892787, "dur": 26, "ph": "X", "name": "ReadAsync 664", "args": {}}, {"pid": 3100, "tid": 34359738368, "ts": 1754736892892815, "dur": 28, "ph": "X", "name": "ReadAsync 318", "args": {}}, {"pid": 3100, "tid": 34359738368, "ts": 1754736892892845, "dur": 24, "ph": "X", "name": "ReadAsync 463", "args": {}}, {"pid": 3100, "tid": 34359738368, "ts": 1754736892892871, "dur": 24, "ph": "X", "name": "ReadAsync 176", "args": {}}, {"pid": 3100, "tid": 34359738368, "ts": 1754736892892898, "dur": 26, "ph": "X", "name": "ReadAsync 372", "args": {}}, {"pid": 3100, "tid": 34359738368, "ts": 1754736892892925, "dur": 25, "ph": "X", "name": "ReadAsync 495", "args": {}}, {"pid": 3100, "tid": 34359738368, "ts": 1754736892892953, "dur": 28, "ph": "X", "name": "ReadAsync 421", "args": {}}, {"pid": 3100, "tid": 34359738368, "ts": 1754736892892983, "dur": 43, "ph": "X", "name": "ReadAsync 373", "args": {}}, {"pid": 3100, "tid": 34359738368, "ts": 1754736892893028, "dur": 21, "ph": "X", "name": "ReadAsync 569", "args": {}}, {"pid": 3100, "tid": 34359738368, "ts": 1754736892893051, "dur": 120, "ph": "X", "name": "ReadAsync 85", "args": {}}, {"pid": 3100, "tid": 34359738368, "ts": 1754736892893173, "dur": 25, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3100, "tid": 34359738368, "ts": 1754736892893200, "dur": 26, "ph": "X", "name": "ReadAsync 454", "args": {}}, {"pid": 3100, "tid": 34359738368, "ts": 1754736892893228, "dur": 18, "ph": "X", "name": "ReadAsync 465", "args": {}}, {"pid": 3100, "tid": 34359738368, "ts": 1754736892893247, "dur": 18, "ph": "X", "name": "ReadAsync 288", "args": {}}, {"pid": 3100, "tid": 34359738368, "ts": 1754736892893267, "dur": 22, "ph": "X", "name": "ReadAsync 266", "args": {}}, {"pid": 3100, "tid": 34359738368, "ts": 1754736892893291, "dur": 26, "ph": "X", "name": "ReadAsync 297", "args": {}}, {"pid": 3100, "tid": 34359738368, "ts": 1754736892893319, "dur": 71, "ph": "X", "name": "ReadAsync 379", "args": {}}, {"pid": 3100, "tid": 34359738368, "ts": 1754736892893391, "dur": 32, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3100, "tid": 34359738368, "ts": 1754736892893425, "dur": 26, "ph": "X", "name": "ReadAsync 605", "args": {}}, {"pid": 3100, "tid": 34359738368, "ts": 1754736892893453, "dur": 51, "ph": "X", "name": "ReadAsync 417", "args": {}}, {"pid": 3100, "tid": 34359738368, "ts": 1754736892893507, "dur": 26, "ph": "X", "name": "ReadAsync 413", "args": {}}, {"pid": 3100, "tid": 34359738368, "ts": 1754736892893535, "dur": 25, "ph": "X", "name": "ReadAsync 443", "args": {}}, {"pid": 3100, "tid": 34359738368, "ts": 1754736892893561, "dur": 21, "ph": "X", "name": "ReadAsync 358", "args": {}}, {"pid": 3100, "tid": 34359738368, "ts": 1754736892893585, "dur": 24, "ph": "X", "name": "ReadAsync 199", "args": {}}, {"pid": 3100, "tid": 34359738368, "ts": 1754736892893612, "dur": 28, "ph": "X", "name": "ReadAsync 244", "args": {}}, {"pid": 3100, "tid": 34359738368, "ts": 1754736892893642, "dur": 25, "ph": "X", "name": "ReadAsync 339", "args": {}}, {"pid": 3100, "tid": 34359738368, "ts": 1754736892893669, "dur": 27, "ph": "X", "name": "ReadAsync 272", "args": {}}, {"pid": 3100, "tid": 34359738368, "ts": 1754736892893698, "dur": 24, "ph": "X", "name": "ReadAsync 363", "args": {}}, {"pid": 3100, "tid": 34359738368, "ts": 1754736892893724, "dur": 25, "ph": "X", "name": "ReadAsync 243", "args": {}}, {"pid": 3100, "tid": 34359738368, "ts": 1754736892893750, "dur": 21, "ph": "X", "name": "ReadAsync 448", "args": {}}, {"pid": 3100, "tid": 34359738368, "ts": 1754736892893773, "dur": 17, "ph": "X", "name": "ReadAsync 76", "args": {}}, {"pid": 3100, "tid": 34359738368, "ts": 1754736892893791, "dur": 22, "ph": "X", "name": "ReadAsync 241", "args": {}}, {"pid": 3100, "tid": 34359738368, "ts": 1754736892893815, "dur": 25, "ph": "X", "name": "ReadAsync 216", "args": {}}, {"pid": 3100, "tid": 34359738368, "ts": 1754736892893841, "dur": 1, "ph": "X", "name": "ProcessMessages 441", "args": {}}, {"pid": 3100, "tid": 34359738368, "ts": 1754736892893842, "dur": 27, "ph": "X", "name": "ReadAsync 441", "args": {}}, {"pid": 3100, "tid": 34359738368, "ts": 1754736892893871, "dur": 26, "ph": "X", "name": "ReadAsync 354", "args": {}}, {"pid": 3100, "tid": 34359738368, "ts": 1754736892893899, "dur": 26, "ph": "X", "name": "ReadAsync 379", "args": {}}, {"pid": 3100, "tid": 34359738368, "ts": 1754736892893926, "dur": 20, "ph": "X", "name": "ReadAsync 410", "args": {}}, {"pid": 3100, "tid": 34359738368, "ts": 1754736892893948, "dur": 25, "ph": "X", "name": "ReadAsync 76", "args": {}}, {"pid": 3100, "tid": 34359738368, "ts": 1754736892893975, "dur": 27, "ph": "X", "name": "ReadAsync 357", "args": {}}, {"pid": 3100, "tid": 34359738368, "ts": 1754736892894004, "dur": 25, "ph": "X", "name": "ReadAsync 542", "args": {}}, {"pid": 3100, "tid": 34359738368, "ts": 1754736892894031, "dur": 25, "ph": "X", "name": "ReadAsync 292", "args": {}}, {"pid": 3100, "tid": 34359738368, "ts": 1754736892894058, "dur": 27, "ph": "X", "name": "ReadAsync 338", "args": {}}, {"pid": 3100, "tid": 34359738368, "ts": 1754736892894087, "dur": 16, "ph": "X", "name": "ReadAsync 506", "args": {}}, {"pid": 3100, "tid": 34359738368, "ts": 1754736892894105, "dur": 46, "ph": "X", "name": "ReadAsync 82", "args": {}}, {"pid": 3100, "tid": 34359738368, "ts": 1754736892894153, "dur": 24, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3100, "tid": 34359738368, "ts": 1754736892894179, "dur": 26, "ph": "X", "name": "ReadAsync 404", "args": {}}, {"pid": 3100, "tid": 34359738368, "ts": 1754736892894208, "dur": 27, "ph": "X", "name": "ReadAsync 564", "args": {}}, {"pid": 3100, "tid": 34359738368, "ts": 1754736892894237, "dur": 26, "ph": "X", "name": "ReadAsync 401", "args": {}}, {"pid": 3100, "tid": 34359738368, "ts": 1754736892894264, "dur": 27, "ph": "X", "name": "ReadAsync 516", "args": {}}, {"pid": 3100, "tid": 34359738368, "ts": 1754736892894293, "dur": 16, "ph": "X", "name": "ReadAsync 410", "args": {}}, {"pid": 3100, "tid": 34359738368, "ts": 1754736892894311, "dur": 47, "ph": "X", "name": "ReadAsync 144", "args": {}}, {"pid": 3100, "tid": 34359738368, "ts": 1754736892894360, "dur": 27, "ph": "X", "name": "ReadAsync 688", "args": {}}, {"pid": 3100, "tid": 34359738368, "ts": 1754736892894389, "dur": 27, "ph": "X", "name": "ReadAsync 316", "args": {}}, {"pid": 3100, "tid": 34359738368, "ts": 1754736892894418, "dur": 17, "ph": "X", "name": "ReadAsync 364", "args": {}}, {"pid": 3100, "tid": 34359738368, "ts": 1754736892894437, "dur": 14, "ph": "X", "name": "ReadAsync 300", "args": {}}, {"pid": 3100, "tid": 34359738368, "ts": 1754736892894453, "dur": 20, "ph": "X", "name": "ReadAsync 305", "args": {}}, {"pid": 3100, "tid": 34359738368, "ts": 1754736892894475, "dur": 24, "ph": "X", "name": "ReadAsync 74", "args": {}}, {"pid": 3100, "tid": 34359738368, "ts": 1754736892894502, "dur": 27, "ph": "X", "name": "ReadAsync 148", "args": {}}, {"pid": 3100, "tid": 34359738368, "ts": 1754736892894531, "dur": 27, "ph": "X", "name": "ReadAsync 550", "args": {}}, {"pid": 3100, "tid": 34359738368, "ts": 1754736892894560, "dur": 27, "ph": "X", "name": "ReadAsync 412", "args": {}}, {"pid": 3100, "tid": 34359738368, "ts": 1754736892894589, "dur": 28, "ph": "X", "name": "ReadAsync 451", "args": {}}, {"pid": 3100, "tid": 34359738368, "ts": 1754736892894619, "dur": 28, "ph": "X", "name": "ReadAsync 436", "args": {}}, {"pid": 3100, "tid": 34359738368, "ts": 1754736892894649, "dur": 21, "ph": "X", "name": "ReadAsync 408", "args": {}}, {"pid": 3100, "tid": 34359738368, "ts": 1754736892894673, "dur": 26, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 3100, "tid": 34359738368, "ts": 1754736892894701, "dur": 25, "ph": "X", "name": "ReadAsync 493", "args": {}}, {"pid": 3100, "tid": 34359738368, "ts": 1754736892894727, "dur": 39, "ph": "X", "name": "ReadAsync 382", "args": {}}, {"pid": 3100, "tid": 34359738368, "ts": 1754736892894768, "dur": 27, "ph": "X", "name": "ReadAsync 507", "args": {}}, {"pid": 3100, "tid": 34359738368, "ts": 1754736892894797, "dur": 24, "ph": "X", "name": "ReadAsync 424", "args": {}}, {"pid": 3100, "tid": 34359738368, "ts": 1754736892894823, "dur": 24, "ph": "X", "name": "ReadAsync 249", "args": {}}, {"pid": 3100, "tid": 34359738368, "ts": 1754736892894849, "dur": 25, "ph": "X", "name": "ReadAsync 282", "args": {}}, {"pid": 3100, "tid": 34359738368, "ts": 1754736892894875, "dur": 26, "ph": "X", "name": "ReadAsync 315", "args": {}}, {"pid": 3100, "tid": 34359738368, "ts": 1754736892894903, "dur": 29, "ph": "X", "name": "ReadAsync 372", "args": {}}, {"pid": 3100, "tid": 34359738368, "ts": 1754736892894934, "dur": 40, "ph": "X", "name": "ReadAsync 467", "args": {}}, {"pid": 3100, "tid": 34359738368, "ts": 1754736892894975, "dur": 27, "ph": "X", "name": "ReadAsync 355", "args": {}}, {"pid": 3100, "tid": 34359738368, "ts": 1754736892895004, "dur": 21, "ph": "X", "name": "ReadAsync 463", "args": {}}, {"pid": 3100, "tid": 34359738368, "ts": 1754736892895027, "dur": 24, "ph": "X", "name": "ReadAsync 79", "args": {}}, {"pid": 3100, "tid": 34359738368, "ts": 1754736892895053, "dur": 25, "ph": "X", "name": "ReadAsync 386", "args": {}}, {"pid": 3100, "tid": 34359738368, "ts": 1754736892895080, "dur": 26, "ph": "X", "name": "ReadAsync 406", "args": {}}, {"pid": 3100, "tid": 34359738368, "ts": 1754736892895108, "dur": 26, "ph": "X", "name": "ReadAsync 426", "args": {}}, {"pid": 3100, "tid": 34359738368, "ts": 1754736892895136, "dur": 25, "ph": "X", "name": "ReadAsync 275", "args": {}}, {"pid": 3100, "tid": 34359738368, "ts": 1754736892895163, "dur": 25, "ph": "X", "name": "ReadAsync 313", "args": {}}, {"pid": 3100, "tid": 34359738368, "ts": 1754736892895190, "dur": 16, "ph": "X", "name": "ReadAsync 289", "args": {}}, {"pid": 3100, "tid": 34359738368, "ts": 1754736892895208, "dur": 22, "ph": "X", "name": "ReadAsync 278", "args": {}}, {"pid": 3100, "tid": 34359738368, "ts": 1754736892895232, "dur": 24, "ph": "X", "name": "ReadAsync 343", "args": {}}, {"pid": 3100, "tid": 34359738368, "ts": 1754736892895258, "dur": 25, "ph": "X", "name": "ReadAsync 427", "args": {}}, {"pid": 3100, "tid": 34359738368, "ts": 1754736892895284, "dur": 27, "ph": "X", "name": "ReadAsync 412", "args": {}}, {"pid": 3100, "tid": 34359738368, "ts": 1754736892895313, "dur": 27, "ph": "X", "name": "ReadAsync 448", "args": {}}, {"pid": 3100, "tid": 34359738368, "ts": 1754736892895341, "dur": 1, "ph": "X", "name": "ProcessMessages 541", "args": {}}, {"pid": 3100, "tid": 34359738368, "ts": 1754736892895343, "dur": 23, "ph": "X", "name": "ReadAsync 541", "args": {}}, {"pid": 3100, "tid": 34359738368, "ts": 1754736892895368, "dur": 26, "ph": "X", "name": "ReadAsync 60", "args": {}}, {"pid": 3100, "tid": 34359738368, "ts": 1754736892895396, "dur": 25, "ph": "X", "name": "ReadAsync 646", "args": {}}, {"pid": 3100, "tid": 34359738368, "ts": 1754736892895422, "dur": 25, "ph": "X", "name": "ReadAsync 470", "args": {}}, {"pid": 3100, "tid": 34359738368, "ts": 1754736892895449, "dur": 25, "ph": "X", "name": "ReadAsync 460", "args": {}}, {"pid": 3100, "tid": 34359738368, "ts": 1754736892895476, "dur": 38, "ph": "X", "name": "ReadAsync 389", "args": {}}, {"pid": 3100, "tid": 34359738368, "ts": 1754736892895516, "dur": 18, "ph": "X", "name": "ReadAsync 624", "args": {}}, {"pid": 3100, "tid": 34359738368, "ts": 1754736892895537, "dur": 28, "ph": "X", "name": "ReadAsync 104", "args": {}}, {"pid": 3100, "tid": 34359738368, "ts": 1754736892895566, "dur": 26, "ph": "X", "name": "ReadAsync 143", "args": {}}, {"pid": 3100, "tid": 34359738368, "ts": 1754736892895594, "dur": 25, "ph": "X", "name": "ReadAsync 566", "args": {}}, {"pid": 3100, "tid": 34359738368, "ts": 1754736892895621, "dur": 27, "ph": "X", "name": "ReadAsync 339", "args": {}}, {"pid": 3100, "tid": 34359738368, "ts": 1754736892895650, "dur": 25, "ph": "X", "name": "ReadAsync 443", "args": {}}, {"pid": 3100, "tid": 34359738368, "ts": 1754736892895676, "dur": 31, "ph": "X", "name": "ReadAsync 310", "args": {}}, {"pid": 3100, "tid": 34359738368, "ts": 1754736892895709, "dur": 20, "ph": "X", "name": "ReadAsync 564", "args": {}}, {"pid": 3100, "tid": 34359738368, "ts": 1754736892895731, "dur": 28, "ph": "X", "name": "ReadAsync 82", "args": {}}, {"pid": 3100, "tid": 34359738368, "ts": 1754736892895761, "dur": 25, "ph": "X", "name": "ReadAsync 438", "args": {}}, {"pid": 3100, "tid": 34359738368, "ts": 1754736892895788, "dur": 24, "ph": "X", "name": "ReadAsync 471", "args": {}}, {"pid": 3100, "tid": 34359738368, "ts": 1754736892895815, "dur": 27, "ph": "X", "name": "ReadAsync 265", "args": {}}, {"pid": 3100, "tid": 34359738368, "ts": 1754736892895844, "dur": 27, "ph": "X", "name": "ReadAsync 360", "args": {}}, {"pid": 3100, "tid": 34359738368, "ts": 1754736892895873, "dur": 22, "ph": "X", "name": "ReadAsync 298", "args": {}}, {"pid": 3100, "tid": 34359738368, "ts": 1754736892895897, "dur": 26, "ph": "X", "name": "ReadAsync 153", "args": {}}, {"pid": 3100, "tid": 34359738368, "ts": 1754736892895925, "dur": 27, "ph": "X", "name": "ReadAsync 453", "args": {}}, {"pid": 3100, "tid": 34359738368, "ts": 1754736892895954, "dur": 26, "ph": "X", "name": "ReadAsync 456", "args": {}}, {"pid": 3100, "tid": 34359738368, "ts": 1754736892895982, "dur": 27, "ph": "X", "name": "ReadAsync 343", "args": {}}, {"pid": 3100, "tid": 34359738368, "ts": 1754736892896011, "dur": 25, "ph": "X", "name": "ReadAsync 483", "args": {}}, {"pid": 3100, "tid": 34359738368, "ts": 1754736892896036, "dur": 1, "ph": "X", "name": "ProcessMessages 306", "args": {}}, {"pid": 3100, "tid": 34359738368, "ts": 1754736892896038, "dur": 23, "ph": "X", "name": "ReadAsync 306", "args": {}}, {"pid": 3100, "tid": 34359738368, "ts": 1754736892896063, "dur": 25, "ph": "X", "name": "ReadAsync 88", "args": {}}, {"pid": 3100, "tid": 34359738368, "ts": 1754736892896090, "dur": 26, "ph": "X", "name": "ReadAsync 425", "args": {}}, {"pid": 3100, "tid": 34359738368, "ts": 1754736892896118, "dur": 25, "ph": "X", "name": "ReadAsync 427", "args": {}}, {"pid": 3100, "tid": 34359738368, "ts": 1754736892896145, "dur": 25, "ph": "X", "name": "ReadAsync 315", "args": {}}, {"pid": 3100, "tid": 34359738368, "ts": 1754736892896171, "dur": 24, "ph": "X", "name": "ReadAsync 196", "args": {}}, {"pid": 3100, "tid": 34359738368, "ts": 1754736892896198, "dur": 24, "ph": "X", "name": "ReadAsync 355", "args": {}}, {"pid": 3100, "tid": 34359738368, "ts": 1754736892896224, "dur": 25, "ph": "X", "name": "ReadAsync 277", "args": {}}, {"pid": 3100, "tid": 34359738368, "ts": 1754736892896251, "dur": 26, "ph": "X", "name": "ReadAsync 408", "args": {}}, {"pid": 3100, "tid": 34359738368, "ts": 1754736892896278, "dur": 25, "ph": "X", "name": "ReadAsync 565", "args": {}}, {"pid": 3100, "tid": 34359738368, "ts": 1754736892896305, "dur": 26, "ph": "X", "name": "ReadAsync 365", "args": {}}, {"pid": 3100, "tid": 34359738368, "ts": 1754736892896332, "dur": 25, "ph": "X", "name": "ReadAsync 436", "args": {}}, {"pid": 3100, "tid": 34359738368, "ts": 1754736892896359, "dur": 25, "ph": "X", "name": "ReadAsync 442", "args": {}}, {"pid": 3100, "tid": 34359738368, "ts": 1754736892896386, "dur": 25, "ph": "X", "name": "ReadAsync 363", "args": {}}, {"pid": 3100, "tid": 34359738368, "ts": 1754736892896413, "dur": 25, "ph": "X", "name": "ReadAsync 358", "args": {}}, {"pid": 3100, "tid": 34359738368, "ts": 1754736892896440, "dur": 25, "ph": "X", "name": "ReadAsync 399", "args": {}}, {"pid": 3100, "tid": 34359738368, "ts": 1754736892896467, "dur": 27, "ph": "X", "name": "ReadAsync 343", "args": {}}, {"pid": 3100, "tid": 34359738368, "ts": 1754736892896496, "dur": 27, "ph": "X", "name": "ReadAsync 510", "args": {}}, {"pid": 3100, "tid": 34359738368, "ts": 1754736892896524, "dur": 26, "ph": "X", "name": "ReadAsync 127", "args": {}}, {"pid": 3100, "tid": 34359738368, "ts": 1754736892896552, "dur": 21, "ph": "X", "name": "ReadAsync 530", "args": {}}, {"pid": 3100, "tid": 34359738368, "ts": 1754736892896575, "dur": 32, "ph": "X", "name": "ReadAsync 92", "args": {}}, {"pid": 3100, "tid": 34359738368, "ts": 1754736892896610, "dur": 50, "ph": "X", "name": "ReadAsync 328", "args": {}}, {"pid": 3100, "tid": 34359738368, "ts": 1754736892896661, "dur": 1, "ph": "X", "name": "ProcessMessages 485", "args": {}}, {"pid": 3100, "tid": 34359738368, "ts": 1754736892896663, "dur": 48, "ph": "X", "name": "ReadAsync 485", "args": {}}, {"pid": 3100, "tid": 34359738368, "ts": 1754736892896713, "dur": 1, "ph": "X", "name": "ProcessMessages 647", "args": {}}, {"pid": 3100, "tid": 34359738368, "ts": 1754736892896715, "dur": 76, "ph": "X", "name": "ReadAsync 647", "args": {}}, {"pid": 3100, "tid": 34359738368, "ts": 1754736892896795, "dur": 2, "ph": "X", "name": "ProcessMessages 596", "args": {}}, {"pid": 3100, "tid": 34359738368, "ts": 1754736892896799, "dur": 90, "ph": "X", "name": "ReadAsync 596", "args": {}}, {"pid": 3100, "tid": 34359738368, "ts": 1754736892896893, "dur": 2, "ph": "X", "name": "ProcessMessages 656", "args": {}}, {"pid": 3100, "tid": 34359738368, "ts": 1754736892896897, "dur": 56, "ph": "X", "name": "ReadAsync 656", "args": {}}, {"pid": 3100, "tid": 34359738368, "ts": 1754736892896956, "dur": 1, "ph": "X", "name": "ProcessMessages 635", "args": {}}, {"pid": 3100, "tid": 34359738368, "ts": 1754736892896958, "dur": 52, "ph": "X", "name": "ReadAsync 635", "args": {}}, {"pid": 3100, "tid": 34359738368, "ts": 1754736892897013, "dur": 53, "ph": "X", "name": "ReadAsync 129", "args": {}}, {"pid": 3100, "tid": 34359738368, "ts": 1754736892897068, "dur": 1, "ph": "X", "name": "ProcessMessages 457", "args": {}}, {"pid": 3100, "tid": 34359738368, "ts": 1754736892897070, "dur": 53, "ph": "X", "name": "ReadAsync 457", "args": {}}, {"pid": 3100, "tid": 34359738368, "ts": 1754736892897125, "dur": 1, "ph": "X", "name": "ProcessMessages 586", "args": {}}, {"pid": 3100, "tid": 34359738368, "ts": 1754736892897127, "dur": 54, "ph": "X", "name": "ReadAsync 586", "args": {}}, {"pid": 3100, "tid": 34359738368, "ts": 1754736892897183, "dur": 1, "ph": "X", "name": "ProcessMessages 680", "args": {}}, {"pid": 3100, "tid": 34359738368, "ts": 1754736892897185, "dur": 54, "ph": "X", "name": "ReadAsync 680", "args": {}}, {"pid": 3100, "tid": 34359738368, "ts": 1754736892897240, "dur": 1, "ph": "X", "name": "ProcessMessages 662", "args": {}}, {"pid": 3100, "tid": 34359738368, "ts": 1754736892897242, "dur": 52, "ph": "X", "name": "ReadAsync 662", "args": {}}, {"pid": 3100, "tid": 34359738368, "ts": 1754736892897296, "dur": 1, "ph": "X", "name": "ProcessMessages 595", "args": {}}, {"pid": 3100, "tid": 34359738368, "ts": 1754736892897297, "dur": 46, "ph": "X", "name": "ReadAsync 595", "args": {}}, {"pid": 3100, "tid": 34359738368, "ts": 1754736892897346, "dur": 53, "ph": "X", "name": "ReadAsync 329", "args": {}}, {"pid": 3100, "tid": 34359738368, "ts": 1754736892897401, "dur": 1, "ph": "X", "name": "ProcessMessages 430", "args": {}}, {"pid": 3100, "tid": 34359738368, "ts": 1754736892897403, "dur": 49, "ph": "X", "name": "ReadAsync 430", "args": {}}, {"pid": 3100, "tid": 34359738368, "ts": 1754736892897455, "dur": 35, "ph": "X", "name": "ReadAsync 240", "args": {}}, {"pid": 3100, "tid": 34359738368, "ts": 1754736892897492, "dur": 25, "ph": "X", "name": "ReadAsync 372", "args": {}}, {"pid": 3100, "tid": 34359738368, "ts": 1754736892897518, "dur": 27, "ph": "X", "name": "ReadAsync 323", "args": {}}, {"pid": 3100, "tid": 34359738368, "ts": 1754736892897547, "dur": 20, "ph": "X", "name": "ReadAsync 286", "args": {}}, {"pid": 3100, "tid": 34359738368, "ts": 1754736892897569, "dur": 114, "ph": "X", "name": "ReadAsync 8", "args": {}}, {"pid": 3100, "tid": 34359738368, "ts": 1754736892897688, "dur": 39, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3100, "tid": 34359738368, "ts": 1754736892897730, "dur": 28, "ph": "X", "name": "ReadAsync 669", "args": {}}, {"pid": 3100, "tid": 34359738368, "ts": 1754736892897760, "dur": 17, "ph": "X", "name": "ReadAsync 410", "args": {}}, {"pid": 3100, "tid": 34359738368, "ts": 1754736892897779, "dur": 24, "ph": "X", "name": "ReadAsync 246", "args": {}}, {"pid": 3100, "tid": 34359738368, "ts": 1754736892897805, "dur": 24, "ph": "X", "name": "ReadAsync 276", "args": {}}, {"pid": 3100, "tid": 34359738368, "ts": 1754736892897831, "dur": 25, "ph": "X", "name": "ReadAsync 309", "args": {}}, {"pid": 3100, "tid": 34359738368, "ts": 1754736892897858, "dur": 24, "ph": "X", "name": "ReadAsync 322", "args": {}}, {"pid": 3100, "tid": 34359738368, "ts": 1754736892897884, "dur": 24, "ph": "X", "name": "ReadAsync 286", "args": {}}, {"pid": 3100, "tid": 34359738368, "ts": 1754736892897910, "dur": 20, "ph": "X", "name": "ReadAsync 217", "args": {}}, {"pid": 3100, "tid": 34359738368, "ts": 1754736892897931, "dur": 60, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 3100, "tid": 34359738368, "ts": 1754736892897994, "dur": 24, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3100, "tid": 34359738368, "ts": 1754736892898019, "dur": 24, "ph": "X", "name": "ReadAsync 224", "args": {}}, {"pid": 3100, "tid": 34359738368, "ts": 1754736892898045, "dur": 26, "ph": "X", "name": "ReadAsync 203", "args": {}}, {"pid": 3100, "tid": 34359738368, "ts": 1754736892898073, "dur": 24, "ph": "X", "name": "ReadAsync 414", "args": {}}, {"pid": 3100, "tid": 34359738368, "ts": 1754736892898099, "dur": 27, "ph": "X", "name": "ReadAsync 286", "args": {}}, {"pid": 3100, "tid": 34359738368, "ts": 1754736892898128, "dur": 21, "ph": "X", "name": "ReadAsync 343", "args": {}}, {"pid": 3100, "tid": 34359738368, "ts": 1754736892898151, "dur": 24, "ph": "X", "name": "ReadAsync 215", "args": {}}, {"pid": 3100, "tid": 34359738368, "ts": 1754736892898177, "dur": 24, "ph": "X", "name": "ReadAsync 267", "args": {}}, {"pid": 3100, "tid": 34359738368, "ts": 1754736892898203, "dur": 23, "ph": "X", "name": "ReadAsync 315", "args": {}}, {"pid": 3100, "tid": 34359738368, "ts": 1754736892898235, "dur": 23, "ph": "X", "name": "ReadAsync 89", "args": {}}, {"pid": 3100, "tid": 34359738368, "ts": 1754736892898260, "dur": 41, "ph": "X", "name": "ReadAsync 148", "args": {}}, {"pid": 3100, "tid": 34359738368, "ts": 1754736892898302, "dur": 25, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3100, "tid": 34359738368, "ts": 1754736892898329, "dur": 27, "ph": "X", "name": "ReadAsync 269", "args": {}}, {"pid": 3100, "tid": 34359738368, "ts": 1754736892898357, "dur": 25, "ph": "X", "name": "ReadAsync 306", "args": {}}, {"pid": 3100, "tid": 34359738368, "ts": 1754736892898384, "dur": 24, "ph": "X", "name": "ReadAsync 262", "args": {}}, {"pid": 3100, "tid": 34359738368, "ts": 1754736892898411, "dur": 24, "ph": "X", "name": "ReadAsync 348", "args": {}}, {"pid": 3100, "tid": 34359738368, "ts": 1754736892898436, "dur": 24, "ph": "X", "name": "ReadAsync 300", "args": {}}, {"pid": 3100, "tid": 34359738368, "ts": 1754736892898462, "dur": 27, "ph": "X", "name": "ReadAsync 208", "args": {}}, {"pid": 3100, "tid": 34359738368, "ts": 1754736892898491, "dur": 20, "ph": "X", "name": "ReadAsync 333", "args": {}}, {"pid": 3100, "tid": 34359738368, "ts": 1754736892898513, "dur": 24, "ph": "X", "name": "ReadAsync 58", "args": {}}, {"pid": 3100, "tid": 34359738368, "ts": 1754736892898539, "dur": 60, "ph": "X", "name": "ReadAsync 237", "args": {}}, {"pid": 3100, "tid": 34359738368, "ts": 1754736892898600, "dur": 24, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3100, "tid": 34359738368, "ts": 1754736892898627, "dur": 25, "ph": "X", "name": "ReadAsync 258", "args": {}}, {"pid": 3100, "tid": 34359738368, "ts": 1754736892898654, "dur": 27, "ph": "X", "name": "ReadAsync 328", "args": {}}, {"pid": 3100, "tid": 34359738368, "ts": 1754736892898682, "dur": 20, "ph": "X", "name": "ReadAsync 290", "args": {}}, {"pid": 3100, "tid": 34359738368, "ts": 1754736892898703, "dur": 1, "ph": "X", "name": "ProcessMessages 52", "args": {}}, {"pid": 3100, "tid": 34359738368, "ts": 1754736892898705, "dur": 43, "ph": "X", "name": "ReadAsync 52", "args": {}}, {"pid": 3100, "tid": 34359738368, "ts": 1754736892898750, "dur": 25, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3100, "tid": 34359738368, "ts": 1754736892898776, "dur": 24, "ph": "X", "name": "ReadAsync 281", "args": {}}, {"pid": 3100, "tid": 34359738368, "ts": 1754736892898803, "dur": 24, "ph": "X", "name": "ReadAsync 354", "args": {}}, {"pid": 3100, "tid": 34359738368, "ts": 1754736892898829, "dur": 61, "ph": "X", "name": "ReadAsync 351", "args": {}}, {"pid": 3100, "tid": 34359738368, "ts": 1754736892898891, "dur": 24, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3100, "tid": 34359738368, "ts": 1754736892898918, "dur": 24, "ph": "X", "name": "ReadAsync 300", "args": {}}, {"pid": 3100, "tid": 34359738368, "ts": 1754736892898944, "dur": 24, "ph": "X", "name": "ReadAsync 406", "args": {}}, {"pid": 3100, "tid": 34359738368, "ts": 1754736892898971, "dur": 53, "ph": "X", "name": "ReadAsync 294", "args": {}}, {"pid": 3100, "tid": 34359738368, "ts": 1754736892899025, "dur": 25, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3100, "tid": 34359738368, "ts": 1754736892899052, "dur": 24, "ph": "X", "name": "ReadAsync 305", "args": {}}, {"pid": 3100, "tid": 34359738368, "ts": 1754736892899078, "dur": 28, "ph": "X", "name": "ReadAsync 183", "args": {}}, {"pid": 3100, "tid": 34359738368, "ts": 1754736892899108, "dur": 22, "ph": "X", "name": "ReadAsync 487", "args": {}}, {"pid": 3100, "tid": 34359738368, "ts": 1754736892899132, "dur": 73, "ph": "X", "name": "ReadAsync 61", "args": {}}, {"pid": 3100, "tid": 34359738368, "ts": 1754736892899207, "dur": 24, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3100, "tid": 34359738368, "ts": 1754736892899234, "dur": 24, "ph": "X", "name": "ReadAsync 227", "args": {}}, {"pid": 3100, "tid": 34359738368, "ts": 1754736892899260, "dur": 28, "ph": "X", "name": "ReadAsync 334", "args": {}}, {"pid": 3100, "tid": 34359738368, "ts": 1754736892899290, "dur": 23, "ph": "X", "name": "ReadAsync 457", "args": {}}, {"pid": 3100, "tid": 34359738368, "ts": 1754736892899314, "dur": 55, "ph": "X", "name": "ReadAsync 138", "args": {}}, {"pid": 3100, "tid": 34359738368, "ts": 1754736892899370, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 3100, "tid": 34359738368, "ts": 1754736892899371, "dur": 24, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3100, "tid": 34359738368, "ts": 1754736892899398, "dur": 24, "ph": "X", "name": "ReadAsync 404", "args": {}}, {"pid": 3100, "tid": 34359738368, "ts": 1754736892899424, "dur": 24, "ph": "X", "name": "ReadAsync 493", "args": {}}, {"pid": 3100, "tid": 34359738368, "ts": 1754736892899450, "dur": 20, "ph": "X", "name": "ReadAsync 366", "args": {}}, {"pid": 3100, "tid": 34359738368, "ts": 1754736892899471, "dur": 42, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 3100, "tid": 34359738368, "ts": 1754736892899515, "dur": 25, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3100, "tid": 34359738368, "ts": 1754736892899542, "dur": 26, "ph": "X", "name": "ReadAsync 314", "args": {}}, {"pid": 3100, "tid": 34359738368, "ts": 1754736892899570, "dur": 24, "ph": "X", "name": "ReadAsync 548", "args": {}}, {"pid": 3100, "tid": 34359738368, "ts": 1754736892899596, "dur": 24, "ph": "X", "name": "ReadAsync 150", "args": {}}, {"pid": 3100, "tid": 34359738368, "ts": 1754736892899622, "dur": 63, "ph": "X", "name": "ReadAsync 225", "args": {}}, {"pid": 3100, "tid": 34359738368, "ts": 1754736892899686, "dur": 24, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3100, "tid": 34359738368, "ts": 1754736892899712, "dur": 24, "ph": "X", "name": "ReadAsync 275", "args": {}}, {"pid": 3100, "tid": 34359738368, "ts": 1754736892899738, "dur": 24, "ph": "X", "name": "ReadAsync 233", "args": {}}, {"pid": 3100, "tid": 34359738368, "ts": 1754736892899764, "dur": 24, "ph": "X", "name": "ReadAsync 290", "args": {}}, {"pid": 3100, "tid": 34359738368, "ts": 1754736892899790, "dur": 20, "ph": "X", "name": "ReadAsync 288", "args": {}}, {"pid": 3100, "tid": 34359738368, "ts": 1754736892899812, "dur": 47, "ph": "X", "name": "ReadAsync 8", "args": {}}, {"pid": 3100, "tid": 34359738368, "ts": 1754736892899861, "dur": 26, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3100, "tid": 34359738368, "ts": 1754736892899889, "dur": 24, "ph": "X", "name": "ReadAsync 261", "args": {}}, {"pid": 3100, "tid": 34359738368, "ts": 1754736892899915, "dur": 24, "ph": "X", "name": "ReadAsync 341", "args": {}}, {"pid": 3100, "tid": 34359738368, "ts": 1754736892899941, "dur": 24, "ph": "X", "name": "ReadAsync 326", "args": {}}, {"pid": 3100, "tid": 34359738368, "ts": 1754736892899966, "dur": 47, "ph": "X", "name": "ReadAsync 144", "args": {}}, {"pid": 3100, "tid": 34359738368, "ts": 1754736892900015, "dur": 26, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3100, "tid": 34359738368, "ts": 1754736892900043, "dur": 33, "ph": "X", "name": "ReadAsync 279", "args": {}}, {"pid": 3100, "tid": 34359738368, "ts": 1754736892900078, "dur": 25, "ph": "X", "name": "ReadAsync 462", "args": {}}, {"pid": 3100, "tid": 34359738368, "ts": 1754736892900106, "dur": 20, "ph": "X", "name": "ReadAsync 269", "args": {}}, {"pid": 3100, "tid": 34359738368, "ts": 1754736892900127, "dur": 54, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 3100, "tid": 34359738368, "ts": 1754736892900183, "dur": 25, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3100, "tid": 34359738368, "ts": 1754736892900210, "dur": 24, "ph": "X", "name": "ReadAsync 155", "args": {}}, {"pid": 3100, "tid": 34359738368, "ts": 1754736892900237, "dur": 26, "ph": "X", "name": "ReadAsync 266", "args": {}}, {"pid": 3100, "tid": 34359738368, "ts": 1754736892900264, "dur": 24, "ph": "X", "name": "ReadAsync 423", "args": {}}, {"pid": 3100, "tid": 34359738368, "ts": 1754736892900290, "dur": 54, "ph": "X", "name": "ReadAsync 138", "args": {}}, {"pid": 3100, "tid": 34359738368, "ts": 1754736892900345, "dur": 25, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3100, "tid": 34359738368, "ts": 1754736892900372, "dur": 23, "ph": "X", "name": "ReadAsync 299", "args": {}}, {"pid": 3100, "tid": 34359738368, "ts": 1754736892900396, "dur": 20, "ph": "X", "name": "ReadAsync 146", "args": {}}, {"pid": 3100, "tid": 34359738368, "ts": 1754736892900418, "dur": 24, "ph": "X", "name": "ReadAsync 40", "args": {}}, {"pid": 3100, "tid": 34359738368, "ts": 1754736892900444, "dur": 25, "ph": "X", "name": "ReadAsync 424", "args": {}}, {"pid": 3100, "tid": 34359738368, "ts": 1754736892900471, "dur": 24, "ph": "X", "name": "ReadAsync 346", "args": {}}, {"pid": 3100, "tid": 34359738368, "ts": 1754736892900497, "dur": 24, "ph": "X", "name": "ReadAsync 394", "args": {}}, {"pid": 3100, "tid": 34359738368, "ts": 1754736892900523, "dur": 26, "ph": "X", "name": "ReadAsync 336", "args": {}}, {"pid": 3100, "tid": 34359738368, "ts": 1754736892900551, "dur": 20, "ph": "X", "name": "ReadAsync 435", "args": {}}, {"pid": 3100, "tid": 34359738368, "ts": 1754736892900572, "dur": 25, "ph": "X", "name": "ReadAsync 171", "args": {}}, {"pid": 3100, "tid": 34359738368, "ts": 1754736892900598, "dur": 20, "ph": "X", "name": "ReadAsync 210", "args": {}}, {"pid": 3100, "tid": 34359738368, "ts": 1754736892900620, "dur": 44, "ph": "X", "name": "ReadAsync 21", "args": {}}, {"pid": 3100, "tid": 34359738368, "ts": 1754736892900665, "dur": 25, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3100, "tid": 34359738368, "ts": 1754736892900692, "dur": 26, "ph": "X", "name": "ReadAsync 327", "args": {}}, {"pid": 3100, "tid": 34359738368, "ts": 1754736892900720, "dur": 24, "ph": "X", "name": "ReadAsync 365", "args": {}}, {"pid": 3100, "tid": 34359738368, "ts": 1754736892900745, "dur": 24, "ph": "X", "name": "ReadAsync 234", "args": {}}, {"pid": 3100, "tid": 34359738368, "ts": 1754736892900771, "dur": 24, "ph": "X", "name": "ReadAsync 432", "args": {}}, {"pid": 3100, "tid": 34359738368, "ts": 1754736892900797, "dur": 24, "ph": "X", "name": "ReadAsync 343", "args": {}}, {"pid": 3100, "tid": 34359738368, "ts": 1754736892900823, "dur": 25, "ph": "X", "name": "ReadAsync 244", "args": {}}, {"pid": 3100, "tid": 34359738368, "ts": 1754736892900850, "dur": 24, "ph": "X", "name": "ReadAsync 291", "args": {}}, {"pid": 3100, "tid": 34359738368, "ts": 1754736892900876, "dur": 24, "ph": "X", "name": "ReadAsync 291", "args": {}}, {"pid": 3100, "tid": 34359738368, "ts": 1754736892900902, "dur": 20, "ph": "X", "name": "ReadAsync 182", "args": {}}, {"pid": 3100, "tid": 34359738368, "ts": 1754736892900924, "dur": 47, "ph": "X", "name": "ReadAsync 58", "args": {}}, {"pid": 3100, "tid": 34359738368, "ts": 1754736892900973, "dur": 24, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3100, "tid": 34359738368, "ts": 1754736892900999, "dur": 24, "ph": "X", "name": "ReadAsync 314", "args": {}}, {"pid": 3100, "tid": 34359738368, "ts": 1754736892901025, "dur": 24, "ph": "X", "name": "ReadAsync 300", "args": {}}, {"pid": 3100, "tid": 34359738368, "ts": 1754736892901051, "dur": 25, "ph": "X", "name": "ReadAsync 285", "args": {}}, {"pid": 3100, "tid": 34359738368, "ts": 1754736892901078, "dur": 24, "ph": "X", "name": "ReadAsync 437", "args": {}}, {"pid": 3100, "tid": 34359738368, "ts": 1754736892901104, "dur": 43, "ph": "X", "name": "ReadAsync 140", "args": {}}, {"pid": 3100, "tid": 34359738368, "ts": 1754736892901148, "dur": 24, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3100, "tid": 34359738368, "ts": 1754736892901175, "dur": 24, "ph": "X", "name": "ReadAsync 314", "args": {}}, {"pid": 3100, "tid": 34359738368, "ts": 1754736892901201, "dur": 27, "ph": "X", "name": "ReadAsync 381", "args": {}}, {"pid": 3100, "tid": 34359738368, "ts": 1754736892901230, "dur": 24, "ph": "X", "name": "ReadAsync 428", "args": {}}, {"pid": 3100, "tid": 34359738368, "ts": 1754736892901256, "dur": 24, "ph": "X", "name": "ReadAsync 322", "args": {}}, {"pid": 3100, "tid": 34359738368, "ts": 1754736892901282, "dur": 23, "ph": "X", "name": "ReadAsync 270", "args": {}}, {"pid": 3100, "tid": 34359738368, "ts": 1754736892901307, "dur": 25, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3100, "tid": 34359738368, "ts": 1754736892901334, "dur": 26, "ph": "X", "name": "ReadAsync 367", "args": {}}, {"pid": 3100, "tid": 34359738368, "ts": 1754736892901362, "dur": 24, "ph": "X", "name": "ReadAsync 321", "args": {}}, {"pid": 3100, "tid": 34359738368, "ts": 1754736892901388, "dur": 24, "ph": "X", "name": "ReadAsync 165", "args": {}}, {"pid": 3100, "tid": 34359738368, "ts": 1754736892901414, "dur": 52, "ph": "X", "name": "ReadAsync 237", "args": {}}, {"pid": 3100, "tid": 34359738368, "ts": 1754736892901468, "dur": 24, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3100, "tid": 34359738368, "ts": 1754736892901494, "dur": 24, "ph": "X", "name": "ReadAsync 320", "args": {}}, {"pid": 3100, "tid": 34359738368, "ts": 1754736892901520, "dur": 24, "ph": "X", "name": "ReadAsync 354", "args": {}}, {"pid": 3100, "tid": 34359738368, "ts": 1754736892901546, "dur": 54, "ph": "X", "name": "ReadAsync 347", "args": {}}, {"pid": 3100, "tid": 34359738368, "ts": 1754736892901601, "dur": 24, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3100, "tid": 34359738368, "ts": 1754736892901627, "dur": 25, "ph": "X", "name": "ReadAsync 201", "args": {}}, {"pid": 3100, "tid": 34359738368, "ts": 1754736892901654, "dur": 24, "ph": "X", "name": "ReadAsync 368", "args": {}}, {"pid": 3100, "tid": 34359738368, "ts": 1754736892901680, "dur": 24, "ph": "X", "name": "ReadAsync 323", "args": {}}, {"pid": 3100, "tid": 34359738368, "ts": 1754736892901706, "dur": 49, "ph": "X", "name": "ReadAsync 144", "args": {}}, {"pid": 3100, "tid": 34359738368, "ts": 1754736892901756, "dur": 24, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3100, "tid": 34359738368, "ts": 1754736892901782, "dur": 24, "ph": "X", "name": "ReadAsync 331", "args": {}}, {"pid": 3100, "tid": 34359738368, "ts": 1754736892901808, "dur": 25, "ph": "X", "name": "ReadAsync 409", "args": {}}, {"pid": 3100, "tid": 34359738368, "ts": 1754736892901835, "dur": 23, "ph": "X", "name": "ReadAsync 346", "args": {}}, {"pid": 3100, "tid": 34359738368, "ts": 1754736892901860, "dur": 24, "ph": "X", "name": "ReadAsync 131", "args": {}}, {"pid": 3100, "tid": 34359738368, "ts": 1754736892901886, "dur": 24, "ph": "X", "name": "ReadAsync 462", "args": {}}, {"pid": 3100, "tid": 34359738368, "ts": 1754736892901912, "dur": 24, "ph": "X", "name": "ReadAsync 376", "args": {}}, {"pid": 3100, "tid": 34359738368, "ts": 1754736892901938, "dur": 24, "ph": "X", "name": "ReadAsync 395", "args": {}}, {"pid": 3100, "tid": 34359738368, "ts": 1754736892901964, "dur": 24, "ph": "X", "name": "ReadAsync 321", "args": {}}, {"pid": 3100, "tid": 34359738368, "ts": 1754736892901989, "dur": 58, "ph": "X", "name": "ReadAsync 240", "args": {}}, {"pid": 3100, "tid": 34359738368, "ts": 1754736892902049, "dur": 24, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3100, "tid": 34359738368, "ts": 1754736892902075, "dur": 24, "ph": "X", "name": "ReadAsync 349", "args": {}}, {"pid": 3100, "tid": 34359738368, "ts": 1754736892902101, "dur": 25, "ph": "X", "name": "ReadAsync 272", "args": {}}, {"pid": 3100, "tid": 34359738368, "ts": 1754736892902129, "dur": 24, "ph": "X", "name": "ReadAsync 409", "args": {}}, {"pid": 3100, "tid": 34359738368, "ts": 1754736892902154, "dur": 45, "ph": "X", "name": "ReadAsync 58", "args": {}}, {"pid": 3100, "tid": 34359738368, "ts": 1754736892902201, "dur": 24, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3100, "tid": 34359738368, "ts": 1754736892902228, "dur": 1, "ph": "X", "name": "ProcessMessages 338", "args": {}}, {"pid": 3100, "tid": 34359738368, "ts": 1754736892902230, "dur": 24, "ph": "X", "name": "ReadAsync 338", "args": {}}, {"pid": 3100, "tid": 34359738368, "ts": 1754736892902255, "dur": 24, "ph": "X", "name": "ReadAsync 479", "args": {}}, {"pid": 3100, "tid": 34359738368, "ts": 1754736892902282, "dur": 56, "ph": "X", "name": "ReadAsync 306", "args": {}}, {"pid": 3100, "tid": 34359738368, "ts": 1754736892902340, "dur": 24, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3100, "tid": 34359738368, "ts": 1754736892902366, "dur": 24, "ph": "X", "name": "ReadAsync 260", "args": {}}, {"pid": 3100, "tid": 34359738368, "ts": 1754736892902392, "dur": 26, "ph": "X", "name": "ReadAsync 370", "args": {}}, {"pid": 3100, "tid": 34359738368, "ts": 1754736892902420, "dur": 23, "ph": "X", "name": "ReadAsync 410", "args": {}}, {"pid": 3100, "tid": 34359738368, "ts": 1754736892902444, "dur": 38, "ph": "X", "name": "ReadAsync 57", "args": {}}, {"pid": 3100, "tid": 34359738368, "ts": 1754736892902484, "dur": 66, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3100, "tid": 34359738368, "ts": 1754736892902555, "dur": 2, "ph": "X", "name": "ProcessMessages 324", "args": {}}, {"pid": 3100, "tid": 34359738368, "ts": 1754736892902559, "dur": 67, "ph": "X", "name": "ReadAsync 324", "args": {}}, {"pid": 3100, "tid": 34359738368, "ts": 1754736892902630, "dur": 1, "ph": "X", "name": "ProcessMessages 721", "args": {}}, {"pid": 3100, "tid": 34359738368, "ts": 1754736892902632, "dur": 52, "ph": "X", "name": "ReadAsync 721", "args": {}}, {"pid": 3100, "tid": 34359738368, "ts": 1754736892902687, "dur": 1, "ph": "X", "name": "ProcessMessages 332", "args": {}}, {"pid": 3100, "tid": 34359738368, "ts": 1754736892902689, "dur": 36, "ph": "X", "name": "ReadAsync 332", "args": {}}, {"pid": 3100, "tid": 34359738368, "ts": 1754736892902728, "dur": 1, "ph": "X", "name": "ProcessMessages 149", "args": {}}, {"pid": 3100, "tid": 34359738368, "ts": 1754736892902729, "dur": 79, "ph": "X", "name": "ReadAsync 149", "args": {}}, {"pid": 3100, "tid": 34359738368, "ts": 1754736892902814, "dur": 1, "ph": "X", "name": "ProcessMessages 300", "args": {}}, {"pid": 3100, "tid": 34359738368, "ts": 1754736892902816, "dur": 73, "ph": "X", "name": "ReadAsync 300", "args": {}}, {"pid": 3100, "tid": 34359738368, "ts": 1754736892902891, "dur": 1, "ph": "X", "name": "ProcessMessages 301", "args": {}}, {"pid": 3100, "tid": 34359738368, "ts": 1754736892902894, "dur": 66, "ph": "X", "name": "ReadAsync 301", "args": {}}, {"pid": 3100, "tid": 34359738368, "ts": 1754736892902964, "dur": 62, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3100, "tid": 34359738368, "ts": 1754736892903028, "dur": 1, "ph": "X", "name": "ProcessMessages 276", "args": {}}, {"pid": 3100, "tid": 34359738368, "ts": 1754736892903030, "dur": 49, "ph": "X", "name": "ReadAsync 276", "args": {}}, {"pid": 3100, "tid": 34359738368, "ts": 1754736892903082, "dur": 53, "ph": "X", "name": "ReadAsync 205", "args": {}}, {"pid": 3100, "tid": 34359738368, "ts": 1754736892903138, "dur": 50, "ph": "X", "name": "ReadAsync 466", "args": {}}, {"pid": 3100, "tid": 34359738368, "ts": 1754736892903189, "dur": 1, "ph": "X", "name": "ProcessMessages 543", "args": {}}, {"pid": 3100, "tid": 34359738368, "ts": 1754736892903191, "dur": 56, "ph": "X", "name": "ReadAsync 543", "args": {}}, {"pid": 3100, "tid": 34359738368, "ts": 1754736892903250, "dur": 1, "ph": "X", "name": "ProcessMessages 758", "args": {}}, {"pid": 3100, "tid": 34359738368, "ts": 1754736892903252, "dur": 55, "ph": "X", "name": "ReadAsync 758", "args": {}}, {"pid": 3100, "tid": 34359738368, "ts": 1754736892903309, "dur": 1, "ph": "X", "name": "ProcessMessages 381", "args": {}}, {"pid": 3100, "tid": 34359738368, "ts": 1754736892903311, "dur": 50, "ph": "X", "name": "ReadAsync 381", "args": {}}, {"pid": 3100, "tid": 34359738368, "ts": 1754736892903364, "dur": 33, "ph": "X", "name": "ReadAsync 237", "args": {}}, {"pid": 3100, "tid": 34359738368, "ts": 1754736892903400, "dur": 44, "ph": "X", "name": "ReadAsync 182", "args": {}}, {"pid": 3100, "tid": 34359738368, "ts": 1754736892903446, "dur": 25, "ph": "X", "name": "ReadAsync 612", "args": {}}, {"pid": 3100, "tid": 34359738368, "ts": 1754736892903473, "dur": 29, "ph": "X", "name": "ReadAsync 501", "args": {}}, {"pid": 3100, "tid": 34359738368, "ts": 1754736892903504, "dur": 25, "ph": "X", "name": "ReadAsync 427", "args": {}}, {"pid": 3100, "tid": 34359738368, "ts": 1754736892903530, "dur": 27, "ph": "X", "name": "ReadAsync 396", "args": {}}, {"pid": 3100, "tid": 34359738368, "ts": 1754736892903559, "dur": 25, "ph": "X", "name": "ReadAsync 315", "args": {}}, {"pid": 3100, "tid": 34359738368, "ts": 1754736892903586, "dur": 20, "ph": "X", "name": "ReadAsync 474", "args": {}}, {"pid": 3100, "tid": 34359738368, "ts": 1754736892903609, "dur": 24, "ph": "X", "name": "ReadAsync 82", "args": {}}, {"pid": 3100, "tid": 34359738368, "ts": 1754736892903635, "dur": 74, "ph": "X", "name": "ReadAsync 240", "args": {}}, {"pid": 3100, "tid": 34359738368, "ts": 1754736892903711, "dur": 30, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3100, "tid": 34359738368, "ts": 1754736892903744, "dur": 25, "ph": "X", "name": "ReadAsync 521", "args": {}}, {"pid": 3100, "tid": 34359738368, "ts": 1754736892903771, "dur": 26, "ph": "X", "name": "ReadAsync 398", "args": {}}, {"pid": 3100, "tid": 34359738368, "ts": 1754736892903799, "dur": 24, "ph": "X", "name": "ReadAsync 500", "args": {}}, {"pid": 3100, "tid": 34359738368, "ts": 1754736892903824, "dur": 25, "ph": "X", "name": "ReadAsync 412", "args": {}}, {"pid": 3100, "tid": 34359738368, "ts": 1754736892903852, "dur": 25, "ph": "X", "name": "ReadAsync 356", "args": {}}, {"pid": 3100, "tid": 34359738368, "ts": 1754736892903878, "dur": 24, "ph": "X", "name": "ReadAsync 340", "args": {}}, {"pid": 3100, "tid": 34359738368, "ts": 1754736892903904, "dur": 24, "ph": "X", "name": "ReadAsync 316", "args": {}}, {"pid": 3100, "tid": 34359738368, "ts": 1754736892903931, "dur": 19, "ph": "X", "name": "ReadAsync 229", "args": {}}, {"pid": 3100, "tid": 34359738368, "ts": 1754736892903953, "dur": 39, "ph": "X", "name": "ReadAsync 8", "args": {}}, {"pid": 3100, "tid": 34359738368, "ts": 1754736892903994, "dur": 25, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3100, "tid": 34359738368, "ts": 1754736892904021, "dur": 25, "ph": "X", "name": "ReadAsync 376", "args": {}}, {"pid": 3100, "tid": 34359738368, "ts": 1754736892904048, "dur": 23, "ph": "X", "name": "ReadAsync 457", "args": {}}, {"pid": 3100, "tid": 34359738368, "ts": 1754736892904073, "dur": 24, "ph": "X", "name": "ReadAsync 237", "args": {}}, {"pid": 3100, "tid": 34359738368, "ts": 1754736892904099, "dur": 48, "ph": "X", "name": "ReadAsync 141", "args": {}}, {"pid": 3100, "tid": 34359738368, "ts": 1754736892904149, "dur": 25, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3100, "tid": 34359738368, "ts": 1754736892904175, "dur": 26, "ph": "X", "name": "ReadAsync 397", "args": {}}, {"pid": 3100, "tid": 34359738368, "ts": 1754736892904204, "dur": 24, "ph": "X", "name": "ReadAsync 470", "args": {}}, {"pid": 3100, "tid": 34359738368, "ts": 1754736892904230, "dur": 20, "ph": "X", "name": "ReadAsync 258", "args": {}}, {"pid": 3100, "tid": 34359738368, "ts": 1754736892904252, "dur": 44, "ph": "X", "name": "ReadAsync 52", "args": {}}, {"pid": 3100, "tid": 34359738368, "ts": 1754736892904298, "dur": 24, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3100, "tid": 34359738368, "ts": 1754736892904324, "dur": 24, "ph": "X", "name": "ReadAsync 376", "args": {}}, {"pid": 3100, "tid": 34359738368, "ts": 1754736892904350, "dur": 25, "ph": "X", "name": "ReadAsync 469", "args": {}}, {"pid": 3100, "tid": 34359738368, "ts": 1754736892904378, "dur": 20, "ph": "X", "name": "ReadAsync 373", "args": {}}, {"pid": 3100, "tid": 34359738368, "ts": 1754736892904399, "dur": 37, "ph": "X", "name": "ReadAsync 8", "args": {}}, {"pid": 3100, "tid": 34359738368, "ts": 1754736892904438, "dur": 24, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3100, "tid": 34359738368, "ts": 1754736892904464, "dur": 24, "ph": "X", "name": "ReadAsync 403", "args": {}}, {"pid": 3100, "tid": 34359738368, "ts": 1754736892904490, "dur": 26, "ph": "X", "name": "ReadAsync 303", "args": {}}, {"pid": 3100, "tid": 34359738368, "ts": 1754736892904519, "dur": 21, "ph": "X", "name": "ReadAsync 416", "args": {}}, {"pid": 3100, "tid": 34359738368, "ts": 1754736892904541, "dur": 72, "ph": "X", "name": "ReadAsync 61", "args": {}}, {"pid": 3100, "tid": 34359738368, "ts": 1754736892904615, "dur": 25, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3100, "tid": 34359738368, "ts": 1754736892904641, "dur": 23, "ph": "X", "name": "ReadAsync 301", "args": {}}, {"pid": 3100, "tid": 34359738368, "ts": 1754736892904666, "dur": 24, "ph": "X", "name": "ReadAsync 204", "args": {}}, {"pid": 3100, "tid": 34359738368, "ts": 1754736892904692, "dur": 24, "ph": "X", "name": "ReadAsync 245", "args": {}}, {"pid": 3100, "tid": 34359738368, "ts": 1754736892904718, "dur": 64, "ph": "X", "name": "ReadAsync 352", "args": {}}, {"pid": 3100, "tid": 34359738368, "ts": 1754736892904785, "dur": 25, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3100, "tid": 34359738368, "ts": 1754736892904811, "dur": 26, "ph": "X", "name": "ReadAsync 334", "args": {}}, {"pid": 3100, "tid": 34359738368, "ts": 1754736892904841, "dur": 24, "ph": "X", "name": "ReadAsync 385", "args": {}}, {"pid": 3100, "tid": 34359738368, "ts": 1754736892904867, "dur": 24, "ph": "X", "name": "ReadAsync 319", "args": {}}, {"pid": 3100, "tid": 34359738368, "ts": 1754736892904894, "dur": 24, "ph": "X", "name": "ReadAsync 418", "args": {}}, {"pid": 3100, "tid": 34359738368, "ts": 1754736892904920, "dur": 24, "ph": "X", "name": "ReadAsync 327", "args": {}}, {"pid": 3100, "tid": 34359738368, "ts": 1754736892904945, "dur": 24, "ph": "X", "name": "ReadAsync 439", "args": {}}, {"pid": 3100, "tid": 34359738368, "ts": 1754736892904971, "dur": 24, "ph": "X", "name": "ReadAsync 315", "args": {}}, {"pid": 3100, "tid": 34359738368, "ts": 1754736892904997, "dur": 23, "ph": "X", "name": "ReadAsync 161", "args": {}}, {"pid": 3100, "tid": 34359738368, "ts": 1754736892905021, "dur": 49, "ph": "X", "name": "ReadAsync 143", "args": {}}, {"pid": 3100, "tid": 34359738368, "ts": 1754736892905072, "dur": 24, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3100, "tid": 34359738368, "ts": 1754736892905098, "dur": 24, "ph": "X", "name": "ReadAsync 342", "args": {}}, {"pid": 3100, "tid": 34359738368, "ts": 1754736892905124, "dur": 24, "ph": "X", "name": "ReadAsync 441", "args": {}}, {"pid": 3100, "tid": 34359738368, "ts": 1754736892905150, "dur": 16, "ph": "X", "name": "ReadAsync 306", "args": {}}, {"pid": 3100, "tid": 34359738368, "ts": 1754736892905167, "dur": 64, "ph": "X", "name": "ReadAsync 68", "args": {}}, {"pid": 3100, "tid": 34359738368, "ts": 1754736892905233, "dur": 26, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3100, "tid": 34359738368, "ts": 1754736892905261, "dur": 25, "ph": "X", "name": "ReadAsync 373", "args": {}}, {"pid": 3100, "tid": 34359738368, "ts": 1754736892905287, "dur": 23, "ph": "X", "name": "ReadAsync 385", "args": {}}, {"pid": 3100, "tid": 34359738368, "ts": 1754736892905313, "dur": 24, "ph": "X", "name": "ReadAsync 293", "args": {}}, {"pid": 3100, "tid": 34359738368, "ts": 1754736892905339, "dur": 40, "ph": "X", "name": "ReadAsync 57", "args": {}}, {"pid": 3100, "tid": 34359738368, "ts": 1754736892905381, "dur": 25, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3100, "tid": 34359738368, "ts": 1754736892905407, "dur": 26, "ph": "X", "name": "ReadAsync 337", "args": {}}, {"pid": 3100, "tid": 34359738368, "ts": 1754736892905435, "dur": 24, "ph": "X", "name": "ReadAsync 416", "args": {}}, {"pid": 3100, "tid": 34359738368, "ts": 1754736892905460, "dur": 20, "ph": "X", "name": "ReadAsync 311", "args": {}}, {"pid": 3100, "tid": 34359738368, "ts": 1754736892905482, "dur": 88, "ph": "X", "name": "ReadAsync 57", "args": {}}, {"pid": 3100, "tid": 34359738368, "ts": 1754736892905576, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 3100, "tid": 34359738368, "ts": 1754736892905579, "dur": 79, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3100, "tid": 34359738368, "ts": 1754736892905662, "dur": 2, "ph": "X", "name": "ProcessMessages 1052", "args": {}}, {"pid": 3100, "tid": 34359738368, "ts": 1754736892905665, "dur": 38, "ph": "X", "name": "ReadAsync 1052", "args": {}}, {"pid": 3100, "tid": 34359738368, "ts": 1754736892905707, "dur": 1, "ph": "X", "name": "ProcessMessages 56", "args": {}}, {"pid": 3100, "tid": 34359738368, "ts": 1754736892905709, "dur": 117, "ph": "X", "name": "ReadAsync 56", "args": {}}, {"pid": 3100, "tid": 34359738368, "ts": 1754736892905830, "dur": 28, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3100, "tid": 34359738368, "ts": 1754736892905860, "dur": 25, "ph": "X", "name": "ReadAsync 219", "args": {}}, {"pid": 3100, "tid": 34359738368, "ts": 1754736892905887, "dur": 24, "ph": "X", "name": "ReadAsync 165", "args": {}}, {"pid": 3100, "tid": 34359738368, "ts": 1754736892905913, "dur": 23, "ph": "X", "name": "ReadAsync 167", "args": {}}, {"pid": 3100, "tid": 34359738368, "ts": 1754736892905938, "dur": 35, "ph": "X", "name": "ReadAsync 185", "args": {}}, {"pid": 3100, "tid": 34359738368, "ts": 1754736892905976, "dur": 1, "ph": "X", "name": "ProcessMessages 21", "args": {}}, {"pid": 3100, "tid": 34359738368, "ts": 1754736892905978, "dur": 53, "ph": "X", "name": "ReadAsync 21", "args": {}}, {"pid": 3100, "tid": 34359738368, "ts": 1754736892906033, "dur": 1, "ph": "X", "name": "ProcessMessages 263", "args": {}}, {"pid": 3100, "tid": 34359738368, "ts": 1754736892906035, "dur": 61, "ph": "X", "name": "ReadAsync 263", "args": {}}, {"pid": 3100, "tid": 34359738368, "ts": 1754736892906102, "dur": 2, "ph": "X", "name": "ProcessMessages 390", "args": {}}, {"pid": 3100, "tid": 34359738368, "ts": 1754736892906105, "dur": 71, "ph": "X", "name": "ReadAsync 390", "args": {}}, {"pid": 3100, "tid": 34359738368, "ts": 1754736892906181, "dur": 2, "ph": "X", "name": "ProcessMessages 814", "args": {}}, {"pid": 3100, "tid": 34359738368, "ts": 1754736892906185, "dur": 67, "ph": "X", "name": "ReadAsync 814", "args": {}}, {"pid": 3100, "tid": 34359738368, "ts": 1754736892906253, "dur": 1, "ph": "X", "name": "ProcessMessages 295", "args": {}}, {"pid": 3100, "tid": 34359738368, "ts": 1754736892906255, "dur": 54, "ph": "X", "name": "ReadAsync 295", "args": {}}, {"pid": 3100, "tid": 34359738368, "ts": 1754736892906313, "dur": 55, "ph": "X", "name": "ReadAsync 90", "args": {}}, {"pid": 3100, "tid": 34359738368, "ts": 1754736892906371, "dur": 1, "ph": "X", "name": "ProcessMessages 716", "args": {}}, {"pid": 3100, "tid": 34359738368, "ts": 1754736892906373, "dur": 55, "ph": "X", "name": "ReadAsync 716", "args": {}}, {"pid": 3100, "tid": 34359738368, "ts": 1754736892906429, "dur": 1, "ph": "X", "name": "ProcessMessages 815", "args": {}}, {"pid": 3100, "tid": 34359738368, "ts": 1754736892906432, "dur": 32, "ph": "X", "name": "ReadAsync 815", "args": {}}, {"pid": 3100, "tid": 34359738368, "ts": 1754736892906466, "dur": 17, "ph": "X", "name": "ReadAsync 637", "args": {}}, {"pid": 3100, "tid": 34359738368, "ts": 1754736892906485, "dur": 23, "ph": "X", "name": "ReadAsync 214", "args": {}}, {"pid": 3100, "tid": 34359738368, "ts": 1754736892906511, "dur": 19, "ph": "X", "name": "ReadAsync 75", "args": {}}, {"pid": 3100, "tid": 34359738368, "ts": 1754736892906531, "dur": 65, "ph": "X", "name": "ReadAsync 240", "args": {}}, {"pid": 3100, "tid": 34359738368, "ts": 1754736892906598, "dur": 25, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3100, "tid": 34359738368, "ts": 1754736892906626, "dur": 29, "ph": "X", "name": "ReadAsync 227", "args": {}}, {"pid": 3100, "tid": 34359738368, "ts": 1754736892906656, "dur": 26, "ph": "X", "name": "ReadAsync 403", "args": {}}, {"pid": 3100, "tid": 34359738368, "ts": 1754736892906685, "dur": 26, "ph": "X", "name": "ReadAsync 347", "args": {}}, {"pid": 3100, "tid": 34359738368, "ts": 1754736892906713, "dur": 26, "ph": "X", "name": "ReadAsync 309", "args": {}}, {"pid": 3100, "tid": 34359738368, "ts": 1754736892906741, "dur": 25, "ph": "X", "name": "ReadAsync 373", "args": {}}, {"pid": 3100, "tid": 34359738368, "ts": 1754736892906768, "dur": 25, "ph": "X", "name": "ReadAsync 294", "args": {}}, {"pid": 3100, "tid": 34359738368, "ts": 1754736892906795, "dur": 25, "ph": "X", "name": "ReadAsync 268", "args": {}}, {"pid": 3100, "tid": 34359738368, "ts": 1754736892906822, "dur": 16, "ph": "X", "name": "ReadAsync 220", "args": {}}, {"pid": 3100, "tid": 34359738368, "ts": 1754736892906840, "dur": 45, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 3100, "tid": 34359738368, "ts": 1754736892906887, "dur": 24, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3100, "tid": 34359738368, "ts": 1754736892906913, "dur": 28, "ph": "X", "name": "ReadAsync 302", "args": {}}, {"pid": 3100, "tid": 34359738368, "ts": 1754736892906943, "dur": 25, "ph": "X", "name": "ReadAsync 486", "args": {}}, {"pid": 3100, "tid": 34359738368, "ts": 1754736892906970, "dur": 21, "ph": "X", "name": "ReadAsync 182", "args": {}}, {"pid": 3100, "tid": 34359738368, "ts": 1754736892906993, "dur": 81, "ph": "X", "name": "ReadAsync 136", "args": {}}, {"pid": 3100, "tid": 34359738368, "ts": 1754736892907076, "dur": 25, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3100, "tid": 34359738368, "ts": 1754736892907104, "dur": 30, "ph": "X", "name": "ReadAsync 395", "args": {}}, {"pid": 3100, "tid": 34359738368, "ts": 1754736892907136, "dur": 25, "ph": "X", "name": "ReadAsync 579", "args": {}}, {"pid": 3100, "tid": 34359738368, "ts": 1754736892907163, "dur": 22, "ph": "X", "name": "ReadAsync 191", "args": {}}, {"pid": 3100, "tid": 34359738368, "ts": 1754736892907187, "dur": 63, "ph": "X", "name": "ReadAsync 66", "args": {}}, {"pid": 3100, "tid": 34359738368, "ts": 1754736892907252, "dur": 25, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3100, "tid": 34359738368, "ts": 1754736892907279, "dur": 27, "ph": "X", "name": "ReadAsync 400", "args": {}}, {"pid": 3100, "tid": 34359738368, "ts": 1754736892907308, "dur": 30, "ph": "X", "name": "ReadAsync 307", "args": {}}, {"pid": 3100, "tid": 34359738368, "ts": 1754736892907340, "dur": 60, "ph": "X", "name": "ReadAsync 479", "args": {}}, {"pid": 3100, "tid": 34359738368, "ts": 1754736892907402, "dur": 24, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3100, "tid": 34359738368, "ts": 1754736892907428, "dur": 26, "ph": "X", "name": "ReadAsync 304", "args": {}}, {"pid": 3100, "tid": 34359738368, "ts": 1754736892907456, "dur": 25, "ph": "X", "name": "ReadAsync 378", "args": {}}, {"pid": 3100, "tid": 34359738368, "ts": 1754736892907483, "dur": 28, "ph": "X", "name": "ReadAsync 467", "args": {}}, {"pid": 3100, "tid": 34359738368, "ts": 1754736892907513, "dur": 33, "ph": "X", "name": "ReadAsync 415", "args": {}}, {"pid": 3100, "tid": 34359738368, "ts": 1754736892907548, "dur": 25, "ph": "X", "name": "ReadAsync 322", "args": {}}, {"pid": 3100, "tid": 34359738368, "ts": 1754736892907574, "dur": 27, "ph": "X", "name": "ReadAsync 370", "args": {}}, {"pid": 3100, "tid": 34359738368, "ts": 1754736892907603, "dur": 26, "ph": "X", "name": "ReadAsync 388", "args": {}}, {"pid": 3100, "tid": 34359738368, "ts": 1754736892907631, "dur": 18, "ph": "X", "name": "ReadAsync 284", "args": {}}, {"pid": 3100, "tid": 34359738368, "ts": 1754736892907652, "dur": 69, "ph": "X", "name": "ReadAsync 237", "args": {}}, {"pid": 3100, "tid": 34359738368, "ts": 1754736892907722, "dur": 24, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3100, "tid": 34359738368, "ts": 1754736892907748, "dur": 28, "ph": "X", "name": "ReadAsync 306", "args": {}}, {"pid": 3100, "tid": 34359738368, "ts": 1754736892907778, "dur": 25, "ph": "X", "name": "ReadAsync 484", "args": {}}, {"pid": 3100, "tid": 34359738368, "ts": 1754736892907806, "dur": 27, "ph": "X", "name": "ReadAsync 354", "args": {}}, {"pid": 3100, "tid": 34359738368, "ts": 1754736892907835, "dur": 28, "ph": "X", "name": "ReadAsync 486", "args": {}}, {"pid": 3100, "tid": 34359738368, "ts": 1754736892907864, "dur": 26, "ph": "X", "name": "ReadAsync 233", "args": {}}, {"pid": 3100, "tid": 34359738368, "ts": 1754736892907892, "dur": 25, "ph": "X", "name": "ReadAsync 341", "args": {}}, {"pid": 3100, "tid": 34359738368, "ts": 1754736892907919, "dur": 25, "ph": "X", "name": "ReadAsync 339", "args": {}}, {"pid": 3100, "tid": 34359738368, "ts": 1754736892907946, "dur": 24, "ph": "X", "name": "ReadAsync 342", "args": {}}, {"pid": 3100, "tid": 34359738368, "ts": 1754736892907972, "dur": 20, "ph": "X", "name": "ReadAsync 220", "args": {}}, {"pid": 3100, "tid": 34359738368, "ts": 1754736892907994, "dur": 49, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 3100, "tid": 34359738368, "ts": 1754736892908046, "dur": 25, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3100, "tid": 34359738368, "ts": 1754736892908073, "dur": 26, "ph": "X", "name": "ReadAsync 380", "args": {}}, {"pid": 3100, "tid": 34359738368, "ts": 1754736892908101, "dur": 27, "ph": "X", "name": "ReadAsync 306", "args": {}}, {"pid": 3100, "tid": 34359738368, "ts": 1754736892908130, "dur": 28, "ph": "X", "name": "ReadAsync 557", "args": {}}, {"pid": 3100, "tid": 34359738368, "ts": 1754736892908161, "dur": 27, "ph": "X", "name": "ReadAsync 328", "args": {}}, {"pid": 3100, "tid": 34359738368, "ts": 1754736892908190, "dur": 25, "ph": "X", "name": "ReadAsync 379", "args": {}}, {"pid": 3100, "tid": 34359738368, "ts": 1754736892908217, "dur": 24, "ph": "X", "name": "ReadAsync 305", "args": {}}, {"pid": 3100, "tid": 34359738368, "ts": 1754736892908243, "dur": 17, "ph": "X", "name": "ReadAsync 359", "args": {}}, {"pid": 3100, "tid": 34359738368, "ts": 1754736892908262, "dur": 25, "ph": "X", "name": "ReadAsync 339", "args": {}}, {"pid": 3100, "tid": 34359738368, "ts": 1754736892908289, "dur": 21, "ph": "X", "name": "ReadAsync 180", "args": {}}, {"pid": 3100, "tid": 34359738368, "ts": 1754736892908311, "dur": 41, "ph": "X", "name": "ReadAsync 57", "args": {}}, {"pid": 3100, "tid": 34359738368, "ts": 1754736892908354, "dur": 25, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3100, "tid": 34359738368, "ts": 1754736892908381, "dur": 31, "ph": "X", "name": "ReadAsync 360", "args": {}}, {"pid": 3100, "tid": 34359738368, "ts": 1754736892908414, "dur": 35, "ph": "X", "name": "ReadAsync 347", "args": {}}, {"pid": 3100, "tid": 34359738368, "ts": 1754736892908453, "dur": 46, "ph": "X", "name": "ReadAsync 369", "args": {}}, {"pid": 3100, "tid": 34359738368, "ts": 1754736892908503, "dur": 1, "ph": "X", "name": "ProcessMessages 444", "args": {}}, {"pid": 3100, "tid": 34359738368, "ts": 1754736892908505, "dur": 54, "ph": "X", "name": "ReadAsync 444", "args": {}}, {"pid": 3100, "tid": 34359738368, "ts": 1754736892908562, "dur": 1, "ph": "X", "name": "ProcessMessages 650", "args": {}}, {"pid": 3100, "tid": 34359738368, "ts": 1754736892908564, "dur": 45, "ph": "X", "name": "ReadAsync 650", "args": {}}, {"pid": 3100, "tid": 34359738368, "ts": 1754736892908612, "dur": 1, "ph": "X", "name": "ProcessMessages 223", "args": {}}, {"pid": 3100, "tid": 34359738368, "ts": 1754736892908614, "dur": 31, "ph": "X", "name": "ReadAsync 223", "args": {}}, {"pid": 3100, "tid": 34359738368, "ts": 1754736892908647, "dur": 26, "ph": "X", "name": "ReadAsync 81", "args": {}}, {"pid": 3100, "tid": 34359738368, "ts": 1754736892908675, "dur": 21, "ph": "X", "name": "ReadAsync 204", "args": {}}, {"pid": 3100, "tid": 34359738368, "ts": 1754736892908698, "dur": 258, "ph": "X", "name": "ReadAsync 21", "args": {}}, {"pid": 3100, "tid": 34359738368, "ts": 1754736892908962, "dur": 38, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3100, "tid": 34359738368, "ts": 1754736892909003, "dur": 47, "ph": "X", "name": "ReadAsync 221", "args": {}}, {"pid": 3100, "tid": 34359738368, "ts": 1754736892909052, "dur": 27, "ph": "X", "name": "ReadAsync 28", "args": {}}, {"pid": 3100, "tid": 34359738368, "ts": 1754736892909080, "dur": 24, "ph": "X", "name": "ReadAsync 163", "args": {}}, {"pid": 3100, "tid": 34359738368, "ts": 1754736892909105, "dur": 149, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 3100, "tid": 34359738368, "ts": 1754736892909256, "dur": 25, "ph": "X", "name": "ReadAsync 47", "args": {}}, {"pid": 3100, "tid": 34359738368, "ts": 1754736892909283, "dur": 25, "ph": "X", "name": "ReadAsync 155", "args": {}}, {"pid": 3100, "tid": 34359738368, "ts": 1754736892909309, "dur": 26, "ph": "X", "name": "ReadAsync 245", "args": {}}, {"pid": 3100, "tid": 34359738368, "ts": 1754736892909338, "dur": 20, "ph": "X", "name": "ReadAsync 183", "args": {}}, {"pid": 3100, "tid": 34359738368, "ts": 1754736892909359, "dur": 88, "ph": "X", "name": "ReadAsync 62", "args": {}}, {"pid": 3100, "tid": 34359738368, "ts": 1754736892909449, "dur": 26, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3100, "tid": 34359738368, "ts": 1754736892909477, "dur": 32, "ph": "X", "name": "ReadAsync 303", "args": {}}, {"pid": 3100, "tid": 34359738368, "ts": 1754736892909511, "dur": 25, "ph": "X", "name": "ReadAsync 478", "args": {}}, {"pid": 3100, "tid": 34359738368, "ts": 1754736892909537, "dur": 27, "ph": "X", "name": "ReadAsync 132", "args": {}}, {"pid": 3100, "tid": 34359738368, "ts": 1754736892909566, "dur": 24, "ph": "X", "name": "ReadAsync 371", "args": {}}, {"pid": 3100, "tid": 34359738368, "ts": 1754736892909592, "dur": 21, "ph": "X", "name": "ReadAsync 251", "args": {}}, {"pid": 3100, "tid": 34359738368, "ts": 1754736892909615, "dur": 28, "ph": "X", "name": "ReadAsync 126", "args": {}}, {"pid": 3100, "tid": 34359738368, "ts": 1754736892909645, "dur": 26, "ph": "X", "name": "ReadAsync 367", "args": {}}, {"pid": 3100, "tid": 34359738368, "ts": 1754736892909672, "dur": 26, "ph": "X", "name": "ReadAsync 302", "args": {}}, {"pid": 3100, "tid": 34359738368, "ts": 1754736892909700, "dur": 24, "ph": "X", "name": "ReadAsync 334", "args": {}}, {"pid": 3100, "tid": 34359738368, "ts": 1754736892909726, "dur": 24, "ph": "X", "name": "ReadAsync 192", "args": {}}, {"pid": 3100, "tid": 34359738368, "ts": 1754736892909752, "dur": 73, "ph": "X", "name": "ReadAsync 237", "args": {}}, {"pid": 3100, "tid": 34359738368, "ts": 1754736892909827, "dur": 28, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3100, "tid": 34359738368, "ts": 1754736892909857, "dur": 24, "ph": "X", "name": "ReadAsync 359", "args": {}}, {"pid": 3100, "tid": 34359738368, "ts": 1754736892909883, "dur": 24, "ph": "X", "name": "ReadAsync 152", "args": {}}, {"pid": 3100, "tid": 34359738368, "ts": 1754736892909909, "dur": 27, "ph": "X", "name": "ReadAsync 483", "args": {}}, {"pid": 3100, "tid": 34359738368, "ts": 1754736892909938, "dur": 26, "ph": "X", "name": "ReadAsync 471", "args": {}}, {"pid": 3100, "tid": 34359738368, "ts": 1754736892909967, "dur": 24, "ph": "X", "name": "ReadAsync 371", "args": {}}, {"pid": 3100, "tid": 34359738368, "ts": 1754736892909993, "dur": 24, "ph": "X", "name": "ReadAsync 244", "args": {}}, {"pid": 3100, "tid": 34359738368, "ts": 1754736892910018, "dur": 17, "ph": "X", "name": "ReadAsync 341", "args": {}}, {"pid": 3100, "tid": 34359738368, "ts": 1754736892910037, "dur": 33, "ph": "X", "name": "ReadAsync 304", "args": {}}, {"pid": 3100, "tid": 34359738368, "ts": 1754736892910071, "dur": 17, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3100, "tid": 34359738368, "ts": 1754736892910090, "dur": 73, "ph": "X", "name": "ReadAsync 225", "args": {}}, {"pid": 3100, "tid": 34359738368, "ts": 1754736892910164, "dur": 32, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3100, "tid": 34359738368, "ts": 1754736892910198, "dur": 27, "ph": "X", "name": "ReadAsync 242", "args": {}}, {"pid": 3100, "tid": 34359738368, "ts": 1754736892910226, "dur": 1, "ph": "X", "name": "ProcessMessages 334", "args": {}}, {"pid": 3100, "tid": 34359738368, "ts": 1754736892910227, "dur": 26, "ph": "X", "name": "ReadAsync 334", "args": {}}, {"pid": 3100, "tid": 34359738368, "ts": 1754736892910255, "dur": 28, "ph": "X", "name": "ReadAsync 308", "args": {}}, {"pid": 3100, "tid": 34359738368, "ts": 1754736892910285, "dur": 25, "ph": "X", "name": "ReadAsync 406", "args": {}}, {"pid": 3100, "tid": 34359738368, "ts": 1754736892910312, "dur": 26, "ph": "X", "name": "ReadAsync 312", "args": {}}, {"pid": 3100, "tid": 34359738368, "ts": 1754736892910340, "dur": 26, "ph": "X", "name": "ReadAsync 352", "args": {}}, {"pid": 3100, "tid": 34359738368, "ts": 1754736892910368, "dur": 24, "ph": "X", "name": "ReadAsync 365", "args": {}}, {"pid": 3100, "tid": 34359738368, "ts": 1754736892910394, "dur": 17, "ph": "X", "name": "ReadAsync 67", "args": {}}, {"pid": 3100, "tid": 34359738368, "ts": 1754736892910413, "dur": 71, "ph": "X", "name": "ReadAsync 240", "args": {}}, {"pid": 3100, "tid": 34359738368, "ts": 1754736892910486, "dur": 27, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3100, "tid": 34359738368, "ts": 1754736892910515, "dur": 60, "ph": "X", "name": "ReadAsync 299", "args": {}}, {"pid": 3100, "tid": 34359738368, "ts": 1754736892910577, "dur": 1, "ph": "X", "name": "ProcessMessages 488", "args": {}}, {"pid": 3100, "tid": 34359738368, "ts": 1754736892910579, "dur": 32, "ph": "X", "name": "ReadAsync 488", "args": {}}, {"pid": 3100, "tid": 34359738368, "ts": 1754736892910613, "dur": 29, "ph": "X", "name": "ReadAsync 631", "args": {}}, {"pid": 3100, "tid": 34359738368, "ts": 1754736892910644, "dur": 17, "ph": "X", "name": "ReadAsync 368", "args": {}}, {"pid": 3100, "tid": 34359738368, "ts": 1754736892910663, "dur": 22, "ph": "X", "name": "ReadAsync 147", "args": {}}, {"pid": 3100, "tid": 34359738368, "ts": 1754736892910687, "dur": 24, "ph": "X", "name": "ReadAsync 225", "args": {}}, {"pid": 3100, "tid": 34359738368, "ts": 1754736892910713, "dur": 20, "ph": "X", "name": "ReadAsync 342", "args": {}}, {"pid": 3100, "tid": 34359738368, "ts": 1754736892910735, "dur": 22, "ph": "X", "name": "ReadAsync 21", "args": {}}, {"pid": 3100, "tid": 34359738368, "ts": 1754736892910759, "dur": 80, "ph": "X", "name": "ReadAsync 237", "args": {}}, {"pid": 3100, "tid": 34359738368, "ts": 1754736892910844, "dur": 32, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3100, "tid": 34359738368, "ts": 1754736892910879, "dur": 78, "ph": "X", "name": "ReadAsync 224", "args": {}}, {"pid": 3100, "tid": 34359738368, "ts": 1754736892910959, "dur": 21, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3100, "tid": 34359738368, "ts": 1754736892910982, "dur": 24, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 3100, "tid": 34359738368, "ts": 1754736892911008, "dur": 16, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 3100, "tid": 34359738368, "ts": 1754736892911026, "dur": 25, "ph": "X", "name": "ReadAsync 44", "args": {}}, {"pid": 3100, "tid": 34359738368, "ts": 1754736892911054, "dur": 29, "ph": "X", "name": "ReadAsync 52", "args": {}}, {"pid": 3100, "tid": 34359738368, "ts": 1754736892911086, "dur": 13, "ph": "X", "name": "ReadAsync 96", "args": {}}, {"pid": 3100, "tid": 34359738368, "ts": 1754736892911101, "dur": 8, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 3100, "tid": 34359738368, "ts": 1754736892911110, "dur": 7, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 3100, "tid": 34359738368, "ts": 1754736892911119, "dur": 7, "ph": "X", "name": "ReadAsync 44", "args": {}}, {"pid": 3100, "tid": 34359738368, "ts": 1754736892911127, "dur": 9, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 3100, "tid": 34359738368, "ts": 1754736892911138, "dur": 27, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 3100, "tid": 34359738368, "ts": 1754736892911167, "dur": 24, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 3100, "tid": 34359738368, "ts": 1754736892911193, "dur": 1, "ph": "X", "name": "ProcessMessages 128", "args": {}}, {"pid": 3100, "tid": 34359738368, "ts": 1754736892911194, "dur": 29, "ph": "X", "name": "ReadAsync 128", "args": {}}, {"pid": 3100, "tid": 34359738368, "ts": 1754736892911226, "dur": 27, "ph": "X", "name": "ReadAsync 124", "args": {}}, {"pid": 3100, "tid": 34359738368, "ts": 1754736892911256, "dur": 1, "ph": "X", "name": "ProcessMessages 128", "args": {}}, {"pid": 3100, "tid": 34359738368, "ts": 1754736892911258, "dur": 38, "ph": "X", "name": "ReadAsync 128", "args": {}}, {"pid": 3100, "tid": 34359738368, "ts": 1754736892911297, "dur": 1, "ph": "X", "name": "ProcessMessages 148", "args": {}}, {"pid": 3100, "tid": 34359738368, "ts": 1754736892911299, "dur": 21, "ph": "X", "name": "ReadAsync 148", "args": {}}, {"pid": 3100, "tid": 34359738368, "ts": 1754736892911325, "dur": 365, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 3100, "tid": 34359738368, "ts": 1754736892911695, "dur": 30, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3100, "tid": 34359738368, "ts": 1754736892911727, "dur": 5609, "ph": "X", "name": "ReadAsync 13", "args": {}}, {"pid": 3100, "tid": 24784, "ts": 1754736894304686, "dur": 655, "ph": "X", "name": "ReadEntireBinlogFromIpcAsync", "args": {}}, {"pid": 3100, "tid": 30064771072, "ph": "M", "name": "thread_name", "args": {"name": "WaitForBuildProgramInputDataBeingWrittenAndSendDagReadyMessageAsync"}}, {"pid": 3100, "tid": 30064771072, "ts": 1754736892738904, "dur": 47797, "ph": "X", "name": "await writeBuildProgramInputDataTask", "args": {}}, {"pid": 3100, "tid": 30064771072, "ts": 1754736892786703, "dur": 96743, "ph": "X", "name": "WritePipe.WaitConnectionAsync", "args": {}}, {"pid": 3100, "tid": 30064771072, "ts": 1754736892883447, "dur": 51, "ph": "X", "name": "WriteDagReadyMessage", "args": {}}, {"pid": 3100, "tid": 24784, "ts": 1754736894305342, "dur": 18, "ph": "X", "name": "WaitForBuildProgramInputDataBeingWrittenAndSendDagReadyMessageAsync", "args": {}}, {"pid": 3100, "tid": 25769803776, "ph": "M", "name": "thread_name", "args": {"name": "BuildAsync"}}, {"pid": 3100, "tid": 25769803776, "ts": 1754736892621382, "dur": 296002, "ph": "X", "name": "RunBackend", "args": {}}, {"pid": 3100, "tid": 25769803776, "ts": 1754736892621498, "dur": 117361, "ph": "X", "name": "BackendProgram.Start", "args": {}}, {"pid": 3100, "tid": 25769803776, "ts": 1754736892917389, "dur": 651979, "ph": "X", "name": "await ExecuteBuildProgram", "args": {}}, {"pid": 3100, "tid": 25769803776, "ts": 1754736893569441, "dur": 733890, "ph": "X", "name": "RunBackend", "args": {}}, {"pid": 3100, "tid": 25769803776, "ts": 1754736893569550, "dur": 122548, "ph": "X", "name": "BackendProgram.Start", "args": {}}, {"pid": 3100, "tid": 25769803776, "ts": 1754736894303334, "dur": 70, "ph": "X", "name": "await WaitForAndApplyScriptUpdaters", "args": {}}, {"pid": 3100, "tid": 25769803776, "ts": 1754736894303348, "dur": 14, "ph": "X", "name": "await <PERSON><PERSON>tUp<PERSON><PERSON>", "args": {}}, {"pid": 3100, "tid": 24784, "ts": 1754736894305360, "dur": 5, "ph": "X", "name": "BuildAsync", "args": {}}, {"pid": 35942, "tid": -1, "ph": "M", "name": "process_name", "args": {"name": "netcorerun.dll"}}, {"pid": 35942, "tid": -1, "ph": "M", "name": "process_sort_index", "args": {"sort_index": "-1"}}, {"pid": 35942, "tid": 1, "ph": "M", "name": "thread_name", "args": {"name": ""}}, {"pid": 35942, "tid": 1, "ts": 1754736893240133, "dur": 308819, "ph": "X", "name": "BuildProgram", "args": {}}, {"pid": 35942, "tid": 1, "ts": 1754736893240922, "dur": 51981, "ph": "X", "name": "BuildProgramContextConstructor", "args": {}}, {"pid": 35942, "tid": 1, "ts": 1754736893495033, "dur": 2771, "ph": "X", "name": "OutputData.Write", "args": {}}, {"pid": 35942, "tid": 1, "ts": 1754736893497806, "dur": 51130, "ph": "X", "name": "Backend.Write", "args": {}}, {"pid": 35942, "tid": 1, "ts": 1754736893499478, "dur": 30537, "ph": "X", "name": "JsonToString", "args": {}}, {"pid": 35942, "tid": 1, "ts": 1754736893554286, "dur": 1095, "ph": "X", "name": "", "args": {}}, {"pid": 35942, "tid": 1, "ts": 1754736893553842, "dur": 1764, "ph": "X", "name": "Write chrome-trace events", "args": {}}, {"cat": "", "pid": 12345, "tid": 0, "ts": 0, "ph": "M", "name": "process_name", "args": {"name": "bee_backend"}}, {"pid": 12345, "tid": 0, "ts": 1754736892883808, "dur": 52, "ph": "X", "name": "IPC_Client_InitializeAndConnectToParent", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1754736892883889, "dur": 1570, "ph": "X", "name": "DriverInitData", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1754736892885475, "dur": 790, "ph": "X", "name": "RemoveStaleOutputs", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1754736892886397, "dur": 51, "ph": "X", "name": "<PERSON><PERSON>", "args": {"detail": "PrepareNodes"}}, {"pid": 12345, "tid": 0, "ts": 1754736892886449, "dur": 377, "ph": "X", "name": "BuildQueueInit", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1754736892891047, "dur": 331, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.InputSystem.dll.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1754736892897392, "dur": 87, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.Rider.Editor.pdb"}}, {"pid": 12345, "tid": 0, "ts": 1754736892903128, "dur": 59, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.VisualScripting.Core.dll"}}, {"pid": 12345, "tid": 0, "ts": 1754736892903456, "dur": 71, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "CopyFiles Library/ScriptAssemblies/CrashKonijn.Goap.Resolver.dll"}}, {"pid": 12345, "tid": 0, "ts": 1754736892906072, "dur": 62, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/4691676790321305727.rsp"}}, {"pid": 12345, "tid": 0, "ts": 1754736892909448, "dur": 286, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.VisualScripting.Shared.Editor.dll"}}, {"pid": 12345, "tid": 0, "ts": 1754736892886842, "dur": 24477, "ph": "X", "name": "EnqueueRequestedNodes", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1754736892911332, "dur": 404, "ph": "X", "name": "WaitForBuildFinished", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1754736892911737, "dur": 194, "ph": "X", "name": "JoinBuildThread", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1754736892912085, "dur": 57, "ph": "X", "name": "BuildQueueDestroyTail", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1754736892912158, "dur": 1148, "ph": "X", "name": "<PERSON><PERSON>", "args": {"detail": "Write AllBuiltNodes"}}, {"pid": 12345, "tid": 1, "ts": 1754736892887119, "dur": 24225, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1754736892911352, "dur": 375, "ph": "X", "name": "CheckDagSignatures", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1754736892887242, "dur": 24166, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1754736892887160, "dur": 24212, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1754736892887150, "dur": 24205, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1754736892887188, "dur": 24195, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1754736892911393, "dur": 54, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.UIElementsSamplesModule.dll_623E7535F4BD3DF5.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1754736892911687, "dur": 61, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.PhysicsModule.dll_06AF3D16206C6E73.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1754736892887227, "dur": 24169, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1754736892887273, "dur": 24168, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1754736892911456, "dur": 62, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.VirtualTexturingModule.dll_38C1DD3B59418845.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1754736892887319, "dur": 24138, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1754736892911644, "dur": 78, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.IMGUIModule.dll_31158FA896333743.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1754736892887346, "dur": 24128, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1754736892887370, "dur": 24117, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1754736892887399, "dur": 24108, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1754736892887430, "dur": 24094, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1754736892911620, "dur": 86, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.DirectorModule.dll_3C3F89D87E20F844.mvfrm"}}, {"pid": 12345, "tid": 13, "ts": 1754736892887468, "dur": 24067, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1754736892911593, "dur": 166, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.AccessibilityModule.dll_4B3448FA7561224A.mvfrm"}}, {"pid": 12345, "tid": 14, "ts": 1754736892887509, "dur": 24045, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1754736892911572, "dur": 54, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ARModule.dll_969BEA2E4A09BAD9.mvfrm"}}, {"pid": 12345, "tid": 15, "ts": 1754736892887541, "dur": 24043, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1754736892887569, "dur": 24025, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1754736892911640, "dur": 95, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.dll_D66E5D73A81B547E.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1754736892917222, "dur": 267, "ph": "X", "name": "ProfilerWriteOutput"}, {"cat": "", "pid": 12345, "tid": 0, "ts": 0, "ph": "M", "name": "process_name", "args": {"name": "bee_backend"}}, {"pid": 12345, "tid": 0, "ts": 1754736893835626, "dur": 104135, "ph": "X", "name": "DriverInitData", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1754736893939766, "dur": 257, "ph": "X", "name": "RemoveStaleOutputs", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1754736893940119, "dur": 53, "ph": "X", "name": "<PERSON><PERSON>", "args": {"detail": "PrepareNodes"}}, {"pid": 12345, "tid": 0, "ts": 1754736893940172, "dur": 415, "ph": "X", "name": "BuildQueueInit", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1754736893944742, "dur": 54, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.TextMeshPro.rsp2"}}, {"pid": 12345, "tid": 0, "ts": 1754736893947876, "dur": 95, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.ShaderGraph.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 0, "ts": 1754736893951030, "dur": 67, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Burst.CodeGen.dll (+2 others)"}}, {"pid": 12345, "tid": 0, "ts": 1754736893940603, "dur": 19902, "ph": "X", "name": "EnqueueRequestedNodes", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1754736893960519, "dur": 335336, "ph": "X", "name": "WaitForBuildFinished", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1754736894295857, "dur": 107, "ph": "X", "name": "JoinBuildThread", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1754736894295964, "dur": 88, "ph": "X", "name": "ThreadStateDestroy", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1754736894296052, "dur": 202, "ph": "X", "name": "JoinBuildThread", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1754736894297129, "dur": 62, "ph": "X", "name": "BuildQueueDestroyTail", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1754736894297213, "dur": 1161, "ph": "X", "name": "<PERSON><PERSON>", "args": {"detail": "Write AllBuiltNodes"}}, {"pid": 12345, "tid": 1, "ts": 1754736893941104, "dur": 19571, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1754736893960868, "dur": 100, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.CrashReportingModule.dll_E0E4ACDF62B3A25F.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1754736893961059, "dur": 87, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UIModule.dll_D89CC358493361BC.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1754736893961149, "dur": 126, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.visualscripting@1.9.4\\Editor\\VisualScripting.Core\\Dependencies\\YamlDotNet\\Unity.VisualScripting.YamlDotNet.dll"}}, {"pid": 12345, "tid": 1, "ts": 1754736893961148, "dur": 128, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.VisualScripting.YamlDotNet.dll_4A5905A13DE9A491.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1754736893961425, "dur": 69, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Collections.rsp2"}}, {"pid": 12345, "tid": 1, "ts": 1754736893961633, "dur": 65, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.TextMeshPro.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 1, "ts": 1754736893961978, "dur": 135, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/PPv2URPConverters.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 1, "ts": 1754736893962227, "dur": 63, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Collections.DocCodeSamples.rsp"}}, {"pid": 12345, "tid": 1, "ts": 1754736893962470, "dur": 169, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1754736893962645, "dur": 185, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1754736893962831, "dur": 193, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1754736893963024, "dur": 212, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1754736893963236, "dur": 207, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1754736893963443, "dur": 247, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1754736893963690, "dur": 820, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1754736893964510, "dur": 260, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1754736893964771, "dur": 195, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1754736893964967, "dur": 238, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1754736893965205, "dur": 177, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1754736893965382, "dur": 247, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1754736893965629, "dur": 211, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1754736893965840, "dur": 247, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1754736893966087, "dur": 354, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1754736893966442, "dur": 186, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1754736893966628, "dur": 361, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1754736893966989, "dur": 172, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1754736893967161, "dur": 195, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1754736893967356, "dur": 172, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1754736893967650, "dur": 277, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1754736893967927, "dur": 350, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1754736893968277, "dur": 96, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.ShaderGraph.Utilities.dll.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1754736893968580, "dur": 109, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\GameSoft\\Unity\\Editor\\2022.3.38f1c1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Runtime.Serialization.Primitives.dll"}}, {"pid": 12345, "tid": 1, "ts": 1754736893968762, "dur": 85, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\GameSoft\\Unity\\Editor\\2022.3.38f1c1\\Editor\\Data\\Tools\\Unity.SourceGenerators\\Unity.Properties.SourceGenerator.dll"}}, {"pid": 12345, "tid": 1, "ts": 1754736893968391, "dur": 459, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.ShaderGraph.Utilities.dll (+2 others)"}}, {"pid": 12345, "tid": 1, "ts": 1754736893968928, "dur": 67, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/GlassSystem.dll.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1754736893969034, "dur": 314, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/GlassSystem.dll (+2 others)"}}, {"pid": 12345, "tid": 1, "ts": 1754736893969383, "dur": 83, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/com.crashkonijn.goap.demos.turn-based.dll.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1754736893969482, "dur": 51, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1754736893969534, "dur": 174, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1754736893969708, "dur": 236, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1754736893969944, "dur": 71, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Core.dll.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1754736893970037, "dur": 737, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Core.dll (+2 others)"}}, {"pid": 12345, "tid": 1, "ts": 1754736893970829, "dur": 64, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Flow.dll.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1754736893970938, "dur": 588, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Flow.dll (+2 others)"}}, {"pid": 12345, "tid": 1, "ts": 1754736893971624, "dur": 210, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.State.dll (+2 others)"}}, {"pid": 12345, "tid": 1, "ts": 1754736893971882, "dur": 276713, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1754736894250042, "dur": 407, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\GameSoft\\Unity\\Editor\\2022.3.38f1c1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.UnityWebRequestAudioModule.dll"}}, {"pid": 12345, "tid": 1, "ts": 1754736894248598, "dur": 2192, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/CrashKonijn.Agent.Core.dll (+pdb)"}}, {"pid": 12345, "tid": 1, "ts": 1754736894250791, "dur": 152, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1754736894250949, "dur": 1281, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/GlassSystem.dll (+pdb)"}}, {"pid": 12345, "tid": 1, "ts": 1754736894252256, "dur": 1293, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Searcher.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 1, "ts": 1754736894253549, "dur": 293, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1754736894254039, "dur": 116, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1754736894254296, "dur": 204, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1754736894254514, "dur": 211, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1754736894254742, "dur": 41120, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1754736893940959, "dur": 19570, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1754736893960536, "dur": 3278, "ph": "X", "name": "CheckDagSignatures", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1754736893963815, "dur": 214, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1754736893964029, "dur": 541, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1754736893964570, "dur": 248, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1754736893964819, "dur": 184, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1754736893965003, "dur": 237, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1754736893965241, "dur": 191, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1754736893965432, "dur": 222, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1754736893965654, "dur": 186, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1754736893965841, "dur": 239, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1754736893966080, "dur": 228, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1754736893966309, "dur": 229, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1754736893966538, "dur": 183, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1754736893966722, "dur": 204, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1754736893966927, "dur": 201, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1754736893967128, "dur": 182, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1754736893967310, "dur": 193, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1754736893967504, "dur": 97, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1754736893967651, "dur": 289, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1754736893967940, "dur": 443, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1754736893968384, "dur": 80, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.VSCode.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1754736893968509, "dur": 64, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/CrashKonijn.Goap.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1754736893968573, "dur": 212, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1754736893968790, "dur": 358, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/CrashKonijn.Agent.Runtime.dll (+2 others)"}}, {"pid": 12345, "tid": 2, "ts": 1754736893969149, "dur": 250, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1754736893969529, "dur": 147, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1754736893969676, "dur": 273, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1754736893969949, "dur": 57, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1754736893970006, "dur": 583, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1754736893970589, "dur": 207, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1754736893970796, "dur": 777, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1754736893971574, "dur": 65, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/URPWizard.dll.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1754736893971654, "dur": 241, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/URPWizard.dll (+2 others)"}}, {"pid": 12345, "tid": 2, "ts": 1754736893971921, "dur": 276662, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1754736894248585, "dur": 1435, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.RenderPipelines.ShaderGraph.ShaderGraphLibrary.dll (+pdb)"}}, {"pid": 12345, "tid": 2, "ts": 1754736894250022, "dur": 67, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1754736894250097, "dur": 1359, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.RenderPipelines.Core.Runtime.dll (+pdb)"}}, {"pid": 12345, "tid": 2, "ts": 1754736894251457, "dur": 106, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1754736894251568, "dur": 1277, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/CrashKonijn.Agent.Runtime.dll (+pdb)"}}, {"pid": 12345, "tid": 2, "ts": 1754736894254300, "dur": 75, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\GameSoft\\Unity\\Editor\\2022.3.38f1c1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Threading.Tasks.Extensions.dll"}}, {"pid": 12345, "tid": 2, "ts": 1754736894252882, "dur": 1551, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/CrashKonijn.Goap.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 2, "ts": 1754736894254520, "dur": 289, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1754736894254824, "dur": 41035, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1754736893941027, "dur": 19560, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1754736893960601, "dur": 68, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.UIElementsSamplesModule.dll_623E7535F4BD3DF5.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1754736893960756, "dur": 66, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.UIBuilderModule.dll_41DA9226DA4902FE.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1754736893961096, "dur": 114, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\GameSoft\\Unity\\Editor\\2022.3.38f1c1\\Editor\\Data\\Tools\\ScriptUpdater\\ApiUpdater.MovedFromExtractor.exe"}}, {"pid": 12345, "tid": 3, "ts": 1754736893961075, "dur": 136, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityWebRequestAssetBundleModule.dll_20062661B6C0E97A.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1754736893961516, "dur": 55, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Universal.Config.Runtime.rsp"}}, {"pid": 12345, "tid": 3, "ts": 1754736893961580, "dur": 52, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/CrashKonijn.Goap.Resolver.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 3, "ts": 1754736893961670, "dur": 85, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Timeline.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 3, "ts": 1754736893961796, "dur": 81, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Cinemachine.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 3, "ts": 1754736893962914, "dur": 631, "ph": "X", "name": "WriteText", "args": {"detail": "Library/Bee/artifacts/1900b0aE.dag/Assembly-CSharp.rsp"}}, {"pid": 12345, "tid": 3, "ts": 1754736893963546, "dur": 423, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1754736893963969, "dur": 584, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1754736893964553, "dur": 188, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1754736893964742, "dur": 166, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1754736893964908, "dur": 256, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1754736893965164, "dur": 223, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1754736893965388, "dur": 253, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1754736893965642, "dur": 204, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1754736893965846, "dur": 222, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1754736893966068, "dur": 184, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1754736893966252, "dur": 192, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1754736893966444, "dur": 179, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1754736893966624, "dur": 178, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1754736893966802, "dur": 480, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1754736893967283, "dur": 167, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1754736893967475, "dur": 218, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1754736893967693, "dur": 244, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1754736893967937, "dur": 334, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1754736893968272, "dur": 64, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.Mathematics.dll.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1754736893968671, "dur": 68, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.mathematics@1.2.6\\Unity.Mathematics\\bool4x4.gen.cs"}}, {"pid": 12345, "tid": 3, "ts": 1754736893968893, "dur": 424, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.mathematics@1.2.6\\Unity.Mathematics\\uint3x3.gen.cs"}}, {"pid": 12345, "tid": 3, "ts": 1754736893968387, "dur": 940, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Mathematics.dll (+2 others)"}}, {"pid": 12345, "tid": 3, "ts": 1754736893969327, "dur": 167, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1754736893969523, "dur": 82, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.Collections.dll.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1754736893969639, "dur": 292, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Collections.dll (+2 others)"}}, {"pid": 12345, "tid": 3, "ts": 1754736893969999, "dur": 95, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/CrashKonijn.Goap.Resolver.dll.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1754736893970121, "dur": 283, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/CrashKonijn.Goap.Resolver.dll (+2 others)"}}, {"pid": 12345, "tid": 3, "ts": 1754736893970440, "dur": 284, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/CrashKonijn.Goap.Runtime.dll (+2 others)"}}, {"pid": 12345, "tid": 3, "ts": 1754736893970793, "dur": 316, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/CrashKonijn.Goap.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 3, "ts": 1754736893971148, "dur": 175, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/com.crashkonijn.goap.demos.simple.dll (+2 others)"}}, {"pid": 12345, "tid": 3, "ts": 1754736893971361, "dur": 277272, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1754736894248648, "dur": 1745, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.RenderPipelines.Universal.Runtime.dll (+pdb)"}}, {"pid": 12345, "tid": 3, "ts": 1754736894250394, "dur": 79, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1754736894250562, "dur": 90, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\GameSoft\\Unity\\Editor\\2022.3.38f1c1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.TextRenderingModule.dll"}}, {"pid": 12345, "tid": 3, "ts": 1754736894250772, "dur": 444, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\GameSoft\\Unity\\Editor\\2022.3.38f1c1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Reflection.Emit.dll"}}, {"pid": 12345, "tid": 3, "ts": 1754736894250482, "dur": 1851, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.InputSystem.dll (+pdb)"}}, {"pid": 12345, "tid": 3, "ts": 1754736894252338, "dur": 676, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1754736894253019, "dur": 1680, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/URPWizard.dll (+pdb)"}}, {"pid": 12345, "tid": 3, "ts": 1754736894254749, "dur": 41108, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1754736893940998, "dur": 19565, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1754736893960892, "dur": 224, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.GIModule.dll_75656E38889B442A.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1754736893961125, "dur": 110, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1754736893961451, "dur": 65, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/CrashKonijn.Goap.Core.rsp2"}}, {"pid": 12345, "tid": 4, "ts": 1754736893961715, "dur": 150, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.State.Editor.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 4, "ts": 1754736893962074, "dur": 83, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/com.unity.cinemachine.editor.rsp"}}, {"pid": 12345, "tid": 4, "ts": 1754736893962168, "dur": 99, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.CollabProxy.Editor.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 4, "ts": 1754736893962409, "dur": 75, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/9149840420361016890.rsp"}}, {"pid": 12345, "tid": 4, "ts": 1754736893962485, "dur": 358, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1754736893962843, "dur": 203, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1754736893963047, "dur": 180, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1754736893963227, "dur": 296, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1754736893963524, "dur": 307, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1754736893963831, "dur": 222, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1754736893964130, "dur": 565, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.render-pipelines.universal@14.0.11\\Editor\\2D\\ShapeEditor\\Shapes\\ShapeExtensions.cs"}}, {"pid": 12345, "tid": 4, "ts": 1754736893964053, "dur": 764, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1754736893964817, "dur": 170, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1754736893964988, "dur": 241, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1754736893965230, "dur": 189, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1754736893965419, "dur": 255, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1754736893965674, "dur": 187, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1754736893965861, "dur": 346, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1754736893966207, "dur": 164, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1754736893966371, "dur": 365, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1754736893966736, "dur": 253, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1754736893966989, "dur": 282, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1754736893967271, "dur": 185, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1754736893967502, "dur": 564, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1754736893968066, "dur": 212, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1754736893968281, "dur": 65, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/CrashKonijn.Agent.Runtime.dll.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1754736893968364, "dur": 66, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.PlasticSCM.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1754736893968582, "dur": 97, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\GameSoft\\Unity\\Editor\\2022.3.38f1c1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.ProfilerModule.dll"}}, {"pid": 12345, "tid": 4, "ts": 1754736893968893, "dur": 192, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\GameSoft\\Unity\\Editor\\2022.3.38f1c1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Security.Cryptography.X509Certificates.dll"}}, {"pid": 12345, "tid": 4, "ts": 1754736893968449, "dur": 1211, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.PlasticSCM.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 4, "ts": 1754736893969726, "dur": 70, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.CollabProxy.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1754736893969809, "dur": 228, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.CollabProxy.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 4, "ts": 1754736893970037, "dur": 110, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1754736893970152, "dur": 442, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1754736893970594, "dur": 213, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1754736893970807, "dur": 277761, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1754736894250042, "dur": 545, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\GameSoft\\Unity\\Editor\\2022.3.38f1c1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Threading.Timer.dll"}}, {"pid": 12345, "tid": 4, "ts": 1754736894248587, "dur": 2042, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.RenderPipelines.Universal.Config.Runtime.dll (+pdb)"}}, {"pid": 12345, "tid": 4, "ts": 1754736894251813, "dur": 52, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\GameSoft\\Unity\\Editor\\2022.3.38f1c1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.IO.FileSystem.Watcher.dll"}}, {"pid": 12345, "tid": 4, "ts": 1754736894250679, "dur": 1326, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Cinemachine.dll (+pdb)"}}, {"pid": 12345, "tid": 4, "ts": 1754736894252006, "dur": 88, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1754736894252101, "dur": 1299, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.VisualScripting.Core.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 4, "ts": 1754736894253433, "dur": 1453, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.VisualScripting.Shared.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 4, "ts": 1754736894254947, "dur": 40906, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1754736893940982, "dur": 19558, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1754736893961060, "dur": 54, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.TLSModule.dll_FEEE89EA26456A87.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1754736893961121, "dur": 114, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1754736893961262, "dur": 81, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/UnityEngine.UI.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 5, "ts": 1754736893961396, "dur": 56, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Mathematics.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 5, "ts": 1754736893961591, "dur": 134, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.ShaderGraph.Utilities.rsp"}}, {"pid": 12345, "tid": 5, "ts": 1754736893961827, "dur": 78, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Shared.Editor.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 5, "ts": 1754736893961959, "dur": 348, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VSCode.Editor.rsp"}}, {"pid": 12345, "tid": 5, "ts": 1754736893962337, "dur": 81, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/10678863128556690338.rsp"}}, {"pid": 12345, "tid": 5, "ts": 1754736893962465, "dur": 163, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1754736893962635, "dur": 191, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1754736893962827, "dur": 198, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1754736893963026, "dur": 197, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1754736893963223, "dur": 194, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1754736893963417, "dur": 225, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1754736893963643, "dur": 158, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1754736893963802, "dur": 207, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1754736893964009, "dur": 555, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1754736893964564, "dur": 275, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1754736893964840, "dur": 167, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1754736893965007, "dur": 218, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1754736893965226, "dur": 180, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1754736893965406, "dur": 211, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1754736893965618, "dur": 187, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1754736893965805, "dur": 221, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1754736893966026, "dur": 193, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1754736893966219, "dur": 508, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1754736893966727, "dur": 244, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1754736893966972, "dur": 165, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1754736893967137, "dur": 184, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1754736893967321, "dur": 261, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1754736893967651, "dur": 265, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1754736893967916, "dur": 358, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1754736893968281, "dur": 67, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/CrashKonijn.Goap.Core.dll.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1754736893968348, "dur": 244, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1754736893968670, "dur": 102, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\GameSoft\\Unity\\Editor\\2022.3.38f1c1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.CrashReportingModule.dll"}}, {"pid": 12345, "tid": 5, "ts": 1754736893968798, "dur": 102, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\GameSoft\\Unity\\Editor\\2022.3.38f1c1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.SubstanceModule.dll"}}, {"pid": 12345, "tid": 5, "ts": 1754736893968596, "dur": 536, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.VSCode.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 5, "ts": 1754736893969172, "dur": 423, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/com.crashkonijn.goap.demos.simple.dll.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1754736893969694, "dur": 262, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1754736893969997, "dur": 92, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.Collections.BurstCompatibilityGen.dll.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1754736893970105, "dur": 216, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Collections.BurstCompatibilityGen.dll (+2 others)"}}, {"pid": 12345, "tid": 5, "ts": 1754736893970322, "dur": 66, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1754736893970394, "dur": 203, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1754736893970597, "dur": 202, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1754736893970800, "dur": 1600, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1754736893972459, "dur": 161, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/PPv2URPConverters.dll (+2 others)"}}, {"pid": 12345, "tid": 5, "ts": 1754736893972650, "dur": 275929, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1754736894250520, "dur": 166, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\GameSoft\\Unity\\Editor\\2022.3.38f1c1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Buffers.dll"}}, {"pid": 12345, "tid": 5, "ts": 1754736894248584, "dur": 2262, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/com.crashkonijn.goap.demos.simple.dll (+pdb)"}}, {"pid": 12345, "tid": 5, "ts": 1754736894250846, "dur": 333, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1754736894251185, "dur": 1183, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/UnityEngine.UI.dll (+pdb)"}}, {"pid": 12345, "tid": 5, "ts": 1754736894252368, "dur": 125, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1754736894252497, "dur": 1314, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/PPv2URPConverters.dll (+pdb)"}}, {"pid": 12345, "tid": 5, "ts": 1754736894254275, "dur": 52, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1754736894254331, "dur": 351, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1754736894254696, "dur": 41201, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1754736893941066, "dur": 19570, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1754736893960655, "dur": 68, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.VFXModule.dll_7FCD2258FCB2D3F4.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1754736893960869, "dur": 51, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1754736893961027, "dur": 116, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.TextCoreFontEngineModule.dll_A7936DE1882F5A05.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1754736893961146, "dur": 116, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.visualscripting@1.9.4\\Runtime\\VisualScripting.Flow\\Dependencies\\NCalc\\Unity.VisualScripting.Antlr3.Runtime.dll"}}, {"pid": 12345, "tid": 6, "ts": 1754736893961145, "dur": 119, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.VisualScripting.Antlr3.Runtime.dll_FBC62C826EB2B4B0.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1754736893961323, "dur": 66, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.Burst.Unsafe.dll_12BA7C2C14719C45.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1754736893961390, "dur": 50, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Core.rsp"}}, {"pid": 12345, "tid": 6, "ts": 1754736893961472, "dur": 53, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Core.Editor.rsp"}}, {"pid": 12345, "tid": 6, "ts": 1754736893961551, "dur": 63, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.State.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 6, "ts": 1754736893961898, "dur": 304, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1754736893962407, "dur": 75, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/16617090947308103586.rsp"}}, {"pid": 12345, "tid": 6, "ts": 1754736893962482, "dur": 358, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1754736893962840, "dur": 399, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1754736893963239, "dur": 324, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1754736893963563, "dur": 174, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1754736893963737, "dur": 241, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1754736893963978, "dur": 576, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1754736893964555, "dur": 255, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1754736893964810, "dur": 167, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1754736893964978, "dur": 224, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1754736893965203, "dur": 190, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1754736893965393, "dur": 191, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1754736893965584, "dur": 195, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1754736893965779, "dur": 302, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1754736893966081, "dur": 378, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1754736893966459, "dur": 206, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1754736893966666, "dur": 176, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1754736893966842, "dur": 192, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1754736893967035, "dur": 152, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1754736893967187, "dur": 602, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1754736893967789, "dur": 131, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1754736893967921, "dur": 633, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1754736893968555, "dur": 112, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Universal.Shaders.dll.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1754736893968668, "dur": 72, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1754736893968762, "dur": 68, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\GameSoft\\Unity\\Editor\\2022.3.38f1c1\\Editor\\Data\\Managed\\UnityEngine\\UnityEditor.UIElementsModule.dll"}}, {"pid": 12345, "tid": 6, "ts": 1754736893969167, "dur": 127, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\GameSoft\\Unity\\Editor\\2022.3.38f1c1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Text.Encoding.dll"}}, {"pid": 12345, "tid": 6, "ts": 1754736893968744, "dur": 618, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Universal.Shaders.dll (+2 others)"}}, {"pid": 12345, "tid": 6, "ts": 1754736893969363, "dur": 58, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1754736893969461, "dur": 81, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1754736893969542, "dur": 110, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1754736893969674, "dur": 289, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1754736893969998, "dur": 94, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.Collections.DocCodeSamples.dll.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1754736893970118, "dur": 260, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Collections.DocCodeSamples.dll (+2 others)"}}, {"pid": 12345, "tid": 6, "ts": 1754736893970402, "dur": 189, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1754736893970591, "dur": 221, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1754736893970812, "dur": 277793, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1754736894250299, "dur": 119, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\GameSoft\\Unity\\Editor\\2022.3.38f1c1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Collections.Specialized.dll"}}, {"pid": 12345, "tid": 6, "ts": 1754736894248610, "dur": 2077, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.InputSystem.ForUI.dll (+pdb)"}}, {"pid": 12345, "tid": 6, "ts": 1754736894250688, "dur": 144, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1754736894250839, "dur": 1321, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Mathematics.dll (+pdb)"}}, {"pid": 12345, "tid": 6, "ts": 1754736894252161, "dur": 270, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1754736894252436, "dur": 1304, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.VisualScripting.State.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 6, "ts": 1754736894253741, "dur": 94, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1754736894254038, "dur": 196, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1754736894254272, "dur": 286, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1754736894254566, "dur": 463, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1754736894255048, "dur": 40826, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1754736893941051, "dur": 19557, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1754736893960627, "dur": 65, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.VehiclesModule.dll_B4028357F84AC7DF.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1754736893960841, "dur": 53, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1754736893960930, "dur": 203, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.InputModule.dll_5F2BB7F24639C806.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1754736893961135, "dur": 107, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.collab-proxy@2.8.2\\Lib\\Editor\\Unity.Plastic.Newtonsoft.Json.dll"}}, {"pid": 12345, "tid": 7, "ts": 1754736893961134, "dur": 109, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.Plastic.Newtonsoft.Json.dll_CF17D4CA2A31CD8C.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1754736893961347, "dur": 74, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Burst.rsp"}}, {"pid": 12345, "tid": 7, "ts": 1754736893961533, "dur": 134, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Universal.Runtime.rsp2"}}, {"pid": 12345, "tid": 7, "ts": 1754736893961797, "dur": 75, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Rider.Editor.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 7, "ts": 1754736893961968, "dur": 150, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/com.crashkonijn.goap.demos.complex.rsp2"}}, {"pid": 12345, "tid": 7, "ts": 1754736893962277, "dur": 70, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1754736893962476, "dur": 384, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1754736893962860, "dur": 203, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1754736893963063, "dur": 233, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1754736893963296, "dur": 236, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1754736893963532, "dur": 116, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1754736893963648, "dur": 157, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1754736893963805, "dur": 245, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1754736893964050, "dur": 541, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1754736893964591, "dur": 214, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1754736893964806, "dur": 199, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1754736893965005, "dur": 247, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1754736893965252, "dur": 207, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1754736893965459, "dur": 200, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1754736893965660, "dur": 174, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1754736893965834, "dur": 228, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1754736893966062, "dur": 221, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1754736893966283, "dur": 194, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1754736893966478, "dur": 178, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1754736893966656, "dur": 172, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1754736893966828, "dur": 208, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1754736893967036, "dur": 175, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1754736893967211, "dur": 336, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1754736893967547, "dur": 109, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1754736893967656, "dur": 275, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1754736893967931, "dur": 363, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1754736893968295, "dur": 70, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.Timeline.dll.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1754736893968396, "dur": 433, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Timeline.dll (+2 others)"}}, {"pid": 12345, "tid": 7, "ts": 1754736893968830, "dur": 58, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1754736893968921, "dur": 55, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1754736893968980, "dur": 75, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.Timeline.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1754736893969074, "dur": 683, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Timeline.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 7, "ts": 1754736893969801, "dur": 144, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1754736893969946, "dur": 70, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.InputSystem.ForUI.dll.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1754736893970057, "dur": 236, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.InputSystem.ForUI.dll (+2 others)"}}, {"pid": 12345, "tid": 7, "ts": 1754736893970352, "dur": 241, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1754736893970593, "dur": 204, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1754736893970797, "dur": 1417, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1754736893972270, "dur": 160, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.SettingsProvider.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 7, "ts": 1754736893972471, "dur": 276122, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1754736894248603, "dur": 1765, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/CrashKonijn.Goap.Runtime.dll (+pdb)"}}, {"pid": 12345, "tid": 7, "ts": 1754736894250405, "dur": 1280, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.InputSystem.TestFramework.dll (+pdb)"}}, {"pid": 12345, "tid": 7, "ts": 1754736894251711, "dur": 1278, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.VisualScripting.SettingsProvider.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 7, "ts": 1754736894253029, "dur": 1428, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Collections.DocCodeSamples.dll (+pdb)"}}, {"pid": 12345, "tid": 7, "ts": 1754736894254493, "dur": 64, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1754736894254563, "dur": 371, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1754736894254934, "dur": 40917, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1754736893941087, "dur": 19572, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1754736893960672, "dur": 65, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.VRModule.dll_2B6EE9DDEAEDE734.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1754736893960837, "dur": 68, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1754736893961005, "dur": 104, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.SubstanceModule.dll_4D1762CCDCC813BC.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1754736893961116, "dur": 114, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1754736893961250, "dur": 74, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/UnityEditor.TestRunner.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 8, "ts": 1754736893961461, "dur": 146, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/CrashKonijn.Goap.Core.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 8, "ts": 1754736893961778, "dur": 74, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VSCode.Editor.rsp2"}}, {"pid": 12345, "tid": 8, "ts": 1754736893962085, "dur": 61, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.InputSystem.TestFramework.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 8, "ts": 1754736893962169, "dur": 167, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Collections.BurstCompatibilityGen.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 8, "ts": 1754736893962475, "dur": 165, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1754736893962669, "dur": 200, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1754736893962869, "dur": 190, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1754736893963059, "dur": 206, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1754736893963265, "dur": 183, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1754736893963448, "dur": 264, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1754736893963712, "dur": 211, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1754736893963923, "dur": 163, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1754736893964086, "dur": 530, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1754736893964617, "dur": 296, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1754736893964913, "dur": 176, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1754736893965089, "dur": 557, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1754736893965646, "dur": 198, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1754736893965845, "dur": 451, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1754736893966297, "dur": 215, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1754736893966512, "dur": 223, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1754736893966735, "dur": 207, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1754736893966943, "dur": 173, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1754736893967116, "dur": 172, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1754736893967288, "dur": 202, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1754736893967491, "dur": 192, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1754736893967683, "dur": 247, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1754736893967930, "dur": 346, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1754736893968277, "dur": 71, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.Searcher.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1754736893968580, "dur": 99, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\GameSoft\\Unity\\Editor\\2022.3.38f1c1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.ServiceModel.Security.dll"}}, {"pid": 12345, "tid": 8, "ts": 1754736893968376, "dur": 368, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Searcher.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 8, "ts": 1754736893968745, "dur": 82, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1754736893968837, "dur": 63, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1754736893968905, "dur": 67, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/com.crashkonijn.docs.getting_started.dll.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1754736893968989, "dur": 67, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Tests.dll.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1754736893969068, "dur": 295, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Tests.dll (+2 others)"}}, {"pid": 12345, "tid": 8, "ts": 1754736893969455, "dur": 83, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1754736893969538, "dur": 132, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1754736893969710, "dur": 237, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1754736893969948, "dur": 68, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.InputSystem.TestFramework.dll.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1754736893970032, "dur": 268, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.InputSystem.TestFramework.dll (+2 others)"}}, {"pid": 12345, "tid": 8, "ts": 1754736893970336, "dur": 258, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1754736893970594, "dur": 208, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1754736893970802, "dur": 1969, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1754736893972772, "dur": 51, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Assembly-CSharp-Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1754736893972833, "dur": 275737, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1754736894250042, "dur": 76, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\GameSoft\\Unity\\Editor\\2022.3.38f1c1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.ComponentModel.dll"}}, {"pid": 12345, "tid": 8, "ts": 1754736894248573, "dur": 1889, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.RenderPipeline.Universal.ShaderLibrary.dll (+pdb)"}}, {"pid": 12345, "tid": 8, "ts": 1754736894250531, "dur": 77, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\GameSoft\\Unity\\Editor\\2022.3.38f1c1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.AIModule.dll"}}, {"pid": 12345, "tid": 8, "ts": 1754736894250508, "dur": 1189, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.VisualScripting.Core.dll (+pdb)"}}, {"pid": 12345, "tid": 8, "ts": 1754736894251698, "dur": 127, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1754736894251833, "dur": 1183, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.RenderPipelines.Universal.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 8, "ts": 1754736894253017, "dur": 188, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1754736894253209, "dur": 1428, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/com.unity.cinemachine.editor.dll (+pdb)"}}, {"pid": 12345, "tid": 8, "ts": 1754736894254690, "dur": 41200, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1754736893941127, "dur": 19558, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1754736893960765, "dur": 54, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.SceneViewModule.dll_2C01C8125CB356D8.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1754736893960997, "dur": 98, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.PropertiesModule.dll_370C590F53957EFB.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1754736893961099, "dur": 128, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\GameSoft\\Unity\\Editor\\2022.3.38f1c1\\Editor\\Data\\PlaybackEngines\\WindowsStandaloneSupport\\UnityEditor.WindowsStandalone.Extensions.dll"}}, {"pid": 12345, "tid": 9, "ts": 1754736893961097, "dur": 132, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.WindowsStandalone.Extensions.dll_35385077F0CEC424.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1754736893961293, "dur": 120, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.NVIDIAModule.dll_3B326C6243819FB3.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1754736893961531, "dur": 207, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/CrashKonijn.Goap.Core.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 9, "ts": 1754736893961968, "dur": 58, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/URPWizard.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 9, "ts": 1754736893962141, "dur": 58, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Collections.BurstCompatibilityGen.rsp2"}}, {"pid": 12345, "tid": 9, "ts": 1754736893962224, "dur": 98, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.CollabProxy.Editor.rsp"}}, {"pid": 12345, "tid": 9, "ts": 1754736893962478, "dur": 180, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1754736893962664, "dur": 233, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1754736893962898, "dur": 199, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1754736893963097, "dur": 179, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1754736893963276, "dur": 244, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1754736893963521, "dur": 406, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1754736893963928, "dur": 186, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1754736893964115, "dur": 598, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1754736893964713, "dur": 175, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1754736893964888, "dur": 289, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1754736893965177, "dur": 243, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1754736893965420, "dur": 226, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1754736893965646, "dur": 198, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1754736893965844, "dur": 228, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1754736893966072, "dur": 190, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1754736893966262, "dur": 208, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1754736893966470, "dur": 176, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1754736893966646, "dur": 179, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1754736893966825, "dur": 226, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1754736893967052, "dur": 287, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1754736893967339, "dur": 217, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1754736893967600, "dur": 107, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1754736893967708, "dur": 238, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1754736893967946, "dur": 572, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1754736893968519, "dur": 60, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Core.ShaderLibrary.dll.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1754736893968762, "dur": 72, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\GameSoft\\Unity\\Editor\\2022.3.38f1c1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.ComponentModel.TypeConverter.dll"}}, {"pid": 12345, "tid": 9, "ts": 1754736893968623, "dur": 385, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Core.ShaderLibrary.dll (+2 others)"}}, {"pid": 12345, "tid": 9, "ts": 1754736893969008, "dur": 61, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1754736893969101, "dur": 65, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1754736893969167, "dur": 111, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.TextMeshPro.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1754736893969306, "dur": 348, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.TextMeshPro.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 9, "ts": 1754736893969766, "dur": 190, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1754736893969999, "dur": 589, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1754736893970588, "dur": 195, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1754736893970783, "dur": 303, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/com.crashkonijn.goap.demos.turn-based.dll (+2 others)"}}, {"pid": 12345, "tid": 9, "ts": 1754736893971120, "dur": 277445, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1754736894248567, "dur": 1461, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Tests.dll (+pdb)"}}, {"pid": 12345, "tid": 9, "ts": 1754736894250423, "dur": 131, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\GameSoft\\Unity\\Editor\\2022.3.38f1c1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Data.Common.dll"}}, {"pid": 12345, "tid": 9, "ts": 1754736894250562, "dur": 343, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\GameSoft\\Unity\\Editor\\2022.3.38f1c1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Diagnostics.Tracing.dll"}}, {"pid": 12345, "tid": 9, "ts": 1754736894250082, "dur": 2039, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.VisualScripting.Flow.dll (+pdb)"}}, {"pid": 12345, "tid": 9, "ts": 1754736894252163, "dur": 1329, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.TextMeshPro.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 9, "ts": 1754736894253530, "dur": 1336, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Mathematics.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 9, "ts": 1754736894254940, "dur": 40981, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1754736893941169, "dur": 19541, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1754736893960761, "dur": 60, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1754736893960832, "dur": 50, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\GameSoft\\Unity\\Editor\\2022.3.38f1c1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.ContentLoadModule.dll"}}, {"pid": 12345, "tid": 10, "ts": 1754736893960829, "dur": 55, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ContentLoadModule.dll_9B44DAA96A1B4B8B.mvfrm"}}, {"pid": 12345, "tid": 10, "ts": 1754736893960996, "dur": 67, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ProfilerModule.dll_6E85C30F177A7F51.mvfrm"}}, {"pid": 12345, "tid": 10, "ts": 1754736893961083, "dur": 143, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1754736893961226, "dur": 61, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityTestProtocolModule.dll_1E012B284EB8934B.mvfrm"}}, {"pid": 12345, "tid": 10, "ts": 1754736893961297, "dur": 53, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/UnityEngine.TestRunner.dll.mvfrm"}}, {"pid": 12345, "tid": 10, "ts": 1754736893962464, "dur": 179, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\GameSoft\\Unity\\Editor\\2022.3.38f1c1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Security.Principal.dll"}}, {"pid": 12345, "tid": 10, "ts": 1754736893963835, "dur": 56, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.test-framework@1.1.33\\UnityEngine.TestRunner\\TestRunner\\SynchronousFilter.cs"}}, {"pid": 12345, "tid": 10, "ts": 1754736893964150, "dur": 367, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.test-framework@1.1.33\\UnityEngine.TestRunner\\Utils\\TestRunCallbackListener.cs"}}, {"pid": 12345, "tid": 10, "ts": 1754736893961362, "dur": 3259, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/UnityEngine.TestRunner.dll (+2 others)"}}, {"pid": 12345, "tid": 10, "ts": 1754736893964696, "dur": 118, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/UnityEditor.TestRunner.dll.mvfrm"}}, {"pid": 12345, "tid": 10, "ts": 1754736893965089, "dur": 56, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\GameSoft\\Unity\\Editor\\2022.3.38f1c1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Collections.Specialized.dll"}}, {"pid": 12345, "tid": 10, "ts": 1754736893965995, "dur": 180, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\GameSoft\\Unity\\Editor\\2022.3.38f1c1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Security.Principal.dll"}}, {"pid": 12345, "tid": 10, "ts": 1754736893966767, "dur": 62, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.test-framework@1.1.33\\UnityEditor.TestRunner\\Api\\IErrorCallbacks.cs"}}, {"pid": 12345, "tid": 10, "ts": 1754736893967593, "dur": 79, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.test-framework@1.1.33\\UnityEditor.TestRunner\\TestLaunchers\\PlayerLauncherContextSettings.cs"}}, {"pid": 12345, "tid": 10, "ts": 1754736893964825, "dur": 3038, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/UnityEditor.TestRunner.dll (+2 others)"}}, {"pid": 12345, "tid": 10, "ts": 1754736893968012, "dur": 198, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/UnityEditor.UI.dll (+2 others)"}}, {"pid": 12345, "tid": 10, "ts": 1754736893968273, "dur": 62, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.Burst.dll.mvfrm"}}, {"pid": 12345, "tid": 10, "ts": 1754736893968345, "dur": 437, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Burst.dll (+2 others)"}}, {"pid": 12345, "tid": 10, "ts": 1754736893968782, "dur": 299, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1754736893969112, "dur": 103, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.Burst.CodeGen.dll.mvfrm"}}, {"pid": 12345, "tid": 10, "ts": 1754736893969233, "dur": 324, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Burst.CodeGen.dll (+2 others)"}}, {"pid": 12345, "tid": 10, "ts": 1754736893969558, "dur": 99, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1754736893969672, "dur": 426, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPP-Configuration Library/ilpp-configuration.nevergeneratedoutput"}}, {"pid": 12345, "tid": 10, "ts": 1754736893970128, "dur": 87, "ph": "X", "name": "StoreTimestampsOfNonGeneratedInputFiles", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1754736893970227, "dur": 276136, "ph": "X", "name": "ILPP-Configuration", "args": {"detail": "Library/ilpp-configuration.nevergeneratedoutput"}}, {"pid": 12345, "tid": 10, "ts": 1754736894248564, "dur": 1475, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/com.crashkonijn.goap.demos.complex.dll (+pdb)"}}, {"pid": 12345, "tid": 10, "ts": 1754736894250068, "dur": 1310, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/com.crashkonijn.goap.demos.turn-based.dll (+pdb)"}}, {"pid": 12345, "tid": 10, "ts": 1754736894251378, "dur": 342, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1754736894251727, "dur": 1364, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.RenderPipelines.Core.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 10, "ts": 1754736894253128, "dur": 1478, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Timeline.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 10, "ts": 1754736894254664, "dur": 41216, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1754736893941200, "dur": 19531, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1754736893960742, "dur": 71, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.QuickSearchModule.dll_BE454E773931EEAC.mvfrm"}}, {"pid": 12345, "tid": 11, "ts": 1754736893961067, "dur": 68, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityAnalyticsCommonModule.dll_3B4AA1572BDDA284.mvfrm"}}, {"pid": 12345, "tid": 11, "ts": 1754736893961138, "dur": 107, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.collections@1.2.4\\Unity.Collections.LowLevel.ILSupport\\Unity.Collections.LowLevel.ILSupport.dll"}}, {"pid": 12345, "tid": 11, "ts": 1754736893961136, "dur": 110, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.Collections.LowLevel.ILSupport.dll_2E40CE80E145E521.mvfrm"}}, {"pid": 12345, "tid": 11, "ts": 1754736893961261, "dur": 314, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.Collections.LowLevel.ILSupport.dll_2E40CE80E145E521.mvfrm"}}, {"pid": 12345, "tid": 11, "ts": 1754736893961581, "dur": 58, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/UnityEngine.UI.dll.mvfrm"}}, {"pid": 12345, "tid": 11, "ts": 1754736893962464, "dur": 158, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\GameSoft\\Unity\\Editor\\2022.3.38f1c1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Security.Principal.dll"}}, {"pid": 12345, "tid": 11, "ts": 1754736893963837, "dur": 56, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.ugui@1.0.0\\Runtime\\UI\\Core\\Utility\\ReflectionMethodsCache.cs"}}, {"pid": 12345, "tid": 11, "ts": 1754736893961691, "dur": 2302, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/UnityEngine.UI.dll (+2 others)"}}, {"pid": 12345, "tid": 11, "ts": 1754736893964046, "dur": 572, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1754736893964619, "dur": 236, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1754736893964856, "dur": 189, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1754736893965046, "dur": 202, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1754736893965249, "dur": 182, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1754736893965431, "dur": 204, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1754736893965635, "dur": 200, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1754736893965835, "dur": 234, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1754736893966069, "dur": 425, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1754736893966494, "dur": 215, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1754736893966710, "dur": 150, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1754736893966861, "dur": 204, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1754736893967066, "dur": 163, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1754736893967230, "dur": 170, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1754736893967401, "dur": 284, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1754736893967685, "dur": 254, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1754736893967939, "dur": 334, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1754736893968273, "dur": 68, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/CrashKonijn.Agent.Core.dll.mvfrm"}}, {"pid": 12345, "tid": 11, "ts": 1754736893968358, "dur": 366, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/CrashKonijn.Agent.Core.dll (+2 others)"}}, {"pid": 12345, "tid": 11, "ts": 1754736893968762, "dur": 104, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\GameSoft\\Unity\\Editor\\2022.3.38f1c1\\Editor\\Data\\Managed\\UnityEngine\\UnityEditor.CoreModule.dll"}}, {"pid": 12345, "tid": 11, "ts": 1754736893969164, "dur": 99, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.crashkonijn.goap@601802d6e1\\Runtime\\CrashKonijn.Goap.Core\\Interfaces\\IGoapAction.cs"}}, {"pid": 12345, "tid": 11, "ts": 1754736893968759, "dur": 554, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/CrashKonijn.Goap.Core.dll (+2 others)"}}, {"pid": 12345, "tid": 11, "ts": 1754736893969313, "dur": 76, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1754736893969457, "dur": 71, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1754736893969528, "dur": 75, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/CrashKonijn.Goap.Runtime.dll.mvfrm"}}, {"pid": 12345, "tid": 11, "ts": 1754736893969624, "dur": 61, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1754736893969685, "dur": 263, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1754736893969948, "dur": 52, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1754736893970001, "dur": 582, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1754736893970584, "dur": 61, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Universal.Config.Runtime.dll.mvfrm"}}, {"pid": 12345, "tid": 11, "ts": 1754736893970671, "dur": 241, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Universal.Config.Runtime.dll (+2 others)"}}, {"pid": 12345, "tid": 11, "ts": 1754736893970969, "dur": 277604, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1754736894249995, "dur": 72, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\GameSoft\\Unity\\Editor\\2022.3.38f1c1\\Editor\\Data\\Managed\\UnityEngine\\UnityEditor.TextCoreFontEngineModule.dll"}}, {"pid": 12345, "tid": 11, "ts": 1754736894250428, "dur": 113, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\GameSoft\\Unity\\Editor\\2022.3.38f1c1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Diagnostics.TraceSource.dll"}}, {"pid": 12345, "tid": 11, "ts": 1754736894248575, "dur": 2125, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.VisualScripting.State.dll (+pdb)"}}, {"pid": 12345, "tid": 11, "ts": 1754736894250754, "dur": 1291, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Burst.dll (+pdb)"}}, {"pid": 12345, "tid": 11, "ts": 1754736894252085, "dur": 1328, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Burst.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 11, "ts": 1754736894253414, "dur": 109, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1754736894253528, "dur": 1456, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.ShaderGraph.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 11, "ts": 1754736894255053, "dur": 40867, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1754736893941233, "dur": 19527, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1754736893960780, "dur": 54, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.UIElementsModule.dll_613AE6E2D149BAF8.mvfrm"}}, {"pid": 12345, "tid": 12, "ts": 1754736893961087, "dur": 130, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\GameSoft\\Unity\\Editor\\2022.3.38f1c1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.UnityWebRequestAudioModule.dll"}}, {"pid": 12345, "tid": 12, "ts": 1754736893961085, "dur": 133, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityWebRequestAudioModule.dll_180837F1985FCEBE.mvfrm"}}, {"pid": 12345, "tid": 12, "ts": 1754736893961412, "dur": 70, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/CrashKonijn.Agent.Core.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 12, "ts": 1754736893961715, "dur": 94, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.State.Editor.rsp2"}}, {"pid": 12345, "tid": 12, "ts": 1754736893961819, "dur": 121, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.ShaderGraph.ShaderGraphLibrary.rsp2"}}, {"pid": 12345, "tid": 12, "ts": 1754736893962226, "dur": 99, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Collections.BurstCompatibilityGen.rsp"}}, {"pid": 12345, "tid": 12, "ts": 1754736893962351, "dur": 138, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/8899139255040401798.rsp"}}, {"pid": 12345, "tid": 12, "ts": 1754736893962489, "dur": 494, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1754736893962983, "dur": 186, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1754736893963169, "dur": 211, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1754736893963380, "dur": 245, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1754736893963625, "dur": 153, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1754736893963778, "dur": 206, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1754736893963985, "dur": 570, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1754736893964555, "dur": 214, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1754736893964770, "dur": 191, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1754736893964961, "dur": 274, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1754736893965235, "dur": 174, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1754736893965409, "dur": 272, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1754736893965681, "dur": 189, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1754736893965870, "dur": 224, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1754736893966094, "dur": 206, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1754736893966301, "dur": 254, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1754736893966556, "dur": 184, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1754736893966740, "dur": 453, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1754736893967193, "dur": 256, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1754736893967449, "dur": 58, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1754736893967563, "dur": 56, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1754736893967654, "dur": 278, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1754736893967933, "dur": 336, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1754736893968270, "dur": 68, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.InputSystem.dll.mvfrm"}}, {"pid": 12345, "tid": 12, "ts": 1754736893968580, "dur": 97, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\GameSoft\\Unity\\Editor\\2022.3.38f1c1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Reflection.Primitives.dll"}}, {"pid": 12345, "tid": 12, "ts": 1754736893968893, "dur": 585, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.inputsystem@1.7.0\\InputSystem\\Controls\\Processors\\ScaleProcessor.cs"}}, {"pid": 12345, "tid": 12, "ts": 1754736893968368, "dur": 1512, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.InputSystem.dll (+2 others)"}}, {"pid": 12345, "tid": 12, "ts": 1754736893969946, "dur": 68, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Core.Runtime.dll.mvfrm"}}, {"pid": 12345, "tid": 12, "ts": 1754736893970026, "dur": 484, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Core.Runtime.dll (+2 others)"}}, {"pid": 12345, "tid": 12, "ts": 1754736893970650, "dur": 201, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipeline.Universal.ShaderLibrary.dll (+2 others)"}}, {"pid": 12345, "tid": 12, "ts": 1754736893970852, "dur": 120, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1754736893971032, "dur": 87, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Universal.Runtime.dll.mvfrm"}}, {"pid": 12345, "tid": 12, "ts": 1754736893971134, "dur": 382, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Universal.Runtime.dll (+2 others)"}}, {"pid": 12345, "tid": 12, "ts": 1754736893971620, "dur": 210, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Cinemachine.dll (+2 others)"}}, {"pid": 12345, "tid": 12, "ts": 1754736893971855, "dur": 276754, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1754736894248625, "dur": 1879, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.RenderPipelines.Universal.Shaders.dll (+pdb)"}}, {"pid": 12345, "tid": 12, "ts": 1754736894250505, "dur": 374, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1754736894250887, "dur": 1309, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.TextMeshPro.dll (+pdb)"}}, {"pid": 12345, "tid": 12, "ts": 1754736894252242, "dur": 1326, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Collections.BurstCompatibilityGen.dll (+pdb)"}}, {"pid": 12345, "tid": 12, "ts": 1754736894253693, "dur": 122, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1754736894254232, "dur": 64, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1754736894254302, "dur": 196, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1754736894254531, "dur": 384, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1754736894254944, "dur": 40958, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1754736893941263, "dur": 19522, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1754736893960837, "dur": 79, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1754736893961108, "dur": 123, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\GameSoft\\Unity\\Editor\\2022.3.38f1c1\\Editor\\Data\\Managed\\UnityEditor.Graphs.dll"}}, {"pid": 12345, "tid": 13, "ts": 1754736893961106, "dur": 126, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.Graphs.dll_34C604C212D62B6E.mvfrm"}}, {"pid": 12345, "tid": 13, "ts": 1754736893961367, "dur": 163, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.Burst.Cecil.Mdb.dll_825BAFEC5BACF8AB.mvfrm"}}, {"pid": 12345, "tid": 13, "ts": 1754736893961686, "dur": 53, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.ShaderGraph.Editor.rsp"}}, {"pid": 12345, "tid": 13, "ts": 1754736893961954, "dur": 231, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/com.crashkonijn.docs.getting_started.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 13, "ts": 1754736893962426, "dur": 63, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/2240406767038398906.rsp"}}, {"pid": 12345, "tid": 13, "ts": 1754736893962489, "dur": 287, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1754736893962779, "dur": 216, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1754736893962995, "dur": 189, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1754736893963184, "dur": 224, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1754736893963408, "dur": 187, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1754736893963595, "dur": 161, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1754736893963757, "dur": 224, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1754736893963981, "dur": 544, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1754736893964525, "dur": 203, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1754736893964728, "dur": 204, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1754736893964933, "dur": 238, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1754736893965172, "dur": 182, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1754736893965354, "dur": 228, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1754736893965583, "dur": 179, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1754736893965762, "dur": 205, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1754736893965968, "dur": 256, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1754736893966224, "dur": 271, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1754736893966495, "dur": 179, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1754736893966674, "dur": 158, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1754736893966832, "dur": 226, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1754736893967058, "dur": 160, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1754736893967218, "dur": 375, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1754736893967649, "dur": 265, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1754736893967944, "dur": 646, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1754736893968671, "dur": 111, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\GameSoft\\Unity\\Editor\\2022.3.38f1c1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.TextCoreTextEngineModule.dll"}}, {"pid": 12345, "tid": 13, "ts": 1754736893968839, "dur": 62, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\GameSoft\\Unity\\Editor\\2022.3.38f1c1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.ComponentModel.Primitives.dll"}}, {"pid": 12345, "tid": 13, "ts": 1754736893968931, "dur": 281, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\GameSoft\\Unity\\Editor\\2022.3.38f1c1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Linq.Expressions.dll"}}, {"pid": 12345, "tid": 13, "ts": 1754736893968591, "dur": 898, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.VisualStudio.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 13, "ts": 1754736893969490, "dur": 132, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1754736893969698, "dur": 255, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1754736893969953, "dur": 65, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1754736893970018, "dur": 569, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1754736893970587, "dur": 197, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1754736893970785, "dur": 326, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/com.crashkonijn.goap.demos.complex.dll (+2 others)"}}, {"pid": 12345, "tid": 13, "ts": 1754736893971150, "dur": 277412, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1754736894250042, "dur": 566, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\GameSoft\\Unity\\Editor\\2022.3.38f1c1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netfx\\System.Numerics.dll"}}, {"pid": 12345, "tid": 13, "ts": 1754736894248565, "dur": 2353, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.RenderPipelines.Core.ShaderLibrary.dll (+pdb)"}}, {"pid": 12345, "tid": 13, "ts": 1754736894250953, "dur": 1291, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Timeline.dll (+pdb)"}}, {"pid": 12345, "tid": 13, "ts": 1754736894252244, "dur": 213, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1754736894252462, "dur": 1338, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.ShaderGraph.Utilities.dll (+pdb)"}}, {"pid": 12345, "tid": 13, "ts": 1754736894253829, "dur": 65, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1754736894253903, "dur": 87, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1754736894254322, "dur": 324, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1754736894254660, "dur": 387, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1754736894255047, "dur": 40818, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1754736893941311, "dur": 19489, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1754736893960916, "dur": 138, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.GridModule.dll_B76D6751422DDE5E.mvfrm"}}, {"pid": 12345, "tid": 14, "ts": 1754736893961089, "dur": 124, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\GameSoft\\Unity\\Editor\\2022.3.38f1c1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.UnityWebRequestTextureModule.dll"}}, {"pid": 12345, "tid": 14, "ts": 1754736893961088, "dur": 126, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityWebRequestTextureModule.dll_3AB3AEE512D0C853.mvfrm"}}, {"pid": 12345, "tid": 14, "ts": 1754736893961264, "dur": 52, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.VisualScripting.IonicZip.dll_840FAE5C8A141D5B.mvfrm"}}, {"pid": 12345, "tid": 14, "ts": 1754736893961450, "dur": 56, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Universal.Config.Runtime.rsp2"}}, {"pid": 12345, "tid": 14, "ts": 1754736893961621, "dur": 93, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Mathematics.Editor.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 14, "ts": 1754736893961769, "dur": 160, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.State.Editor.rsp"}}, {"pid": 12345, "tid": 14, "ts": 1754736893962093, "dur": 147, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Tests.rsp2"}}, {"pid": 12345, "tid": 14, "ts": 1754736893962471, "dur": 365, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1754736893962836, "dur": 274, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1754736893963110, "dur": 168, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1754736893963278, "dur": 304, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1754736893963583, "dur": 166, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1754736893963749, "dur": 196, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1754736893963945, "dur": 174, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1754736893964120, "dur": 580, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1754736893964718, "dur": 185, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1754736893964903, "dur": 213, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1754736893965116, "dur": 224, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1754736893965341, "dur": 223, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1754736893965564, "dur": 164, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1754736893965728, "dur": 169, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1754736893965897, "dur": 306, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1754736893966203, "dur": 173, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1754736893966376, "dur": 914, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1754736893967290, "dur": 180, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1754736893967470, "dur": 85, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1754736893967555, "dur": 99, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1754736893967655, "dur": 276, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1754736893967931, "dur": 416, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1754736893968348, "dur": 71, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.TextMeshPro.dll.mvfrm"}}, {"pid": 12345, "tid": 14, "ts": 1754736893968580, "dur": 129, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\GameSoft\\Unity\\Editor\\2022.3.38f1c1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Diagnostics.FileVersionInfo.dll"}}, {"pid": 12345, "tid": 14, "ts": 1754736893968446, "dur": 579, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.TextMeshPro.dll (+2 others)"}}, {"pid": 12345, "tid": 14, "ts": 1754736893969025, "dur": 76, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1754736893969122, "dur": 192, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.Burst.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 14, "ts": 1754736893969314, "dur": 84, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1754736893969403, "dur": 343, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Burst.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 14, "ts": 1754736893969746, "dur": 84, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1754736893969851, "dur": 108, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1754736893969960, "dur": 67, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1754736893970027, "dur": 565, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1754736893970592, "dur": 190, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1754736893970783, "dur": 286, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/com.crashkonijn.docs.getting_started.dll (+2 others)"}}, {"pid": 12345, "tid": 14, "ts": 1754736893971123, "dur": 277437, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1754736894248562, "dur": 1525, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/CrashKonijn.Goap.Resolver.dll (+pdb)"}}, {"pid": 12345, "tid": 14, "ts": 1754736894250088, "dur": 182, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1754736894250423, "dur": 75, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\GameSoft\\Unity\\Editor\\2022.3.38f1c1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.IO.Pipes.dll"}}, {"pid": 12345, "tid": 14, "ts": 1754736894250277, "dur": 1357, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/com.crashkonijn.docs.getting_started.dll (+pdb)"}}, {"pid": 12345, "tid": 14, "ts": 1754736894251635, "dur": 436, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1754736894252077, "dur": 1305, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.CollabProxy.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 14, "ts": 1754736894253382, "dur": 134, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1754736894253521, "dur": 1463, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.VisualScripting.Flow.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 14, "ts": 1754736894255056, "dur": 40816, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1754736893941346, "dur": 19466, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1754736893960820, "dur": 51, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.AudioModule.dll_F8B5F041D001A362.mvfrm"}}, {"pid": 12345, "tid": 15, "ts": 1754736893960914, "dur": 127, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.AudioModule.dll_F8B5F041D001A362.mvfrm"}}, {"pid": 12345, "tid": 15, "ts": 1754736893961088, "dur": 129, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\GameSoft\\Unity\\Editor\\2022.3.38f1c1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.UnityWebRequestModule.dll"}}, {"pid": 12345, "tid": 15, "ts": 1754736893961086, "dur": 132, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityWebRequestModule.dll_55F435D8463A793C.mvfrm"}}, {"pid": 12345, "tid": 15, "ts": 1754736893961304, "dur": 51, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Burst.rsp2"}}, {"pid": 12345, "tid": 15, "ts": 1754736893961396, "dur": 370, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Core.Runtime.rsp"}}, {"pid": 12345, "tid": 15, "ts": 1754736893961870, "dur": 55, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VSCode.Editor.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 15, "ts": 1754736893962058, "dur": 189, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Assembly-CSharp.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 15, "ts": 1754736893962484, "dur": 170, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1754736893962657, "dur": 325, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1754736893962983, "dur": 200, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1754736893963183, "dur": 237, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1754736893963421, "dur": 257, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1754736893963678, "dur": 205, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1754736893963883, "dur": 184, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1754736893964067, "dur": 615, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1754736893964701, "dur": 228, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1754736893964929, "dur": 239, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1754736893965169, "dur": 162, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1754736893965331, "dur": 171, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1754736893965502, "dur": 637, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1754736893966140, "dur": 568, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1754736893966708, "dur": 213, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1754736893966921, "dur": 217, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1754736893967138, "dur": 466, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1754736893967647, "dur": 266, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1754736893967930, "dur": 497, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1754736893968428, "dur": 68, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.VisualStudio.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 15, "ts": 1754736893968513, "dur": 58, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.Rider.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 15, "ts": 1754736893968671, "dur": 142, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\GameSoft\\Unity\\Editor\\2022.3.38f1c1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.TilemapModule.dll"}}, {"pid": 12345, "tid": 15, "ts": 1754736893968839, "dur": 61, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\GameSoft\\Unity\\Editor\\2022.3.38f1c1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Collections.Concurrent.dll"}}, {"pid": 12345, "tid": 15, "ts": 1754736893968604, "dur": 518, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Rider.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 15, "ts": 1754736893969122, "dur": 69, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1754736893969196, "dur": 81, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/com.crashkonijn.goap.demos.complex.dll.mvfrm"}}, {"pid": 12345, "tid": 15, "ts": 1754736893969278, "dur": 166, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1754736893969449, "dur": 97, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1754736893969546, "dur": 94, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1754736893969717, "dur": 249, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1754736893969966, "dur": 59, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1754736893970025, "dur": 559, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1754736893970648, "dur": 374, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Core.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 15, "ts": 1754736893971022, "dur": 115, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1754736893971160, "dur": 50, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.ShaderGraph.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 15, "ts": 1754736893971221, "dur": 716, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.ShaderGraph.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 15, "ts": 1754736893972021, "dur": 328, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Universal.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 15, "ts": 1754736893972448, "dur": 214, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/com.unity.cinemachine.editor.dll (+2 others)"}}, {"pid": 12345, "tid": 15, "ts": 1754736893972686, "dur": 275967, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1754736894248654, "dur": 1808, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/CrashKonijn.Goap.Core.dll (+pdb)"}}, {"pid": 12345, "tid": 15, "ts": 1754736894250532, "dur": 58, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\GameSoft\\Unity\\Editor\\2022.3.38f1c1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.HotReloadModule.dll"}}, {"pid": 12345, "tid": 15, "ts": 1754736894250491, "dur": 1300, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Collections.dll (+pdb)"}}, {"pid": 12345, "tid": 15, "ts": 1754736894251845, "dur": 1293, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.PlasticSCM.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 15, "ts": 1754736894253139, "dur": 176, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1754736894253320, "dur": 1461, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/UnityEditor.UI.dll (+pdb)"}}, {"pid": 12345, "tid": 15, "ts": 1754736894254818, "dur": 41057, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1754736893941376, "dur": 19448, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1754736893960868, "dur": 61, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1754736893961034, "dur": 93, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.TerrainPhysicsModule.dll_34719EFEEA08287A.mvfrm"}}, {"pid": 12345, "tid": 16, "ts": 1754736893961150, "dur": 95, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1754736893961325, "dur": 116, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Burst.CodeGen.rsp2"}}, {"pid": 12345, "tid": 16, "ts": 1754736893961861, "dur": 52, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.PlasticSCM.Editor.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 16, "ts": 1754736893962476, "dur": 165, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1754736893962650, "dur": 189, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1754736893962839, "dur": 196, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1754736893963035, "dur": 171, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1754736893963206, "dur": 189, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1754736893963395, "dur": 270, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1754736893963665, "dur": 219, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1754736893963885, "dur": 169, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1754736893964054, "dur": 571, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1754736893964625, "dur": 220, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1754736893964845, "dur": 194, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1754736893965040, "dur": 246, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1754736893965286, "dur": 175, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1754736893965461, "dur": 223, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1754736893965684, "dur": 185, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1754736893965869, "dur": 253, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1754736893966122, "dur": 200, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1754736893966322, "dur": 210, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1754736893966532, "dur": 205, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1754736893966737, "dur": 241, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1754736893966978, "dur": 157, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1754736893967135, "dur": 165, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1754736893967301, "dur": 178, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1754736893967479, "dur": 219, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1754736893967699, "dur": 237, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1754736893967936, "dur": 650, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1754736893968588, "dur": 63, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.ShaderGraph.ShaderGraphLibrary.dll.mvfrm"}}, {"pid": 12345, "tid": 16, "ts": 1754736893968762, "dur": 84, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\GameSoft\\Unity\\Editor\\2022.3.38f1c1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.UnityAnalyticsCommonModule.dll"}}, {"pid": 12345, "tid": 16, "ts": 1754736893968892, "dur": 154, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\GameSoft\\Unity\\Editor\\2022.3.38f1c1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.AppContext.dll"}}, {"pid": 12345, "tid": 16, "ts": 1754736893969062, "dur": 78, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\GameSoft\\Unity\\Editor\\2022.3.38f1c1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Diagnostics.Contracts.dll"}}, {"pid": 12345, "tid": 16, "ts": 1754736893969167, "dur": 52, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\GameSoft\\Unity\\Editor\\2022.3.38f1c1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.IO.Compression.ZipFile.dll"}}, {"pid": 12345, "tid": 16, "ts": 1754736893968688, "dur": 696, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.ShaderGraph.ShaderGraphLibrary.dll (+2 others)"}}, {"pid": 12345, "tid": 16, "ts": 1754736893969524, "dur": 99, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.Mathematics.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 16, "ts": 1754736893969647, "dur": 274, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Mathematics.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 16, "ts": 1754736893970021, "dur": 565, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1754736893970586, "dur": 204, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1754736893970838, "dur": 65, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Core.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 16, "ts": 1754736893970929, "dur": 797, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Core.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 16, "ts": 1754736893971773, "dur": 64, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Flow.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 16, "ts": 1754736893971852, "dur": 321, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Flow.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 16, "ts": 1754736893972263, "dur": 216, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.State.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 16, "ts": 1754736893972570, "dur": 163, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Shared.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 16, "ts": 1754736893972987, "dur": 2478, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\Bee\\artifacts\\1900b0aE.dag\\Assembly-CSharp.rsp"}}, {"pid": 12345, "tid": 16, "ts": 1754736893972824, "dur": 2641, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Assembly-CSharp.dll (+2 others)"}}, {"pid": 12345, "tid": 16, "ts": 1754736893975514, "dur": 318807, "ph": "X", "name": "Csc", "args": {"detail": "Library/Bee/artifacts/1900b0aE.dag/Assembly-CSharp.dll (+2 others)"}}, {"pid": 12345, "tid": 0, "ts": 1754736894301980, "dur": 1509, "ph": "X", "name": "ProfilerWriteOutput"}, {"pid": 3100, "tid": 24784, "ts": 1754736894305409, "dur": 18, "ph": "X", "name": "Wait for external events", "args": {"First to finish": "buildprogram0.traceevents"}}, {"pid": 3100, "tid": 24784, "ts": 1754736894305732, "dur": 346, "ph": "X", "name": "Wait for external events", "args": {"First to finish": "backend1.traceevents"}}, {"pid": 3100, "tid": 24784, "ts": 1754736894306329, "dur": 13, "ph": "X", "name": "Wait for external events", "args": {"First to finish": "backend2.traceevents"}}, {"pid": 3100, "tid": 24784, "ts": 1754736894305558, "dur": 174, "ph": "X", "name": "buildprogram0.traceevents", "args": {}}, {"pid": 3100, "tid": 24784, "ts": 1754736894306143, "dur": 185, "ph": "X", "name": "backend1.traceevents", "args": {}}, {"pid": 3100, "tid": 24784, "ts": 1754736894307849, "dur": 779, "ph": "X", "name": "backend2.traceevents", "args": {}}, {"pid": 3100, "tid": 24784, "ts": 1754736894303876, "dur": 4792, "ph": "X", "name": "Write chrome-trace events", "args": {}}, {}]}