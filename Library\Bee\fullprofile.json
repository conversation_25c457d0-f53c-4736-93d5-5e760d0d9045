{"Instructions Readme": "1) Open Chrome or Edge, 2) go to chrome://tracing, 3) click Load, 4) navigate to this file.", "processInfo": {}, "traceEvents": [{"pid": 3100, "tid": -1, "ph": "M", "name": "process_name", "args": {"name": "BeeDriver"}}, {"pid": 3100, "tid": -1, "ph": "M", "name": "process_sort_index", "args": {"sort_index": "-2"}}, {"pid": 3100, "tid": 24785, "ph": "M", "name": "thread_name", "args": {"name": "Thread Pool Worker"}}, {"pid": 3100, "tid": 24785, "ts": 1754737154205325, "dur": 6, "ph": "X", "name": "ChromeTraceHeader", "args": {}}, {"pid": 3100, "tid": 24785, "ts": 1754737154205343, "dur": 3, "ph": "X", "name": "Thread Pool Worker", "args": {}}, {"pid": 3100, "tid": 1, "ph": "M", "name": "thread_name", "args": {"name": ""}}, {"pid": 3100, "tid": 1, "ts": 1754737153671476, "dur": 1494, "ph": "X", "name": "<Add>b__0", "args": {}}, {"pid": 3100, "tid": 1, "ts": 1754737153672974, "dur": 61807, "ph": "X", "name": "<Add>b__0", "args": {}}, {"pid": 3100, "tid": 1, "ts": 1754737153734783, "dur": 19167, "ph": "X", "name": "WriteJson", "args": {}}, {"pid": 3100, "tid": 24785, "ts": 1754737154205346, "dur": 7, "ph": "X", "name": "", "args": {}}, {"pid": 3100, "tid": 103079215104, "ph": "M", "name": "thread_name", "args": {"name": "ReadEntireBinlogFromIpcAsync"}}, {"pid": 3100, "tid": 103079215104, "ts": 1754737153671428, "dur": 151591, "ph": "X", "name": "WaitForConnectionAsync", "args": {}}, {"pid": 3100, "tid": 103079215104, "ts": 1754737153823021, "dur": 381797, "ph": "X", "name": "UpdateFromStreamAsync", "args": {}}, {"pid": 3100, "tid": 103079215104, "ts": 1754737153823036, "dur": 44, "ph": "X", "name": "ReadAsync 0", "args": {}}, {"pid": 3100, "tid": 103079215104, "ts": 1754737153823082, "dur": 3, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 3100, "tid": 103079215104, "ts": 1754737153823086, "dur": 258, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 3100, "tid": 103079215104, "ts": 1754737153823349, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 3100, "tid": 103079215104, "ts": 1754737153823351, "dur": 42, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3100, "tid": 103079215104, "ts": 1754737153823397, "dur": 6, "ph": "X", "name": "ProcessMessages 41", "args": {}}, {"pid": 3100, "tid": 103079215104, "ts": 1754737153823404, "dur": 2586, "ph": "X", "name": "ReadAsync 41", "args": {}}, {"pid": 3100, "tid": 103079215104, "ts": 1754737153825995, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 3100, "tid": 103079215104, "ts": 1754737153825997, "dur": 50, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3100, "tid": 103079215104, "ts": 1754737153826050, "dur": 1, "ph": "X", "name": "ProcessMessages 388", "args": {}}, {"pid": 3100, "tid": 103079215104, "ts": 1754737153826052, "dur": 42, "ph": "X", "name": "ReadAsync 388", "args": {}}, {"pid": 3100, "tid": 103079215104, "ts": 1754737153826096, "dur": 1, "ph": "X", "name": "ProcessMessages 360", "args": {}}, {"pid": 3100, "tid": 103079215104, "ts": 1754737153826097, "dur": 46, "ph": "X", "name": "ReadAsync 360", "args": {}}, {"pid": 3100, "tid": 103079215104, "ts": 1754737153826147, "dur": 42, "ph": "X", "name": "ReadAsync 388", "args": {}}, {"pid": 3100, "tid": 103079215104, "ts": 1754737153826192, "dur": 1, "ph": "X", "name": "ProcessMessages 602", "args": {}}, {"pid": 3100, "tid": 103079215104, "ts": 1754737153826194, "dur": 32, "ph": "X", "name": "ReadAsync 602", "args": {}}, {"pid": 3100, "tid": 103079215104, "ts": 1754737153826228, "dur": 39, "ph": "X", "name": "ReadAsync 696", "args": {}}, {"pid": 3100, "tid": 103079215104, "ts": 1754737153826273, "dur": 73, "ph": "X", "name": "ReadAsync 239", "args": {}}, {"pid": 3100, "tid": 103079215104, "ts": 1754737153826348, "dur": 3, "ph": "X", "name": "ProcessMessages 451", "args": {}}, {"pid": 3100, "tid": 103079215104, "ts": 1754737153826352, "dur": 44, "ph": "X", "name": "ReadAsync 451", "args": {}}, {"pid": 3100, "tid": 103079215104, "ts": 1754737153826398, "dur": 1, "ph": "X", "name": "ProcessMessages 696", "args": {}}, {"pid": 3100, "tid": 103079215104, "ts": 1754737153826400, "dur": 161, "ph": "X", "name": "ReadAsync 696", "args": {}}, {"pid": 3100, "tid": 103079215104, "ts": 1754737153826565, "dur": 38, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3100, "tid": 103079215104, "ts": 1754737153826605, "dur": 64, "ph": "X", "name": "ReadAsync 624", "args": {}}, {"pid": 3100, "tid": 103079215104, "ts": 1754737153826673, "dur": 1, "ph": "X", "name": "ProcessMessages 577", "args": {}}, {"pid": 3100, "tid": 103079215104, "ts": 1754737153826674, "dur": 34, "ph": "X", "name": "ReadAsync 577", "args": {}}, {"pid": 3100, "tid": 103079215104, "ts": 1754737153826710, "dur": 1, "ph": "X", "name": "ProcessMessages 212", "args": {}}, {"pid": 3100, "tid": 103079215104, "ts": 1754737153826711, "dur": 35, "ph": "X", "name": "ReadAsync 212", "args": {}}, {"pid": 3100, "tid": 103079215104, "ts": 1754737153826750, "dur": 37, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3100, "tid": 103079215104, "ts": 1754737153826790, "dur": 1, "ph": "X", "name": "ProcessMessages 610", "args": {}}, {"pid": 3100, "tid": 103079215104, "ts": 1754737153826792, "dur": 41, "ph": "X", "name": "ReadAsync 610", "args": {}}, {"pid": 3100, "tid": 103079215104, "ts": 1754737153826835, "dur": 18, "ph": "X", "name": "ReadAsync 569", "args": {}}, {"pid": 3100, "tid": 103079215104, "ts": 1754737153826855, "dur": 22, "ph": "X", "name": "ReadAsync 334", "args": {}}, {"pid": 3100, "tid": 103079215104, "ts": 1754737153826879, "dur": 23, "ph": "X", "name": "ReadAsync 224", "args": {}}, {"pid": 3100, "tid": 103079215104, "ts": 1754737153826904, "dur": 25, "ph": "X", "name": "ReadAsync 456", "args": {}}, {"pid": 3100, "tid": 103079215104, "ts": 1754737153826931, "dur": 23, "ph": "X", "name": "ReadAsync 439", "args": {}}, {"pid": 3100, "tid": 103079215104, "ts": 1754737153826956, "dur": 25, "ph": "X", "name": "ReadAsync 350", "args": {}}, {"pid": 3100, "tid": 103079215104, "ts": 1754737153826983, "dur": 24, "ph": "X", "name": "ReadAsync 406", "args": {}}, {"pid": 3100, "tid": 103079215104, "ts": 1754737153827009, "dur": 28, "ph": "X", "name": "ReadAsync 492", "args": {}}, {"pid": 3100, "tid": 103079215104, "ts": 1754737153827039, "dur": 23, "ph": "X", "name": "ReadAsync 470", "args": {}}, {"pid": 3100, "tid": 103079215104, "ts": 1754737153827063, "dur": 33, "ph": "X", "name": "ReadAsync 412", "args": {}}, {"pid": 3100, "tid": 103079215104, "ts": 1754737153827099, "dur": 1, "ph": "X", "name": "ProcessMessages 389", "args": {}}, {"pid": 3100, "tid": 103079215104, "ts": 1754737153827101, "dur": 39, "ph": "X", "name": "ReadAsync 389", "args": {}}, {"pid": 3100, "tid": 103079215104, "ts": 1754737153827144, "dur": 32, "ph": "X", "name": "ReadAsync 621", "args": {}}, {"pid": 3100, "tid": 103079215104, "ts": 1754737153827177, "dur": 22, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3100, "tid": 103079215104, "ts": 1754737153827201, "dur": 31, "ph": "X", "name": "ReadAsync 327", "args": {}}, {"pid": 3100, "tid": 103079215104, "ts": 1754737153827233, "dur": 27, "ph": "X", "name": "ReadAsync 466", "args": {}}, {"pid": 3100, "tid": 103079215104, "ts": 1754737153827263, "dur": 34, "ph": "X", "name": "ReadAsync 527", "args": {}}, {"pid": 3100, "tid": 103079215104, "ts": 1754737153827298, "dur": 26, "ph": "X", "name": "ReadAsync 485", "args": {}}, {"pid": 3100, "tid": 103079215104, "ts": 1754737153827327, "dur": 1, "ph": "X", "name": "ProcessMessages 350", "args": {}}, {"pid": 3100, "tid": 103079215104, "ts": 1754737153827328, "dur": 30, "ph": "X", "name": "ReadAsync 350", "args": {}}, {"pid": 3100, "tid": 103079215104, "ts": 1754737153827360, "dur": 25, "ph": "X", "name": "ReadAsync 525", "args": {}}, {"pid": 3100, "tid": 103079215104, "ts": 1754737153827390, "dur": 42, "ph": "X", "name": "ReadAsync 262", "args": {}}, {"pid": 3100, "tid": 103079215104, "ts": 1754737153827435, "dur": 1, "ph": "X", "name": "ProcessMessages 687", "args": {}}, {"pid": 3100, "tid": 103079215104, "ts": 1754737153827436, "dur": 54, "ph": "X", "name": "ReadAsync 687", "args": {}}, {"pid": 3100, "tid": 103079215104, "ts": 1754737153827492, "dur": 30, "ph": "X", "name": "ReadAsync 248", "args": {}}, {"pid": 3100, "tid": 103079215104, "ts": 1754737153827524, "dur": 23, "ph": "X", "name": "ReadAsync 582", "args": {}}, {"pid": 3100, "tid": 103079215104, "ts": 1754737153827549, "dur": 27, "ph": "X", "name": "ReadAsync 476", "args": {}}, {"pid": 3100, "tid": 103079215104, "ts": 1754737153827577, "dur": 23, "ph": "X", "name": "ReadAsync 346", "args": {}}, {"pid": 3100, "tid": 103079215104, "ts": 1754737153827602, "dur": 23, "ph": "X", "name": "ReadAsync 527", "args": {}}, {"pid": 3100, "tid": 103079215104, "ts": 1754737153827626, "dur": 15, "ph": "X", "name": "ReadAsync 181", "args": {}}, {"pid": 3100, "tid": 103079215104, "ts": 1754737153827643, "dur": 19, "ph": "X", "name": "ReadAsync 409", "args": {}}, {"pid": 3100, "tid": 103079215104, "ts": 1754737153827664, "dur": 29, "ph": "X", "name": "ReadAsync 223", "args": {}}, {"pid": 3100, "tid": 103079215104, "ts": 1754737153827693, "dur": 1, "ph": "X", "name": "ProcessMessages 360", "args": {}}, {"pid": 3100, "tid": 103079215104, "ts": 1754737153827695, "dur": 22, "ph": "X", "name": "ReadAsync 360", "args": {}}, {"pid": 3100, "tid": 103079215104, "ts": 1754737153827719, "dur": 21, "ph": "X", "name": "ReadAsync 593", "args": {}}, {"pid": 3100, "tid": 103079215104, "ts": 1754737153827741, "dur": 23, "ph": "X", "name": "ReadAsync 345", "args": {}}, {"pid": 3100, "tid": 103079215104, "ts": 1754737153827766, "dur": 20, "ph": "X", "name": "ReadAsync 347", "args": {}}, {"pid": 3100, "tid": 103079215104, "ts": 1754737153827788, "dur": 66, "ph": "X", "name": "ReadAsync 344", "args": {}}, {"pid": 3100, "tid": 103079215104, "ts": 1754737153827856, "dur": 20, "ph": "X", "name": "ReadAsync 409", "args": {}}, {"pid": 3100, "tid": 103079215104, "ts": 1754737153827877, "dur": 23, "ph": "X", "name": "ReadAsync 645", "args": {}}, {"pid": 3100, "tid": 103079215104, "ts": 1754737153827901, "dur": 19, "ph": "X", "name": "ReadAsync 449", "args": {}}, {"pid": 3100, "tid": 103079215104, "ts": 1754737153827922, "dur": 20, "ph": "X", "name": "ReadAsync 367", "args": {}}, {"pid": 3100, "tid": 103079215104, "ts": 1754737153827944, "dur": 21, "ph": "X", "name": "ReadAsync 258", "args": {}}, {"pid": 3100, "tid": 103079215104, "ts": 1754737153827966, "dur": 18, "ph": "X", "name": "ReadAsync 390", "args": {}}, {"pid": 3100, "tid": 103079215104, "ts": 1754737153827986, "dur": 42, "ph": "X", "name": "ReadAsync 365", "args": {}}, {"pid": 3100, "tid": 103079215104, "ts": 1754737153828033, "dur": 47, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3100, "tid": 103079215104, "ts": 1754737153828083, "dur": 1, "ph": "X", "name": "ProcessMessages 683", "args": {}}, {"pid": 3100, "tid": 103079215104, "ts": 1754737153828084, "dur": 36, "ph": "X", "name": "ReadAsync 683", "args": {}}, {"pid": 3100, "tid": 103079215104, "ts": 1754737153828123, "dur": 27, "ph": "X", "name": "ReadAsync 824", "args": {}}, {"pid": 3100, "tid": 103079215104, "ts": 1754737153828153, "dur": 1, "ph": "X", "name": "ProcessMessages 352", "args": {}}, {"pid": 3100, "tid": 103079215104, "ts": 1754737153828154, "dur": 32, "ph": "X", "name": "ReadAsync 352", "args": {}}, {"pid": 3100, "tid": 103079215104, "ts": 1754737153828188, "dur": 23, "ph": "X", "name": "ReadAsync 615", "args": {}}, {"pid": 3100, "tid": 103079215104, "ts": 1754737153828214, "dur": 25, "ph": "X", "name": "ReadAsync 492", "args": {}}, {"pid": 3100, "tid": 103079215104, "ts": 1754737153828240, "dur": 21, "ph": "X", "name": "ReadAsync 489", "args": {}}, {"pid": 3100, "tid": 103079215104, "ts": 1754737153828264, "dur": 22, "ph": "X", "name": "ReadAsync 383", "args": {}}, {"pid": 3100, "tid": 103079215104, "ts": 1754737153828288, "dur": 22, "ph": "X", "name": "ReadAsync 470", "args": {}}, {"pid": 3100, "tid": 103079215104, "ts": 1754737153828311, "dur": 25, "ph": "X", "name": "ReadAsync 460", "args": {}}, {"pid": 3100, "tid": 103079215104, "ts": 1754737153828340, "dur": 29, "ph": "X", "name": "ReadAsync 332", "args": {}}, {"pid": 3100, "tid": 103079215104, "ts": 1754737153828371, "dur": 25, "ph": "X", "name": "ReadAsync 564", "args": {}}, {"pid": 3100, "tid": 103079215104, "ts": 1754737153828398, "dur": 24, "ph": "X", "name": "ReadAsync 321", "args": {}}, {"pid": 3100, "tid": 103079215104, "ts": 1754737153828423, "dur": 23, "ph": "X", "name": "ReadAsync 457", "args": {}}, {"pid": 3100, "tid": 103079215104, "ts": 1754737153828447, "dur": 22, "ph": "X", "name": "ReadAsync 466", "args": {}}, {"pid": 3100, "tid": 103079215104, "ts": 1754737153828470, "dur": 2, "ph": "X", "name": "ProcessMessages 431", "args": {}}, {"pid": 3100, "tid": 103079215104, "ts": 1754737153828473, "dur": 22, "ph": "X", "name": "ReadAsync 431", "args": {}}, {"pid": 3100, "tid": 103079215104, "ts": 1754737153828496, "dur": 27, "ph": "X", "name": "ReadAsync 368", "args": {}}, {"pid": 3100, "tid": 103079215104, "ts": 1754737153828526, "dur": 30, "ph": "X", "name": "ReadAsync 413", "args": {}}, {"pid": 3100, "tid": 103079215104, "ts": 1754737153828558, "dur": 25, "ph": "X", "name": "ReadAsync 571", "args": {}}, {"pid": 3100, "tid": 103079215104, "ts": 1754737153828585, "dur": 28, "ph": "X", "name": "ReadAsync 717", "args": {}}, {"pid": 3100, "tid": 103079215104, "ts": 1754737153828614, "dur": 22, "ph": "X", "name": "ReadAsync 359", "args": {}}, {"pid": 3100, "tid": 103079215104, "ts": 1754737153828638, "dur": 23, "ph": "X", "name": "ReadAsync 386", "args": {}}, {"pid": 3100, "tid": 103079215104, "ts": 1754737153828662, "dur": 18, "ph": "X", "name": "ReadAsync 336", "args": {}}, {"pid": 3100, "tid": 103079215104, "ts": 1754737153828681, "dur": 23, "ph": "X", "name": "ReadAsync 197", "args": {}}, {"pid": 3100, "tid": 103079215104, "ts": 1754737153828705, "dur": 22, "ph": "X", "name": "ReadAsync 258", "args": {}}, {"pid": 3100, "tid": 103079215104, "ts": 1754737153828728, "dur": 18, "ph": "X", "name": "ReadAsync 354", "args": {}}, {"pid": 3100, "tid": 103079215104, "ts": 1754737153828747, "dur": 20, "ph": "X", "name": "ReadAsync 133", "args": {}}, {"pid": 3100, "tid": 103079215104, "ts": 1754737153828768, "dur": 25, "ph": "X", "name": "ReadAsync 161", "args": {}}, {"pid": 3100, "tid": 103079215104, "ts": 1754737153828794, "dur": 29, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3100, "tid": 103079215104, "ts": 1754737153828824, "dur": 23, "ph": "X", "name": "ReadAsync 321", "args": {}}, {"pid": 3100, "tid": 103079215104, "ts": 1754737153828847, "dur": 1, "ph": "X", "name": "ProcessMessages 532", "args": {}}, {"pid": 3100, "tid": 103079215104, "ts": 1754737153828849, "dur": 20, "ph": "X", "name": "ReadAsync 532", "args": {}}, {"pid": 3100, "tid": 103079215104, "ts": 1754737153828870, "dur": 25, "ph": "X", "name": "ReadAsync 349", "args": {}}, {"pid": 3100, "tid": 103079215104, "ts": 1754737153828897, "dur": 22, "ph": "X", "name": "ReadAsync 281", "args": {}}, {"pid": 3100, "tid": 103079215104, "ts": 1754737153828920, "dur": 21, "ph": "X", "name": "ReadAsync 323", "args": {}}, {"pid": 3100, "tid": 103079215104, "ts": 1754737153828942, "dur": 18, "ph": "X", "name": "ReadAsync 342", "args": {}}, {"pid": 3100, "tid": 103079215104, "ts": 1754737153828961, "dur": 18, "ph": "X", "name": "ReadAsync 246", "args": {}}, {"pid": 3100, "tid": 103079215104, "ts": 1754737153828981, "dur": 22, "ph": "X", "name": "ReadAsync 128", "args": {}}, {"pid": 3100, "tid": 103079215104, "ts": 1754737153829004, "dur": 30, "ph": "X", "name": "ReadAsync 290", "args": {}}, {"pid": 3100, "tid": 103079215104, "ts": 1754737153829037, "dur": 1, "ph": "X", "name": "ProcessMessages 390", "args": {}}, {"pid": 3100, "tid": 103079215104, "ts": 1754737153829039, "dur": 45, "ph": "X", "name": "ReadAsync 390", "args": {}}, {"pid": 3100, "tid": 103079215104, "ts": 1754737153829087, "dur": 1, "ph": "X", "name": "ProcessMessages 496", "args": {}}, {"pid": 3100, "tid": 103079215104, "ts": 1754737153829088, "dur": 39, "ph": "X", "name": "ReadAsync 496", "args": {}}, {"pid": 3100, "tid": 103079215104, "ts": 1754737153829129, "dur": 31, "ph": "X", "name": "ReadAsync 443", "args": {}}, {"pid": 3100, "tid": 103079215104, "ts": 1754737153829163, "dur": 54, "ph": "X", "name": "ReadAsync 150", "args": {}}, {"pid": 3100, "tid": 103079215104, "ts": 1754737153829220, "dur": 26, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3100, "tid": 103079215104, "ts": 1754737153829248, "dur": 21, "ph": "X", "name": "ReadAsync 485", "args": {}}, {"pid": 3100, "tid": 103079215104, "ts": 1754737153829271, "dur": 31, "ph": "X", "name": "ReadAsync 317", "args": {}}, {"pid": 3100, "tid": 103079215104, "ts": 1754737153829305, "dur": 1, "ph": "X", "name": "ProcessMessages 261", "args": {}}, {"pid": 3100, "tid": 103079215104, "ts": 1754737153829306, "dur": 34, "ph": "X", "name": "ReadAsync 261", "args": {}}, {"pid": 3100, "tid": 103079215104, "ts": 1754737153829342, "dur": 22, "ph": "X", "name": "ReadAsync 539", "args": {}}, {"pid": 3100, "tid": 103079215104, "ts": 1754737153829366, "dur": 22, "ph": "X", "name": "ReadAsync 275", "args": {}}, {"pid": 3100, "tid": 103079215104, "ts": 1754737153829389, "dur": 29, "ph": "X", "name": "ReadAsync 8", "args": {}}, {"pid": 3100, "tid": 103079215104, "ts": 1754737153829422, "dur": 1, "ph": "X", "name": "ProcessMessages 436", "args": {}}, {"pid": 3100, "tid": 103079215104, "ts": 1754737153829424, "dur": 37, "ph": "X", "name": "ReadAsync 436", "args": {}}, {"pid": 3100, "tid": 103079215104, "ts": 1754737153829463, "dur": 27, "ph": "X", "name": "ReadAsync 571", "args": {}}, {"pid": 3100, "tid": 103079215104, "ts": 1754737153829493, "dur": 1, "ph": "X", "name": "ProcessMessages 272", "args": {}}, {"pid": 3100, "tid": 103079215104, "ts": 1754737153829495, "dur": 27, "ph": "X", "name": "ReadAsync 272", "args": {}}, {"pid": 3100, "tid": 103079215104, "ts": 1754737153829524, "dur": 38, "ph": "X", "name": "ReadAsync 450", "args": {}}, {"pid": 3100, "tid": 103079215104, "ts": 1754737153829564, "dur": 21, "ph": "X", "name": "ReadAsync 164", "args": {}}, {"pid": 3100, "tid": 103079215104, "ts": 1754737153829586, "dur": 21, "ph": "X", "name": "ReadAsync 109", "args": {}}, {"pid": 3100, "tid": 103079215104, "ts": 1754737153829609, "dur": 15, "ph": "X", "name": "ReadAsync 144", "args": {}}, {"pid": 3100, "tid": 103079215104, "ts": 1754737153829625, "dur": 25, "ph": "X", "name": "ReadAsync 264", "args": {}}, {"pid": 3100, "tid": 103079215104, "ts": 1754737153829652, "dur": 28, "ph": "X", "name": "ReadAsync 317", "args": {}}, {"pid": 3100, "tid": 103079215104, "ts": 1754737153829681, "dur": 23, "ph": "X", "name": "ReadAsync 412", "args": {}}, {"pid": 3100, "tid": 103079215104, "ts": 1754737153829705, "dur": 1, "ph": "X", "name": "ProcessMessages 560", "args": {}}, {"pid": 3100, "tid": 103079215104, "ts": 1754737153829706, "dur": 21, "ph": "X", "name": "ReadAsync 560", "args": {}}, {"pid": 3100, "tid": 103079215104, "ts": 1754737153829730, "dur": 21, "ph": "X", "name": "ReadAsync 227", "args": {}}, {"pid": 3100, "tid": 103079215104, "ts": 1754737153829752, "dur": 20, "ph": "X", "name": "ReadAsync 409", "args": {}}, {"pid": 3100, "tid": 103079215104, "ts": 1754737153829773, "dur": 42, "ph": "X", "name": "ReadAsync 357", "args": {}}, {"pid": 3100, "tid": 103079215104, "ts": 1754737153829817, "dur": 25, "ph": "X", "name": "ReadAsync 275", "args": {}}, {"pid": 3100, "tid": 103079215104, "ts": 1754737153829844, "dur": 23, "ph": "X", "name": "ReadAsync 632", "args": {}}, {"pid": 3100, "tid": 103079215104, "ts": 1754737153829871, "dur": 31, "ph": "X", "name": "ReadAsync 289", "args": {}}, {"pid": 3100, "tid": 103079215104, "ts": 1754737153829903, "dur": 1, "ph": "X", "name": "ProcessMessages 227", "args": {}}, {"pid": 3100, "tid": 103079215104, "ts": 1754737153829905, "dur": 24, "ph": "X", "name": "ReadAsync 227", "args": {}}, {"pid": 3100, "tid": 103079215104, "ts": 1754737153829931, "dur": 29, "ph": "X", "name": "ReadAsync 392", "args": {}}, {"pid": 3100, "tid": 103079215104, "ts": 1754737153829961, "dur": 26, "ph": "X", "name": "ReadAsync 313", "args": {}}, {"pid": 3100, "tid": 103079215104, "ts": 1754737153829989, "dur": 45, "ph": "X", "name": "ReadAsync 418", "args": {}}, {"pid": 3100, "tid": 103079215104, "ts": 1754737153830037, "dur": 1, "ph": "X", "name": "ProcessMessages 321", "args": {}}, {"pid": 3100, "tid": 103079215104, "ts": 1754737153830038, "dur": 32, "ph": "X", "name": "ReadAsync 321", "args": {}}, {"pid": 3100, "tid": 103079215104, "ts": 1754737153830073, "dur": 57, "ph": "X", "name": "ReadAsync 534", "args": {}}, {"pid": 3100, "tid": 103079215104, "ts": 1754737153830132, "dur": 1, "ph": "X", "name": "ProcessMessages 377", "args": {}}, {"pid": 3100, "tid": 103079215104, "ts": 1754737153830134, "dur": 30, "ph": "X", "name": "ReadAsync 377", "args": {}}, {"pid": 3100, "tid": 103079215104, "ts": 1754737153830166, "dur": 20, "ph": "X", "name": "ReadAsync 731", "args": {}}, {"pid": 3100, "tid": 103079215104, "ts": 1754737153830187, "dur": 30, "ph": "X", "name": "ReadAsync 264", "args": {}}, {"pid": 3100, "tid": 103079215104, "ts": 1754737153830219, "dur": 22, "ph": "X", "name": "ReadAsync 285", "args": {}}, {"pid": 3100, "tid": 103079215104, "ts": 1754737153830243, "dur": 24, "ph": "X", "name": "ReadAsync 190", "args": {}}, {"pid": 3100, "tid": 103079215104, "ts": 1754737153830268, "dur": 1, "ph": "X", "name": "ProcessMessages 308", "args": {}}, {"pid": 3100, "tid": 103079215104, "ts": 1754737153830269, "dur": 22, "ph": "X", "name": "ReadAsync 308", "args": {}}, {"pid": 3100, "tid": 103079215104, "ts": 1754737153830293, "dur": 22, "ph": "X", "name": "ReadAsync 402", "args": {}}, {"pid": 3100, "tid": 103079215104, "ts": 1754737153830316, "dur": 22, "ph": "X", "name": "ReadAsync 270", "args": {}}, {"pid": 3100, "tid": 103079215104, "ts": 1754737153830339, "dur": 23, "ph": "X", "name": "ReadAsync 291", "args": {}}, {"pid": 3100, "tid": 103079215104, "ts": 1754737153830365, "dur": 32, "ph": "X", "name": "ReadAsync 295", "args": {}}, {"pid": 3100, "tid": 103079215104, "ts": 1754737153830398, "dur": 23, "ph": "X", "name": "ReadAsync 271", "args": {}}, {"pid": 3100, "tid": 103079215104, "ts": 1754737153830423, "dur": 1, "ph": "X", "name": "ProcessMessages 413", "args": {}}, {"pid": 3100, "tid": 103079215104, "ts": 1754737153830424, "dur": 25, "ph": "X", "name": "ReadAsync 413", "args": {}}, {"pid": 3100, "tid": 103079215104, "ts": 1754737153830452, "dur": 1, "ph": "X", "name": "ProcessMessages 396", "args": {}}, {"pid": 3100, "tid": 103079215104, "ts": 1754737153830453, "dur": 45, "ph": "X", "name": "ReadAsync 396", "args": {}}, {"pid": 3100, "tid": 103079215104, "ts": 1754737153830500, "dur": 21, "ph": "X", "name": "ReadAsync 417", "args": {}}, {"pid": 3100, "tid": 103079215104, "ts": 1754737153830522, "dur": 20, "ph": "X", "name": "ReadAsync 517", "args": {}}, {"pid": 3100, "tid": 103079215104, "ts": 1754737153830544, "dur": 27, "ph": "X", "name": "ReadAsync 154", "args": {}}, {"pid": 3100, "tid": 103079215104, "ts": 1754737153830574, "dur": 24, "ph": "X", "name": "ReadAsync 131", "args": {}}, {"pid": 3100, "tid": 103079215104, "ts": 1754737153830600, "dur": 22, "ph": "X", "name": "ReadAsync 532", "args": {}}, {"pid": 3100, "tid": 103079215104, "ts": 1754737153830624, "dur": 23, "ph": "X", "name": "ReadAsync 257", "args": {}}, {"pid": 3100, "tid": 103079215104, "ts": 1754737153830648, "dur": 21, "ph": "X", "name": "ReadAsync 182", "args": {}}, {"pid": 3100, "tid": 103079215104, "ts": 1754737153830673, "dur": 22, "ph": "X", "name": "ReadAsync 303", "args": {}}, {"pid": 3100, "tid": 103079215104, "ts": 1754737153830697, "dur": 19, "ph": "X", "name": "ReadAsync 397", "args": {}}, {"pid": 3100, "tid": 103079215104, "ts": 1754737153830717, "dur": 21, "ph": "X", "name": "ReadAsync 161", "args": {}}, {"pid": 3100, "tid": 103079215104, "ts": 1754737153830740, "dur": 21, "ph": "X", "name": "ReadAsync 276", "args": {}}, {"pid": 3100, "tid": 103079215104, "ts": 1754737153830763, "dur": 21, "ph": "X", "name": "ReadAsync 442", "args": {}}, {"pid": 3100, "tid": 103079215104, "ts": 1754737153830785, "dur": 22, "ph": "X", "name": "ReadAsync 372", "args": {}}, {"pid": 3100, "tid": 103079215104, "ts": 1754737153830808, "dur": 22, "ph": "X", "name": "ReadAsync 336", "args": {}}, {"pid": 3100, "tid": 103079215104, "ts": 1754737153830832, "dur": 21, "ph": "X", "name": "ReadAsync 380", "args": {}}, {"pid": 3100, "tid": 103079215104, "ts": 1754737153830854, "dur": 18, "ph": "X", "name": "ReadAsync 341", "args": {}}, {"pid": 3100, "tid": 103079215104, "ts": 1754737153830873, "dur": 21, "ph": "X", "name": "ReadAsync 190", "args": {}}, {"pid": 3100, "tid": 103079215104, "ts": 1754737153830895, "dur": 19, "ph": "X", "name": "ReadAsync 250", "args": {}}, {"pid": 3100, "tid": 103079215104, "ts": 1754737153830916, "dur": 22, "ph": "X", "name": "ReadAsync 307", "args": {}}, {"pid": 3100, "tid": 103079215104, "ts": 1754737153830939, "dur": 18, "ph": "X", "name": "ReadAsync 364", "args": {}}, {"pid": 3100, "tid": 103079215104, "ts": 1754737153830958, "dur": 14, "ph": "X", "name": "ReadAsync 208", "args": {}}, {"pid": 3100, "tid": 103079215104, "ts": 1754737153830974, "dur": 19, "ph": "X", "name": "ReadAsync 265", "args": {}}, {"pid": 3100, "tid": 103079215104, "ts": 1754737153830995, "dur": 36, "ph": "X", "name": "ReadAsync 195", "args": {}}, {"pid": 3100, "tid": 103079215104, "ts": 1754737153831035, "dur": 25, "ph": "X", "name": "ReadAsync 403", "args": {}}, {"pid": 3100, "tid": 103079215104, "ts": 1754737153831062, "dur": 27, "ph": "X", "name": "ReadAsync 209", "args": {}}, {"pid": 3100, "tid": 103079215104, "ts": 1754737153831093, "dur": 41, "ph": "X", "name": "ReadAsync 409", "args": {}}, {"pid": 3100, "tid": 103079215104, "ts": 1754737153831137, "dur": 21, "ph": "X", "name": "ReadAsync 741", "args": {}}, {"pid": 3100, "tid": 103079215104, "ts": 1754737153831160, "dur": 15, "ph": "X", "name": "ReadAsync 659", "args": {}}, {"pid": 3100, "tid": 103079215104, "ts": 1754737153831176, "dur": 28, "ph": "X", "name": "ReadAsync 336", "args": {}}, {"pid": 3100, "tid": 103079215104, "ts": 1754737153831205, "dur": 24, "ph": "X", "name": "ReadAsync 276", "args": {}}, {"pid": 3100, "tid": 103079215104, "ts": 1754737153831230, "dur": 22, "ph": "X", "name": "ReadAsync 386", "args": {}}, {"pid": 3100, "tid": 103079215104, "ts": 1754737153831253, "dur": 21, "ph": "X", "name": "ReadAsync 511", "args": {}}, {"pid": 3100, "tid": 103079215104, "ts": 1754737153831276, "dur": 22, "ph": "X", "name": "ReadAsync 305", "args": {}}, {"pid": 3100, "tid": 103079215104, "ts": 1754737153831299, "dur": 24, "ph": "X", "name": "ReadAsync 349", "args": {}}, {"pid": 3100, "tid": 103079215104, "ts": 1754737153831325, "dur": 18, "ph": "X", "name": "ReadAsync 414", "args": {}}, {"pid": 3100, "tid": 103079215104, "ts": 1754737153831344, "dur": 19, "ph": "X", "name": "ReadAsync 513", "args": {}}, {"pid": 3100, "tid": 103079215104, "ts": 1754737153831365, "dur": 26, "ph": "X", "name": "ReadAsync 219", "args": {}}, {"pid": 3100, "tid": 103079215104, "ts": 1754737153831394, "dur": 32, "ph": "X", "name": "ReadAsync 259", "args": {}}, {"pid": 3100, "tid": 103079215104, "ts": 1754737153831429, "dur": 23, "ph": "X", "name": "ReadAsync 609", "args": {}}, {"pid": 3100, "tid": 103079215104, "ts": 1754737153831453, "dur": 23, "ph": "X", "name": "ReadAsync 338", "args": {}}, {"pid": 3100, "tid": 103079215104, "ts": 1754737153831477, "dur": 21, "ph": "X", "name": "ReadAsync 383", "args": {}}, {"pid": 3100, "tid": 103079215104, "ts": 1754737153831500, "dur": 30, "ph": "X", "name": "ReadAsync 323", "args": {}}, {"pid": 3100, "tid": 103079215104, "ts": 1754737153831531, "dur": 17, "ph": "X", "name": "ReadAsync 382", "args": {}}, {"pid": 3100, "tid": 103079215104, "ts": 1754737153831549, "dur": 104, "ph": "X", "name": "ReadAsync 21", "args": {}}, {"pid": 3100, "tid": 103079215104, "ts": 1754737153831656, "dur": 1, "ph": "X", "name": "ProcessMessages 336", "args": {}}, {"pid": 3100, "tid": 103079215104, "ts": 1754737153831657, "dur": 41, "ph": "X", "name": "ReadAsync 336", "args": {}}, {"pid": 3100, "tid": 103079215104, "ts": 1754737153831701, "dur": 1, "ph": "X", "name": "ProcessMessages 545", "args": {}}, {"pid": 3100, "tid": 103079215104, "ts": 1754737153831703, "dur": 34, "ph": "X", "name": "ReadAsync 545", "args": {}}, {"pid": 3100, "tid": 103079215104, "ts": 1754737153831739, "dur": 36, "ph": "X", "name": "ReadAsync 332", "args": {}}, {"pid": 3100, "tid": 103079215104, "ts": 1754737153831778, "dur": 1, "ph": "X", "name": "ProcessMessages 372", "args": {}}, {"pid": 3100, "tid": 103079215104, "ts": 1754737153831780, "dur": 54, "ph": "X", "name": "ReadAsync 372", "args": {}}, {"pid": 3100, "tid": 103079215104, "ts": 1754737153831839, "dur": 42, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 3100, "tid": 103079215104, "ts": 1754737153831883, "dur": 1, "ph": "X", "name": "ProcessMessages 179", "args": {}}, {"pid": 3100, "tid": 103079215104, "ts": 1754737153831885, "dur": 38, "ph": "X", "name": "ReadAsync 179", "args": {}}, {"pid": 3100, "tid": 103079215104, "ts": 1754737153831926, "dur": 1, "ph": "X", "name": "ProcessMessages 283", "args": {}}, {"pid": 3100, "tid": 103079215104, "ts": 1754737153831928, "dur": 52, "ph": "X", "name": "ReadAsync 283", "args": {}}, {"pid": 3100, "tid": 103079215104, "ts": 1754737153831985, "dur": 41, "ph": "X", "name": "ReadAsync 334", "args": {}}, {"pid": 3100, "tid": 103079215104, "ts": 1754737153832029, "dur": 42, "ph": "X", "name": "ReadAsync 485", "args": {}}, {"pid": 3100, "tid": 103079215104, "ts": 1754737153832073, "dur": 1, "ph": "X", "name": "ProcessMessages 311", "args": {}}, {"pid": 3100, "tid": 103079215104, "ts": 1754737153832075, "dur": 43, "ph": "X", "name": "ReadAsync 311", "args": {}}, {"pid": 3100, "tid": 103079215104, "ts": 1754737153832121, "dur": 1, "ph": "X", "name": "ProcessMessages 556", "args": {}}, {"pid": 3100, "tid": 103079215104, "ts": 1754737153832123, "dur": 32, "ph": "X", "name": "ReadAsync 556", "args": {}}, {"pid": 3100, "tid": 103079215104, "ts": 1754737153832155, "dur": 1, "ph": "X", "name": "ProcessMessages 497", "args": {}}, {"pid": 3100, "tid": 103079215104, "ts": 1754737153832156, "dur": 22, "ph": "X", "name": "ReadAsync 497", "args": {}}, {"pid": 3100, "tid": 103079215104, "ts": 1754737153832179, "dur": 18, "ph": "X", "name": "ReadAsync 260", "args": {}}, {"pid": 3100, "tid": 103079215104, "ts": 1754737153832199, "dur": 38, "ph": "X", "name": "ReadAsync 112", "args": {}}, {"pid": 3100, "tid": 103079215104, "ts": 1754737153832241, "dur": 25, "ph": "X", "name": "ReadAsync 359", "args": {}}, {"pid": 3100, "tid": 103079215104, "ts": 1754737153832268, "dur": 29, "ph": "X", "name": "ReadAsync 682", "args": {}}, {"pid": 3100, "tid": 103079215104, "ts": 1754737153832302, "dur": 34, "ph": "X", "name": "ReadAsync 260", "args": {}}, {"pid": 3100, "tid": 103079215104, "ts": 1754737153832339, "dur": 124, "ph": "X", "name": "ReadAsync 642", "args": {}}, {"pid": 3100, "tid": 103079215104, "ts": 1754737153832467, "dur": 37, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3100, "tid": 103079215104, "ts": 1754737153832507, "dur": 1, "ph": "X", "name": "ProcessMessages 670", "args": {}}, {"pid": 3100, "tid": 103079215104, "ts": 1754737153832509, "dur": 47, "ph": "X", "name": "ReadAsync 670", "args": {}}, {"pid": 3100, "tid": 103079215104, "ts": 1754737153832557, "dur": 32, "ph": "X", "name": "ReadAsync 711", "args": {}}, {"pid": 3100, "tid": 103079215104, "ts": 1754737153832591, "dur": 30, "ph": "X", "name": "ReadAsync 470", "args": {}}, {"pid": 3100, "tid": 103079215104, "ts": 1754737153832623, "dur": 65, "ph": "X", "name": "ReadAsync 298", "args": {}}, {"pid": 3100, "tid": 103079215104, "ts": 1754737153832692, "dur": 31, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3100, "tid": 103079215104, "ts": 1754737153832725, "dur": 37, "ph": "X", "name": "ReadAsync 741", "args": {}}, {"pid": 3100, "tid": 103079215104, "ts": 1754737153832766, "dur": 37, "ph": "X", "name": "ReadAsync 387", "args": {}}, {"pid": 3100, "tid": 103079215104, "ts": 1754737153832806, "dur": 25, "ph": "X", "name": "ReadAsync 865", "args": {}}, {"pid": 3100, "tid": 103079215104, "ts": 1754737153832832, "dur": 18, "ph": "X", "name": "ReadAsync 360", "args": {}}, {"pid": 3100, "tid": 103079215104, "ts": 1754737153832852, "dur": 25, "ph": "X", "name": "ReadAsync 82", "args": {}}, {"pid": 3100, "tid": 103079215104, "ts": 1754737153832879, "dur": 22, "ph": "X", "name": "ReadAsync 327", "args": {}}, {"pid": 3100, "tid": 103079215104, "ts": 1754737153832902, "dur": 23, "ph": "X", "name": "ReadAsync 343", "args": {}}, {"pid": 3100, "tid": 103079215104, "ts": 1754737153832927, "dur": 22, "ph": "X", "name": "ReadAsync 367", "args": {}}, {"pid": 3100, "tid": 103079215104, "ts": 1754737153832950, "dur": 22, "ph": "X", "name": "ReadAsync 327", "args": {}}, {"pid": 3100, "tid": 103079215104, "ts": 1754737153832974, "dur": 22, "ph": "X", "name": "ReadAsync 268", "args": {}}, {"pid": 3100, "tid": 103079215104, "ts": 1754737153832998, "dur": 23, "ph": "X", "name": "ReadAsync 292", "args": {}}, {"pid": 3100, "tid": 103079215104, "ts": 1754737153833022, "dur": 21, "ph": "X", "name": "ReadAsync 61", "args": {}}, {"pid": 3100, "tid": 103079215104, "ts": 1754737153833044, "dur": 33, "ph": "X", "name": "ReadAsync 345", "args": {}}, {"pid": 3100, "tid": 103079215104, "ts": 1754737153833081, "dur": 1, "ph": "X", "name": "ProcessMessages 335", "args": {}}, {"pid": 3100, "tid": 103079215104, "ts": 1754737153833082, "dur": 42, "ph": "X", "name": "ReadAsync 335", "args": {}}, {"pid": 3100, "tid": 103079215104, "ts": 1754737153833126, "dur": 18, "ph": "X", "name": "ReadAsync 552", "args": {}}, {"pid": 3100, "tid": 103079215104, "ts": 1754737153833146, "dur": 31, "ph": "X", "name": "ReadAsync 503", "args": {}}, {"pid": 3100, "tid": 103079215104, "ts": 1754737153833180, "dur": 23, "ph": "X", "name": "ReadAsync 102", "args": {}}, {"pid": 3100, "tid": 103079215104, "ts": 1754737153833204, "dur": 22, "ph": "X", "name": "ReadAsync 280", "args": {}}, {"pid": 3100, "tid": 103079215104, "ts": 1754737153833227, "dur": 21, "ph": "X", "name": "ReadAsync 385", "args": {}}, {"pid": 3100, "tid": 103079215104, "ts": 1754737153833250, "dur": 32, "ph": "X", "name": "ReadAsync 411", "args": {}}, {"pid": 3100, "tid": 103079215104, "ts": 1754737153833284, "dur": 1, "ph": "X", "name": "ProcessMessages 311", "args": {}}, {"pid": 3100, "tid": 103079215104, "ts": 1754737153833285, "dur": 33, "ph": "X", "name": "ReadAsync 311", "args": {}}, {"pid": 3100, "tid": 103079215104, "ts": 1754737153833321, "dur": 22, "ph": "X", "name": "ReadAsync 633", "args": {}}, {"pid": 3100, "tid": 103079215104, "ts": 1754737153833344, "dur": 25, "ph": "X", "name": "ReadAsync 300", "args": {}}, {"pid": 3100, "tid": 103079215104, "ts": 1754737153833371, "dur": 22, "ph": "X", "name": "ReadAsync 77", "args": {}}, {"pid": 3100, "tid": 103079215104, "ts": 1754737153833394, "dur": 1, "ph": "X", "name": "ProcessMessages 270", "args": {}}, {"pid": 3100, "tid": 103079215104, "ts": 1754737153833396, "dur": 21, "ph": "X", "name": "ReadAsync 270", "args": {}}, {"pid": 3100, "tid": 103079215104, "ts": 1754737153833419, "dur": 21, "ph": "X", "name": "ReadAsync 461", "args": {}}, {"pid": 3100, "tid": 103079215104, "ts": 1754737153833442, "dur": 29, "ph": "X", "name": "ReadAsync 465", "args": {}}, {"pid": 3100, "tid": 103079215104, "ts": 1754737153833474, "dur": 1, "ph": "X", "name": "ProcessMessages 291", "args": {}}, {"pid": 3100, "tid": 103079215104, "ts": 1754737153833475, "dur": 33, "ph": "X", "name": "ReadAsync 291", "args": {}}, {"pid": 3100, "tid": 103079215104, "ts": 1754737153833511, "dur": 22, "ph": "X", "name": "ReadAsync 640", "args": {}}, {"pid": 3100, "tid": 103079215104, "ts": 1754737153833535, "dur": 25, "ph": "X", "name": "ReadAsync 188", "args": {}}, {"pid": 3100, "tid": 103079215104, "ts": 1754737153833562, "dur": 23, "ph": "X", "name": "ReadAsync 262", "args": {}}, {"pid": 3100, "tid": 103079215104, "ts": 1754737153833588, "dur": 22, "ph": "X", "name": "ReadAsync 427", "args": {}}, {"pid": 3100, "tid": 103079215104, "ts": 1754737153833611, "dur": 22, "ph": "X", "name": "ReadAsync 441", "args": {}}, {"pid": 3100, "tid": 103079215104, "ts": 1754737153833634, "dur": 23, "ph": "X", "name": "ReadAsync 282", "args": {}}, {"pid": 3100, "tid": 103079215104, "ts": 1754737153833659, "dur": 22, "ph": "X", "name": "ReadAsync 387", "args": {}}, {"pid": 3100, "tid": 103079215104, "ts": 1754737153833682, "dur": 20, "ph": "X", "name": "ReadAsync 364", "args": {}}, {"pid": 3100, "tid": 103079215104, "ts": 1754737153833704, "dur": 22, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 3100, "tid": 103079215104, "ts": 1754737153833727, "dur": 22, "ph": "X", "name": "ReadAsync 387", "args": {}}, {"pid": 3100, "tid": 103079215104, "ts": 1754737153833750, "dur": 22, "ph": "X", "name": "ReadAsync 379", "args": {}}, {"pid": 3100, "tid": 103079215104, "ts": 1754737153833774, "dur": 22, "ph": "X", "name": "ReadAsync 485", "args": {}}, {"pid": 3100, "tid": 103079215104, "ts": 1754737153833798, "dur": 26, "ph": "X", "name": "ReadAsync 403", "args": {}}, {"pid": 3100, "tid": 103079215104, "ts": 1754737153833826, "dur": 18, "ph": "X", "name": "ReadAsync 338", "args": {}}, {"pid": 3100, "tid": 103079215104, "ts": 1754737153833845, "dur": 21, "ph": "X", "name": "ReadAsync 433", "args": {}}, {"pid": 3100, "tid": 103079215104, "ts": 1754737153833869, "dur": 29, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3100, "tid": 103079215104, "ts": 1754737153833900, "dur": 1, "ph": "X", "name": "ProcessMessages 584", "args": {}}, {"pid": 3100, "tid": 103079215104, "ts": 1754737153833901, "dur": 26, "ph": "X", "name": "ReadAsync 584", "args": {}}, {"pid": 3100, "tid": 103079215104, "ts": 1754737153833929, "dur": 23, "ph": "X", "name": "ReadAsync 486", "args": {}}, {"pid": 3100, "tid": 103079215104, "ts": 1754737153833955, "dur": 19, "ph": "X", "name": "ReadAsync 344", "args": {}}, {"pid": 3100, "tid": 103079215104, "ts": 1754737153833976, "dur": 18, "ph": "X", "name": "ReadAsync 489", "args": {}}, {"pid": 3100, "tid": 103079215104, "ts": 1754737153833996, "dur": 23, "ph": "X", "name": "ReadAsync 100", "args": {}}, {"pid": 3100, "tid": 103079215104, "ts": 1754737153834020, "dur": 19, "ph": "X", "name": "ReadAsync 302", "args": {}}, {"pid": 3100, "tid": 103079215104, "ts": 1754737153834041, "dur": 24, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 3100, "tid": 103079215104, "ts": 1754737153834066, "dur": 32, "ph": "X", "name": "ReadAsync 376", "args": {}}, {"pid": 3100, "tid": 103079215104, "ts": 1754737153834101, "dur": 1, "ph": "X", "name": "ProcessMessages 496", "args": {}}, {"pid": 3100, "tid": 103079215104, "ts": 1754737153834103, "dur": 40, "ph": "X", "name": "ReadAsync 496", "args": {}}, {"pid": 3100, "tid": 103079215104, "ts": 1754737153834145, "dur": 31, "ph": "X", "name": "ReadAsync 532", "args": {}}, {"pid": 3100, "tid": 103079215104, "ts": 1754737153834179, "dur": 1, "ph": "X", "name": "ProcessMessages 483", "args": {}}, {"pid": 3100, "tid": 103079215104, "ts": 1754737153834182, "dur": 40, "ph": "X", "name": "ReadAsync 483", "args": {}}, {"pid": 3100, "tid": 103079215104, "ts": 1754737153834226, "dur": 43, "ph": "X", "name": "ReadAsync 176", "args": {}}, {"pid": 3100, "tid": 103079215104, "ts": 1754737153834270, "dur": 23, "ph": "X", "name": "ReadAsync 776", "args": {}}, {"pid": 3100, "tid": 103079215104, "ts": 1754737153834295, "dur": 24, "ph": "X", "name": "ReadAsync 451", "args": {}}, {"pid": 3100, "tid": 103079215104, "ts": 1754737153834321, "dur": 25, "ph": "X", "name": "ReadAsync 375", "args": {}}, {"pid": 3100, "tid": 103079215104, "ts": 1754737153834348, "dur": 15, "ph": "X", "name": "ReadAsync 309", "args": {}}, {"pid": 3100, "tid": 103079215104, "ts": 1754737153834364, "dur": 18, "ph": "X", "name": "ReadAsync 172", "args": {}}, {"pid": 3100, "tid": 103079215104, "ts": 1754737153834383, "dur": 21, "ph": "X", "name": "ReadAsync 150", "args": {}}, {"pid": 3100, "tid": 103079215104, "ts": 1754737153834405, "dur": 22, "ph": "X", "name": "ReadAsync 376", "args": {}}, {"pid": 3100, "tid": 103079215104, "ts": 1754737153834429, "dur": 22, "ph": "X", "name": "ReadAsync 517", "args": {}}, {"pid": 3100, "tid": 103079215104, "ts": 1754737153834452, "dur": 23, "ph": "X", "name": "ReadAsync 339", "args": {}}, {"pid": 3100, "tid": 103079215104, "ts": 1754737153834481, "dur": 39, "ph": "X", "name": "ReadAsync 371", "args": {}}, {"pid": 3100, "tid": 103079215104, "ts": 1754737153834523, "dur": 1, "ph": "X", "name": "ProcessMessages 618", "args": {}}, {"pid": 3100, "tid": 103079215104, "ts": 1754737153834524, "dur": 37, "ph": "X", "name": "ReadAsync 618", "args": {}}, {"pid": 3100, "tid": 103079215104, "ts": 1754737153834563, "dur": 32, "ph": "X", "name": "ReadAsync 98", "args": {}}, {"pid": 3100, "tid": 103079215104, "ts": 1754737153834599, "dur": 32, "ph": "X", "name": "ReadAsync 565", "args": {}}, {"pid": 3100, "tid": 103079215104, "ts": 1754737153834632, "dur": 25, "ph": "X", "name": "ReadAsync 749", "args": {}}, {"pid": 3100, "tid": 103079215104, "ts": 1754737153834660, "dur": 22, "ph": "X", "name": "ReadAsync 302", "args": {}}, {"pid": 3100, "tid": 103079215104, "ts": 1754737153834683, "dur": 25, "ph": "X", "name": "ReadAsync 361", "args": {}}, {"pid": 3100, "tid": 103079215104, "ts": 1754737153834712, "dur": 21, "ph": "X", "name": "ReadAsync 424", "args": {}}, {"pid": 3100, "tid": 103079215104, "ts": 1754737153834735, "dur": 23, "ph": "X", "name": "ReadAsync 332", "args": {}}, {"pid": 3100, "tid": 103079215104, "ts": 1754737153834760, "dur": 21, "ph": "X", "name": "ReadAsync 382", "args": {}}, {"pid": 3100, "tid": 103079215104, "ts": 1754737153834783, "dur": 25, "ph": "X", "name": "ReadAsync 391", "args": {}}, {"pid": 3100, "tid": 103079215104, "ts": 1754737153834810, "dur": 1, "ph": "X", "name": "ProcessMessages 411", "args": {}}, {"pid": 3100, "tid": 103079215104, "ts": 1754737153834811, "dur": 33, "ph": "X", "name": "ReadAsync 411", "args": {}}, {"pid": 3100, "tid": 103079215104, "ts": 1754737153834847, "dur": 1, "ph": "X", "name": "ProcessMessages 573", "args": {}}, {"pid": 3100, "tid": 103079215104, "ts": 1754737153834849, "dur": 32, "ph": "X", "name": "ReadAsync 573", "args": {}}, {"pid": 3100, "tid": 103079215104, "ts": 1754737153834884, "dur": 22, "ph": "X", "name": "ReadAsync 608", "args": {}}, {"pid": 3100, "tid": 103079215104, "ts": 1754737153834908, "dur": 28, "ph": "X", "name": "ReadAsync 82", "args": {}}, {"pid": 3100, "tid": 103079215104, "ts": 1754737153834939, "dur": 1, "ph": "X", "name": "ProcessMessages 367", "args": {}}, {"pid": 3100, "tid": 103079215104, "ts": 1754737153834940, "dur": 31, "ph": "X", "name": "ReadAsync 367", "args": {}}, {"pid": 3100, "tid": 103079215104, "ts": 1754737153834973, "dur": 21, "ph": "X", "name": "ReadAsync 603", "args": {}}, {"pid": 3100, "tid": 103079215104, "ts": 1754737153834996, "dur": 23, "ph": "X", "name": "ReadAsync 204", "args": {}}, {"pid": 3100, "tid": 103079215104, "ts": 1754737153835021, "dur": 21, "ph": "X", "name": "ReadAsync 287", "args": {}}, {"pid": 3100, "tid": 103079215104, "ts": 1754737153835044, "dur": 39, "ph": "X", "name": "ReadAsync 250", "args": {}}, {"pid": 3100, "tid": 103079215104, "ts": 1754737153835087, "dur": 43, "ph": "X", "name": "ReadAsync 274", "args": {}}, {"pid": 3100, "tid": 103079215104, "ts": 1754737153835133, "dur": 30, "ph": "X", "name": "ReadAsync 668", "args": {}}, {"pid": 3100, "tid": 103079215104, "ts": 1754737153835165, "dur": 1, "ph": "X", "name": "ProcessMessages 564", "args": {}}, {"pid": 3100, "tid": 103079215104, "ts": 1754737153835166, "dur": 33, "ph": "X", "name": "ReadAsync 564", "args": {}}, {"pid": 3100, "tid": 103079215104, "ts": 1754737153835201, "dur": 22, "ph": "X", "name": "ReadAsync 513", "args": {}}, {"pid": 3100, "tid": 103079215104, "ts": 1754737153835224, "dur": 17, "ph": "X", "name": "ReadAsync 305", "args": {}}, {"pid": 3100, "tid": 103079215104, "ts": 1754737153835243, "dur": 28, "ph": "X", "name": "ReadAsync 67", "args": {}}, {"pid": 3100, "tid": 103079215104, "ts": 1754737153835275, "dur": 31, "ph": "X", "name": "ReadAsync 330", "args": {}}, {"pid": 3100, "tid": 103079215104, "ts": 1754737153835308, "dur": 21, "ph": "X", "name": "ReadAsync 622", "args": {}}, {"pid": 3100, "tid": 103079215104, "ts": 1754737153835330, "dur": 25, "ph": "X", "name": "ReadAsync 235", "args": {}}, {"pid": 3100, "tid": 103079215104, "ts": 1754737153835358, "dur": 25, "ph": "X", "name": "ReadAsync 294", "args": {}}, {"pid": 3100, "tid": 103079215104, "ts": 1754737153835384, "dur": 17, "ph": "X", "name": "ReadAsync 449", "args": {}}, {"pid": 3100, "tid": 103079215104, "ts": 1754737153835403, "dur": 21, "ph": "X", "name": "ReadAsync 77", "args": {}}, {"pid": 3100, "tid": 103079215104, "ts": 1754737153835426, "dur": 22, "ph": "X", "name": "ReadAsync 408", "args": {}}, {"pid": 3100, "tid": 103079215104, "ts": 1754737153835450, "dur": 29, "ph": "X", "name": "ReadAsync 419", "args": {}}, {"pid": 3100, "tid": 103079215104, "ts": 1754737153835483, "dur": 28, "ph": "X", "name": "ReadAsync 502", "args": {}}, {"pid": 3100, "tid": 103079215104, "ts": 1754737153835513, "dur": 34, "ph": "X", "name": "ReadAsync 536", "args": {}}, {"pid": 3100, "tid": 103079215104, "ts": 1754737153835550, "dur": 27, "ph": "X", "name": "ReadAsync 359", "args": {}}, {"pid": 3100, "tid": 103079215104, "ts": 1754737153835579, "dur": 23, "ph": "X", "name": "ReadAsync 619", "args": {}}, {"pid": 3100, "tid": 103079215104, "ts": 1754737153835604, "dur": 23, "ph": "X", "name": "ReadAsync 237", "args": {}}, {"pid": 3100, "tid": 103079215104, "ts": 1754737153835629, "dur": 24, "ph": "X", "name": "ReadAsync 506", "args": {}}, {"pid": 3100, "tid": 103079215104, "ts": 1754737153835655, "dur": 22, "ph": "X", "name": "ReadAsync 311", "args": {}}, {"pid": 3100, "tid": 103079215104, "ts": 1754737153835679, "dur": 21, "ph": "X", "name": "ReadAsync 367", "args": {}}, {"pid": 3100, "tid": 103079215104, "ts": 1754737153835701, "dur": 17, "ph": "X", "name": "ReadAsync 349", "args": {}}, {"pid": 3100, "tid": 103079215104, "ts": 1754737153835720, "dur": 18, "ph": "X", "name": "ReadAsync 325", "args": {}}, {"pid": 3100, "tid": 103079215104, "ts": 1754737153835739, "dur": 28, "ph": "X", "name": "ReadAsync 244", "args": {}}, {"pid": 3100, "tid": 103079215104, "ts": 1754737153835768, "dur": 22, "ph": "X", "name": "ReadAsync 308", "args": {}}, {"pid": 3100, "tid": 103079215104, "ts": 1754737153835791, "dur": 22, "ph": "X", "name": "ReadAsync 440", "args": {}}, {"pid": 3100, "tid": 103079215104, "ts": 1754737153835815, "dur": 21, "ph": "X", "name": "ReadAsync 285", "args": {}}, {"pid": 3100, "tid": 103079215104, "ts": 1754737153835838, "dur": 22, "ph": "X", "name": "ReadAsync 371", "args": {}}, {"pid": 3100, "tid": 103079215104, "ts": 1754737153835861, "dur": 21, "ph": "X", "name": "ReadAsync 408", "args": {}}, {"pid": 3100, "tid": 103079215104, "ts": 1754737153835884, "dur": 26, "ph": "X", "name": "ReadAsync 83", "args": {}}, {"pid": 3100, "tid": 103079215104, "ts": 1754737153835911, "dur": 18, "ph": "X", "name": "ReadAsync 280", "args": {}}, {"pid": 3100, "tid": 103079215104, "ts": 1754737153835931, "dur": 14, "ph": "X", "name": "ReadAsync 192", "args": {}}, {"pid": 3100, "tid": 103079215104, "ts": 1754737153835946, "dur": 21, "ph": "X", "name": "ReadAsync 248", "args": {}}, {"pid": 3100, "tid": 103079215104, "ts": 1754737153835968, "dur": 26, "ph": "X", "name": "ReadAsync 204", "args": {}}, {"pid": 3100, "tid": 103079215104, "ts": 1754737153835998, "dur": 37, "ph": "X", "name": "ReadAsync 223", "args": {}}, {"pid": 3100, "tid": 103079215104, "ts": 1754737153836038, "dur": 1, "ph": "X", "name": "ProcessMessages 451", "args": {}}, {"pid": 3100, "tid": 103079215104, "ts": 1754737153836039, "dur": 38, "ph": "X", "name": "ReadAsync 451", "args": {}}, {"pid": 3100, "tid": 103079215104, "ts": 1754737153836081, "dur": 23, "ph": "X", "name": "ReadAsync 430", "args": {}}, {"pid": 3100, "tid": 103079215104, "ts": 1754737153836106, "dur": 19, "ph": "X", "name": "ReadAsync 405", "args": {}}, {"pid": 3100, "tid": 103079215104, "ts": 1754737153836126, "dur": 22, "ph": "X", "name": "ReadAsync 232", "args": {}}, {"pid": 3100, "tid": 103079215104, "ts": 1754737153836150, "dur": 22, "ph": "X", "name": "ReadAsync 316", "args": {}}, {"pid": 3100, "tid": 103079215104, "ts": 1754737153836173, "dur": 22, "ph": "X", "name": "ReadAsync 416", "args": {}}, {"pid": 3100, "tid": 103079215104, "ts": 1754737153836196, "dur": 28, "ph": "X", "name": "ReadAsync 272", "args": {}}, {"pid": 3100, "tid": 103079215104, "ts": 1754737153836225, "dur": 21, "ph": "X", "name": "ReadAsync 303", "args": {}}, {"pid": 3100, "tid": 103079215104, "ts": 1754737153836248, "dur": 18, "ph": "X", "name": "ReadAsync 418", "args": {}}, {"pid": 3100, "tid": 103079215104, "ts": 1754737153836268, "dur": 21, "ph": "X", "name": "ReadAsync 160", "args": {}}, {"pid": 3100, "tid": 103079215104, "ts": 1754737153836291, "dur": 22, "ph": "X", "name": "ReadAsync 268", "args": {}}, {"pid": 3100, "tid": 103079215104, "ts": 1754737153836315, "dur": 24, "ph": "X", "name": "ReadAsync 258", "args": {}}, {"pid": 3100, "tid": 103079215104, "ts": 1754737153836340, "dur": 18, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3100, "tid": 103079215104, "ts": 1754737153836359, "dur": 42, "ph": "X", "name": "ReadAsync 228", "args": {}}, {"pid": 3100, "tid": 103079215104, "ts": 1754737153836403, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 3100, "tid": 103079215104, "ts": 1754737153836405, "dur": 45, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3100, "tid": 103079215104, "ts": 1754737153836453, "dur": 1, "ph": "X", "name": "ProcessMessages 392", "args": {}}, {"pid": 3100, "tid": 103079215104, "ts": 1754737153836454, "dur": 34, "ph": "X", "name": "ReadAsync 392", "args": {}}, {"pid": 3100, "tid": 103079215104, "ts": 1754737153836491, "dur": 1, "ph": "X", "name": "ProcessMessages 585", "args": {}}, {"pid": 3100, "tid": 103079215104, "ts": 1754737153836494, "dur": 60, "ph": "X", "name": "ReadAsync 585", "args": {}}, {"pid": 3100, "tid": 103079215104, "ts": 1754737153836556, "dur": 23, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3100, "tid": 103079215104, "ts": 1754737153836582, "dur": 24, "ph": "X", "name": "ReadAsync 296", "args": {}}, {"pid": 3100, "tid": 103079215104, "ts": 1754737153836607, "dur": 26, "ph": "X", "name": "ReadAsync 373", "args": {}}, {"pid": 3100, "tid": 103079215104, "ts": 1754737153836635, "dur": 27, "ph": "X", "name": "ReadAsync 313", "args": {}}, {"pid": 3100, "tid": 103079215104, "ts": 1754737153836664, "dur": 22, "ph": "X", "name": "ReadAsync 433", "args": {}}, {"pid": 3100, "tid": 103079215104, "ts": 1754737153836687, "dur": 22, "ph": "X", "name": "ReadAsync 248", "args": {}}, {"pid": 3100, "tid": 103079215104, "ts": 1754737153836710, "dur": 23, "ph": "X", "name": "ReadAsync 246", "args": {}}, {"pid": 3100, "tid": 103079215104, "ts": 1754737153836734, "dur": 18, "ph": "X", "name": "ReadAsync 323", "args": {}}, {"pid": 3100, "tid": 103079215104, "ts": 1754737153836754, "dur": 18, "ph": "X", "name": "ReadAsync 286", "args": {}}, {"pid": 3100, "tid": 103079215104, "ts": 1754737153836773, "dur": 17, "ph": "X", "name": "ReadAsync 94", "args": {}}, {"pid": 3100, "tid": 103079215104, "ts": 1754737153836792, "dur": 73, "ph": "X", "name": "ReadAsync 143", "args": {}}, {"pid": 3100, "tid": 103079215104, "ts": 1754737153836866, "dur": 24, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3100, "tid": 103079215104, "ts": 1754737153836894, "dur": 28, "ph": "X", "name": "ReadAsync 203", "args": {}}, {"pid": 3100, "tid": 103079215104, "ts": 1754737153836924, "dur": 22, "ph": "X", "name": "ReadAsync 176", "args": {}}, {"pid": 3100, "tid": 103079215104, "ts": 1754737153836949, "dur": 24, "ph": "X", "name": "ReadAsync 343", "args": {}}, {"pid": 3100, "tid": 103079215104, "ts": 1754737153836974, "dur": 22, "ph": "X", "name": "ReadAsync 291", "args": {}}, {"pid": 3100, "tid": 103079215104, "ts": 1754737153836998, "dur": 28, "ph": "X", "name": "ReadAsync 382", "args": {}}, {"pid": 3100, "tid": 103079215104, "ts": 1754737153837027, "dur": 28, "ph": "X", "name": "ReadAsync 229", "args": {}}, {"pid": 3100, "tid": 103079215104, "ts": 1754737153837057, "dur": 1, "ph": "X", "name": "ProcessMessages 390", "args": {}}, {"pid": 3100, "tid": 103079215104, "ts": 1754737153837059, "dur": 42, "ph": "X", "name": "ReadAsync 390", "args": {}}, {"pid": 3100, "tid": 103079215104, "ts": 1754737153837103, "dur": 1, "ph": "X", "name": "ProcessMessages 342", "args": {}}, {"pid": 3100, "tid": 103079215104, "ts": 1754737153837110, "dur": 32, "ph": "X", "name": "ReadAsync 342", "args": {}}, {"pid": 3100, "tid": 103079215104, "ts": 1754737153837145, "dur": 26, "ph": "X", "name": "ReadAsync 148", "args": {}}, {"pid": 3100, "tid": 103079215104, "ts": 1754737153837172, "dur": 21, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3100, "tid": 103079215104, "ts": 1754737153837195, "dur": 34, "ph": "X", "name": "ReadAsync 235", "args": {}}, {"pid": 3100, "tid": 103079215104, "ts": 1754737153837231, "dur": 1, "ph": "X", "name": "ProcessMessages 249", "args": {}}, {"pid": 3100, "tid": 103079215104, "ts": 1754737153837233, "dur": 37, "ph": "X", "name": "ReadAsync 249", "args": {}}, {"pid": 3100, "tid": 103079215104, "ts": 1754737153837271, "dur": 24, "ph": "X", "name": "ReadAsync 628", "args": {}}, {"pid": 3100, "tid": 103079215104, "ts": 1754737153837297, "dur": 22, "ph": "X", "name": "ReadAsync 270", "args": {}}, {"pid": 3100, "tid": 103079215104, "ts": 1754737153837321, "dur": 31, "ph": "X", "name": "ReadAsync 216", "args": {}}, {"pid": 3100, "tid": 103079215104, "ts": 1754737153837356, "dur": 41, "ph": "X", "name": "ReadAsync 243", "args": {}}, {"pid": 3100, "tid": 103079215104, "ts": 1754737153837400, "dur": 1, "ph": "X", "name": "ProcessMessages 414", "args": {}}, {"pid": 3100, "tid": 103079215104, "ts": 1754737153837402, "dur": 30, "ph": "X", "name": "ReadAsync 414", "args": {}}, {"pid": 3100, "tid": 103079215104, "ts": 1754737153837434, "dur": 81, "ph": "X", "name": "ReadAsync 66", "args": {}}, {"pid": 3100, "tid": 103079215104, "ts": 1754737153837519, "dur": 47, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3100, "tid": 103079215104, "ts": 1754737153837568, "dur": 1, "ph": "X", "name": "ProcessMessages 691", "args": {}}, {"pid": 3100, "tid": 103079215104, "ts": 1754737153837571, "dur": 29, "ph": "X", "name": "ReadAsync 691", "args": {}}, {"pid": 3100, "tid": 103079215104, "ts": 1754737153837604, "dur": 54, "ph": "X", "name": "ReadAsync 237", "args": {}}, {"pid": 3100, "tid": 103079215104, "ts": 1754737153837662, "dur": 27, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3100, "tid": 103079215104, "ts": 1754737153837691, "dur": 22, "ph": "X", "name": "ReadAsync 404", "args": {}}, {"pid": 3100, "tid": 103079215104, "ts": 1754737153837715, "dur": 22, "ph": "X", "name": "ReadAsync 286", "args": {}}, {"pid": 3100, "tid": 103079215104, "ts": 1754737153837740, "dur": 23, "ph": "X", "name": "ReadAsync 276", "args": {}}, {"pid": 3100, "tid": 103079215104, "ts": 1754737153837765, "dur": 45, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 3100, "tid": 103079215104, "ts": 1754737153837814, "dur": 30, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3100, "tid": 103079215104, "ts": 1754737153837846, "dur": 21, "ph": "X", "name": "ReadAsync 305", "args": {}}, {"pid": 3100, "tid": 103079215104, "ts": 1754737153837869, "dur": 27, "ph": "X", "name": "ReadAsync 352", "args": {}}, {"pid": 3100, "tid": 103079215104, "ts": 1754737153837898, "dur": 27, "ph": "X", "name": "ReadAsync 291", "args": {}}, {"pid": 3100, "tid": 103079215104, "ts": 1754737153837926, "dur": 31, "ph": "X", "name": "ReadAsync 52", "args": {}}, {"pid": 3100, "tid": 103079215104, "ts": 1754737153837959, "dur": 22, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3100, "tid": 103079215104, "ts": 1754737153837982, "dur": 14, "ph": "X", "name": "ReadAsync 311", "args": {}}, {"pid": 3100, "tid": 103079215104, "ts": 1754737153837999, "dur": 20, "ph": "X", "name": "ReadAsync 137", "args": {}}, {"pid": 3100, "tid": 103079215104, "ts": 1754737153838020, "dur": 21, "ph": "X", "name": "ReadAsync 234", "args": {}}, {"pid": 3100, "tid": 103079215104, "ts": 1754737153838043, "dur": 34, "ph": "X", "name": "ReadAsync 297", "args": {}}, {"pid": 3100, "tid": 103079215104, "ts": 1754737153838081, "dur": 46, "ph": "X", "name": "ReadAsync 57", "args": {}}, {"pid": 3100, "tid": 103079215104, "ts": 1754737153838131, "dur": 41, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3100, "tid": 103079215104, "ts": 1754737153838176, "dur": 1, "ph": "X", "name": "ProcessMessages 786", "args": {}}, {"pid": 3100, "tid": 103079215104, "ts": 1754737153838177, "dur": 36, "ph": "X", "name": "ReadAsync 786", "args": {}}, {"pid": 3100, "tid": 103079215104, "ts": 1754737153838215, "dur": 51, "ph": "X", "name": "ReadAsync 370", "args": {}}, {"pid": 3100, "tid": 103079215104, "ts": 1754737153838270, "dur": 36, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3100, "tid": 103079215104, "ts": 1754737153838308, "dur": 29, "ph": "X", "name": "ReadAsync 604", "args": {}}, {"pid": 3100, "tid": 103079215104, "ts": 1754737153838338, "dur": 22, "ph": "X", "name": "ReadAsync 621", "args": {}}, {"pid": 3100, "tid": 103079215104, "ts": 1754737153838364, "dur": 50, "ph": "X", "name": "ReadAsync 58", "args": {}}, {"pid": 3100, "tid": 103079215104, "ts": 1754737153838416, "dur": 29, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3100, "tid": 103079215104, "ts": 1754737153838446, "dur": 34, "ph": "X", "name": "ReadAsync 519", "args": {}}, {"pid": 3100, "tid": 103079215104, "ts": 1754737153838483, "dur": 19, "ph": "X", "name": "ReadAsync 493", "args": {}}, {"pid": 3100, "tid": 103079215104, "ts": 1754737153838503, "dur": 1, "ph": "X", "name": "ProcessMessages 225", "args": {}}, {"pid": 3100, "tid": 103079215104, "ts": 1754737153838504, "dur": 55, "ph": "X", "name": "ReadAsync 225", "args": {}}, {"pid": 3100, "tid": 103079215104, "ts": 1754737153838561, "dur": 22, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3100, "tid": 103079215104, "ts": 1754737153838585, "dur": 21, "ph": "X", "name": "ReadAsync 374", "args": {}}, {"pid": 3100, "tid": 103079215104, "ts": 1754737153838608, "dur": 22, "ph": "X", "name": "ReadAsync 134", "args": {}}, {"pid": 3100, "tid": 103079215104, "ts": 1754737153838631, "dur": 30, "ph": "X", "name": "ReadAsync 390", "args": {}}, {"pid": 3100, "tid": 103079215104, "ts": 1754737153838664, "dur": 1, "ph": "X", "name": "ProcessMessages 196", "args": {}}, {"pid": 3100, "tid": 103079215104, "ts": 1754737153838666, "dur": 43, "ph": "X", "name": "ReadAsync 196", "args": {}}, {"pid": 3100, "tid": 103079215104, "ts": 1754737153838710, "dur": 22, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3100, "tid": 103079215104, "ts": 1754737153838733, "dur": 1, "ph": "X", "name": "ProcessMessages 221", "args": {}}, {"pid": 3100, "tid": 103079215104, "ts": 1754737153838734, "dur": 26, "ph": "X", "name": "ReadAsync 221", "args": {}}, {"pid": 3100, "tid": 103079215104, "ts": 1754737153838762, "dur": 22, "ph": "X", "name": "ReadAsync 377", "args": {}}, {"pid": 3100, "tid": 103079215104, "ts": 1754737153838785, "dur": 17, "ph": "X", "name": "ReadAsync 417", "args": {}}, {"pid": 3100, "tid": 103079215104, "ts": 1754737153838804, "dur": 45, "ph": "X", "name": "ReadAsync 57", "args": {}}, {"pid": 3100, "tid": 103079215104, "ts": 1754737153838850, "dur": 21, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3100, "tid": 103079215104, "ts": 1754737153838873, "dur": 23, "ph": "X", "name": "ReadAsync 283", "args": {}}, {"pid": 3100, "tid": 103079215104, "ts": 1754737153838897, "dur": 17, "ph": "X", "name": "ReadAsync 297", "args": {}}, {"pid": 3100, "tid": 103079215104, "ts": 1754737153838917, "dur": 21, "ph": "X", "name": "ReadAsync 213", "args": {}}, {"pid": 3100, "tid": 103079215104, "ts": 1754737153838939, "dur": 62, "ph": "X", "name": "ReadAsync 237", "args": {}}, {"pid": 3100, "tid": 103079215104, "ts": 1754737153839005, "dur": 41, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3100, "tid": 103079215104, "ts": 1754737153839049, "dur": 1, "ph": "X", "name": "ProcessMessages 369", "args": {}}, {"pid": 3100, "tid": 103079215104, "ts": 1754737153839051, "dur": 47, "ph": "X", "name": "ReadAsync 369", "args": {}}, {"pid": 3100, "tid": 103079215104, "ts": 1754737153839101, "dur": 1, "ph": "X", "name": "ProcessMessages 368", "args": {}}, {"pid": 3100, "tid": 103079215104, "ts": 1754737153839102, "dur": 42, "ph": "X", "name": "ReadAsync 368", "args": {}}, {"pid": 3100, "tid": 103079215104, "ts": 1754737153839146, "dur": 2, "ph": "X", "name": "ProcessMessages 245", "args": {}}, {"pid": 3100, "tid": 103079215104, "ts": 1754737153839148, "dur": 75, "ph": "X", "name": "ReadAsync 245", "args": {}}, {"pid": 3100, "tid": 103079215104, "ts": 1754737153839227, "dur": 28, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3100, "tid": 103079215104, "ts": 1754737153839256, "dur": 22, "ph": "X", "name": "ReadAsync 244", "args": {}}, {"pid": 3100, "tid": 103079215104, "ts": 1754737153839281, "dur": 49, "ph": "X", "name": "ReadAsync 201", "args": {}}, {"pid": 3100, "tid": 103079215104, "ts": 1754737153839333, "dur": 25, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3100, "tid": 103079215104, "ts": 1754737153839360, "dur": 30, "ph": "X", "name": "ReadAsync 383", "args": {}}, {"pid": 3100, "tid": 103079215104, "ts": 1754737153839392, "dur": 29, "ph": "X", "name": "ReadAsync 233", "args": {}}, {"pid": 3100, "tid": 103079215104, "ts": 1754737153839424, "dur": 1, "ph": "X", "name": "ProcessMessages 419", "args": {}}, {"pid": 3100, "tid": 103079215104, "ts": 1754737153839425, "dur": 40, "ph": "X", "name": "ReadAsync 419", "args": {}}, {"pid": 3100, "tid": 103079215104, "ts": 1754737153839467, "dur": 25, "ph": "X", "name": "ReadAsync 425", "args": {}}, {"pid": 3100, "tid": 103079215104, "ts": 1754737153839495, "dur": 22, "ph": "X", "name": "ReadAsync 289", "args": {}}, {"pid": 3100, "tid": 103079215104, "ts": 1754737153839519, "dur": 18, "ph": "X", "name": "ReadAsync 365", "args": {}}, {"pid": 3100, "tid": 103079215104, "ts": 1754737153839539, "dur": 19, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 3100, "tid": 103079215104, "ts": 1754737153839561, "dur": 1, "ph": "X", "name": "ProcessMessages 187", "args": {}}, {"pid": 3100, "tid": 103079215104, "ts": 1754737153839562, "dur": 72, "ph": "X", "name": "ReadAsync 187", "args": {}}, {"pid": 3100, "tid": 103079215104, "ts": 1754737153839635, "dur": 22, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3100, "tid": 103079215104, "ts": 1754737153839659, "dur": 23, "ph": "X", "name": "ReadAsync 337", "args": {}}, {"pid": 3100, "tid": 103079215104, "ts": 1754737153839684, "dur": 23, "ph": "X", "name": "ReadAsync 355", "args": {}}, {"pid": 3100, "tid": 103079215104, "ts": 1754737153839709, "dur": 21, "ph": "X", "name": "ReadAsync 410", "args": {}}, {"pid": 3100, "tid": 103079215104, "ts": 1754737153839733, "dur": 19, "ph": "X", "name": "ReadAsync 346", "args": {}}, {"pid": 3100, "tid": 103079215104, "ts": 1754737153839753, "dur": 25, "ph": "X", "name": "ReadAsync 285", "args": {}}, {"pid": 3100, "tid": 103079215104, "ts": 1754737153839780, "dur": 1, "ph": "X", "name": "ProcessMessages 212", "args": {}}, {"pid": 3100, "tid": 103079215104, "ts": 1754737153839782, "dur": 35, "ph": "X", "name": "ReadAsync 212", "args": {}}, {"pid": 3100, "tid": 103079215104, "ts": 1754737153839820, "dur": 1, "ph": "X", "name": "ProcessMessages 419", "args": {}}, {"pid": 3100, "tid": 103079215104, "ts": 1754737153839822, "dur": 34, "ph": "X", "name": "ReadAsync 419", "args": {}}, {"pid": 3100, "tid": 103079215104, "ts": 1754737153839858, "dur": 19, "ph": "X", "name": "ReadAsync 346", "args": {}}, {"pid": 3100, "tid": 103079215104, "ts": 1754737153839878, "dur": 67, "ph": "X", "name": "ReadAsync 57", "args": {}}, {"pid": 3100, "tid": 103079215104, "ts": 1754737153839947, "dur": 21, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3100, "tid": 103079215104, "ts": 1754737153839969, "dur": 29, "ph": "X", "name": "ReadAsync 265", "args": {}}, {"pid": 3100, "tid": 103079215104, "ts": 1754737153840000, "dur": 24, "ph": "X", "name": "ReadAsync 280", "args": {}}, {"pid": 3100, "tid": 103079215104, "ts": 1754737153840025, "dur": 20, "ph": "X", "name": "ReadAsync 354", "args": {}}, {"pid": 3100, "tid": 103079215104, "ts": 1754737153840047, "dur": 25, "ph": "X", "name": "ReadAsync 225", "args": {}}, {"pid": 3100, "tid": 103079215104, "ts": 1754737153840075, "dur": 30, "ph": "X", "name": "ReadAsync 331", "args": {}}, {"pid": 3100, "tid": 103079215104, "ts": 1754737153840108, "dur": 1, "ph": "X", "name": "ProcessMessages 21", "args": {}}, {"pid": 3100, "tid": 103079215104, "ts": 1754737153840109, "dur": 34, "ph": "X", "name": "ReadAsync 21", "args": {}}, {"pid": 3100, "tid": 103079215104, "ts": 1754737153840146, "dur": 36, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3100, "tid": 103079215104, "ts": 1754737153840185, "dur": 1, "ph": "X", "name": "ProcessMessages 392", "args": {}}, {"pid": 3100, "tid": 103079215104, "ts": 1754737153840187, "dur": 32, "ph": "X", "name": "ReadAsync 392", "args": {}}, {"pid": 3100, "tid": 103079215104, "ts": 1754737153840220, "dur": 26, "ph": "X", "name": "ReadAsync 460", "args": {}}, {"pid": 3100, "tid": 103079215104, "ts": 1754737153840249, "dur": 24, "ph": "X", "name": "ReadAsync 258", "args": {}}, {"pid": 3100, "tid": 103079215104, "ts": 1754737153840274, "dur": 18, "ph": "X", "name": "ReadAsync 343", "args": {}}, {"pid": 3100, "tid": 103079215104, "ts": 1754737153840294, "dur": 18, "ph": "X", "name": "ReadAsync 262", "args": {}}, {"pid": 3100, "tid": 103079215104, "ts": 1754737153840313, "dur": 32, "ph": "X", "name": "ReadAsync 118", "args": {}}, {"pid": 3100, "tid": 103079215104, "ts": 1754737153840348, "dur": 1, "ph": "X", "name": "ProcessMessages 260", "args": {}}, {"pid": 3100, "tid": 103079215104, "ts": 1754737153840349, "dur": 38, "ph": "X", "name": "ReadAsync 260", "args": {}}, {"pid": 3100, "tid": 103079215104, "ts": 1754737153840389, "dur": 23, "ph": "X", "name": "ReadAsync 487", "args": {}}, {"pid": 3100, "tid": 103079215104, "ts": 1754737153840415, "dur": 76, "ph": "X", "name": "ReadAsync 237", "args": {}}, {"pid": 3100, "tid": 103079215104, "ts": 1754737153840495, "dur": 33, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3100, "tid": 103079215104, "ts": 1754737153840529, "dur": 25, "ph": "X", "name": "ReadAsync 392", "args": {}}, {"pid": 3100, "tid": 103079215104, "ts": 1754737153840557, "dur": 1, "ph": "X", "name": "ProcessMessages 340", "args": {}}, {"pid": 3100, "tid": 103079215104, "ts": 1754737153840559, "dur": 25, "ph": "X", "name": "ReadAsync 340", "args": {}}, {"pid": 3100, "tid": 103079215104, "ts": 1754737153840585, "dur": 59, "ph": "X", "name": "ReadAsync 289", "args": {}}, {"pid": 3100, "tid": 103079215104, "ts": 1754737153840646, "dur": 23, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3100, "tid": 103079215104, "ts": 1754737153840672, "dur": 26, "ph": "X", "name": "ReadAsync 249", "args": {}}, {"pid": 3100, "tid": 103079215104, "ts": 1754737153840700, "dur": 22, "ph": "X", "name": "ReadAsync 326", "args": {}}, {"pid": 3100, "tid": 103079215104, "ts": 1754737153840724, "dur": 17, "ph": "X", "name": "ReadAsync 394", "args": {}}, {"pid": 3100, "tid": 103079215104, "ts": 1754737153840743, "dur": 63, "ph": "X", "name": "ReadAsync 67", "args": {}}, {"pid": 3100, "tid": 103079215104, "ts": 1754737153840808, "dur": 22, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3100, "tid": 103079215104, "ts": 1754737153840831, "dur": 21, "ph": "X", "name": "ReadAsync 331", "args": {}}, {"pid": 3100, "tid": 103079215104, "ts": 1754737153840854, "dur": 22, "ph": "X", "name": "ReadAsync 288", "args": {}}, {"pid": 3100, "tid": 103079215104, "ts": 1754737153840878, "dur": 25, "ph": "X", "name": "ReadAsync 291", "args": {}}, {"pid": 3100, "tid": 103079215104, "ts": 1754737153840905, "dur": 19, "ph": "X", "name": "ReadAsync 307", "args": {}}, {"pid": 3100, "tid": 103079215104, "ts": 1754737153840925, "dur": 23, "ph": "X", "name": "ReadAsync 457", "args": {}}, {"pid": 3100, "tid": 103079215104, "ts": 1754737153840952, "dur": 29, "ph": "X", "name": "ReadAsync 209", "args": {}}, {"pid": 3100, "tid": 103079215104, "ts": 1754737153840983, "dur": 23, "ph": "X", "name": "ReadAsync 488", "args": {}}, {"pid": 3100, "tid": 103079215104, "ts": 1754737153841008, "dur": 25, "ph": "X", "name": "ReadAsync 317", "args": {}}, {"pid": 3100, "tid": 103079215104, "ts": 1754737153841037, "dur": 1, "ph": "X", "name": "ProcessMessages 127", "args": {}}, {"pid": 3100, "tid": 103079215104, "ts": 1754737153841038, "dur": 31, "ph": "X", "name": "ReadAsync 127", "args": {}}, {"pid": 3100, "tid": 103079215104, "ts": 1754737153841071, "dur": 55, "ph": "X", "name": "ReadAsync 196", "args": {}}, {"pid": 3100, "tid": 103079215104, "ts": 1754737153841130, "dur": 32, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3100, "tid": 103079215104, "ts": 1754737153841164, "dur": 35, "ph": "X", "name": "ReadAsync 505", "args": {}}, {"pid": 3100, "tid": 103079215104, "ts": 1754737153841200, "dur": 27, "ph": "X", "name": "ReadAsync 229", "args": {}}, {"pid": 3100, "tid": 103079215104, "ts": 1754737153841231, "dur": 1, "ph": "X", "name": "ProcessMessages 354", "args": {}}, {"pid": 3100, "tid": 103079215104, "ts": 1754737153841232, "dur": 70, "ph": "X", "name": "ReadAsync 354", "args": {}}, {"pid": 3100, "tid": 103079215104, "ts": 1754737153841306, "dur": 29, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3100, "tid": 103079215104, "ts": 1754737153841338, "dur": 1, "ph": "X", "name": "ProcessMessages 502", "args": {}}, {"pid": 3100, "tid": 103079215104, "ts": 1754737153841340, "dur": 32, "ph": "X", "name": "ReadAsync 502", "args": {}}, {"pid": 3100, "tid": 103079215104, "ts": 1754737153841375, "dur": 21, "ph": "X", "name": "ReadAsync 569", "args": {}}, {"pid": 3100, "tid": 103079215104, "ts": 1754737153841397, "dur": 77, "ph": "X", "name": "ReadAsync 52", "args": {}}, {"pid": 3100, "tid": 103079215104, "ts": 1754737153841480, "dur": 35, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3100, "tid": 103079215104, "ts": 1754737153841518, "dur": 42, "ph": "X", "name": "ReadAsync 260", "args": {}}, {"pid": 3100, "tid": 103079215104, "ts": 1754737153841563, "dur": 1, "ph": "X", "name": "ProcessMessages 589", "args": {}}, {"pid": 3100, "tid": 103079215104, "ts": 1754737153841565, "dur": 26, "ph": "X", "name": "ReadAsync 589", "args": {}}, {"pid": 3100, "tid": 103079215104, "ts": 1754737153841592, "dur": 73, "ph": "X", "name": "ReadAsync 248", "args": {}}, {"pid": 3100, "tid": 103079215104, "ts": 1754737153841668, "dur": 24, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3100, "tid": 103079215104, "ts": 1754737153841693, "dur": 21, "ph": "X", "name": "ReadAsync 399", "args": {}}, {"pid": 3100, "tid": 103079215104, "ts": 1754737153841716, "dur": 23, "ph": "X", "name": "ReadAsync 299", "args": {}}, {"pid": 3100, "tid": 103079215104, "ts": 1754737153841742, "dur": 21, "ph": "X", "name": "ReadAsync 295", "args": {}}, {"pid": 3100, "tid": 103079215104, "ts": 1754737153841764, "dur": 42, "ph": "X", "name": "ReadAsync 52", "args": {}}, {"pid": 3100, "tid": 103079215104, "ts": 1754737153841808, "dur": 26, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3100, "tid": 103079215104, "ts": 1754737153841837, "dur": 21, "ph": "X", "name": "ReadAsync 233", "args": {}}, {"pid": 3100, "tid": 103079215104, "ts": 1754737153841859, "dur": 21, "ph": "X", "name": "ReadAsync 472", "args": {}}, {"pid": 3100, "tid": 103079215104, "ts": 1754737153841882, "dur": 19, "ph": "X", "name": "ReadAsync 303", "args": {}}, {"pid": 3100, "tid": 103079215104, "ts": 1754737153841902, "dur": 71, "ph": "X", "name": "ReadAsync 62", "args": {}}, {"pid": 3100, "tid": 103079215104, "ts": 1754737153841975, "dur": 60, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3100, "tid": 103079215104, "ts": 1754737153842040, "dur": 2, "ph": "X", "name": "ProcessMessages 818", "args": {}}, {"pid": 3100, "tid": 103079215104, "ts": 1754737153842043, "dur": 53, "ph": "X", "name": "ReadAsync 818", "args": {}}, {"pid": 3100, "tid": 103079215104, "ts": 1754737153842099, "dur": 1, "ph": "X", "name": "ProcessMessages 869", "args": {}}, {"pid": 3100, "tid": 103079215104, "ts": 1754737153842101, "dur": 28, "ph": "X", "name": "ReadAsync 869", "args": {}}, {"pid": 3100, "tid": 103079215104, "ts": 1754737153842132, "dur": 36, "ph": "X", "name": "ReadAsync 336", "args": {}}, {"pid": 3100, "tid": 103079215104, "ts": 1754737153842170, "dur": 1, "ph": "X", "name": "ProcessMessages 434", "args": {}}, {"pid": 3100, "tid": 103079215104, "ts": 1754737153842172, "dur": 49, "ph": "X", "name": "ReadAsync 434", "args": {}}, {"pid": 3100, "tid": 103079215104, "ts": 1754737153842225, "dur": 31, "ph": "X", "name": "ReadAsync 357", "args": {}}, {"pid": 3100, "tid": 103079215104, "ts": 1754737153842258, "dur": 52, "ph": "X", "name": "ReadAsync 52", "args": {}}, {"pid": 3100, "tid": 103079215104, "ts": 1754737153842313, "dur": 25, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3100, "tid": 103079215104, "ts": 1754737153842339, "dur": 24, "ph": "X", "name": "ReadAsync 350", "args": {}}, {"pid": 3100, "tid": 103079215104, "ts": 1754737153842365, "dur": 21, "ph": "X", "name": "ReadAsync 317", "args": {}}, {"pid": 3100, "tid": 103079215104, "ts": 1754737153842388, "dur": 27, "ph": "X", "name": "ReadAsync 479", "args": {}}, {"pid": 3100, "tid": 103079215104, "ts": 1754737153842419, "dur": 32, "ph": "X", "name": "ReadAsync 309", "args": {}}, {"pid": 3100, "tid": 103079215104, "ts": 1754737153842455, "dur": 25, "ph": "X", "name": "ReadAsync 566", "args": {}}, {"pid": 3100, "tid": 103079215104, "ts": 1754737153842482, "dur": 21, "ph": "X", "name": "ReadAsync 529", "args": {}}, {"pid": 3100, "tid": 103079215104, "ts": 1754737153842504, "dur": 28, "ph": "X", "name": "ReadAsync 427", "args": {}}, {"pid": 3100, "tid": 103079215104, "ts": 1754737153842534, "dur": 18, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3100, "tid": 103079215104, "ts": 1754737153842554, "dur": 75, "ph": "X", "name": "ReadAsync 228", "args": {}}, {"pid": 3100, "tid": 103079215104, "ts": 1754737153842631, "dur": 26, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3100, "tid": 103079215104, "ts": 1754737153842658, "dur": 22, "ph": "X", "name": "ReadAsync 287", "args": {}}, {"pid": 3100, "tid": 103079215104, "ts": 1754737153842681, "dur": 14, "ph": "X", "name": "ReadAsync 501", "args": {}}, {"pid": 3100, "tid": 103079215104, "ts": 1754737153842696, "dur": 23, "ph": "X", "name": "ReadAsync 256", "args": {}}, {"pid": 3100, "tid": 103079215104, "ts": 1754737153842721, "dur": 22, "ph": "X", "name": "ReadAsync 270", "args": {}}, {"pid": 3100, "tid": 103079215104, "ts": 1754737153842745, "dur": 22, "ph": "X", "name": "ReadAsync 427", "args": {}}, {"pid": 3100, "tid": 103079215104, "ts": 1754737153842768, "dur": 18, "ph": "X", "name": "ReadAsync 287", "args": {}}, {"pid": 3100, "tid": 103079215104, "ts": 1754737153842788, "dur": 17, "ph": "X", "name": "ReadAsync 402", "args": {}}, {"pid": 3100, "tid": 103079215104, "ts": 1754737153842807, "dur": 17, "ph": "X", "name": "ReadAsync 323", "args": {}}, {"pid": 3100, "tid": 103079215104, "ts": 1754737153842826, "dur": 22, "ph": "X", "name": "ReadAsync 90", "args": {}}, {"pid": 3100, "tid": 103079215104, "ts": 1754737153842850, "dur": 17, "ph": "X", "name": "ReadAsync 181", "args": {}}, {"pid": 3100, "tid": 103079215104, "ts": 1754737153842868, "dur": 48, "ph": "X", "name": "ReadAsync 56", "args": {}}, {"pid": 3100, "tid": 103079215104, "ts": 1754737153842917, "dur": 21, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3100, "tid": 103079215104, "ts": 1754737153842939, "dur": 23, "ph": "X", "name": "ReadAsync 254", "args": {}}, {"pid": 3100, "tid": 103079215104, "ts": 1754737153842964, "dur": 15, "ph": "X", "name": "ReadAsync 314", "args": {}}, {"pid": 3100, "tid": 103079215104, "ts": 1754737153842980, "dur": 28, "ph": "X", "name": "ReadAsync 403", "args": {}}, {"pid": 3100, "tid": 103079215104, "ts": 1754737153843013, "dur": 30, "ph": "X", "name": "ReadAsync 178", "args": {}}, {"pid": 3100, "tid": 103079215104, "ts": 1754737153843047, "dur": 33, "ph": "X", "name": "ReadAsync 62", "args": {}}, {"pid": 3100, "tid": 103079215104, "ts": 1754737153843084, "dur": 58, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3100, "tid": 103079215104, "ts": 1754737153843146, "dur": 44, "ph": "X", "name": "ReadAsync 401", "args": {}}, {"pid": 3100, "tid": 103079215104, "ts": 1754737153843193, "dur": 1, "ph": "X", "name": "ProcessMessages 551", "args": {}}, {"pid": 3100, "tid": 103079215104, "ts": 1754737153843195, "dur": 26, "ph": "X", "name": "ReadAsync 551", "args": {}}, {"pid": 3100, "tid": 103079215104, "ts": 1754737153843222, "dur": 59, "ph": "X", "name": "ReadAsync 225", "args": {}}, {"pid": 3100, "tid": 103079215104, "ts": 1754737153843286, "dur": 36, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3100, "tid": 103079215104, "ts": 1754737153843323, "dur": 20, "ph": "X", "name": "ReadAsync 710", "args": {}}, {"pid": 3100, "tid": 103079215104, "ts": 1754737153843344, "dur": 1, "ph": "X", "name": "ProcessMessages 376", "args": {}}, {"pid": 3100, "tid": 103079215104, "ts": 1754737153843346, "dur": 70, "ph": "X", "name": "ReadAsync 376", "args": {}}, {"pid": 3100, "tid": 103079215104, "ts": 1754737153843419, "dur": 1, "ph": "X", "name": "ProcessMessages 140", "args": {}}, {"pid": 3100, "tid": 103079215104, "ts": 1754737153843420, "dur": 38, "ph": "X", "name": "ReadAsync 140", "args": {}}, {"pid": 3100, "tid": 103079215104, "ts": 1754737153843462, "dur": 1, "ph": "X", "name": "ProcessMessages 348", "args": {}}, {"pid": 3100, "tid": 103079215104, "ts": 1754737153843463, "dur": 44, "ph": "X", "name": "ReadAsync 348", "args": {}}, {"pid": 3100, "tid": 103079215104, "ts": 1754737153843510, "dur": 1, "ph": "X", "name": "ProcessMessages 602", "args": {}}, {"pid": 3100, "tid": 103079215104, "ts": 1754737153843512, "dur": 40, "ph": "X", "name": "ReadAsync 602", "args": {}}, {"pid": 3100, "tid": 103079215104, "ts": 1754737153843553, "dur": 59, "ph": "X", "name": "ReadAsync 245", "args": {}}, {"pid": 3100, "tid": 103079215104, "ts": 1754737153843615, "dur": 33, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3100, "tid": 103079215104, "ts": 1754737153843651, "dur": 24, "ph": "X", "name": "ReadAsync 484", "args": {}}, {"pid": 3100, "tid": 103079215104, "ts": 1754737153843677, "dur": 23, "ph": "X", "name": "ReadAsync 33", "args": {}}, {"pid": 3100, "tid": 103079215104, "ts": 1754737153843701, "dur": 27, "ph": "X", "name": "ReadAsync 441", "args": {}}, {"pid": 3100, "tid": 103079215104, "ts": 1754737153843731, "dur": 1, "ph": "X", "name": "ProcessMessages 144", "args": {}}, {"pid": 3100, "tid": 103079215104, "ts": 1754737153843733, "dur": 71, "ph": "X", "name": "ReadAsync 144", "args": {}}, {"pid": 3100, "tid": 103079215104, "ts": 1754737153843805, "dur": 27, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3100, "tid": 103079215104, "ts": 1754737153843833, "dur": 30, "ph": "X", "name": "ReadAsync 338", "args": {}}, {"pid": 3100, "tid": 103079215104, "ts": 1754737153843867, "dur": 1, "ph": "X", "name": "ProcessMessages 430", "args": {}}, {"pid": 3100, "tid": 103079215104, "ts": 1754737153843868, "dur": 52, "ph": "X", "name": "ReadAsync 430", "args": {}}, {"pid": 3100, "tid": 103079215104, "ts": 1754737153843923, "dur": 1, "ph": "X", "name": "ProcessMessages 623", "args": {}}, {"pid": 3100, "tid": 103079215104, "ts": 1754737153843924, "dur": 40, "ph": "X", "name": "ReadAsync 623", "args": {}}, {"pid": 3100, "tid": 103079215104, "ts": 1754737153843966, "dur": 36, "ph": "X", "name": "ReadAsync 593", "args": {}}, {"pid": 3100, "tid": 103079215104, "ts": 1754737153844006, "dur": 1, "ph": "X", "name": "ProcessMessages 368", "args": {}}, {"pid": 3100, "tid": 103079215104, "ts": 1754737153844008, "dur": 45, "ph": "X", "name": "ReadAsync 368", "args": {}}, {"pid": 3100, "tid": 103079215104, "ts": 1754737153844057, "dur": 1, "ph": "X", "name": "ProcessMessages 428", "args": {}}, {"pid": 3100, "tid": 103079215104, "ts": 1754737153844059, "dur": 38, "ph": "X", "name": "ReadAsync 428", "args": {}}, {"pid": 3100, "tid": 103079215104, "ts": 1754737153844099, "dur": 2, "ph": "X", "name": "ProcessMessages 61", "args": {}}, {"pid": 3100, "tid": 103079215104, "ts": 1754737153844101, "dur": 36, "ph": "X", "name": "ReadAsync 61", "args": {}}, {"pid": 3100, "tid": 103079215104, "ts": 1754737153844140, "dur": 26, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3100, "tid": 103079215104, "ts": 1754737153844169, "dur": 29, "ph": "X", "name": "ReadAsync 348", "args": {}}, {"pid": 3100, "tid": 103079215104, "ts": 1754737153844199, "dur": 1, "ph": "X", "name": "ProcessMessages 440", "args": {}}, {"pid": 3100, "tid": 103079215104, "ts": 1754737153844201, "dur": 48, "ph": "X", "name": "ReadAsync 440", "args": {}}, {"pid": 3100, "tid": 103079215104, "ts": 1754737153844252, "dur": 1, "ph": "X", "name": "ProcessMessages 369", "args": {}}, {"pid": 3100, "tid": 103079215104, "ts": 1754737153844253, "dur": 67, "ph": "X", "name": "ReadAsync 369", "args": {}}, {"pid": 3100, "tid": 103079215104, "ts": 1754737153844324, "dur": 38, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3100, "tid": 103079215104, "ts": 1754737153844364, "dur": 35, "ph": "X", "name": "ReadAsync 749", "args": {}}, {"pid": 3100, "tid": 103079215104, "ts": 1754737153844403, "dur": 30, "ph": "X", "name": "ReadAsync 303", "args": {}}, {"pid": 3100, "tid": 103079215104, "ts": 1754737153844435, "dur": 21, "ph": "X", "name": "ReadAsync 56", "args": {}}, {"pid": 3100, "tid": 103079215104, "ts": 1754737153844460, "dur": 31, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3100, "tid": 103079215104, "ts": 1754737153844493, "dur": 28, "ph": "X", "name": "ReadAsync 428", "args": {}}, {"pid": 3100, "tid": 103079215104, "ts": 1754737153844523, "dur": 1, "ph": "X", "name": "ProcessMessages 208", "args": {}}, {"pid": 3100, "tid": 103079215104, "ts": 1754737153844525, "dur": 27, "ph": "X", "name": "ReadAsync 208", "args": {}}, {"pid": 3100, "tid": 103079215104, "ts": 1754737153844554, "dur": 77, "ph": "X", "name": "ReadAsync 485", "args": {}}, {"pid": 3100, "tid": 103079215104, "ts": 1754737153844635, "dur": 33, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3100, "tid": 103079215104, "ts": 1754737153844670, "dur": 1, "ph": "X", "name": "ProcessMessages 508", "args": {}}, {"pid": 3100, "tid": 103079215104, "ts": 1754737153844671, "dur": 28, "ph": "X", "name": "ReadAsync 508", "args": {}}, {"pid": 3100, "tid": 103079215104, "ts": 1754737153844701, "dur": 19, "ph": "X", "name": "ReadAsync 538", "args": {}}, {"pid": 3100, "tid": 103079215104, "ts": 1754737153844722, "dur": 73, "ph": "X", "name": "ReadAsync 62", "args": {}}, {"pid": 3100, "tid": 103079215104, "ts": 1754737153844796, "dur": 41, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3100, "tid": 103079215104, "ts": 1754737153844838, "dur": 21, "ph": "X", "name": "ReadAsync 383", "args": {}}, {"pid": 3100, "tid": 103079215104, "ts": 1754737153844861, "dur": 22, "ph": "X", "name": "ReadAsync 59", "args": {}}, {"pid": 3100, "tid": 103079215104, "ts": 1754737153844884, "dur": 25, "ph": "X", "name": "ReadAsync 315", "args": {}}, {"pid": 3100, "tid": 103079215104, "ts": 1754737153844910, "dur": 25, "ph": "X", "name": "ReadAsync 170", "args": {}}, {"pid": 3100, "tid": 103079215104, "ts": 1754737153844938, "dur": 25, "ph": "X", "name": "ReadAsync 306", "args": {}}, {"pid": 3100, "tid": 103079215104, "ts": 1754737153844965, "dur": 15, "ph": "X", "name": "ReadAsync 281", "args": {}}, {"pid": 3100, "tid": 103079215104, "ts": 1754737153844981, "dur": 18, "ph": "X", "name": "ReadAsync 226", "args": {}}, {"pid": 3100, "tid": 103079215104, "ts": 1754737153845001, "dur": 22, "ph": "X", "name": "ReadAsync 130", "args": {}}, {"pid": 3100, "tid": 103079215104, "ts": 1754737153845025, "dur": 22, "ph": "X", "name": "ReadAsync 264", "args": {}}, {"pid": 3100, "tid": 103079215104, "ts": 1754737153845049, "dur": 21, "ph": "X", "name": "ReadAsync 148", "args": {}}, {"pid": 3100, "tid": 103079215104, "ts": 1754737153845072, "dur": 21, "ph": "X", "name": "ReadAsync 170", "args": {}}, {"pid": 3100, "tid": 103079215104, "ts": 1754737153845095, "dur": 74, "ph": "X", "name": "ReadAsync 67", "args": {}}, {"pid": 3100, "tid": 103079215104, "ts": 1754737153845172, "dur": 41, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3100, "tid": 103079215104, "ts": 1754737153845215, "dur": 30, "ph": "X", "name": "ReadAsync 409", "args": {}}, {"pid": 3100, "tid": 103079215104, "ts": 1754737153845247, "dur": 32, "ph": "X", "name": "ReadAsync 315", "args": {}}, {"pid": 3100, "tid": 103079215104, "ts": 1754737153845281, "dur": 31, "ph": "X", "name": "ReadAsync 509", "args": {}}, {"pid": 3100, "tid": 103079215104, "ts": 1754737153845314, "dur": 23, "ph": "X", "name": "ReadAsync 390", "args": {}}, {"pid": 3100, "tid": 103079215104, "ts": 1754737153845339, "dur": 18, "ph": "X", "name": "ReadAsync 535", "args": {}}, {"pid": 3100, "tid": 103079215104, "ts": 1754737153845359, "dur": 26, "ph": "X", "name": "ReadAsync 88", "args": {}}, {"pid": 3100, "tid": 103079215104, "ts": 1754737153845386, "dur": 21, "ph": "X", "name": "ReadAsync 289", "args": {}}, {"pid": 3100, "tid": 103079215104, "ts": 1754737153845408, "dur": 20, "ph": "X", "name": "ReadAsync 178", "args": {}}, {"pid": 3100, "tid": 103079215104, "ts": 1754737153845430, "dur": 61, "ph": "X", "name": "ReadAsync 62", "args": {}}, {"pid": 3100, "tid": 103079215104, "ts": 1754737153845495, "dur": 38, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3100, "tid": 103079215104, "ts": 1754737153845535, "dur": 2, "ph": "X", "name": "ProcessMessages 368", "args": {}}, {"pid": 3100, "tid": 103079215104, "ts": 1754737153845538, "dur": 42, "ph": "X", "name": "ReadAsync 368", "args": {}}, {"pid": 3100, "tid": 103079215104, "ts": 1754737153845583, "dur": 21, "ph": "X", "name": "ReadAsync 538", "args": {}}, {"pid": 3100, "tid": 103079215104, "ts": 1754737153845605, "dur": 22, "ph": "X", "name": "ReadAsync 347", "args": {}}, {"pid": 3100, "tid": 103079215104, "ts": 1754737153845629, "dur": 19, "ph": "X", "name": "ReadAsync 230", "args": {}}, {"pid": 3100, "tid": 103079215104, "ts": 1754737153845650, "dur": 20, "ph": "X", "name": "ReadAsync 240", "args": {}}, {"pid": 3100, "tid": 103079215104, "ts": 1754737153845671, "dur": 1, "ph": "X", "name": "ProcessMessages 291", "args": {}}, {"pid": 3100, "tid": 103079215104, "ts": 1754737153845673, "dur": 22, "ph": "X", "name": "ReadAsync 291", "args": {}}, {"pid": 3100, "tid": 103079215104, "ts": 1754737153845696, "dur": 18, "ph": "X", "name": "ReadAsync 207", "args": {}}, {"pid": 3100, "tid": 103079215104, "ts": 1754737153845715, "dur": 17, "ph": "X", "name": "ReadAsync 178", "args": {}}, {"pid": 3100, "tid": 103079215104, "ts": 1754737153845734, "dur": 61, "ph": "X", "name": "ReadAsync 62", "args": {}}, {"pid": 3100, "tid": 103079215104, "ts": 1754737153845798, "dur": 24, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3100, "tid": 103079215104, "ts": 1754737153845823, "dur": 23, "ph": "X", "name": "ReadAsync 306", "args": {}}, {"pid": 3100, "tid": 103079215104, "ts": 1754737153845848, "dur": 22, "ph": "X", "name": "ReadAsync 423", "args": {}}, {"pid": 3100, "tid": 103079215104, "ts": 1754737153845871, "dur": 21, "ph": "X", "name": "ReadAsync 319", "args": {}}, {"pid": 3100, "tid": 103079215104, "ts": 1754737153845896, "dur": 55, "ph": "X", "name": "ReadAsync 58", "args": {}}, {"pid": 3100, "tid": 103079215104, "ts": 1754737153845953, "dur": 21, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3100, "tid": 103079215104, "ts": 1754737153845975, "dur": 24, "ph": "X", "name": "ReadAsync 284", "args": {}}, {"pid": 3100, "tid": 103079215104, "ts": 1754737153846002, "dur": 27, "ph": "X", "name": "ReadAsync 333", "args": {}}, {"pid": 3100, "tid": 103079215104, "ts": 1754737153846031, "dur": 1, "ph": "X", "name": "ProcessMessages 548", "args": {}}, {"pid": 3100, "tid": 103079215104, "ts": 1754737153846033, "dur": 36, "ph": "X", "name": "ReadAsync 548", "args": {}}, {"pid": 3100, "tid": 103079215104, "ts": 1754737153846073, "dur": 52, "ph": "X", "name": "ReadAsync 66", "args": {}}, {"pid": 3100, "tid": 103079215104, "ts": 1754737153846129, "dur": 37, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3100, "tid": 103079215104, "ts": 1754737153846168, "dur": 1, "ph": "X", "name": "ProcessMessages 697", "args": {}}, {"pid": 3100, "tid": 103079215104, "ts": 1754737153846170, "dur": 31, "ph": "X", "name": "ReadAsync 697", "args": {}}, {"pid": 3100, "tid": 103079215104, "ts": 1754737153846202, "dur": 1, "ph": "X", "name": "ProcessMessages 355", "args": {}}, {"pid": 3100, "tid": 103079215104, "ts": 1754737153846204, "dur": 20, "ph": "X", "name": "ReadAsync 355", "args": {}}, {"pid": 3100, "tid": 103079215104, "ts": 1754737153846227, "dur": 69, "ph": "X", "name": "ReadAsync 134", "args": {}}, {"pid": 3100, "tid": 103079215104, "ts": 1754737153846299, "dur": 45, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3100, "tid": 103079215104, "ts": 1754737153846347, "dur": 24, "ph": "X", "name": "ReadAsync 530", "args": {}}, {"pid": 3100, "tid": 103079215104, "ts": 1754737153846373, "dur": 27, "ph": "X", "name": "ReadAsync 618", "args": {}}, {"pid": 3100, "tid": 103079215104, "ts": 1754737153846404, "dur": 32, "ph": "X", "name": "ReadAsync 415", "args": {}}, {"pid": 3100, "tid": 103079215104, "ts": 1754737153846438, "dur": 21, "ph": "X", "name": "ReadAsync 614", "args": {}}, {"pid": 3100, "tid": 103079215104, "ts": 1754737153846460, "dur": 24, "ph": "X", "name": "ReadAsync 205", "args": {}}, {"pid": 3100, "tid": 103079215104, "ts": 1754737153846487, "dur": 22, "ph": "X", "name": "ReadAsync 452", "args": {}}, {"pid": 3100, "tid": 103079215104, "ts": 1754737153846512, "dur": 28, "ph": "X", "name": "ReadAsync 134", "args": {}}, {"pid": 3100, "tid": 103079215104, "ts": 1754737153846542, "dur": 61, "ph": "X", "name": "ReadAsync 197", "args": {}}, {"pid": 3100, "tid": 103079215104, "ts": 1754737153846608, "dur": 36, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3100, "tid": 103079215104, "ts": 1754737153846647, "dur": 1, "ph": "X", "name": "ProcessMessages 794", "args": {}}, {"pid": 3100, "tid": 103079215104, "ts": 1754737153846648, "dur": 27, "ph": "X", "name": "ReadAsync 794", "args": {}}, {"pid": 3100, "tid": 103079215104, "ts": 1754737153846678, "dur": 23, "ph": "X", "name": "ReadAsync 350", "args": {}}, {"pid": 3100, "tid": 103079215104, "ts": 1754737153846703, "dur": 29, "ph": "X", "name": "ReadAsync 393", "args": {}}, {"pid": 3100, "tid": 103079215104, "ts": 1754737153846735, "dur": 24, "ph": "X", "name": "ReadAsync 372", "args": {}}, {"pid": 3100, "tid": 103079215104, "ts": 1754737153846761, "dur": 23, "ph": "X", "name": "ReadAsync 444", "args": {}}, {"pid": 3100, "tid": 103079215104, "ts": 1754737153846786, "dur": 21, "ph": "X", "name": "ReadAsync 446", "args": {}}, {"pid": 3100, "tid": 103079215104, "ts": 1754737153846809, "dur": 20, "ph": "X", "name": "ReadAsync 86", "args": {}}, {"pid": 3100, "tid": 103079215104, "ts": 1754737153846830, "dur": 99, "ph": "X", "name": "ReadAsync 240", "args": {}}, {"pid": 3100, "tid": 103079215104, "ts": 1754737153846930, "dur": 22, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3100, "tid": 103079215104, "ts": 1754737153846954, "dur": 22, "ph": "X", "name": "ReadAsync 300", "args": {}}, {"pid": 3100, "tid": 103079215104, "ts": 1754737153846977, "dur": 24, "ph": "X", "name": "ReadAsync 377", "args": {}}, {"pid": 3100, "tid": 103079215104, "ts": 1754737153847003, "dur": 22, "ph": "X", "name": "ReadAsync 479", "args": {}}, {"pid": 3100, "tid": 103079215104, "ts": 1754737153847026, "dur": 29, "ph": "X", "name": "ReadAsync 419", "args": {}}, {"pid": 3100, "tid": 103079215104, "ts": 1754737153847059, "dur": 46, "ph": "X", "name": "ReadAsync 375", "args": {}}, {"pid": 3100, "tid": 103079215104, "ts": 1754737153847107, "dur": 1, "ph": "X", "name": "ProcessMessages 580", "args": {}}, {"pid": 3100, "tid": 103079215104, "ts": 1754737153847108, "dur": 37, "ph": "X", "name": "ReadAsync 580", "args": {}}, {"pid": 3100, "tid": 103079215104, "ts": 1754737153847148, "dur": 1, "ph": "X", "name": "ProcessMessages 423", "args": {}}, {"pid": 3100, "tid": 103079215104, "ts": 1754737153847150, "dur": 25, "ph": "X", "name": "ReadAsync 423", "args": {}}, {"pid": 3100, "tid": 103079215104, "ts": 1754737153847177, "dur": 66, "ph": "X", "name": "ReadAsync 237", "args": {}}, {"pid": 3100, "tid": 103079215104, "ts": 1754737153847246, "dur": 32, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3100, "tid": 103079215104, "ts": 1754737153847279, "dur": 30, "ph": "X", "name": "ReadAsync 431", "args": {}}, {"pid": 3100, "tid": 103079215104, "ts": 1754737153847314, "dur": 50, "ph": "X", "name": "ReadAsync 103", "args": {}}, {"pid": 3100, "tid": 103079215104, "ts": 1754737153847367, "dur": 1, "ph": "X", "name": "ProcessMessages 564", "args": {}}, {"pid": 3100, "tid": 103079215104, "ts": 1754737153847369, "dur": 43, "ph": "X", "name": "ReadAsync 564", "args": {}}, {"pid": 3100, "tid": 103079215104, "ts": 1754737153847415, "dur": 1, "ph": "X", "name": "ProcessMessages 321", "args": {}}, {"pid": 3100, "tid": 103079215104, "ts": 1754737153847417, "dur": 46, "ph": "X", "name": "ReadAsync 321", "args": {}}, {"pid": 3100, "tid": 103079215104, "ts": 1754737153847466, "dur": 1, "ph": "X", "name": "ProcessMessages 430", "args": {}}, {"pid": 3100, "tid": 103079215104, "ts": 1754737153847467, "dur": 42, "ph": "X", "name": "ReadAsync 430", "args": {}}, {"pid": 3100, "tid": 103079215104, "ts": 1754737153847511, "dur": 27, "ph": "X", "name": "ReadAsync 539", "args": {}}, {"pid": 3100, "tid": 103079215104, "ts": 1754737153847540, "dur": 24, "ph": "X", "name": "ReadAsync 118", "args": {}}, {"pid": 3100, "tid": 103079215104, "ts": 1754737153847566, "dur": 67, "ph": "X", "name": "ReadAsync 193", "args": {}}, {"pid": 3100, "tid": 103079215104, "ts": 1754737153847634, "dur": 23, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3100, "tid": 103079215104, "ts": 1754737153847659, "dur": 22, "ph": "X", "name": "ReadAsync 317", "args": {}}, {"pid": 3100, "tid": 103079215104, "ts": 1754737153847682, "dur": 21, "ph": "X", "name": "ReadAsync 207", "args": {}}, {"pid": 3100, "tid": 103079215104, "ts": 1754737153847705, "dur": 21, "ph": "X", "name": "ReadAsync 359", "args": {}}, {"pid": 3100, "tid": 103079215104, "ts": 1754737153847727, "dur": 57, "ph": "X", "name": "ReadAsync 225", "args": {}}, {"pid": 3100, "tid": 103079215104, "ts": 1754737153847786, "dur": 36, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3100, "tid": 103079215104, "ts": 1754737153847825, "dur": 42, "ph": "X", "name": "ReadAsync 243", "args": {}}, {"pid": 3100, "tid": 103079215104, "ts": 1754737153847868, "dur": 21, "ph": "X", "name": "ReadAsync 268", "args": {}}, {"pid": 3100, "tid": 103079215104, "ts": 1754737153847891, "dur": 21, "ph": "X", "name": "ReadAsync 382", "args": {}}, {"pid": 3100, "tid": 103079215104, "ts": 1754737153847914, "dur": 21, "ph": "X", "name": "ReadAsync 379", "args": {}}, {"pid": 3100, "tid": 103079215104, "ts": 1754737153847936, "dur": 40, "ph": "X", "name": "ReadAsync 349", "args": {}}, {"pid": 3100, "tid": 103079215104, "ts": 1754737153847978, "dur": 22, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3100, "tid": 103079215104, "ts": 1754737153848002, "dur": 27, "ph": "X", "name": "ReadAsync 277", "args": {}}, {"pid": 3100, "tid": 103079215104, "ts": 1754737153848030, "dur": 1, "ph": "X", "name": "ProcessMessages 415", "args": {}}, {"pid": 3100, "tid": 103079215104, "ts": 1754737153848032, "dur": 22, "ph": "X", "name": "ReadAsync 415", "args": {}}, {"pid": 3100, "tid": 103079215104, "ts": 1754737153848055, "dur": 29, "ph": "X", "name": "ReadAsync 455", "args": {}}, {"pid": 3100, "tid": 103079215104, "ts": 1754737153848087, "dur": 1, "ph": "X", "name": "ProcessMessages 76", "args": {}}, {"pid": 3100, "tid": 103079215104, "ts": 1754737153848088, "dur": 27, "ph": "X", "name": "ReadAsync 76", "args": {}}, {"pid": 3100, "tid": 103079215104, "ts": 1754737153848117, "dur": 48, "ph": "X", "name": "ReadAsync 237", "args": {}}, {"pid": 3100, "tid": 103079215104, "ts": 1754737153848168, "dur": 27, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3100, "tid": 103079215104, "ts": 1754737153848197, "dur": 22, "ph": "X", "name": "ReadAsync 435", "args": {}}, {"pid": 3100, "tid": 103079215104, "ts": 1754737153848220, "dur": 21, "ph": "X", "name": "ReadAsync 421", "args": {}}, {"pid": 3100, "tid": 103079215104, "ts": 1754737153848243, "dur": 22, "ph": "X", "name": "ReadAsync 340", "args": {}}, {"pid": 3100, "tid": 103079215104, "ts": 1754737153848266, "dur": 27, "ph": "X", "name": "ReadAsync 346", "args": {}}, {"pid": 3100, "tid": 103079215104, "ts": 1754737153848295, "dur": 23, "ph": "X", "name": "ReadAsync 282", "args": {}}, {"pid": 3100, "tid": 103079215104, "ts": 1754737153848322, "dur": 33, "ph": "X", "name": "ReadAsync 321", "args": {}}, {"pid": 3100, "tid": 103079215104, "ts": 1754737153848357, "dur": 20, "ph": "X", "name": "ReadAsync 580", "args": {}}, {"pid": 3100, "tid": 103079215104, "ts": 1754737153848378, "dur": 21, "ph": "X", "name": "ReadAsync 94", "args": {}}, {"pid": 3100, "tid": 103079215104, "ts": 1754737153848403, "dur": 51, "ph": "X", "name": "ReadAsync 143", "args": {}}, {"pid": 3100, "tid": 103079215104, "ts": 1754737153848455, "dur": 29, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3100, "tid": 103079215104, "ts": 1754737153848485, "dur": 24, "ph": "X", "name": "ReadAsync 282", "args": {}}, {"pid": 3100, "tid": 103079215104, "ts": 1754737153848511, "dur": 25, "ph": "X", "name": "ReadAsync 398", "args": {}}, {"pid": 3100, "tid": 103079215104, "ts": 1754737153848538, "dur": 31, "ph": "X", "name": "ReadAsync 347", "args": {}}, {"pid": 3100, "tid": 103079215104, "ts": 1754737153848570, "dur": 22, "ph": "X", "name": "ReadAsync 340", "args": {}}, {"pid": 3100, "tid": 103079215104, "ts": 1754737153848594, "dur": 21, "ph": "X", "name": "ReadAsync 419", "args": {}}, {"pid": 3100, "tid": 103079215104, "ts": 1754737153848617, "dur": 21, "ph": "X", "name": "ReadAsync 315", "args": {}}, {"pid": 3100, "tid": 103079215104, "ts": 1754737153848639, "dur": 21, "ph": "X", "name": "ReadAsync 265", "args": {}}, {"pid": 3100, "tid": 103079215104, "ts": 1754737153848664, "dur": 28, "ph": "X", "name": "ReadAsync 114", "args": {}}, {"pid": 3100, "tid": 103079215104, "ts": 1754737153848694, "dur": 42, "ph": "X", "name": "ReadAsync 146", "args": {}}, {"pid": 3100, "tid": 103079215104, "ts": 1754737153848737, "dur": 26, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3100, "tid": 103079215104, "ts": 1754737153848765, "dur": 22, "ph": "X", "name": "ReadAsync 243", "args": {}}, {"pid": 3100, "tid": 103079215104, "ts": 1754737153848788, "dur": 20, "ph": "X", "name": "ReadAsync 424", "args": {}}, {"pid": 3100, "tid": 103079215104, "ts": 1754737153848809, "dur": 21, "ph": "X", "name": "ReadAsync 120", "args": {}}, {"pid": 3100, "tid": 103079215104, "ts": 1754737153848832, "dur": 22, "ph": "X", "name": "ReadAsync 327", "args": {}}, {"pid": 3100, "tid": 103079215104, "ts": 1754737153848856, "dur": 21, "ph": "X", "name": "ReadAsync 304", "args": {}}, {"pid": 3100, "tid": 103079215104, "ts": 1754737153848879, "dur": 22, "ph": "X", "name": "ReadAsync 363", "args": {}}, {"pid": 3100, "tid": 103079215104, "ts": 1754737153848902, "dur": 23, "ph": "X", "name": "ReadAsync 264", "args": {}}, {"pid": 3100, "tid": 103079215104, "ts": 1754737153848926, "dur": 307, "ph": "X", "name": "ReadAsync 396", "args": {}}, {"pid": 3100, "tid": 103079215104, "ts": 1754737153849237, "dur": 28, "ph": "X", "name": "ReadAsync 1", "args": {}}, {"pid": 3100, "tid": 103079215104, "ts": 1754737153849268, "dur": 24, "ph": "X", "name": "ReadAsync 295", "args": {}}, {"pid": 3100, "tid": 103079215104, "ts": 1754737153849296, "dur": 49, "ph": "X", "name": "ReadAsync 21", "args": {}}, {"pid": 3100, "tid": 103079215104, "ts": 1754737153849349, "dur": 24, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3100, "tid": 103079215104, "ts": 1754737153849375, "dur": 117, "ph": "X", "name": "ReadAsync 224", "args": {}}, {"pid": 3100, "tid": 103079215104, "ts": 1754737153849496, "dur": 44, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3100, "tid": 103079215104, "ts": 1754737153849545, "dur": 65, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 3100, "tid": 103079215104, "ts": 1754737153849615, "dur": 2, "ph": "X", "name": "ProcessMessages 192", "args": {}}, {"pid": 3100, "tid": 103079215104, "ts": 1754737153849619, "dur": 57, "ph": "X", "name": "ReadAsync 192", "args": {}}, {"pid": 3100, "tid": 103079215104, "ts": 1754737153849679, "dur": 1, "ph": "X", "name": "ProcessMessages 208", "args": {}}, {"pid": 3100, "tid": 103079215104, "ts": 1754737153849682, "dur": 32, "ph": "X", "name": "ReadAsync 208", "args": {}}, {"pid": 3100, "tid": 103079215104, "ts": 1754737153849716, "dur": 1, "ph": "X", "name": "ProcessMessages 124", "args": {}}, {"pid": 3100, "tid": 103079215104, "ts": 1754737153849718, "dur": 31, "ph": "X", "name": "ReadAsync 124", "args": {}}, {"pid": 3100, "tid": 103079215104, "ts": 1754737153849751, "dur": 1, "ph": "X", "name": "ProcessMessages 148", "args": {}}, {"pid": 3100, "tid": 103079215104, "ts": 1754737153849753, "dur": 29, "ph": "X", "name": "ReadAsync 148", "args": {}}, {"pid": 3100, "tid": 103079215104, "ts": 1754737153849785, "dur": 1, "ph": "X", "name": "ProcessMessages 112", "args": {}}, {"pid": 3100, "tid": 103079215104, "ts": 1754737153849787, "dur": 44, "ph": "X", "name": "ReadAsync 112", "args": {}}, {"pid": 3100, "tid": 103079215104, "ts": 1754737153849833, "dur": 2, "ph": "X", "name": "ProcessMessages 140", "args": {}}, {"pid": 3100, "tid": 103079215104, "ts": 1754737153849836, "dur": 63, "ph": "X", "name": "ReadAsync 140", "args": {}}, {"pid": 3100, "tid": 103079215104, "ts": 1754737153849900, "dur": 2, "ph": "X", "name": "ProcessMessages 260", "args": {}}, {"pid": 3100, "tid": 103079215104, "ts": 1754737153849902, "dur": 26, "ph": "X", "name": "ReadAsync 260", "args": {}}, {"pid": 3100, "tid": 103079215104, "ts": 1754737153849932, "dur": 41, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 3100, "tid": 103079215104, "ts": 1754737153849975, "dur": 21, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3100, "tid": 103079215104, "ts": 1754737153849998, "dur": 23, "ph": "X", "name": "ReadAsync 84", "args": {}}, {"pid": 3100, "tid": 103079215104, "ts": 1754737153850024, "dur": 38, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 3100, "tid": 103079215104, "ts": 1754737153850067, "dur": 1, "ph": "X", "name": "ProcessMessages 96", "args": {}}, {"pid": 3100, "tid": 103079215104, "ts": 1754737153850069, "dur": 31, "ph": "X", "name": "ReadAsync 96", "args": {}}, {"pid": 3100, "tid": 103079215104, "ts": 1754737153850103, "dur": 30, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 3100, "tid": 103079215104, "ts": 1754737153850138, "dur": 27, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 3100, "tid": 103079215104, "ts": 1754737153850167, "dur": 37, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 3100, "tid": 103079215104, "ts": 1754737153850208, "dur": 1, "ph": "X", "name": "ProcessMessages 96", "args": {}}, {"pid": 3100, "tid": 103079215104, "ts": 1754737153850210, "dur": 41, "ph": "X", "name": "ReadAsync 96", "args": {}}, {"pid": 3100, "tid": 103079215104, "ts": 1754737153850254, "dur": 1, "ph": "X", "name": "ProcessMessages 128", "args": {}}, {"pid": 3100, "tid": 103079215104, "ts": 1754737153850256, "dur": 33, "ph": "X", "name": "ReadAsync 128", "args": {}}, {"pid": 3100, "tid": 103079215104, "ts": 1754737153850292, "dur": 1, "ph": "X", "name": "ProcessMessages 160", "args": {}}, {"pid": 3100, "tid": 103079215104, "ts": 1754737153850294, "dur": 30, "ph": "X", "name": "ReadAsync 160", "args": {}}, {"pid": 3100, "tid": 103079215104, "ts": 1754737153850326, "dur": 1, "ph": "X", "name": "ProcessMessages 156", "args": {}}, {"pid": 3100, "tid": 103079215104, "ts": 1754737153850328, "dur": 33, "ph": "X", "name": "ReadAsync 156", "args": {}}, {"pid": 3100, "tid": 103079215104, "ts": 1754737153850364, "dur": 1, "ph": "X", "name": "ProcessMessages 112", "args": {}}, {"pid": 3100, "tid": 103079215104, "ts": 1754737153850366, "dur": 32, "ph": "X", "name": "ReadAsync 112", "args": {}}, {"pid": 3100, "tid": 103079215104, "ts": 1754737153850402, "dur": 1, "ph": "X", "name": "ProcessMessages 148", "args": {}}, {"pid": 3100, "tid": 103079215104, "ts": 1754737153850404, "dur": 38, "ph": "X", "name": "ReadAsync 148", "args": {}}, {"pid": 3100, "tid": 103079215104, "ts": 1754737153850446, "dur": 1, "ph": "X", "name": "ProcessMessages 128", "args": {}}, {"pid": 3100, "tid": 103079215104, "ts": 1754737153850448, "dur": 46, "ph": "X", "name": "ReadAsync 128", "args": {}}, {"pid": 3100, "tid": 103079215104, "ts": 1754737153850499, "dur": 2, "ph": "X", "name": "ProcessMessages 176", "args": {}}, {"pid": 3100, "tid": 103079215104, "ts": 1754737153850504, "dur": 51, "ph": "X", "name": "ReadAsync 176", "args": {}}, {"pid": 3100, "tid": 103079215104, "ts": 1754737153850557, "dur": 1, "ph": "X", "name": "ProcessMessages 316", "args": {}}, {"pid": 3100, "tid": 103079215104, "ts": 1754737153850559, "dur": 24, "ph": "X", "name": "ReadAsync 316", "args": {}}, {"pid": 3100, "tid": 103079215104, "ts": 1754737153850585, "dur": 33, "ph": "X", "name": "ReadAsync 52", "args": {}}, {"pid": 3100, "tid": 103079215104, "ts": 1754737153850620, "dur": 1, "ph": "X", "name": "ProcessMessages 64", "args": {}}, {"pid": 3100, "tid": 103079215104, "ts": 1754737153850622, "dur": 35, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 3100, "tid": 103079215104, "ts": 1754737153850659, "dur": 1, "ph": "X", "name": "ProcessMessages 176", "args": {}}, {"pid": 3100, "tid": 103079215104, "ts": 1754737153850661, "dur": 25, "ph": "X", "name": "ReadAsync 176", "args": {}}, {"pid": 3100, "tid": 103079215104, "ts": 1754737153850688, "dur": 22, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 3100, "tid": 103079215104, "ts": 1754737153850713, "dur": 23, "ph": "X", "name": "ReadAsync 108", "args": {}}, {"pid": 3100, "tid": 103079215104, "ts": 1754737153850737, "dur": 21, "ph": "X", "name": "ReadAsync 116", "args": {}}, {"pid": 3100, "tid": 103079215104, "ts": 1754737153850761, "dur": 26, "ph": "X", "name": "ReadAsync 96", "args": {}}, {"pid": 3100, "tid": 103079215104, "ts": 1754737153850790, "dur": 1, "ph": "X", "name": "ProcessMessages 76", "args": {}}, {"pid": 3100, "tid": 103079215104, "ts": 1754737153850792, "dur": 29, "ph": "X", "name": "ReadAsync 76", "args": {}}, {"pid": 3100, "tid": 103079215104, "ts": 1754737153850822, "dur": 1, "ph": "X", "name": "ProcessMessages 144", "args": {}}, {"pid": 3100, "tid": 103079215104, "ts": 1754737153850823, "dur": 31, "ph": "X", "name": "ReadAsync 144", "args": {}}, {"pid": 3100, "tid": 103079215104, "ts": 1754737153850857, "dur": 30, "ph": "X", "name": "ReadAsync 112", "args": {}}, {"pid": 3100, "tid": 103079215104, "ts": 1754737153850889, "dur": 27, "ph": "X", "name": "ReadAsync 116", "args": {}}, {"pid": 3100, "tid": 103079215104, "ts": 1754737153850919, "dur": 24, "ph": "X", "name": "ReadAsync 144", "args": {}}, {"pid": 3100, "tid": 103079215104, "ts": 1754737153850945, "dur": 25, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 3100, "tid": 103079215104, "ts": 1754737153850973, "dur": 26, "ph": "X", "name": "ReadAsync 96", "args": {}}, {"pid": 3100, "tid": 103079215104, "ts": 1754737153851001, "dur": 37, "ph": "X", "name": "ReadAsync 140", "args": {}}, {"pid": 3100, "tid": 103079215104, "ts": 1754737153851040, "dur": 1, "ph": "X", "name": "ProcessMessages 148", "args": {}}, {"pid": 3100, "tid": 103079215104, "ts": 1754737153851042, "dur": 29, "ph": "X", "name": "ReadAsync 148", "args": {}}, {"pid": 3100, "tid": 103079215104, "ts": 1754737153851074, "dur": 1, "ph": "X", "name": "ProcessMessages 112", "args": {}}, {"pid": 3100, "tid": 103079215104, "ts": 1754737153851076, "dur": 26, "ph": "X", "name": "ReadAsync 112", "args": {}}, {"pid": 3100, "tid": 103079215104, "ts": 1754737153851105, "dur": 27, "ph": "X", "name": "ReadAsync 128", "args": {}}, {"pid": 3100, "tid": 103079215104, "ts": 1754737153851134, "dur": 1, "ph": "X", "name": "ProcessMessages 112", "args": {}}, {"pid": 3100, "tid": 103079215104, "ts": 1754737153851136, "dur": 49, "ph": "X", "name": "ReadAsync 112", "args": {}}, {"pid": 3100, "tid": 103079215104, "ts": 1754737153851187, "dur": 1, "ph": "X", "name": "ProcessMessages 160", "args": {}}, {"pid": 3100, "tid": 103079215104, "ts": 1754737153851189, "dur": 26, "ph": "X", "name": "ReadAsync 160", "args": {}}, {"pid": 3100, "tid": 103079215104, "ts": 1754737153851218, "dur": 22, "ph": "X", "name": "ReadAsync 76", "args": {}}, {"pid": 3100, "tid": 103079215104, "ts": 1754737153851242, "dur": 33, "ph": "X", "name": "ReadAsync 68", "args": {}}, {"pid": 3100, "tid": 103079215104, "ts": 1754737153851278, "dur": 32, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 3100, "tid": 103079215104, "ts": 1754737153851313, "dur": 1, "ph": "X", "name": "ProcessMessages 128", "args": {}}, {"pid": 3100, "tid": 103079215104, "ts": 1754737153851315, "dur": 38, "ph": "X", "name": "ReadAsync 128", "args": {}}, {"pid": 3100, "tid": 103079215104, "ts": 1754737153851356, "dur": 35, "ph": "X", "name": "ReadAsync 156", "args": {}}, {"pid": 3100, "tid": 103079215104, "ts": 1754737153851393, "dur": 2, "ph": "X", "name": "ProcessMessages 164", "args": {}}, {"pid": 3100, "tid": 103079215104, "ts": 1754737153851396, "dur": 30, "ph": "X", "name": "ReadAsync 164", "args": {}}, {"pid": 3100, "tid": 103079215104, "ts": 1754737153851428, "dur": 1, "ph": "X", "name": "ProcessMessages 188", "args": {}}, {"pid": 3100, "tid": 103079215104, "ts": 1754737153851429, "dur": 24, "ph": "X", "name": "ReadAsync 188", "args": {}}, {"pid": 3100, "tid": 103079215104, "ts": 1754737153851455, "dur": 28, "ph": "X", "name": "ReadAsync 116", "args": {}}, {"pid": 3100, "tid": 103079215104, "ts": 1754737153851485, "dur": 111, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 3100, "tid": 103079215104, "ts": 1754737153851601, "dur": 39, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3100, "tid": 103079215104, "ts": 1754737153851643, "dur": 27, "ph": "X", "name": "ReadAsync 100", "args": {}}, {"pid": 3100, "tid": 103079215104, "ts": 1754737153851672, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 3100, "tid": 103079215104, "ts": 1754737153851674, "dur": 27, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 3100, "tid": 103079215104, "ts": 1754737153851703, "dur": 1691, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 3100, "tid": 103079215104, "ts": 1754737153853397, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 3100, "tid": 103079215104, "ts": 1754737153853401, "dur": 36, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3100, "tid": 103079215104, "ts": 1754737153853440, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 3100, "tid": 103079215104, "ts": 1754737153853442, "dur": 193, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 3100, "tid": 103079215104, "ts": 1754737153853638, "dur": 23, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3100, "tid": 103079215104, "ts": 1754737153853666, "dur": 29, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 3100, "tid": 103079215104, "ts": 1754737153853699, "dur": 179, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 3100, "tid": 103079215104, "ts": 1754737153853881, "dur": 22, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3100, "tid": 103079215104, "ts": 1754737153853906, "dur": 2957, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 3100, "tid": 103079215104, "ts": 1754737153856868, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 3100, "tid": 103079215104, "ts": 1754737153856870, "dur": 39, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3100, "tid": 103079215104, "ts": 1754737153856912, "dur": 25, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 3100, "tid": 103079215104, "ts": 1754737153856939, "dur": 35, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 3100, "tid": 103079215104, "ts": 1754737153856976, "dur": 23, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3100, "tid": 103079215104, "ts": 1754737153857000, "dur": 197, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 3100, "tid": 103079215104, "ts": 1754737153857202, "dur": 37, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3100, "tid": 103079215104, "ts": 1754737153857243, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 3100, "tid": 103079215104, "ts": 1754737153857244, "dur": 77, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 3100, "tid": 103079215104, "ts": 1754737153857326, "dur": 45, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3100, "tid": 103079215104, "ts": 1754737153857372, "dur": 1, "ph": "X", "name": "ProcessMessages 160", "args": {}}, {"pid": 3100, "tid": 103079215104, "ts": 1754737153857374, "dur": 24, "ph": "X", "name": "ReadAsync 160", "args": {}}, {"pid": 3100, "tid": 103079215104, "ts": 1754737153857401, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 3100, "tid": 103079215104, "ts": 1754737153857402, "dur": 26, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 3100, "tid": 103079215104, "ts": 1754737153857431, "dur": 31, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 3100, "tid": 103079215104, "ts": 1754737153857466, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 3100, "tid": 103079215104, "ts": 1754737153857469, "dur": 38, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3100, "tid": 103079215104, "ts": 1754737153857511, "dur": 27, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 3100, "tid": 103079215104, "ts": 1754737153857541, "dur": 59, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 3100, "tid": 103079215104, "ts": 1754737153857602, "dur": 21, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3100, "tid": 103079215104, "ts": 1754737153857625, "dur": 58, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 3100, "tid": 103079215104, "ts": 1754737153857686, "dur": 22, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3100, "tid": 103079215104, "ts": 1754737153857712, "dur": 49, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 3100, "tid": 103079215104, "ts": 1754737153857763, "dur": 19, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3100, "tid": 103079215104, "ts": 1754737153857785, "dur": 22, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 3100, "tid": 103079215104, "ts": 1754737153857810, "dur": 20, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 3100, "tid": 103079215104, "ts": 1754737153857833, "dur": 22, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 3100, "tid": 103079215104, "ts": 1754737153857857, "dur": 52, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 3100, "tid": 103079215104, "ts": 1754737153857912, "dur": 24, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3100, "tid": 103079215104, "ts": 1754737153857937, "dur": 39, "ph": "X", "name": "ReadAsync 52", "args": {}}, {"pid": 3100, "tid": 103079215104, "ts": 1754737153857978, "dur": 21, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3100, "tid": 103079215104, "ts": 1754737153858002, "dur": 25, "ph": "X", "name": "ReadAsync 52", "args": {}}, {"pid": 3100, "tid": 103079215104, "ts": 1754737153858030, "dur": 1, "ph": "X", "name": "ProcessMessages 76", "args": {}}, {"pid": 3100, "tid": 103079215104, "ts": 1754737153858032, "dur": 40, "ph": "X", "name": "ReadAsync 76", "args": {}}, {"pid": 3100, "tid": 103079215104, "ts": 1754737153858076, "dur": 1, "ph": "X", "name": "ProcessMessages 100", "args": {}}, {"pid": 3100, "tid": 103079215104, "ts": 1754737153858078, "dur": 43, "ph": "X", "name": "ReadAsync 100", "args": {}}, {"pid": 3100, "tid": 103079215104, "ts": 1754737153858124, "dur": 1, "ph": "X", "name": "ProcessMessages 64", "args": {}}, {"pid": 3100, "tid": 103079215104, "ts": 1754737153858126, "dur": 41, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 3100, "tid": 103079215104, "ts": 1754737153858169, "dur": 127, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 3100, "tid": 103079215104, "ts": 1754737153858299, "dur": 28, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3100, "tid": 103079215104, "ts": 1754737153858329, "dur": 36, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 3100, "tid": 103079215104, "ts": 1754737153858368, "dur": 1, "ph": "X", "name": "ProcessMessages 76", "args": {}}, {"pid": 3100, "tid": 103079215104, "ts": 1754737153858369, "dur": 36, "ph": "X", "name": "ReadAsync 76", "args": {}}, {"pid": 3100, "tid": 103079215104, "ts": 1754737153858408, "dur": 1, "ph": "X", "name": "ProcessMessages 84", "args": {}}, {"pid": 3100, "tid": 103079215104, "ts": 1754737153858409, "dur": 26, "ph": "X", "name": "ReadAsync 84", "args": {}}, {"pid": 3100, "tid": 103079215104, "ts": 1754737153858438, "dur": 22, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 3100, "tid": 103079215104, "ts": 1754737153858463, "dur": 23, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 3100, "tid": 103079215104, "ts": 1754737153858488, "dur": 15, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 3100, "tid": 103079215104, "ts": 1754737153858505, "dur": 46, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 3100, "tid": 103079215104, "ts": 1754737153858553, "dur": 22, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3100, "tid": 103079215104, "ts": 1754737153858576, "dur": 97, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 3100, "tid": 103079215104, "ts": 1754737153858678, "dur": 26, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3100, "tid": 103079215104, "ts": 1754737153858706, "dur": 35, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 3100, "tid": 103079215104, "ts": 1754737153858743, "dur": 18, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3100, "tid": 103079215104, "ts": 1754737153858762, "dur": 19, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 3100, "tid": 103079215104, "ts": 1754737153858783, "dur": 23, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 3100, "tid": 103079215104, "ts": 1754737153858809, "dur": 23, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 3100, "tid": 103079215104, "ts": 1754737153858834, "dur": 56, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 3100, "tid": 103079215104, "ts": 1754737153858891, "dur": 29, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3100, "tid": 103079215104, "ts": 1754737153858923, "dur": 28, "ph": "X", "name": "ReadAsync 68", "args": {}}, {"pid": 3100, "tid": 103079215104, "ts": 1754737153858954, "dur": 58, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 3100, "tid": 103079215104, "ts": 1754737153859015, "dur": 37, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3100, "tid": 103079215104, "ts": 1754737153859055, "dur": 1, "ph": "X", "name": "ProcessMessages 36", "args": {}}, {"pid": 3100, "tid": 103079215104, "ts": 1754737153859057, "dur": 56, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 3100, "tid": 103079215104, "ts": 1754737153859116, "dur": 35, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3100, "tid": 103079215104, "ts": 1754737153859155, "dur": 36, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 3100, "tid": 103079215104, "ts": 1754737153859193, "dur": 33, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 3100, "tid": 103079215104, "ts": 1754737153859230, "dur": 34, "ph": "X", "name": "ReadAsync 28", "args": {}}, {"pid": 3100, "tid": 103079215104, "ts": 1754737153859268, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 3100, "tid": 103079215104, "ts": 1754737153859269, "dur": 32, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 3100, "tid": 103079215104, "ts": 1754737153859304, "dur": 33, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 3100, "tid": 103079215104, "ts": 1754737153859339, "dur": 2, "ph": "X", "name": "ProcessMessages 52", "args": {}}, {"pid": 3100, "tid": 103079215104, "ts": 1754737153859342, "dur": 23, "ph": "X", "name": "ReadAsync 52", "args": {}}, {"pid": 3100, "tid": 103079215104, "ts": 1754737153859367, "dur": 28, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 3100, "tid": 103079215104, "ts": 1754737153859397, "dur": 18, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3100, "tid": 103079215104, "ts": 1754737153859417, "dur": 63, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 3100, "tid": 103079215104, "ts": 1754737153859481, "dur": 26, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3100, "tid": 103079215104, "ts": 1754737153859508, "dur": 19, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 3100, "tid": 103079215104, "ts": 1754737153859529, "dur": 241, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 3100, "tid": 103079215104, "ts": 1754737153859774, "dur": 48, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3100, "tid": 103079215104, "ts": 1754737153859824, "dur": 53, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 3100, "tid": 103079215104, "ts": 1754737153859881, "dur": 32, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3100, "tid": 103079215104, "ts": 1754737153859915, "dur": 1, "ph": "X", "name": "ProcessMessages 52", "args": {}}, {"pid": 3100, "tid": 103079215104, "ts": 1754737153859917, "dur": 36, "ph": "X", "name": "ReadAsync 52", "args": {}}, {"pid": 3100, "tid": 103079215104, "ts": 1754737153859955, "dur": 24, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 3100, "tid": 103079215104, "ts": 1754737153859981, "dur": 36, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 3100, "tid": 103079215104, "ts": 1754737153860021, "dur": 38, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3100, "tid": 103079215104, "ts": 1754737153860062, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 3100, "tid": 103079215104, "ts": 1754737153860063, "dur": 106, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 3100, "tid": 103079215104, "ts": 1754737153860176, "dur": 63, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3100, "tid": 103079215104, "ts": 1754737153860241, "dur": 26, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 3100, "tid": 103079215104, "ts": 1754737153860268, "dur": 33, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 3100, "tid": 103079215104, "ts": 1754737153860305, "dur": 26, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3100, "tid": 103079215104, "ts": 1754737153860333, "dur": 22, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 3100, "tid": 103079215104, "ts": 1754737153860357, "dur": 75, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 3100, "tid": 103079215104, "ts": 1754737153860433, "dur": 30, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3100, "tid": 103079215104, "ts": 1754737153860465, "dur": 150, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 3100, "tid": 103079215104, "ts": 1754737153860619, "dur": 29, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3100, "tid": 103079215104, "ts": 1754737153860650, "dur": 30, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 3100, "tid": 103079215104, "ts": 1754737153860682, "dur": 21, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 3100, "tid": 103079215104, "ts": 1754737153860704, "dur": 20, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 3100, "tid": 103079215104, "ts": 1754737153860726, "dur": 21, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 3100, "tid": 103079215104, "ts": 1754737153860749, "dur": 164, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 3100, "tid": 103079215104, "ts": 1754737153860917, "dur": 31, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3100, "tid": 103079215104, "ts": 1754737153860950, "dur": 1, "ph": "X", "name": "ProcessMessages 52", "args": {}}, {"pid": 3100, "tid": 103079215104, "ts": 1754737153860952, "dur": 61, "ph": "X", "name": "ReadAsync 52", "args": {}}, {"pid": 3100, "tid": 103079215104, "ts": 1754737153861018, "dur": 43, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3100, "tid": 103079215104, "ts": 1754737153861064, "dur": 139, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 3100, "tid": 103079215104, "ts": 1754737153861207, "dur": 50, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3100, "tid": 103079215104, "ts": 1754737153861262, "dur": 38, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 3100, "tid": 103079215104, "ts": 1754737153861302, "dur": 40, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 3100, "tid": 103079215104, "ts": 1754737153861346, "dur": 33, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 3100, "tid": 103079215104, "ts": 1754737153861382, "dur": 158, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 3100, "tid": 103079215104, "ts": 1754737153861545, "dur": 28, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3100, "tid": 103079215104, "ts": 1754737153861575, "dur": 256, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 3100, "tid": 103079215104, "ts": 1754737153861835, "dur": 25, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3100, "tid": 103079215104, "ts": 1754737153861862, "dur": 2, "ph": "X", "name": "ProcessMessages 8", "args": {}}, {"pid": 3100, "tid": 103079215104, "ts": 1754737153861864, "dur": 292795, "ph": "X", "name": "ReadAsync 8", "args": {}}, {"pid": 3100, "tid": 103079215104, "ts": 1754737154154669, "dur": 3, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 3100, "tid": 103079215104, "ts": 1754737154154673, "dur": 35, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3100, "tid": 103079215104, "ts": 1754737154154709, "dur": 26, "ph": "X", "name": "ProcessMessages 192", "args": {}}, {"pid": 3100, "tid": 103079215104, "ts": 1754737154154736, "dur": 3507, "ph": "X", "name": "ReadAsync 192", "args": {}}, {"pid": 3100, "tid": 103079215104, "ts": 1754737154158253, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 3100, "tid": 103079215104, "ts": 1754737154158257, "dur": 39, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3100, "tid": 103079215104, "ts": 1754737154158298, "dur": 1, "ph": "X", "name": "ProcessMessages 148", "args": {}}, {"pid": 3100, "tid": 103079215104, "ts": 1754737154158300, "dur": 35, "ph": "X", "name": "ReadAsync 148", "args": {}}, {"pid": 3100, "tid": 103079215104, "ts": 1754737154158340, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 3100, "tid": 103079215104, "ts": 1754737154158342, "dur": 48, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 3100, "tid": 103079215104, "ts": 1754737154158395, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 3100, "tid": 103079215104, "ts": 1754737154158397, "dur": 334, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 3100, "tid": 103079215104, "ts": 1754737154158737, "dur": 28, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3100, "tid": 103079215104, "ts": 1754737154158768, "dur": 339, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 3100, "tid": 103079215104, "ts": 1754737154159112, "dur": 31, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3100, "tid": 103079215104, "ts": 1754737154159147, "dur": 467, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 3100, "tid": 103079215104, "ts": 1754737154159619, "dur": 56, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3100, "tid": 103079215104, "ts": 1754737154159679, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 3100, "tid": 103079215104, "ts": 1754737154159680, "dur": 31, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 3100, "tid": 103079215104, "ts": 1754737154159713, "dur": 30, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 3100, "tid": 103079215104, "ts": 1754737154159747, "dur": 73, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 3100, "tid": 103079215104, "ts": 1754737154159823, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 3100, "tid": 103079215104, "ts": 1754737154159825, "dur": 31, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3100, "tid": 103079215104, "ts": 1754737154159859, "dur": 23, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 3100, "tid": 103079215104, "ts": 1754737154159886, "dur": 146, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 3100, "tid": 103079215104, "ts": 1754737154160035, "dur": 22, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3100, "tid": 103079215104, "ts": 1754737154160059, "dur": 139, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 3100, "tid": 103079215104, "ts": 1754737154160203, "dur": 36, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3100, "tid": 103079215104, "ts": 1754737154160242, "dur": 164, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 3100, "tid": 103079215104, "ts": 1754737154160409, "dur": 31, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3100, "tid": 103079215104, "ts": 1754737154160445, "dur": 100, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 3100, "tid": 103079215104, "ts": 1754737154160548, "dur": 23, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3100, "tid": 103079215104, "ts": 1754737154160572, "dur": 198, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 3100, "tid": 103079215104, "ts": 1754737154160774, "dur": 58, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3100, "tid": 103079215104, "ts": 1754737154160834, "dur": 38, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 3100, "tid": 103079215104, "ts": 1754737154160875, "dur": 2, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 3100, "tid": 103079215104, "ts": 1754737154160879, "dur": 212, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 3100, "tid": 103079215104, "ts": 1754737154161095, "dur": 44, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3100, "tid": 103079215104, "ts": 1754737154161142, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 3100, "tid": 103079215104, "ts": 1754737154161144, "dur": 253, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 3100, "tid": 103079215104, "ts": 1754737154161403, "dur": 30, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3100, "tid": 103079215104, "ts": 1754737154161436, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 3100, "tid": 103079215104, "ts": 1754737154161438, "dur": 353, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 3100, "tid": 103079215104, "ts": 1754737154161796, "dur": 38, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3100, "tid": 103079215104, "ts": 1754737154161836, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 3100, "tid": 103079215104, "ts": 1754737154161838, "dur": 165, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 3100, "tid": 103079215104, "ts": 1754737154162009, "dur": 34, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3100, "tid": 103079215104, "ts": 1754737154162046, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 3100, "tid": 103079215104, "ts": 1754737154162048, "dur": 241, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 3100, "tid": 103079215104, "ts": 1754737154162293, "dur": 42, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3100, "tid": 103079215104, "ts": 1754737154162340, "dur": 72, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 3100, "tid": 103079215104, "ts": 1754737154162417, "dur": 34, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3100, "tid": 103079215104, "ts": 1754737154162456, "dur": 148, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 3100, "tid": 103079215104, "ts": 1754737154162609, "dur": 29, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3100, "tid": 103079215104, "ts": 1754737154162641, "dur": 34, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 3100, "tid": 103079215104, "ts": 1754737154162676, "dur": 23, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3100, "tid": 103079215104, "ts": 1754737154162706, "dur": 39, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 3100, "tid": 103079215104, "ts": 1754737154162752, "dur": 31, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3100, "tid": 103079215104, "ts": 1754737154162787, "dur": 64, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 3100, "tid": 103079215104, "ts": 1754737154162856, "dur": 31, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3100, "tid": 103079215104, "ts": 1754737154162889, "dur": 169, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 3100, "tid": 103079215104, "ts": 1754737154163061, "dur": 26, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3100, "tid": 103079215104, "ts": 1754737154163091, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 3100, "tid": 103079215104, "ts": 1754737154163092, "dur": 35, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 3100, "tid": 103079215104, "ts": 1754737154163131, "dur": 1, "ph": "X", "name": "ProcessMessages 64", "args": {}}, {"pid": 3100, "tid": 103079215104, "ts": 1754737154163133, "dur": 35, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 3100, "tid": 103079215104, "ts": 1754737154163171, "dur": 27, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 3100, "tid": 103079215104, "ts": 1754737154163201, "dur": 1, "ph": "X", "name": "ProcessMessages 80", "args": {}}, {"pid": 3100, "tid": 103079215104, "ts": 1754737154163203, "dur": 40, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 3100, "tid": 103079215104, "ts": 1754737154163246, "dur": 1, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 3100, "tid": 103079215104, "ts": 1754737154163248, "dur": 36, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 3100, "tid": 103079215104, "ts": 1754737154163288, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 3100, "tid": 103079215104, "ts": 1754737154163290, "dur": 21, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 3100, "tid": 103079215104, "ts": 1754737154163315, "dur": 31, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 3100, "tid": 103079215104, "ts": 1754737154163350, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 3100, "tid": 103079215104, "ts": 1754737154163352, "dur": 28, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 3100, "tid": 103079215104, "ts": 1754737154163384, "dur": 35, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 3100, "tid": 103079215104, "ts": 1754737154163422, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 3100, "tid": 103079215104, "ts": 1754737154163424, "dur": 36, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 3100, "tid": 103079215104, "ts": 1754737154163464, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 3100, "tid": 103079215104, "ts": 1754737154163465, "dur": 31, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 3100, "tid": 103079215104, "ts": 1754737154163501, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 3100, "tid": 103079215104, "ts": 1754737154163503, "dur": 30, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 3100, "tid": 103079215104, "ts": 1754737154163535, "dur": 27, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 3100, "tid": 103079215104, "ts": 1754737154163566, "dur": 25, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 3100, "tid": 103079215104, "ts": 1754737154163593, "dur": 28, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 3100, "tid": 103079215104, "ts": 1754737154163625, "dur": 1, "ph": "X", "name": "ProcessMessages 28", "args": {}}, {"pid": 3100, "tid": 103079215104, "ts": 1754737154163626, "dur": 42, "ph": "X", "name": "ReadAsync 28", "args": {}}, {"pid": 3100, "tid": 103079215104, "ts": 1754737154163672, "dur": 1, "ph": "X", "name": "ProcessMessages 52", "args": {}}, {"pid": 3100, "tid": 103079215104, "ts": 1754737154163674, "dur": 51, "ph": "X", "name": "ReadAsync 52", "args": {}}, {"pid": 3100, "tid": 103079215104, "ts": 1754737154163729, "dur": 26, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3100, "tid": 103079215104, "ts": 1754737154163759, "dur": 25, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 3100, "tid": 103079215104, "ts": 1754737154163789, "dur": 30, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 3100, "tid": 103079215104, "ts": 1754737154163822, "dur": 26, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 3100, "tid": 103079215104, "ts": 1754737154163850, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 3100, "tid": 103079215104, "ts": 1754737154163852, "dur": 34, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 3100, "tid": 103079215104, "ts": 1754737154163889, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 3100, "tid": 103079215104, "ts": 1754737154163891, "dur": 30, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 3100, "tid": 103079215104, "ts": 1754737154163924, "dur": 24, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 3100, "tid": 103079215104, "ts": 1754737154163951, "dur": 39, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 3100, "tid": 103079215104, "ts": 1754737154163993, "dur": 26, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3100, "tid": 103079215104, "ts": 1754737154164022, "dur": 24, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 3100, "tid": 103079215104, "ts": 1754737154164049, "dur": 25, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 3100, "tid": 103079215104, "ts": 1754737154164077, "dur": 24, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 3100, "tid": 103079215104, "ts": 1754737154164104, "dur": 35, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 3100, "tid": 103079215104, "ts": 1754737154164142, "dur": 24, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3100, "tid": 103079215104, "ts": 1754737154164169, "dur": 28, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 3100, "tid": 103079215104, "ts": 1754737154164199, "dur": 54, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 3100, "tid": 103079215104, "ts": 1754737154164257, "dur": 28, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3100, "tid": 103079215104, "ts": 1754737154164287, "dur": 1, "ph": "X", "name": "ProcessMessages 52", "args": {}}, {"pid": 3100, "tid": 103079215104, "ts": 1754737154164289, "dur": 26, "ph": "X", "name": "ReadAsync 52", "args": {}}, {"pid": 3100, "tid": 103079215104, "ts": 1754737154164316, "dur": 1, "ph": "X", "name": "ProcessMessages 44", "args": {}}, {"pid": 3100, "tid": 103079215104, "ts": 1754737154164318, "dur": 23, "ph": "X", "name": "ReadAsync 44", "args": {}}, {"pid": 3100, "tid": 103079215104, "ts": 1754737154164342, "dur": 1, "ph": "X", "name": "ProcessMessages 36", "args": {}}, {"pid": 3100, "tid": 103079215104, "ts": 1754737154164345, "dur": 23, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 3100, "tid": 103079215104, "ts": 1754737154164372, "dur": 25, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 3100, "tid": 103079215104, "ts": 1754737154164399, "dur": 22, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 3100, "tid": 103079215104, "ts": 1754737154164424, "dur": 24, "ph": "X", "name": "ReadAsync 28", "args": {}}, {"pid": 3100, "tid": 103079215104, "ts": 1754737154164451, "dur": 33, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 3100, "tid": 103079215104, "ts": 1754737154164486, "dur": 22, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 3100, "tid": 103079215104, "ts": 1754737154164510, "dur": 28, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 3100, "tid": 103079215104, "ts": 1754737154164541, "dur": 1, "ph": "X", "name": "ProcessMessages 92", "args": {}}, {"pid": 3100, "tid": 103079215104, "ts": 1754737154164543, "dur": 27, "ph": "X", "name": "ReadAsync 92", "args": {}}, {"pid": 3100, "tid": 103079215104, "ts": 1754737154164572, "dur": 24, "ph": "X", "name": "ReadAsync 84", "args": {}}, {"pid": 3100, "tid": 103079215104, "ts": 1754737154164599, "dur": 30, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 3100, "tid": 103079215104, "ts": 1754737154164633, "dur": 116, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 3100, "tid": 103079215104, "ts": 1754737154164751, "dur": 26, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3100, "tid": 103079215104, "ts": 1754737154164780, "dur": 32, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 3100, "tid": 103079215104, "ts": 1754737154164815, "dur": 1, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 3100, "tid": 103079215104, "ts": 1754737154164817, "dur": 30, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 3100, "tid": 103079215104, "ts": 1754737154164850, "dur": 65, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 3100, "tid": 103079215104, "ts": 1754737154164919, "dur": 36, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3100, "tid": 103079215104, "ts": 1754737154164959, "dur": 213, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 3100, "tid": 103079215104, "ts": 1754737154165176, "dur": 41, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3100, "tid": 103079215104, "ts": 1754737154165220, "dur": 1, "ph": "X", "name": "ProcessMessages 36", "args": {}}, {"pid": 3100, "tid": 103079215104, "ts": 1754737154165222, "dur": 31, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 3100, "tid": 103079215104, "ts": 1754737154165258, "dur": 31, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 3100, "tid": 103079215104, "ts": 1754737154165292, "dur": 1, "ph": "X", "name": "ProcessMessages 28", "args": {}}, {"pid": 3100, "tid": 103079215104, "ts": 1754737154165293, "dur": 26, "ph": "X", "name": "ReadAsync 28", "args": {}}, {"pid": 3100, "tid": 103079215104, "ts": 1754737154165322, "dur": 121, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 3100, "tid": 103079215104, "ts": 1754737154165447, "dur": 35, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3100, "tid": 103079215104, "ts": 1754737154165487, "dur": 221, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 3100, "tid": 103079215104, "ts": 1754737154165711, "dur": 4, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 3100, "tid": 103079215104, "ts": 1754737154165716, "dur": 34, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3100, "tid": 103079215104, "ts": 1754737154165753, "dur": 1, "ph": "X", "name": "ProcessMessages 36", "args": {}}, {"pid": 3100, "tid": 103079215104, "ts": 1754737154165755, "dur": 28712, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 3100, "tid": 103079215104, "ts": 1754737154194477, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 3100, "tid": 103079215104, "ts": 1754737154194481, "dur": 53, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3100, "tid": 103079215104, "ts": 1754737154194537, "dur": 1182, "ph": "X", "name": "ProcessMessages 2950", "args": {}}, {"pid": 3100, "tid": 103079215104, "ts": 1754737154195721, "dur": 1484, "ph": "X", "name": "ReadAsync 2950", "args": {}}, {"pid": 3100, "tid": 103079215104, "ts": 1754737154197210, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 3100, "tid": 103079215104, "ts": 1754737154197211, "dur": 48, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3100, "tid": 103079215104, "ts": 1754737154197261, "dur": 1, "ph": "X", "name": "ProcessMessages 13", "args": {}}, {"pid": 3100, "tid": 103079215104, "ts": 1754737154197263, "dur": 7550, "ph": "X", "name": "ReadAsync 13", "args": {}}, {"pid": 3100, "tid": 24785, "ts": 1754737154205354, "dur": 958, "ph": "X", "name": "ReadEntireBinlogFromIpcAsync", "args": {}}, {"pid": 3100, "tid": 98784247808, "ph": "M", "name": "thread_name", "args": {"name": "WaitForBuildProgramInputDataBeingWrittenAndSendDagReadyMessageAsync"}}, {"pid": 3100, "tid": 98784247808, "ts": 1754737153671279, "dur": 82688, "ph": "X", "name": "await writeBuildProgramInputDataTask", "args": {}}, {"pid": 3100, "tid": 98784247808, "ts": 1754737153753969, "dur": 69045, "ph": "X", "name": "WritePipe.WaitConnectionAsync", "args": {}}, {"pid": 3100, "tid": 98784247808, "ts": 1754737153823015, "dur": 37, "ph": "X", "name": "WriteDagReadyMessage", "args": {}}, {"pid": 3100, "tid": 24785, "ts": 1754737154206314, "dur": 4, "ph": "X", "name": "WaitForBuildProgramInputDataBeingWrittenAndSendDagReadyMessageAsync", "args": {}}, {"pid": 3100, "tid": 94489280512, "ph": "M", "name": "thread_name", "args": {"name": "BuildAsync"}}, {"pid": 3100, "tid": 94489280512, "ts": 1754737153548039, "dur": 656821, "ph": "X", "name": "RunBackend", "args": {}}, {"pid": 3100, "tid": 94489280512, "ts": 1754737153548164, "dur": 123071, "ph": "X", "name": "BackendProgram.Start", "args": {}}, {"pid": 3100, "tid": 94489280512, "ts": 1754737154204862, "dur": 39, "ph": "X", "name": "await WaitForAndApplyScriptUpdaters", "args": {}}, {"pid": 3100, "tid": 94489280512, "ts": 1754737154204874, "dur": 13, "ph": "X", "name": "await <PERSON><PERSON>tUp<PERSON><PERSON>", "args": {}}, {"pid": 3100, "tid": 94489280512, "ts": 1754737154204903, "dur": 1, "ph": "X", "name": "await taskToReadBuildProgramOutput", "args": {}}, {"pid": 3100, "tid": 24785, "ts": 1754737154206320, "dur": 4, "ph": "X", "name": "BuildAsync", "args": {}}, {"cat": "", "pid": 12345, "tid": 0, "ts": 0, "ph": "M", "name": "process_name", "args": {"name": "bee_backend"}}, {"pid": 12345, "tid": 0, "ts": 1754737153823518, "dur": 1506, "ph": "X", "name": "DriverInitData", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1754737153825038, "dur": 782, "ph": "X", "name": "RemoveStaleOutputs", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1754737153825928, "dur": 58, "ph": "X", "name": "<PERSON><PERSON>", "args": {"detail": "PrepareNodes"}}, {"pid": 12345, "tid": 0, "ts": 1754737153825986, "dur": 461, "ph": "X", "name": "BuildQueueInit", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1754737153826791, "dur": 55, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.QuickSearchModule.dll_BE454E773931EEAC.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1754737153826899, "dur": 193, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.TextCoreFontEngineModule.dll_2933F28E75319B3A.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1754737153827961, "dur": 52, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ParticleSystemModule.dll_33F6F62BD0C52DED.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1754737153832070, "dur": 90, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.Searcher.Editor.ref.dll_12B7E1785E41BE0E.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1754737153832270, "dur": 110, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.Searcher.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1754737153843612, "dur": 53, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.RenderPipelines.Core.Editor.dll"}}, {"pid": 12345, "tid": 0, "ts": 1754737153849433, "dur": 312, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Assembly-CSharp-Editor.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 0, "ts": 1754737153826474, "dur": 23404, "ph": "X", "name": "EnqueueRequestedNodes", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1754737153849891, "dur": 346670, "ph": "X", "name": "WaitForBuildFinished", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1754737154196562, "dur": 287, "ph": "X", "name": "JoinBuildThread", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1754737154197617, "dur": 68, "ph": "X", "name": "BuildQueueDestroyTail", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1754737154197721, "dur": 1077, "ph": "X", "name": "<PERSON><PERSON>", "args": {"detail": "Write AllBuiltNodes"}}, {"pid": 12345, "tid": 1, "ts": 1754737153826854, "dur": 23129, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1754737153849989, "dur": 56, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.VehiclesModule.dll_B4028357F84AC7DF.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1754737153850185, "dur": 92, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ContentLoadModule.dll_9B44DAA96A1B4B8B.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1754737153850386, "dur": 110, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1754737153850496, "dur": 338, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityWebRequestAssetBundleModule.dll_20062661B6C0E97A.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1754737153850841, "dur": 67, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/UnityEngine.TestRunner.dll.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1754737153851952, "dur": 171, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\GameSoft\\Unity\\Editor\\2022.3.38f1c1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Runtime.Handles.dll"}}, {"pid": 12345, "tid": 1, "ts": 1754737153853532, "dur": 333, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.test-framework@1.1.33\\UnityEngine.TestRunner\\Utils\\AssemblyProvider\\IScriptingRuntimeProxy.cs"}}, {"pid": 12345, "tid": 1, "ts": 1754737153850924, "dur": 3198, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/UnityEngine.TestRunner.dll (+2 others)"}}, {"pid": 12345, "tid": 1, "ts": 1754737153854186, "dur": 199, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/UnityEditor.TestRunner.dll.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1754737153854983, "dur": 50, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\GameSoft\\Unity\\Editor\\2022.3.38f1c1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.IO.IsolatedStorage.dll"}}, {"pid": 12345, "tid": 1, "ts": 1754737153855681, "dur": 61, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\GameSoft\\Unity\\Editor\\2022.3.38f1c1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Threading.Tasks.dll"}}, {"pid": 12345, "tid": 1, "ts": 1754737153856759, "dur": 69, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.test-framework@1.1.33\\UnityEditor.TestRunner\\NUnitExtension\\Attributes\\AssetPipelineIgnore.cs"}}, {"pid": 12345, "tid": 1, "ts": 1754737153857093, "dur": 157, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.test-framework@1.1.33\\UnityEditor.TestRunner\\TestLaunchers\\PostbuildCleanupAttributeFinder.cs"}}, {"pid": 12345, "tid": 1, "ts": 1754737153854401, "dur": 2940, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/UnityEditor.TestRunner.dll (+2 others)"}}, {"pid": 12345, "tid": 1, "ts": 1754737153857489, "dur": 195, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/UnityEditor.UI.dll (+2 others)"}}, {"pid": 12345, "tid": 1, "ts": 1754737153857743, "dur": 74, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.Burst.dll.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1754737153857850, "dur": 420, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Burst.dll (+2 others)"}}, {"pid": 12345, "tid": 1, "ts": 1754737153858271, "dur": 62, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1754737153858359, "dur": 160, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.Burst.CodeGen.dll.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1754737153858531, "dur": 234, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Burst.CodeGen.dll (+2 others)"}}, {"pid": 12345, "tid": 1, "ts": 1754737153858766, "dur": 101, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1754737153858896, "dur": 411, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPP-Configuration Library/ilpp-configuration.nevergeneratedoutput"}}, {"pid": 12345, "tid": 1, "ts": 1754737153859335, "dur": 78, "ph": "X", "name": "StoreTimestampsOfNonGeneratedInputFiles", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1754737153859850, "dur": 295305, "ph": "X", "name": "ILPP-Configuration", "args": {"detail": "Library/ilpp-configuration.nevergeneratedoutput"}}, {"pid": 12345, "tid": 1, "ts": 1754737154157271, "dur": 1419, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/com.crashkonijn.goap.demos.complex.dll (+pdb)"}}, {"pid": 12345, "tid": 1, "ts": 1754737154158738, "dur": 1294, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.VisualScripting.Flow.dll (+pdb)"}}, {"pid": 12345, "tid": 1, "ts": 1754737154160032, "dur": 83, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1754737154160124, "dur": 1393, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/UnityEngine.UI.dll (+pdb)"}}, {"pid": 12345, "tid": 1, "ts": 1754737154161517, "dur": 81, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1754737154162074, "dur": 52, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\GameSoft\\Unity\\Editor\\2022.3.38f1c1\\Editor\\Data\\Tools\\ilpp\\Unity.ILPP.Runner\\api-ms-win-core-profile-l1-1-0.dll"}}, {"pid": 12345, "tid": 1, "ts": 1754737154161609, "dur": 1657, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.ShaderGraph.Utilities.dll (+pdb)"}}, {"pid": 12345, "tid": 1, "ts": 1754737154163266, "dur": 95, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1754737154163371, "dur": 1645, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.ShaderGraph.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 1, "ts": 1754737154165112, "dur": 666, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1754737154165807, "dur": 30771, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1754737153826735, "dur": 23199, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1754737153850295, "dur": 96, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\GameSoft\\Unity\\Editor\\2022.3.38f1c1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.TerrainModule.dll"}}, {"pid": 12345, "tid": 2, "ts": 1754737153850393, "dur": 85, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\GameSoft\\Unity\\Editor\\2022.3.38f1c1\\Editor\\Data\\Tools\\ScriptUpdater\\ApiUpdater.MovedFromExtractor.exe"}}, {"pid": 12345, "tid": 2, "ts": 1754737153850294, "dur": 185, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.TerrainModule.dll_20F5B884F62AB761.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1754737153850592, "dur": 220, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.VisualScripting.TextureAssets.dll_E1772EF4FEB70289.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1754737153850837, "dur": 171, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Collections.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 2, "ts": 1754737153851041, "dur": 105, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Universal.Runtime.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 2, "ts": 1754737153851474, "dur": 54, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/GlassSystem.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 2, "ts": 1754737153851655, "dur": 131, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Collections.DocCodeSamples.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 2, "ts": 1754737153851958, "dur": 161, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1754737153852128, "dur": 280, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1754737153852408, "dur": 305, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1754737153852713, "dur": 238, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1754737153852951, "dur": 216, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1754737153853167, "dur": 197, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1754737153853364, "dur": 495, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1754737153853859, "dur": 208, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1754737153854067, "dur": 203, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1754737153854270, "dur": 229, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1754737153854500, "dur": 257, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1754737153854757, "dur": 306, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1754737153855063, "dur": 227, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1754737153855291, "dur": 177, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1754737153855468, "dur": 512, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1754737153855981, "dur": 170, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1754737153856151, "dur": 244, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1754737153856395, "dur": 436, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1754737153856831, "dur": 182, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1754737153857014, "dur": 51, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1754737153857065, "dur": 213, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1754737153857278, "dur": 159, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1754737153857437, "dur": 322, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1754737153857760, "dur": 68, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.VSCode.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1754737153857857, "dur": 334, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.VSCode.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 2, "ts": 1754737153858191, "dur": 103, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1754737153858340, "dur": 55, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1754737153858396, "dur": 100, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/com.crashkonijn.goap.demos.complex.dll.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1754737153858623, "dur": 257, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1754737153858920, "dur": 55, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.InputSystem.TestFramework.dll.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1754737153858990, "dur": 170, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.InputSystem.TestFramework.dll (+2 others)"}}, {"pid": 12345, "tid": 2, "ts": 1754737153859193, "dur": 167, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1754737153859360, "dur": 112, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1754737153859472, "dur": 603, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1754737153860075, "dur": 297170, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1754737154157245, "dur": 1467, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Tests.dll (+pdb)"}}, {"pid": 12345, "tid": 2, "ts": 1754737154158713, "dur": 51, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1754737154159570, "dur": 57, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\GameSoft\\Unity\\Editor\\2022.3.38f1c1\\Editor\\Data\\Tools\\ilpp\\Unity.ILPP.Runner\\System.Security.Cryptography.Xml.dll"}}, {"pid": 12345, "tid": 2, "ts": 1754737154158800, "dur": 1343, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Mathematics.dll (+pdb)"}}, {"pid": 12345, "tid": 2, "ts": 1754737154160293, "dur": 79, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\GameSoft\\Unity\\Editor\\2022.3.38f1c1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.ContentLoadModule.dll"}}, {"pid": 12345, "tid": 2, "ts": 1754737154160646, "dur": 112, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\GameSoft\\Unity\\Editor\\2022.3.38f1c1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.IO.Compression.ZipFile.dll"}}, {"pid": 12345, "tid": 2, "ts": 1754737154161345, "dur": 496, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\GameSoft\\Unity\\Editor\\2022.3.38f1c1\\Editor\\Data\\Tools\\ilpp\\Unity.ILPP.Runner\\Microsoft.AspNetCore.SignalR.Common.dll"}}, {"pid": 12345, "tid": 2, "ts": 1754737154162074, "dur": 82, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\GameSoft\\Unity\\Editor\\2022.3.38f1c1\\Editor\\Data\\Tools\\ilpp\\Unity.ILPP.Runner\\System.Threading.dll"}}, {"pid": 12345, "tid": 2, "ts": 1754737154162176, "dur": 52, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\GameSoft\\Unity\\Editor\\2022.3.38f1c1\\Editor\\Data\\Tools\\ilpp\\Unity.ILPP.Runner\\System.Threading.Thread.dll"}}, {"pid": 12345, "tid": 2, "ts": 1754737154160187, "dur": 2660, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/GlassSystem.dll (+pdb)"}}, {"pid": 12345, "tid": 2, "ts": 1754737154162848, "dur": 72, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1754737154163574, "dur": 145, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\GameSoft\\Unity\\Editor\\2022.3.38f1c1\\Editor\\Data\\Tools\\ilpp\\Unity.ILPP.Runner\\Microsoft.AspNetCore.Html.Abstractions.dll"}}, {"pid": 12345, "tid": 2, "ts": 1754737154162929, "dur": 2077, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/CrashKonijn.Goap.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 2, "ts": 1754737154165062, "dur": 244, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1754737154165324, "dur": 31266, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1754737153826751, "dur": 23197, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1754737153849956, "dur": 55, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.UIElementsSamplesModule.dll_623E7535F4BD3DF5.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1754737153850096, "dur": 89, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1754737153850238, "dur": 90, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ScreenCaptureModule.dll_C5337FBB8908AF36.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1754737153850352, "dur": 65, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UIModule.dll_D89CC358493361BC.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1754737153850419, "dur": 117, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.collections@1.2.4\\Unity.Collections.LowLevel.ILSupport\\Unity.Collections.LowLevel.ILSupport.dll"}}, {"pid": 12345, "tid": 3, "ts": 1754737153850418, "dur": 119, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.Collections.LowLevel.ILSupport.dll_2E40CE80E145E521.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1754737153850589, "dur": 306, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Burst.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 3, "ts": 1754737153850993, "dur": 86, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Searcher.Editor.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 3, "ts": 1754737153851283, "dur": 68, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Cinemachine.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 3, "ts": 1754737153851624, "dur": 65, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.CollabProxy.Editor.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 3, "ts": 1754737153851979, "dur": 169, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1754737153852151, "dur": 210, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1754737153852362, "dur": 206, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1754737153852568, "dur": 288, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1754737153852857, "dur": 208, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1754737153853065, "dur": 239, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1754737153853305, "dur": 181, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1754737153853486, "dur": 566, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1754737153854052, "dur": 232, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1754737153854284, "dur": 212, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1754737153854496, "dur": 526, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1754737153855022, "dur": 177, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1754737153855199, "dur": 254, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1754737153855453, "dur": 154, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1754737153855607, "dur": 167, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1754737153855774, "dur": 493, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1754737153856268, "dur": 185, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1754737153856453, "dur": 165, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1754737153856618, "dur": 258, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1754737153856876, "dur": 169, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1754737153857045, "dur": 251, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1754737153857296, "dur": 114, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1754737153857410, "dur": 335, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1754737153857754, "dur": 68, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.Mathematics.dll.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1754737153857845, "dur": 182, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.Mathematics.dll.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1754737153858498, "dur": 50, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.mathematics@1.2.6\\Unity.Mathematics\\uint4x3.gen.cs"}}, {"pid": 12345, "tid": 3, "ts": 1754737153858027, "dur": 528, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Mathematics.dll (+2 others)"}}, {"pid": 12345, "tid": 3, "ts": 1754737153858556, "dur": 285, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1754737153858879, "dur": 79, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.Collections.dll.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1754737153858970, "dur": 318, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Collections.dll (+2 others)"}}, {"pid": 12345, "tid": 3, "ts": 1754737153859346, "dur": 51, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/CrashKonijn.Goap.Resolver.dll.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1754737153859419, "dur": 178, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/CrashKonijn.Goap.Resolver.dll (+2 others)"}}, {"pid": 12345, "tid": 3, "ts": 1754737153859625, "dur": 374, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/CrashKonijn.Goap.Runtime.dll (+2 others)"}}, {"pid": 12345, "tid": 3, "ts": 1754737153860060, "dur": 347, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/CrashKonijn.Goap.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 3, "ts": 1754737153860439, "dur": 268, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/com.crashkonijn.goap.demos.simple.dll (+2 others)"}}, {"pid": 12345, "tid": 3, "ts": 1754737153860754, "dur": 296531, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1754737154157286, "dur": 1434, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.RenderPipelines.Universal.Shaders.dll (+pdb)"}}, {"pid": 12345, "tid": 3, "ts": 1754737154158784, "dur": 1363, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Collections.dll (+pdb)"}}, {"pid": 12345, "tid": 3, "ts": 1754737154160148, "dur": 90, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1754737154160410, "dur": 132, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\GameSoft\\Unity\\Editor\\2022.3.38f1c1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.UnityWebRequestWWWModule.dll"}}, {"pid": 12345, "tid": 3, "ts": 1754737154161540, "dur": 122, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\GameSoft\\Unity\\Editor\\2022.3.38f1c1\\Editor\\Data\\Tools\\ilpp\\Unity.ILPP.Runner\\System.IO.FileSystem.dll"}}, {"pid": 12345, "tid": 3, "ts": 1754737154161704, "dur": 107, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\GameSoft\\Unity\\Editor\\2022.3.38f1c1\\Editor\\Data\\Tools\\ilpp\\Unity.ILPP.Runner\\System.Net.Requests.dll"}}, {"pid": 12345, "tid": 3, "ts": 1754737154161878, "dur": 111, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\GameSoft\\Unity\\Editor\\2022.3.38f1c1\\Editor\\Data\\Tools\\ilpp\\Unity.ILPP.Runner\\System.Reflection.Emit.dll"}}, {"pid": 12345, "tid": 3, "ts": 1754737154160245, "dur": 2421, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/com.unity.cinemachine.editor.dll (+pdb)"}}, {"pid": 12345, "tid": 3, "ts": 1754737154162667, "dur": 128, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1754737154163501, "dur": 79, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\GameSoft\\Unity\\Editor\\2022.3.38f1c1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Resources.Writer.dll"}}, {"pid": 12345, "tid": 3, "ts": 1754737154162805, "dur": 2430, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Collections.BurstCompatibilityGen.dll (+pdb)"}}, {"pid": 12345, "tid": 3, "ts": 1754737154165332, "dur": 31335, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1754737153826699, "dur": 23222, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1754737153850182, "dur": 86, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ClusterRendererModule.dll_D863307DB92126C9.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1754737153850285, "dur": 207, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1754737153850492, "dur": 75, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.RuntimeInitializeOnLoadManagerInitializerModule.dll_359E4B0AFE7F50CE.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1754737153850584, "dur": 61, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/UnityEditor.UI.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 4, "ts": 1754737153850859, "dur": 176, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/CrashKonijn.Agent.Core.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 4, "ts": 1754737153851152, "dur": 57, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.ShaderGraph.Editor.rsp"}}, {"pid": 12345, "tid": 4, "ts": 1754737153851352, "dur": 207, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.TextMeshPro.Editor.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 4, "ts": 1754737153851680, "dur": 115, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Tests.rsp"}}, {"pid": 12345, "tid": 4, "ts": 1754737153851979, "dur": 386, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1754737153852365, "dur": 196, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1754737153852561, "dur": 198, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1754737153852759, "dur": 224, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1754737153852983, "dur": 178, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1754737153853161, "dur": 289, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1754737153853451, "dur": 509, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1754737153853961, "dur": 213, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1754737153854194, "dur": 234, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1754737153854428, "dur": 390, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1754737153854818, "dur": 231, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1754737153855049, "dur": 199, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1754737153855248, "dur": 219, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1754737153855467, "dur": 165, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1754737153855632, "dur": 297, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1754737153855929, "dur": 171, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1754737153856100, "dur": 399, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1754737153856499, "dur": 255, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1754737153856754, "dur": 315, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1754737153857109, "dur": 86, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1754737153857228, "dur": 174, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1754737153857425, "dur": 338, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1754737153857764, "dur": 65, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/CrashKonijn.Goap.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1754737153857846, "dur": 72, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.ShaderGraph.ShaderGraphLibrary.dll.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1754737153857932, "dur": 433, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.ShaderGraph.ShaderGraphLibrary.dll (+2 others)"}}, {"pid": 12345, "tid": 4, "ts": 1754737153858366, "dur": 59, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1754737153858443, "dur": 376, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/CrashKonijn.Agent.Runtime.dll (+2 others)"}}, {"pid": 12345, "tid": 4, "ts": 1754737153858880, "dur": 76, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/CrashKonijn.Goap.Runtime.dll.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1754737153858967, "dur": 375, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1754737153859342, "dur": 81, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.Collections.BurstCompatibilityGen.dll.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1754737153859436, "dur": 254, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Collections.BurstCompatibilityGen.dll (+2 others)"}}, {"pid": 12345, "tid": 4, "ts": 1754737153859717, "dur": 354, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1754737153860071, "dur": 1719, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1754737153861791, "dur": 81, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Assembly-CSharp-Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1754737153861888, "dur": 295393, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1754737154157282, "dur": 1419, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.InputSystem.ForUI.dll (+pdb)"}}, {"pid": 12345, "tid": 4, "ts": 1754737154159570, "dur": 101, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\GameSoft\\Unity\\Editor\\2022.3.38f1c1\\Editor\\Data\\Tools\\ilpp\\Unity.ILPP.Runner\\System.IO.Compression.dll"}}, {"pid": 12345, "tid": 4, "ts": 1754737154158749, "dur": 1531, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.RenderPipelines.Core.Runtime.dll (+pdb)"}}, {"pid": 12345, "tid": 4, "ts": 1754737154160281, "dur": 98, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1754737154160404, "dur": 211, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\GameSoft\\Unity\\Editor\\2022.3.38f1c1\\Editor\\Data\\Managed\\UnityEngine\\UnityEditor.SceneViewModule.dll"}}, {"pid": 12345, "tid": 4, "ts": 1754737154160967, "dur": 71, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\GameSoft\\Unity\\Editor\\2022.3.38f1c1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.AppContext.dll"}}, {"pid": 12345, "tid": 4, "ts": 1754737154161338, "dur": 217, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\GameSoft\\Unity\\Editor\\2022.3.38f1c1\\Editor\\Data\\Tools\\ilpp\\Unity.ILPP.Runner\\coreclr.dll"}}, {"pid": 12345, "tid": 4, "ts": 1754737154161832, "dur": 253, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\GameSoft\\Unity\\Editor\\2022.3.38f1c1\\Editor\\Data\\Tools\\ilpp\\Unity.ILPP.Runner\\Microsoft.Extensions.FileProviders.Abstractions.dll"}}, {"pid": 12345, "tid": 4, "ts": 1754737154162453, "dur": 314, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\GameSoft\\Unity\\Editor\\2022.3.38f1c1\\Editor\\Data\\Tools\\ilpp\\Unity.ILPP.Runner\\System.Reflection.Emit.Lightweight.dll"}}, {"pid": 12345, "tid": 4, "ts": 1754737154163319, "dur": 110, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\GameSoft\\Unity\\Editor\\2022.3.38f1c1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Text.Encoding.Extensions.dll"}}, {"pid": 12345, "tid": 4, "ts": 1754737154160387, "dur": 3104, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.RenderPipelines.Core.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 4, "ts": 1754737154163491, "dur": 76, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1754737154163576, "dur": 165, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\Bee\\artifacts\\1900b0aE.dag\\Unity.Mathematics.Editor.dll"}}, {"pid": 12345, "tid": 4, "ts": 1754737154164282, "dur": 132, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\GameSoft\\Unity\\Editor\\2022.3.38f1c1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Net.WebSockets.dll"}}, {"pid": 12345, "tid": 4, "ts": 1754737154164486, "dur": 65, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\GameSoft\\Unity\\Editor\\2022.3.38f1c1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\System.ComponentModel.Composition.dll"}}, {"pid": 12345, "tid": 4, "ts": 1754737154164678, "dur": 79, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\GameSoft\\Unity\\Editor\\2022.3.38f1c1\\Editor\\Data\\Tools\\ilpp\\Unity.ILPP.Runner\\Microsoft.AspNetCore.Authentication.Cookies.dll"}}, {"pid": 12345, "tid": 4, "ts": 1754737154164902, "dur": 176, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\GameSoft\\Unity\\Editor\\2022.3.38f1c1\\Editor\\Data\\Tools\\ilpp\\Unity.ILPP.Runner\\Microsoft.Extensions.FileProviders.Composite.dll"}}, {"pid": 12345, "tid": 4, "ts": 1754737154163575, "dur": 2066, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Mathematics.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 4, "ts": 1754737154165707, "dur": 30848, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1754737153826674, "dur": 23233, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1754737153849918, "dur": 2918, "ph": "X", "name": "CheckDagSignatures", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1754737153852837, "dur": 195, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1754737153853032, "dur": 204, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1754737153853236, "dur": 167, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1754737153853403, "dur": 525, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1754737153853933, "dur": 212, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1754737153854145, "dur": 409, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1754737153854555, "dur": 230, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1754737153854785, "dur": 170, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1754737153854955, "dur": 381, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1754737153855336, "dur": 198, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1754737153855535, "dur": 423, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1754737153855958, "dur": 215, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1754737153856173, "dur": 179, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1754737153856353, "dur": 169, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1754737153856523, "dur": 171, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1754737153856694, "dur": 570, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1754737153857264, "dur": 181, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1754737153857445, "dur": 312, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1754737153857762, "dur": 66, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.PlasticSCM.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1754737153857844, "dur": 143, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.PlasticSCM.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1754737153857988, "dur": 875, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.PlasticSCM.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 5, "ts": 1754737153858979, "dur": 79, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.CollabProxy.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1754737153859073, "dur": 258, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.CollabProxy.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 5, "ts": 1754737153859331, "dur": 75, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1754737153859411, "dur": 57, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1754737153859468, "dur": 586, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1754737153860054, "dur": 261, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/com.crashkonijn.docs.getting_started.dll (+2 others)"}}, {"pid": 12345, "tid": 5, "ts": 1754737153860315, "dur": 154, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1754737153860472, "dur": 296792, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1754737154157267, "dur": 1445, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.VisualScripting.State.dll (+pdb)"}}, {"pid": 12345, "tid": 5, "ts": 1754737154158756, "dur": 1683, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/com.crashkonijn.docs.getting_started.dll (+pdb)"}}, {"pid": 12345, "tid": 5, "ts": 1754737154160440, "dur": 100, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1754737154160880, "dur": 94, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\GameSoft\\Unity\\Editor\\2022.3.38f1c1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Security.SecureString.dll"}}, {"pid": 12345, "tid": 5, "ts": 1754737154161874, "dur": 209, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\GameSoft\\Unity\\Editor\\2022.3.38f1c1\\Editor\\Data\\Tools\\ilpp\\Unity.ILPP.Runner\\System.Net.WebSockets.dll"}}, {"pid": 12345, "tid": 5, "ts": 1754737154162116, "dur": 66, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\GameSoft\\Unity\\Editor\\2022.3.38f1c1\\Editor\\Data\\Tools\\ilpp\\Unity.ILPP.Runner\\System.Security.AccessControl.dll"}}, {"pid": 12345, "tid": 5, "ts": 1754737154162760, "dur": 121, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Assets\\MathNet.Numerics.dll"}}, {"pid": 12345, "tid": 5, "ts": 1754737154160550, "dur": 2348, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.CollabProxy.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 5, "ts": 1754737154162898, "dur": 291, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1754737154163320, "dur": 96, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\GameSoft\\Unity\\Editor\\2022.3.38f1c1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Collections.dll"}}, {"pid": 12345, "tid": 5, "ts": 1754737154163819, "dur": 313, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.collab-proxy@2.8.2\\Lib\\Editor\\Unity.Plastic.Newtonsoft.Json.dll"}}, {"pid": 12345, "tid": 5, "ts": 1754737154164147, "dur": 143, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\GameSoft\\Unity\\Editor\\2022.3.38f1c1\\Editor\\Data\\Tools\\ilpp\\Unity.ILPP.Runner\\api-ms-win-core-file-l1-1-0.dll"}}, {"pid": 12345, "tid": 5, "ts": 1754737154164381, "dur": 70, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\GameSoft\\Unity\\Editor\\2022.3.38f1c1\\Editor\\Data\\Tools\\ilpp\\Unity.ILPP.Runner\\Microsoft.AspNetCore.Http.Extensions.dll"}}, {"pid": 12345, "tid": 5, "ts": 1754737154164529, "dur": 189, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\GameSoft\\Unity\\Editor\\2022.3.38f1c1\\Editor\\Data\\Tools\\ilpp\\Unity.ILPP.Runner\\Microsoft.AspNetCore.WebSockets.dll"}}, {"pid": 12345, "tid": 5, "ts": 1754737154164750, "dur": 169, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\GameSoft\\Unity\\Editor\\2022.3.38f1c1\\Editor\\Data\\Tools\\ilpp\\Unity.ILPP.Runner\\Microsoft.Extensions.FileSystemGlobbing.dll"}}, {"pid": 12345, "tid": 5, "ts": 1754737154165023, "dur": 691, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\GameSoft\\Unity\\Editor\\2022.3.38f1c1\\Editor\\Data\\Tools\\ilpp\\Unity.ILPP.Runner\\System.Diagnostics.Process.dll"}}, {"pid": 12345, "tid": 5, "ts": 1754737154163199, "dur": 2979, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Timeline.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 5, "ts": 1754737154166235, "dur": 30354, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1754737153826788, "dur": 23176, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1754737153849975, "dur": 56, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityWebRequestWWWModule.dll_BA3DE9AF0772CF8F.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1754737153850159, "dur": 61, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.AIModule.dll_DD29252AECEC5C10.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1754737153850407, "dur": 135, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.collab-proxy@2.8.2\\Lib\\Editor\\Unity.Plastic.Newtonsoft.Json.dll"}}, {"pid": 12345, "tid": 6, "ts": 1754737153850405, "dur": 139, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.Plastic.Newtonsoft.Json.dll_CF17D4CA2A31CD8C.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1754737153850582, "dur": 85, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Burst.rsp2"}}, {"pid": 12345, "tid": 6, "ts": 1754737153850847, "dur": 66, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/UnityEngine.UI.dll.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1754737153851128, "dur": 56, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\GameSoft\\Unity\\Editor\\2022.3.38f1c1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netfx\\System.ServiceModel.Web.dll"}}, {"pid": 12345, "tid": 6, "ts": 1754737153851956, "dur": 158, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\GameSoft\\Unity\\Editor\\2022.3.38f1c1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Runtime.InteropServices.dll"}}, {"pid": 12345, "tid": 6, "ts": 1754737153852168, "dur": 66, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\GameSoft\\Unity\\Editor\\2022.3.38f1c1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Runtime.Serialization.Primitives.dll"}}, {"pid": 12345, "tid": 6, "ts": 1754737153852514, "dur": 60, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\GameSoft\\Unity\\Editor\\2022.3.38f1c1\\Editor\\Data\\NetStandard\\ref\\2.1.0\\netstandard.dll"}}, {"pid": 12345, "tid": 6, "ts": 1754737153850927, "dur": 2601, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/UnityEngine.UI.dll (+2 others)"}}, {"pid": 12345, "tid": 6, "ts": 1754737153853529, "dur": 369, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1754737153853932, "dur": 202, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1754737153854134, "dur": 203, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1754737153854337, "dur": 178, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1754737153854515, "dur": 282, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1754737153854797, "dur": 240, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1754737153855037, "dur": 195, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1754737153855232, "dur": 219, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1754737153855451, "dur": 308, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1754737153855759, "dur": 208, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1754737153855967, "dur": 296, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1754737153856263, "dur": 657, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1754737153856920, "dur": 216, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1754737153857170, "dur": 66, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1754737153857236, "dur": 239, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1754737153857476, "dur": 291, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1754737153857767, "dur": 66, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.Rider.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1754737153858011, "dur": 61, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\GameSoft\\Unity\\Editor\\2022.3.38f1c1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.IO.Pipes.dll"}}, {"pid": 12345, "tid": 6, "ts": 1754737153857859, "dur": 411, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Rider.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 6, "ts": 1754737153858270, "dur": 271, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1754737153858607, "dur": 274, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1754737153858928, "dur": 418, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1754737153859346, "dur": 116, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1754737153859462, "dur": 70, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Universal.Config.Runtime.dll.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1754737153859551, "dur": 206, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Universal.Config.Runtime.dll (+2 others)"}}, {"pid": 12345, "tid": 6, "ts": 1754737153859808, "dur": 252, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1754737153860060, "dur": 1091, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1754737153861152, "dur": 50, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.SettingsProvider.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1754737153861229, "dur": 172, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.SettingsProvider.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 6, "ts": 1754737153861449, "dur": 295857, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1754737154157307, "dur": 1392, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.RenderPipelines.Universal.Runtime.dll (+pdb)"}}, {"pid": 12345, "tid": 6, "ts": 1754737154158699, "dur": 56, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1754737154158811, "dur": 152, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\GameSoft\\Unity\\Editor\\2022.3.38f1c1\\Editor\\Data\\Managed\\UnityEngine\\UnityEditor.UnityConnectModule.dll"}}, {"pid": 12345, "tid": 6, "ts": 1754737154160096, "dur": 205, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\GameSoft\\Unity\\Editor\\2022.3.38f1c1\\Editor\\Data\\Tools\\ilpp\\Unity.ILPP.Runner\\Microsoft.VisualBasic.dll"}}, {"pid": 12345, "tid": 6, "ts": 1754737154158765, "dur": 2547, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/com.crashkonijn.goap.demos.turn-based.dll (+pdb)"}}, {"pid": 12345, "tid": 6, "ts": 1754737154161371, "dur": 50, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\Bee\\artifacts\\1900b0aE.dag\\URPWizard.dll"}}, {"pid": 12345, "tid": 6, "ts": 1754737154161749, "dur": 61, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\GameSoft\\Unity\\Editor\\2022.3.38f1c1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.UnityAnalyticsModule.dll"}}, {"pid": 12345, "tid": 6, "ts": 1754737154161832, "dur": 252, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\GameSoft\\Unity\\Editor\\2022.3.38f1c1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netfx\\System.Net.dll"}}, {"pid": 12345, "tid": 6, "ts": 1754737154162116, "dur": 71, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\GameSoft\\Unity\\Editor\\2022.3.38f1c1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Diagnostics.Tracing.dll"}}, {"pid": 12345, "tid": 6, "ts": 1754737154161360, "dur": 2608, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/URPWizard.dll (+pdb)"}}, {"pid": 12345, "tid": 6, "ts": 1754737154164105, "dur": 186, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\Bee\\artifacts\\1900b0aE.dag\\post-processed\\Unity.Collections.DocCodeSamples.dll"}}, {"pid": 12345, "tid": 6, "ts": 1754737154164105, "dur": 186, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.Collections.DocCodeSamples.dll"}}, {"pid": 12345, "tid": 6, "ts": 1754737154164332, "dur": 638, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1754737154164978, "dur": 55, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1754737154165071, "dur": 244, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1754737154165316, "dur": 31281, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1754737153826886, "dur": 23108, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1754737153850386, "dur": 99, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1754737153850588, "dur": 171, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.InputSystem.rsp2"}}, {"pid": 12345, "tid": 7, "ts": 1754737153850875, "dur": 61, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Flow.rsp"}}, {"pid": 12345, "tid": 7, "ts": 1754737153850946, "dur": 96, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Flow.rsp"}}, {"pid": 12345, "tid": 7, "ts": 1754737153851054, "dur": 103, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Burst.Editor.rsp2"}}, {"pid": 12345, "tid": 7, "ts": 1754737153851191, "dur": 71, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Burst.Editor.rsp"}}, {"pid": 12345, "tid": 7, "ts": 1754737153851285, "dur": 74, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Rider.Editor.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 7, "ts": 1754737153851510, "dur": 232, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/PPv2URPConverters.rsp2"}}, {"pid": 12345, "tid": 7, "ts": 1754737153851772, "dur": 76, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/9870516708743257357.rsp"}}, {"pid": 12345, "tid": 7, "ts": 1754737153851971, "dur": 169, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1754737153852146, "dur": 200, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1754737153852346, "dur": 189, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1754737153852535, "dur": 296, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1754737153852832, "dur": 317, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1754737153853149, "dur": 205, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1754737153853354, "dur": 169, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1754737153853524, "dur": 577, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1754737153854101, "dur": 271, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1754737153854373, "dur": 190, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1754737153854563, "dur": 173, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1754737153854737, "dur": 189, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1754737153854927, "dur": 218, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1754737153855145, "dur": 217, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1754737153855363, "dur": 179, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1754737153855543, "dur": 393, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1754737153855937, "dur": 212, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1754737153856149, "dur": 185, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1754737153856334, "dur": 171, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1754737153856506, "dur": 192, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1754737153856698, "dur": 282, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1754737153857063, "dur": 217, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1754737153857280, "dur": 148, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1754737153857428, "dur": 316, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1754737153857745, "dur": 66, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.InputSystem.dll.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1754737153858012, "dur": 58, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\GameSoft\\Unity\\Editor\\2022.3.38f1c1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Diagnostics.Debug.dll"}}, {"pid": 12345, "tid": 7, "ts": 1754737153857870, "dur": 967, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.InputSystem.dll (+2 others)"}}, {"pid": 12345, "tid": 7, "ts": 1754737153858919, "dur": 75, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Core.Runtime.dll.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1754737153859007, "dur": 356, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Core.Runtime.dll (+2 others)"}}, {"pid": 12345, "tid": 7, "ts": 1754737153859364, "dur": 59, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1754737153859531, "dur": 223, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipeline.Universal.ShaderLibrary.dll (+2 others)"}}, {"pid": 12345, "tid": 7, "ts": 1754737153859754, "dur": 61, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1754737153859837, "dur": 66, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Universal.Runtime.dll.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1754737153859917, "dur": 428, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Universal.Runtime.dll (+2 others)"}}, {"pid": 12345, "tid": 7, "ts": 1754737153860417, "dur": 69, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Cinemachine.dll.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1754737153860532, "dur": 295, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Cinemachine.dll (+2 others)"}}, {"pid": 12345, "tid": 7, "ts": 1754737153860870, "dur": 298714, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1754737154160880, "dur": 59, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\GameSoft\\Unity\\Editor\\2022.3.38f1c1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.UnityWebRequestModule.dll"}}, {"pid": 12345, "tid": 7, "ts": 1754737154161429, "dur": 101, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\GameSoft\\Unity\\Editor\\2022.3.38f1c1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Security.Claims.dll"}}, {"pid": 12345, "tid": 7, "ts": 1754737154161540, "dur": 301, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\GameSoft\\Unity\\Editor\\2022.3.38f1c1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.ServiceModel.Duplex.dll"}}, {"pid": 12345, "tid": 7, "ts": 1754737154159585, "dur": 2355, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/CrashKonijn.Agent.Runtime.dll (+pdb)"}}, {"pid": 12345, "tid": 7, "ts": 1754737154161941, "dur": 313, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1754737154162323, "dur": 74, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\GameSoft\\Unity\\Editor\\2022.3.38f1c1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.AudioModule.dll"}}, {"pid": 12345, "tid": 7, "ts": 1754737154163140, "dur": 187, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\GameSoft\\Unity\\Editor\\2022.3.38f1c1\\Editor\\Data\\Tools\\ilpp\\Unity.ILPP.Runner\\hostfxr.dll"}}, {"pid": 12345, "tid": 7, "ts": 1754737154163819, "dur": 245, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\GameSoft\\Unity\\Editor\\2022.3.38f1c1\\Editor\\Data\\Tools\\ilpp\\Unity.ILPP.Runner\\System.Runtime.Serialization.Primitives.dll"}}, {"pid": 12345, "tid": 7, "ts": 1754737154164283, "dur": 117, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\GameSoft\\Unity\\Editor\\2022.3.38f1c1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.IMGUIModule.dll"}}, {"pid": 12345, "tid": 7, "ts": 1754737154162264, "dur": 2748, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Searcher.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 7, "ts": 1754737154165013, "dur": 74, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1754737154165109, "dur": 587, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1754737154165720, "dur": 30846, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1754737153826913, "dur": 23094, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1754737153850234, "dur": 127, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.HotReloadModule.dll_3D201F391D2079F2.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1754737153850395, "dur": 103, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1754737153850498, "dur": 224, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/UnityEngine.UI.rsp2"}}, {"pid": 12345, "tid": 8, "ts": 1754737153851047, "dur": 76, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Mathematics.Editor.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 8, "ts": 1754737153851293, "dur": 70, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Rider.Editor.rsp2"}}, {"pid": 12345, "tid": 8, "ts": 1754737153851531, "dur": 76, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/URPWizard.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 8, "ts": 1754737153851652, "dur": 190, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.InputSystem.TestFramework.rsp2"}}, {"pid": 12345, "tid": 8, "ts": 1754737153851913, "dur": 52, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/7759033573704817190.rsp"}}, {"pid": 12345, "tid": 8, "ts": 1754737153851966, "dur": 387, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1754737153852353, "dur": 199, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1754737153852553, "dur": 174, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1754737153852727, "dur": 286, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1754737153853013, "dur": 281, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1754737153853294, "dur": 177, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1754737153853471, "dur": 501, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1754737153853973, "dur": 197, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1754737153854187, "dur": 225, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1754737153854412, "dur": 230, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1754737153854642, "dur": 204, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1754737153854846, "dur": 222, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1754737153855069, "dur": 225, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1754737153855294, "dur": 174, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1754737153855468, "dur": 478, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1754737153855947, "dur": 166, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1754737153856113, "dur": 188, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1754737153856301, "dur": 181, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1754737153856482, "dur": 196, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1754737153856679, "dur": 251, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1754737153856931, "dur": 169, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1754737153857225, "dur": 181, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1754737153857406, "dur": 345, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1754737153857752, "dur": 62, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/CrashKonijn.Agent.Runtime.dll.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1754737153857837, "dur": 87, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/CrashKonijn.Agent.Runtime.dll.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1754737153857925, "dur": 75, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/com.crashkonijn.docs.getting_started.dll.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1754737153858031, "dur": 71, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/GlassSystem.dll.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1754737153858122, "dur": 343, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/GlassSystem.dll (+2 others)"}}, {"pid": 12345, "tid": 8, "ts": 1754737153858465, "dur": 76, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1754737153858620, "dur": 257, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1754737153858878, "dur": 82, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.Mathematics.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1754737153858973, "dur": 271, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Mathematics.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 8, "ts": 1754737153859345, "dur": 117, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1754737153859463, "dur": 72, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Core.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1754737153859547, "dur": 367, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Core.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 8, "ts": 1754737153859915, "dur": 80, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1754737153860015, "dur": 57, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.ShaderGraph.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1754737153860072, "dur": 211, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1754737153860288, "dur": 843, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.ShaderGraph.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 8, "ts": 1754737153861195, "dur": 53, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Universal.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1754737153861258, "dur": 409, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Universal.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 8, "ts": 1754737153861801, "dur": 250, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/com.unity.cinemachine.editor.dll (+2 others)"}}, {"pid": 12345, "tid": 8, "ts": 1754737153862091, "dur": 209, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Assembly-CSharp.dll (+2 others)"}}, {"pid": 12345, "tid": 8, "ts": 1754737153862367, "dur": 332624, "ph": "X", "name": "Csc", "args": {"detail": "Library/Bee/artifacts/1900b0aE.dag/Assembly-CSharp.dll (+2 others)"}}, {"pid": 12345, "tid": 9, "ts": 1754737153826954, "dur": 23067, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1754737153850084, "dur": 51, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1754737153850175, "dur": 145, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.AudioModule.dll_F8B5F041D001A362.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1754737153850322, "dur": 59, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\GameSoft\\Unity\\Editor\\2022.3.38f1c1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.TLSModule.dll"}}, {"pid": 12345, "tid": 9, "ts": 1754737153850321, "dur": 62, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.TLSModule.dll_FEEE89EA26456A87.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1754737153850383, "dur": 111, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1754737153850804, "dur": 76, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Mathematics.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 9, "ts": 1754737153850947, "dur": 66, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Universal.Runtime.rsp2"}}, {"pid": 12345, "tid": 9, "ts": 1754737153851058, "dur": 108, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Burst.Editor.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 9, "ts": 1754737153851328, "dur": 89, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Shared.Editor.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 9, "ts": 1754737153851517, "dur": 150, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/com.unity.cinemachine.editor.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 9, "ts": 1754737153851676, "dur": 80, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Collections.DocCodeSamples.rsp2"}}, {"pid": 12345, "tid": 9, "ts": 1754737153851978, "dur": 158, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1754737153852139, "dur": 230, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1754737153852369, "dur": 184, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1754737153852553, "dur": 240, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1754737153852794, "dur": 201, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1754737153852995, "dur": 300, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1754737153853296, "dur": 178, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1754737153853474, "dur": 547, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1754737153854021, "dur": 235, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1754737153854256, "dur": 220, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1754737153854477, "dur": 295, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1754737153854772, "dur": 199, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1754737153854972, "dur": 179, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1754737153855151, "dur": 214, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1754737153855365, "dur": 282, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1754737153855647, "dur": 263, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1754737153855911, "dur": 455, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1754737153856366, "dur": 160, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1754737153856526, "dur": 195, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1754737153856722, "dur": 510, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1754737153857232, "dur": 228, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1754737153857460, "dur": 382, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1754737153857844, "dur": 69, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Universal.Shaders.dll.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1754737153858065, "dur": 61, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\GameSoft\\Unity\\Editor\\2022.3.38f1c1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netfx\\System.Data.dll"}}, {"pid": 12345, "tid": 9, "ts": 1754737153857935, "dur": 505, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Universal.Shaders.dll (+2 others)"}}, {"pid": 12345, "tid": 9, "ts": 1754737153858440, "dur": 52, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1754737153858562, "dur": 53, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1754737153858615, "dur": 266, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1754737153858919, "dur": 76, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.InputSystem.ForUI.dll.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1754737153859016, "dur": 246, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.InputSystem.ForUI.dll (+2 others)"}}, {"pid": 12345, "tid": 9, "ts": 1754737153859262, "dur": 160, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1754737153859467, "dur": 600, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1754737153860068, "dur": 1683, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1754737153861752, "dur": 64, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/PPv2URPConverters.dll.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1754737153861843, "dur": 183, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/PPv2URPConverters.dll (+2 others)"}}, {"pid": 12345, "tid": 9, "ts": 1754737153862053, "dur": 295194, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1754737154157250, "dur": 1469, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.RenderPipelines.Universal.Config.Runtime.dll (+pdb)"}}, {"pid": 12345, "tid": 9, "ts": 1754737154158761, "dur": 99, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.RenderPipelines.Universal.Config.Runtime.dll (+pdb)"}}, {"pid": 12345, "tid": 9, "ts": 1754737154158862, "dur": 1407, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Burst.dll (+pdb)"}}, {"pid": 12345, "tid": 9, "ts": 1754737154160270, "dur": 54, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1754737154161392, "dur": 76, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\GameSoft\\Unity\\Editor\\2022.3.38f1c1\\Editor\\Data\\Tools\\ilpp\\Unity.ILPP.Runner\\System.Diagnostics.TraceSource.dll"}}, {"pid": 12345, "tid": 9, "ts": 1754737154161471, "dur": 90, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\GameSoft\\Unity\\Editor\\2022.3.38f1c1\\Editor\\Data\\Tools\\ilpp\\Unity.ILPP.Runner\\System.Drawing.dll"}}, {"pid": 12345, "tid": 9, "ts": 1754737154161584, "dur": 71, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\GameSoft\\Unity\\Editor\\2022.3.38f1c1\\Editor\\Data\\Tools\\ilpp\\Unity.ILPP.Runner\\System.IO.Pipes.AccessControl.dll"}}, {"pid": 12345, "tid": 9, "ts": 1754737154161730, "dur": 71, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\GameSoft\\Unity\\Editor\\2022.3.38f1c1\\Editor\\Data\\Tools\\ilpp\\Unity.ILPP.Runner\\System.Net.Security.dll"}}, {"pid": 12345, "tid": 9, "ts": 1754737154162068, "dur": 101, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\GameSoft\\Unity\\Editor\\2022.3.38f1c1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.StreamingModule.dll"}}, {"pid": 12345, "tid": 9, "ts": 1754737154162172, "dur": 309, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\GameSoft\\Unity\\Editor\\2022.3.38f1c1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.SubsystemsModule.dll"}}, {"pid": 12345, "tid": 9, "ts": 1754737154162758, "dur": 124, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\GameSoft\\Unity\\Editor\\2022.3.38f1c1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Reflection.Emit.ILGeneration.dll"}}, {"pid": 12345, "tid": 9, "ts": 1754737154160333, "dur": 2701, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.RenderPipelines.Universal.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 9, "ts": 1754737154163045, "dur": 71, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1754737154163408, "dur": 114, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\GameSoft\\Unity\\Editor\\2022.3.38f1c1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Buffers.dll"}}, {"pid": 12345, "tid": 9, "ts": 1754737154163572, "dur": 151, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\GameSoft\\Unity\\Editor\\2022.3.38f1c1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Memory.dll"}}, {"pid": 12345, "tid": 9, "ts": 1754737154164283, "dur": 81, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\GameSoft\\Unity\\Editor\\2022.3.38f1c1\\Editor\\Data\\Tools\\ilpp\\Unity.ILPP.Runner\\Microsoft.AspNetCore.Rewrite.dll"}}, {"pid": 12345, "tid": 9, "ts": 1754737154163125, "dur": 2277, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/UnityEditor.UI.dll (+pdb)"}}, {"pid": 12345, "tid": 9, "ts": 1754737154165446, "dur": 31108, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1754737153826991, "dur": 23047, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1754737153850049, "dur": 61, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.CoreModule.dll_B6DB781081D3DD3C.mvfrm"}}, {"pid": 12345, "tid": 10, "ts": 1754737153850136, "dur": 129, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.CoreModule.dll_B6DB781081D3DD3C.mvfrm"}}, {"pid": 12345, "tid": 10, "ts": 1754737153850385, "dur": 95, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\GameSoft\\Unity\\Editor\\2022.3.38f1c1\\Editor\\Data\\Managed\\UnityEditor.Graphs.dll"}}, {"pid": 12345, "tid": 10, "ts": 1754737153850384, "dur": 97, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.Graphs.dll_34C604C212D62B6E.mvfrm"}}, {"pid": 12345, "tid": 10, "ts": 1754737153850613, "dur": 247, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.Burst.Unsafe.dll_12BA7C2C14719C45.mvfrm"}}, {"pid": 12345, "tid": 10, "ts": 1754737153851039, "dur": 60, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Searcher.Editor.rsp"}}, {"pid": 12345, "tid": 10, "ts": 1754737153851292, "dur": 113, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Timeline.Editor.rsp"}}, {"pid": 12345, "tid": 10, "ts": 1754737153851441, "dur": 203, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Cinemachine.rsp"}}, {"pid": 12345, "tid": 10, "ts": 1754737153851961, "dur": 434, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1754737153852395, "dur": 231, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1754737153852626, "dur": 185, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1754737153852811, "dur": 240, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1754737153853052, "dur": 213, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1754737153853265, "dur": 163, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1754737153853428, "dur": 502, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1754737153853933, "dur": 187, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1754737153854120, "dur": 218, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1754737153854338, "dur": 347, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1754737153854685, "dur": 219, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1754737153854905, "dur": 260, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1754737153855165, "dur": 561, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1754737153855727, "dur": 197, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1754737153855924, "dur": 164, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1754737153856088, "dur": 205, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1754737153856293, "dur": 245, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1754737153856538, "dur": 178, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1754737153856716, "dur": 259, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1754737153856975, "dur": 230, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1754737153857224, "dur": 183, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1754737153857407, "dur": 341, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1754737153857748, "dur": 65, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/CrashKonijn.Goap.Core.dll.mvfrm"}}, {"pid": 12345, "tid": 10, "ts": 1754737153857832, "dur": 72, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Core.ShaderLibrary.dll.mvfrm"}}, {"pid": 12345, "tid": 10, "ts": 1754737153857905, "dur": 133, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1754737153858042, "dur": 284, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Core.ShaderLibrary.dll (+2 others)"}}, {"pid": 12345, "tid": 10, "ts": 1754737153858375, "dur": 149, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/com.crashkonijn.goap.demos.simple.dll.mvfrm"}}, {"pid": 12345, "tid": 10, "ts": 1754737153858550, "dur": 66, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1754737153858616, "dur": 287, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1754737153858920, "dur": 424, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1754737153859344, "dur": 120, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1754737153859464, "dur": 612, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1754737153860077, "dur": 297166, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1754737154157244, "dur": 1547, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.RenderPipelines.Core.ShaderLibrary.dll (+pdb)"}}, {"pid": 12345, "tid": 10, "ts": 1754737154159809, "dur": 67, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\GameSoft\\Unity\\Editor\\2022.3.38f1c1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.ProfilerModule.dll"}}, {"pid": 12345, "tid": 10, "ts": 1754737154158829, "dur": 1840, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.TextMeshPro.dll (+pdb)"}}, {"pid": 12345, "tid": 10, "ts": 1754737154160669, "dur": 386, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1754737154161832, "dur": 241, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\GameSoft\\Unity\\Editor\\2022.3.38f1c1\\Editor\\Data\\Tools\\ilpp\\Unity.ILPP.Runner\\Microsoft.Extensions.Options.ConfigurationExtensions.dll"}}, {"pid": 12345, "tid": 10, "ts": 1754737154162074, "dur": 109, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\GameSoft\\Unity\\Editor\\2022.3.38f1c1\\Editor\\Data\\Tools\\ilpp\\Unity.ILPP.Runner\\Microsoft.Extensions.Options.DataAnnotations.dll"}}, {"pid": 12345, "tid": 10, "ts": 1754737154162511, "dur": 95, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\GameSoft\\Unity\\Editor\\2022.3.38f1c1\\Editor\\Data\\Tools\\ilpp\\Unity.ILPP.Runner\\System.IO.Pipelines.dll"}}, {"pid": 12345, "tid": 10, "ts": 1754737154162663, "dur": 107, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\GameSoft\\Unity\\Editor\\2022.3.38f1c1\\Editor\\Data\\Tools\\ilpp\\Unity.ILPP.Runner\\System.Net.Mail.dll"}}, {"pid": 12345, "tid": 10, "ts": 1754737154163408, "dur": 173, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\Bee\\artifacts\\1900b0aE.dag\\UnityEngine.UI.dll"}}, {"pid": 12345, "tid": 10, "ts": 1754737154161065, "dur": 2533, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.VisualScripting.Flow.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 10, "ts": 1754737154163721, "dur": 393, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\Bee\\artifacts\\1900b0aE.dag\\post-processed\\Unity.RenderPipelines.Universal.Config.Runtime.dll"}}, {"pid": 12345, "tid": 10, "ts": 1754737154163720, "dur": 394, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.RenderPipelines.Universal.Config.Runtime.dll"}}, {"pid": 12345, "tid": 10, "ts": 1754737154164247, "dur": 50, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1754737154164621, "dur": 199, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1754737154164977, "dur": 52, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.RenderPipeline.Universal.ShaderLibrary.pdb"}}, {"pid": 12345, "tid": 10, "ts": 1754737154165089, "dur": 233, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1754737154165333, "dur": 31226, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1754737153827019, "dur": 23040, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1754737153850069, "dur": 61, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.EditorToolbarModule.dll_F4EF6CA575FE5075.mvfrm"}}, {"pid": 12345, "tid": 11, "ts": 1754737153850302, "dur": 50, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.StreamingModule.dll_706C4E83917515AE.mvfrm"}}, {"pid": 12345, "tid": 11, "ts": 1754737153850390, "dur": 104, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1754737153850495, "dur": 377, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/UnityEngine.UI.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 11, "ts": 1754737153850892, "dur": 110, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Core.Editor.rsp"}}, {"pid": 12345, "tid": 11, "ts": 1754737153851025, "dur": 88, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Timeline.rsp2"}}, {"pid": 12345, "tid": 11, "ts": 1754737153851151, "dur": 53, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Mathematics.Editor.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 11, "ts": 1754737153851286, "dur": 51, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Universal.Editor.rsp"}}, {"pid": 12345, "tid": 11, "ts": 1754737153851349, "dur": 89, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.SettingsProvider.Editor.rsp2"}}, {"pid": 12345, "tid": 11, "ts": 1754737153851977, "dur": 160, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1754737153852142, "dur": 183, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1754737153852326, "dur": 191, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1754737153852517, "dur": 372, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1754737153852889, "dur": 217, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1754737153853106, "dur": 223, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1754737153853329, "dur": 539, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1754737153853869, "dur": 231, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1754737153854100, "dur": 287, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1754737153854387, "dur": 281, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1754737153854668, "dur": 184, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1754737153854853, "dur": 231, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1754737153855084, "dur": 196, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1754737153855280, "dur": 200, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1754737153855508, "dur": 592, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.visualscripting@1.9.4\\Runtime\\VisualScripting.Core\\Reflection\\TypeName.cs"}}, {"pid": 12345, "tid": 11, "ts": 1754737153855480, "dur": 817, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1754737153856297, "dur": 179, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1754737153856477, "dur": 175, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1754737153856652, "dur": 267, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1754737153856919, "dur": 385, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1754737153857305, "dur": 114, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1754737153857419, "dur": 341, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1754737153857762, "dur": 70, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.VisualStudio.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 11, "ts": 1754737153857862, "dur": 368, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.VisualStudio.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 11, "ts": 1754737153858230, "dur": 136, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1754737153858372, "dur": 196, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.TextMeshPro.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 11, "ts": 1754737153858604, "dur": 252, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.TextMeshPro.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 11, "ts": 1754737153858856, "dur": 55, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1754737153858948, "dur": 395, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1754737153859344, "dur": 57, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.Collections.DocCodeSamples.dll.mvfrm"}}, {"pid": 12345, "tid": 11, "ts": 1754737153859416, "dur": 223, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Collections.DocCodeSamples.dll (+2 others)"}}, {"pid": 12345, "tid": 11, "ts": 1754737153859666, "dur": 415, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1754737153860081, "dur": 297159, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1754737154157242, "dur": 1516, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/CrashKonijn.Goap.Resolver.dll (+pdb)"}}, {"pid": 12345, "tid": 11, "ts": 1754737154160177, "dur": 80, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\GameSoft\\Unity\\Editor\\2022.3.38f1c1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Threading.Thread.dll"}}, {"pid": 12345, "tid": 11, "ts": 1754737154158798, "dur": 1530, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Cinemachine.dll (+pdb)"}}, {"pid": 12345, "tid": 11, "ts": 1754737154160329, "dur": 381, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1754737154161832, "dur": 71, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\GameSoft\\Unity\\Editor\\2022.3.38f1c1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netfx\\System.Runtime.Serialization.dll"}}, {"pid": 12345, "tid": 11, "ts": 1754737154161998, "dur": 71, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\GameSoft\\Unity\\Editor\\2022.3.38f1c1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.IO.IsolatedStorage.dll"}}, {"pid": 12345, "tid": 11, "ts": 1754737154162075, "dur": 107, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\GameSoft\\Unity\\Editor\\2022.3.38f1c1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.IO.UnmanagedMemoryStream.dll"}}, {"pid": 12345, "tid": 11, "ts": 1754737154160715, "dur": 1715, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.VisualScripting.Core.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 11, "ts": 1754737154162431, "dur": 75, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1754737154162872, "dur": 50, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\GameSoft\\Unity\\Editor\\2022.3.38f1c1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Diagnostics.StackTrace.dll"}}, {"pid": 12345, "tid": 11, "ts": 1754737154163175, "dur": 123, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\GameSoft\\Unity\\Editor\\2022.3.38f1c1\\Editor\\Data\\Tools\\ilpp\\Unity.ILPP.Runner\\api-ms-win-core-sysinfo-l1-1-0.dll"}}, {"pid": 12345, "tid": 11, "ts": 1754737154162517, "dur": 2203, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.VisualScripting.State.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 11, "ts": 1754737154164812, "dur": 105, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1754737154165116, "dur": 840, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1754737154165985, "dur": 30601, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1754737153827061, "dur": 23048, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1754737153850122, "dur": 70, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\GameSoft\\Unity\\Editor\\2022.3.38f1c1\\Editor\\Data\\Managed\\UnityEngine\\UnityEditor.UnityConnectModule.dll"}}, {"pid": 12345, "tid": 12, "ts": 1754737153850115, "dur": 78, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.UnityConnectModule.dll_591A98B8662B29F2.mvfrm"}}, {"pid": 12345, "tid": 12, "ts": 1754737153850302, "dur": 91, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1754737153850421, "dur": 103, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1754737153850604, "dur": 169, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/UnityEditor.UI.rsp"}}, {"pid": 12345, "tid": 12, "ts": 1754737153851129, "dur": 174, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Flow.Editor.rsp"}}, {"pid": 12345, "tid": 12, "ts": 1754737153851349, "dur": 76, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.InputSystem.ForUI.rsp2"}}, {"pid": 12345, "tid": 12, "ts": 1754737153851478, "dur": 162, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.TextMeshPro.Editor.rsp"}}, {"pid": 12345, "tid": 12, "ts": 1754737153851655, "dur": 115, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Assembly-CSharp-Editor.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 12, "ts": 1754737153851888, "dur": 87, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/9149840420361016890.rsp"}}, {"pid": 12345, "tid": 12, "ts": 1754737153851975, "dur": 399, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1754737153852374, "dur": 234, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1754737153852608, "dur": 184, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1754737153852792, "dur": 276, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1754737153853068, "dur": 242, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1754737153853310, "dur": 183, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1754737153853493, "dur": 503, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1754737153853996, "dur": 204, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1754737153854200, "dur": 244, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1754737153854444, "dur": 194, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1754737153854639, "dur": 178, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1754737153854817, "dur": 207, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1754737153855025, "dur": 169, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1754737153855194, "dur": 360, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1754737153855554, "dur": 667, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1754737153856221, "dur": 192, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1754737153856413, "dur": 180, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1754737153856593, "dur": 327, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1754737153856920, "dur": 315, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1754737153857235, "dur": 233, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1754737153857468, "dur": 286, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1754737153857755, "dur": 66, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.Timeline.dll.mvfrm"}}, {"pid": 12345, "tid": 12, "ts": 1754737153857845, "dur": 70, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.Timeline.dll.mvfrm"}}, {"pid": 12345, "tid": 12, "ts": 1754737153857916, "dur": 503, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Timeline.dll (+2 others)"}}, {"pid": 12345, "tid": 12, "ts": 1754737153858420, "dur": 95, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1754737153858536, "dur": 91, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.Timeline.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 12, "ts": 1754737153858639, "dur": 594, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Timeline.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 12, "ts": 1754737153859277, "dur": 73, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1754737153859350, "dur": 115, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1754737153859465, "dur": 591, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1754737153860057, "dur": 303, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/com.crashkonijn.goap.demos.complex.dll (+2 others)"}}, {"pid": 12345, "tid": 12, "ts": 1754737153860393, "dur": 296874, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1754737154159468, "dur": 73, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.visualscripting@1.9.4\\Editor\\VisualScripting.Core\\EditorAssetResources\\Unity.VisualScripting.TextureAssets.dll"}}, {"pid": 12345, "tid": 12, "ts": 1754737154157282, "dur": 2270, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/com.crashkonijn.goap.demos.simple.dll (+pdb)"}}, {"pid": 12345, "tid": 12, "ts": 1754737154159553, "dur": 60, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1754737154160589, "dur": 83, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\GameSoft\\Unity\\Editor\\2022.3.38f1c1\\Editor\\Data\\Tools\\ilpp\\Unity.ILPP.Runner\\Microsoft.AspNetCore.Server.HttpSys.dll"}}, {"pid": 12345, "tid": 12, "ts": 1754737154160967, "dur": 260, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\GameSoft\\Unity\\Editor\\2022.3.38f1c1\\Editor\\Data\\Tools\\ilpp\\Unity.ILPP.Runner\\System.Text.Encodings.Web.dll"}}, {"pid": 12345, "tid": 12, "ts": 1754737154161493, "dur": 59, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\GameSoft\\Unity\\Editor\\2022.3.38f1c1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Diagnostics.Tracing.dll"}}, {"pid": 12345, "tid": 12, "ts": 1754737154159623, "dur": 2186, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.InputSystem.TestFramework.dll (+pdb)"}}, {"pid": 12345, "tid": 12, "ts": 1754737154161810, "dur": 85, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1754737154162074, "dur": 59, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\GameSoft\\Unity\\Editor\\2022.3.38f1c1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.IO.FileSystem.Watcher.dll"}}, {"pid": 12345, "tid": 12, "ts": 1754737154162173, "dur": 132, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\GameSoft\\Unity\\Editor\\2022.3.38f1c1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Runtime.Serialization.Formatters.dll"}}, {"pid": 12345, "tid": 12, "ts": 1754737154162518, "dur": 53, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\GameSoft\\Unity\\Editor\\2022.3.38f1c1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Linq.Parallel.dll"}}, {"pid": 12345, "tid": 12, "ts": 1754737154162872, "dur": 456, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\GameSoft\\Unity\\Editor\\2022.3.38f1c1\\Editor\\Data\\Tools\\ilpp\\Unity.ILPP.Runner\\Microsoft.AspNetCore.Http.Connections.Common.dll"}}, {"pid": 12345, "tid": 12, "ts": 1754737154163494, "dur": 51, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\GameSoft\\Unity\\Editor\\2022.3.38f1c1\\Editor\\Data\\Tools\\ilpp\\Unity.ILPP.Runner\\System.Diagnostics.EventLog.Messages.dll"}}, {"pid": 12345, "tid": 12, "ts": 1754737154163575, "dur": 78, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\GameSoft\\Unity\\Editor\\2022.3.38f1c1\\Editor\\Data\\Tools\\ilpp\\Unity.ILPP.Runner\\System.Diagnostics.Tracing.dll"}}, {"pid": 12345, "tid": 12, "ts": 1754737154164103, "dur": 63, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\GameSoft\\Unity\\Editor\\2022.3.38f1c1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Xml.XmlDocument.dll"}}, {"pid": 12345, "tid": 12, "ts": 1754737154164210, "dur": 477, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\GameSoft\\Unity\\Editor\\2022.3.38f1c1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Globalization.Calendars.dll"}}, {"pid": 12345, "tid": 12, "ts": 1754737154164749, "dur": 99, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\GameSoft\\Unity\\Editor\\2022.3.38f1c1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Security.Cryptography.Primitives.dll"}}, {"pid": 12345, "tid": 12, "ts": 1754737154164913, "dur": 760, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\GameSoft\\Unity\\Editor\\2022.3.38f1c1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Threading.dll"}}, {"pid": 12345, "tid": 12, "ts": 1754737154161907, "dur": 3824, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/PPv2URPConverters.dll (+pdb)"}}, {"pid": 12345, "tid": 12, "ts": 1754737154165787, "dur": 30787, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1754737153827092, "dur": 23025, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1754737153850184, "dur": 68, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.CoreModule.dll_D9FC4B6329D94867.mvfrm"}}, {"pid": 12345, "tid": 13, "ts": 1754737153850281, "dur": 60, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.PerformanceReportingModule.dll_A00BE41311F50426.mvfrm"}}, {"pid": 12345, "tid": 13, "ts": 1754737153850387, "dur": 102, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1754737153850522, "dur": 153, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.VisualScripting.YamlDotNet.dll_4A5905A13DE9A491.mvfrm"}}, {"pid": 12345, "tid": 13, "ts": 1754737153850846, "dur": 103, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Universal.Config.Runtime.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 13, "ts": 1754737153851013, "dur": 165, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/CrashKonijn.Goap.Core.rsp"}}, {"pid": 12345, "tid": 13, "ts": 1754737153851201, "dur": 143, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Universal.Editor.rsp2"}}, {"pid": 12345, "tid": 13, "ts": 1754737153851363, "dur": 83, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.PlasticSCM.Editor.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 13, "ts": 1754737153851616, "dur": 59, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/com.unity.cinemachine.editor.rsp"}}, {"pid": 12345, "tid": 13, "ts": 1754737153851764, "dur": 114, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/17525389461119239690.rsp"}}, {"pid": 12345, "tid": 13, "ts": 1754737153851959, "dur": 159, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1754737153852122, "dur": 251, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1754737153852373, "dur": 215, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1754737153852588, "dur": 176, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1754737153852764, "dur": 159, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1754737153852923, "dur": 214, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1754737153853137, "dur": 228, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1754737153853365, "dur": 523, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1754737153853889, "dur": 224, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1754737153854113, "dur": 213, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1754737153854327, "dur": 249, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1754737153854576, "dur": 180, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1754737153854756, "dur": 186, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1754737153854942, "dur": 197, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1754737153855139, "dur": 220, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1754737153855359, "dur": 281, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1754737153855640, "dur": 268, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1754737153855908, "dur": 173, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1754737153856081, "dur": 236, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1754737153856317, "dur": 206, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1754737153856524, "dur": 227, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1754737153856751, "dur": 242, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1754737153857023, "dur": 186, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1754737153857229, "dur": 175, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1754737153857405, "dur": 345, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1754737153857750, "dur": 110, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.ShaderGraph.Utilities.dll.mvfrm"}}, {"pid": 12345, "tid": 13, "ts": 1754737153857897, "dur": 337, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.ShaderGraph.Utilities.dll (+2 others)"}}, {"pid": 12345, "tid": 13, "ts": 1754737153858235, "dur": 190, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1754737153858451, "dur": 142, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/com.crashkonijn.goap.demos.turn-based.dll.mvfrm"}}, {"pid": 12345, "tid": 13, "ts": 1754737153858629, "dur": 253, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1754737153858921, "dur": 424, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1754737153859346, "dur": 118, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1754737153859464, "dur": 593, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1754737153860057, "dur": 366, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1754737153860423, "dur": 116, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/URPWizard.dll.mvfrm"}}, {"pid": 12345, "tid": 13, "ts": 1754737153860560, "dur": 243, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/URPWizard.dll (+2 others)"}}, {"pid": 12345, "tid": 13, "ts": 1754737153860836, "dur": 296442, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1754737154157280, "dur": 1438, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/CrashKonijn.Agent.Core.dll (+pdb)"}}, {"pid": 12345, "tid": 13, "ts": 1754737154159578, "dur": 220, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\GameSoft\\Unity\\Editor\\2022.3.38f1c1\\Editor\\Data\\Tools\\ilpp\\Unity.ILPP.Runner\\Microsoft.AspNetCore.DataProtection.dll"}}, {"pid": 12345, "tid": 13, "ts": 1754737154160062, "dur": 65, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\GameSoft\\Unity\\Editor\\2022.3.38f1c1\\Editor\\Data\\Tools\\ilpp\\Unity.ILPP.Runner\\System.Reflection.Emit.ILGeneration.dll"}}, {"pid": 12345, "tid": 13, "ts": 1754737154160440, "dur": 51, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\GameSoft\\Unity\\Editor\\2022.3.38f1c1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.IO.IsolatedStorage.dll"}}, {"pid": 12345, "tid": 13, "ts": 1754737154158758, "dur": 2107, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/CrashKonijn.Goap.Core.dll (+pdb)"}}, {"pid": 12345, "tid": 13, "ts": 1754737154161187, "dur": 57, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\GameSoft\\Unity\\Editor\\2022.3.38f1c1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Diagnostics.Process.dll"}}, {"pid": 12345, "tid": 13, "ts": 1754737154161373, "dur": 177, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\GameSoft\\Unity\\Editor\\2022.3.38f1c1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Collections.Concurrent.dll"}}, {"pid": 12345, "tid": 13, "ts": 1754737154162068, "dur": 56, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\GameSoft\\Unity\\Editor\\2022.3.38f1c1\\Editor\\Data\\Tools\\ilpp\\Unity.ILPP.Runner\\System.Diagnostics.FileVersionInfo.dll"}}, {"pid": 12345, "tid": 13, "ts": 1754737154162453, "dur": 328, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\GameSoft\\Unity\\Editor\\2022.3.38f1c1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.ComponentModel.Primitives.dll"}}, {"pid": 12345, "tid": 13, "ts": 1754737154160924, "dur": 2185, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.TextMeshPro.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 13, "ts": 1754737154163110, "dur": 82, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1754737154163494, "dur": 118, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\GameSoft\\Unity\\Editor\\2022.3.38f1c1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.IO.dll"}}, {"pid": 12345, "tid": 13, "ts": 1754737154163766, "dur": 730, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\GameSoft\\Unity\\Editor\\2022.3.38f1c1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Buffers.dll"}}, {"pid": 12345, "tid": 13, "ts": 1754737154164677, "dur": 79, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\GameSoft\\Unity\\Editor\\2022.3.38f1c1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Text.Encoding.dll"}}, {"pid": 12345, "tid": 13, "ts": 1754737154164812, "dur": 224, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.visualscripting@1.9.4\\Editor\\VisualScripting.Core\\Dependencies\\DotNetZip\\Unity.VisualScripting.IonicZip.dll"}}, {"pid": 12345, "tid": 13, "ts": 1754737154163204, "dur": 2698, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.VisualScripting.Shared.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 13, "ts": 1754737154165969, "dur": 30611, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1754737153827134, "dur": 23000, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1754737153850175, "dur": 77, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ARModule.dll_969BEA2E4A09BAD9.mvfrm"}}, {"pid": 12345, "tid": 14, "ts": 1754737153850325, "dur": 194, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\GameSoft\\Unity\\Editor\\2022.3.38f1c1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.UIElementsModule.dll"}}, {"pid": 12345, "tid": 14, "ts": 1754737153850325, "dur": 195, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UIElementsModule.dll_61E9542AD38DD443.mvfrm"}}, {"pid": 12345, "tid": 14, "ts": 1754737153850597, "dur": 322, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/UnityEditor.TestRunner.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 14, "ts": 1754737153850990, "dur": 54, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1754737153851061, "dur": 102, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.TextMeshPro.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 14, "ts": 1754737153851273, "dur": 80, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.PlasticSCM.Editor.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 14, "ts": 1754737153851396, "dur": 147, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.InputSystem.ForUI.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 14, "ts": 1754737153851623, "dur": 235, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Tests.rsp2"}}, {"pid": 12345, "tid": 14, "ts": 1754737153851947, "dur": 173, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1754737153852124, "dur": 219, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1754737153852343, "dur": 201, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1754737153852544, "dur": 194, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1754737153852738, "dur": 181, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1754737153852920, "dur": 208, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1754737153853128, "dur": 213, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1754737153853341, "dur": 180, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1754737153853521, "dur": 546, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1754737153854068, "dur": 199, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1754737153854267, "dur": 243, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1754737153854510, "dur": 220, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1754737153854730, "dur": 195, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1754737153854925, "dur": 193, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1754737153855119, "dur": 199, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1754737153855318, "dur": 211, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1754737153855529, "dur": 233, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1754737153855763, "dur": 185, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1754737153855948, "dur": 178, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1754737153856127, "dur": 841, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1754737153856968, "dur": 270, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1754737153857238, "dur": 214, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1754737153857452, "dur": 303, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1754737153857756, "dur": 66, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.TextMeshPro.dll.mvfrm"}}, {"pid": 12345, "tid": 14, "ts": 1754737153857838, "dur": 467, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.TextMeshPro.dll (+2 others)"}}, {"pid": 12345, "tid": 14, "ts": 1754737153858370, "dur": 158, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.Burst.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 14, "ts": 1754737153858579, "dur": 240, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Burst.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 14, "ts": 1754737153858923, "dur": 423, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1754737153859347, "dur": 116, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1754737153859463, "dur": 301, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1754737153859765, "dur": 78, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Core.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 14, "ts": 1754737153859861, "dur": 817, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Core.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 14, "ts": 1754737153860819, "dur": 291, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Flow.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 14, "ts": 1754737153861197, "dur": 206, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.State.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 14, "ts": 1754737153861438, "dur": 60, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Shared.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 14, "ts": 1754737153861525, "dur": 205, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Shared.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 14, "ts": 1754737153861784, "dur": 75, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Assembly-CSharp.dll.mvfrm"}}, {"pid": 12345, "tid": 14, "ts": 1754737153861870, "dur": 295405, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1754737154158031, "dur": 176, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\GameSoft\\Unity\\Editor\\2022.3.38f1c1\\Editor\\Data\\Tools\\ilpp\\Unity.ILPP.Runner\\Microsoft.Extensions.Options.DataAnnotations.dll"}}, {"pid": 12345, "tid": 14, "ts": 1754737154157277, "dur": 1882, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/CrashKonijn.Goap.Runtime.dll (+pdb)"}}, {"pid": 12345, "tid": 14, "ts": 1754737154159160, "dur": 76, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1754737154160107, "dur": 109, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\GameSoft\\Unity\\Editor\\2022.3.38f1c1\\Editor\\Data\\Tools\\ilpp\\Unity.ILPP.Runner\\Microsoft.JSInterop.dll"}}, {"pid": 12345, "tid": 14, "ts": 1754737154160441, "dur": 52, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\GameSoft\\Unity\\Editor\\2022.3.38f1c1\\Editor\\Data\\Tools\\ilpp\\Unity.ILPP.Runner\\System.Runtime.CompilerServices.VisualC.dll"}}, {"pid": 12345, "tid": 14, "ts": 1754737154159245, "dur": 1912, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Timeline.dll (+pdb)"}}, {"pid": 12345, "tid": 14, "ts": 1754737154161158, "dur": 121, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1754737154161934, "dur": 53, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\GameSoft\\Unity\\Editor\\2022.3.38f1c1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.ServiceModel.Duplex.dll"}}, {"pid": 12345, "tid": 14, "ts": 1754737154162759, "dur": 63, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\GameSoft\\Unity\\Editor\\2022.3.38f1c1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.ScreenCaptureModule.dll"}}, {"pid": 12345, "tid": 14, "ts": 1754737154163133, "dur": 203, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\GameSoft\\Unity\\Editor\\2022.3.38f1c1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.ComponentModel.Primitives.dll"}}, {"pid": 12345, "tid": 14, "ts": 1754737154161306, "dur": 2252, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Collections.DocCodeSamples.dll (+pdb)"}}, {"pid": 12345, "tid": 14, "ts": 1754737154163741, "dur": 412, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1754737154164162, "dur": 272, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\Bee\\artifacts\\1900b0aE.dag\\post-processed\\Unity.ShaderGraph.Utilities.pdb"}}, {"pid": 12345, "tid": 14, "ts": 1754737154164161, "dur": 274, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.ShaderGraph.Utilities.pdb"}}, {"pid": 12345, "tid": 14, "ts": 1754737154164436, "dur": 64, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1754737154164558, "dur": 97, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1754737154164701, "dur": 69, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1754737154164903, "dur": 66, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1754737154165060, "dur": 72, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "CopyFiles Library/ScriptAssemblies/CrashKonijn.Goap.Editor.dll"}}, {"pid": 12345, "tid": 14, "ts": 1754737154165132, "dur": 1092, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1754737154166250, "dur": 30333, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1754737153827164, "dur": 23007, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1754737153850294, "dur": 55, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1754737153850382, "dur": 134, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\GameSoft\\Unity\\Editor\\2022.3.38f1c1\\Editor\\Data\\PlaybackEngines\\WindowsStandaloneSupport\\UnityEditor.WindowsStandalone.Extensions.dll"}}, {"pid": 12345, "tid": 15, "ts": 1754737153850381, "dur": 137, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.WindowsStandalone.Extensions.dll_35385077F0CEC424.mvfrm"}}, {"pid": 12345, "tid": 15, "ts": 1754737153850585, "dur": 101, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/UnityEditor.UI.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 15, "ts": 1754737153850861, "dur": 96, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Universal.Config.Runtime.rsp2"}}, {"pid": 12345, "tid": 15, "ts": 1754737153851059, "dur": 188, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.TextMeshPro.rsp2"}}, {"pid": 12345, "tid": 15, "ts": 1754737153851340, "dur": 50, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.TextMeshPro.Editor.rsp2"}}, {"pid": 12345, "tid": 15, "ts": 1754737153851498, "dur": 53, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/com.crashkonijn.goap.demos.complex.rsp2"}}, {"pid": 12345, "tid": 15, "ts": 1754737153851681, "dur": 237, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Assembly-CSharp-Editor.rsp2"}}, {"pid": 12345, "tid": 15, "ts": 1754737153851969, "dur": 228, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1754737153852202, "dur": 357, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1754737153852560, "dur": 265, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1754737153852825, "dur": 315, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1754737153853140, "dur": 191, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1754737153853331, "dur": 562, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1754737153853893, "dur": 205, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1754737153854098, "dur": 158, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1754737153854256, "dur": 221, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1754737153854477, "dur": 238, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1754737153854715, "dur": 206, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1754737153854922, "dur": 274, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1754737153855196, "dur": 241, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1754737153855437, "dur": 414, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1754737153855852, "dur": 187, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1754737153856040, "dur": 249, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1754737153856290, "dur": 166, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1754737153856456, "dur": 257, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1754737153856713, "dur": 266, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1754737153856980, "dur": 140, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1754737153857229, "dur": 175, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1754737153857404, "dur": 345, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1754737153857749, "dur": 67, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.Searcher.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 15, "ts": 1754737153857845, "dur": 327, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Searcher.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 15, "ts": 1754737153858210, "dur": 70, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1754737153858285, "dur": 83, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Tests.dll.mvfrm"}}, {"pid": 12345, "tid": 15, "ts": 1754737153858368, "dur": 142, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1754737153858529, "dur": 250, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Tests.dll (+2 others)"}}, {"pid": 12345, "tid": 15, "ts": 1754737153858807, "dur": 110, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1754737153858918, "dur": 64, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Core.dll.mvfrm"}}, {"pid": 12345, "tid": 15, "ts": 1754737153859005, "dur": 699, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Core.dll (+2 others)"}}, {"pid": 12345, "tid": 15, "ts": 1754737153859757, "dur": 84, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Flow.dll.mvfrm"}}, {"pid": 12345, "tid": 15, "ts": 1754737153859858, "dur": 771, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Flow.dll (+2 others)"}}, {"pid": 12345, "tid": 15, "ts": 1754737153860756, "dur": 175, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.State.dll (+2 others)"}}, {"pid": 12345, "tid": 15, "ts": 1754737153860968, "dur": 296303, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1754737154157273, "dur": 1439, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.RenderPipelines.ShaderGraph.ShaderGraphLibrary.dll (+pdb)"}}, {"pid": 12345, "tid": 15, "ts": 1754737154159571, "dur": 66, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\GameSoft\\Unity\\Editor\\2022.3.38f1c1\\Editor\\Data\\Tools\\ilpp\\Unity.ILPP.Runner\\System.Security.dll"}}, {"pid": 12345, "tid": 15, "ts": 1754737154160120, "dur": 67, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\GameSoft\\Unity\\Editor\\2022.3.38f1c1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Diagnostics.FileVersionInfo.dll"}}, {"pid": 12345, "tid": 15, "ts": 1754737154160293, "dur": 65, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\GameSoft\\Unity\\Editor\\2022.3.38f1c1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Net.Security.dll"}}, {"pid": 12345, "tid": 15, "ts": 1754737154158776, "dur": 1845, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.InputSystem.dll (+pdb)"}}, {"pid": 12345, "tid": 15, "ts": 1754737154160621, "dur": 77, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1754737154161197, "dur": 135, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\GameSoft\\Unity\\Editor\\2022.3.38f1c1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Threading.Thread.dll"}}, {"pid": 12345, "tid": 15, "ts": 1754737154161340, "dur": 61, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\GameSoft\\Unity\\Editor\\2022.3.38f1c1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Xml.ReaderWriter.dll"}}, {"pid": 12345, "tid": 15, "ts": 1754737154161540, "dur": 301, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\GameSoft\\Unity\\Editor\\2022.3.38f1c1\\Editor\\Data\\Tools\\ilpp\\Unity.ILPP.Runner\\api-ms-win-core-timezone-l1-1-0.dll"}}, {"pid": 12345, "tid": 15, "ts": 1754737154161880, "dur": 301, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\GameSoft\\Unity\\Editor\\2022.3.38f1c1\\Editor\\Data\\Tools\\ilpp\\Unity.ILPP.Runner\\hostfxr.dll"}}, {"pid": 12345, "tid": 15, "ts": 1754737154162380, "dur": 86, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\GameSoft\\Unity\\Editor\\2022.3.38f1c1\\Editor\\Data\\Tools\\ilpp\\Unity.ILPP.Runner\\Microsoft.Extensions.Localization.Abstractions.dll"}}, {"pid": 12345, "tid": 15, "ts": 1754737154160709, "dur": 2517, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Burst.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 15, "ts": 1754737154163231, "dur": 391, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1754737154163938, "dur": 63, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1754737154164152, "dur": 118, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\Bee\\artifacts\\1900b0aE.dag\\post-processed\\Unity.RenderPipelines.Core.Editor.pdb"}}, {"pid": 12345, "tid": 15, "ts": 1754737154164151, "dur": 120, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.RenderPipelines.Core.Editor.pdb"}}, {"pid": 12345, "tid": 15, "ts": 1754737154164273, "dur": 86, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1754737154164369, "dur": 152, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1754737154164530, "dur": 351, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1754737154164931, "dur": 177, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "CopyFiles Library/ScriptAssemblies/com.crashkonijn.goap.demos.simple.pdb"}}, {"pid": 12345, "tid": 15, "ts": 1754737154165109, "dur": 327, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1754737154165464, "dur": 31105, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1754737153827211, "dur": 22974, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1754737153850226, "dur": 85, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.GameCenterModule.dll_09D119D0E0AA1ABF.mvfrm"}}, {"pid": 12345, "tid": 16, "ts": 1754737153850401, "dur": 106, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1754737153850591, "dur": 203, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/UnityEditor.TestRunner.rsp"}}, {"pid": 12345, "tid": 16, "ts": 1754737153850868, "dur": 92, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/CrashKonijn.Goap.Core.rsp2"}}, {"pid": 12345, "tid": 16, "ts": 1754737153850972, "dur": 61, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/CrashKonijn.Agent.Runtime.rsp2"}}, {"pid": 12345, "tid": 16, "ts": 1754737153851064, "dur": 106, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Flow.Editor.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 16, "ts": 1754737153851259, "dur": 115, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VSCode.Editor.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 16, "ts": 1754737153851451, "dur": 113, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/CrashKonijn.Goap.Editor.rsp"}}, {"pid": 12345, "tid": 16, "ts": 1754737153851610, "dur": 53, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Assembly-CSharp.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 16, "ts": 1754737153851699, "dur": 211, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.InputSystem.TestFramework.rsp"}}, {"pid": 12345, "tid": 16, "ts": 1754737153851947, "dur": 166, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1754737153852117, "dur": 215, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1754737153852332, "dur": 220, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1754737153852552, "dur": 183, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1754737153852736, "dur": 155, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1754737153852891, "dur": 330, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1754737153853221, "dur": 167, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1754737153853388, "dur": 496, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1754737153853885, "dur": 232, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1754737153854117, "dur": 368, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1754737153854485, "dur": 260, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1754737153854745, "dur": 192, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1754737153854937, "dur": 256, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1754737153855194, "dur": 244, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1754737153855439, "dur": 329, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1754737153855768, "dur": 223, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1754737153855991, "dur": 188, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1754737153856179, "dur": 209, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1754737153856388, "dur": 171, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1754737153856559, "dur": 360, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1754737153856919, "dur": 188, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1754737153857156, "dur": 51, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1754737153857228, "dur": 173, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1754737153857416, "dur": 331, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1754737153857748, "dur": 65, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/CrashKonijn.Agent.Core.dll.mvfrm"}}, {"pid": 12345, "tid": 16, "ts": 1754737153857835, "dur": 519, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/CrashKonijn.Agent.Core.dll (+2 others)"}}, {"pid": 12345, "tid": 16, "ts": 1754737153858355, "dur": 70, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1754737153858431, "dur": 392, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/CrashKonijn.Goap.Core.dll (+2 others)"}}, {"pid": 12345, "tid": 16, "ts": 1754737153858920, "dur": 424, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1754737153859345, "dur": 121, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1754737153859466, "dur": 589, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1754737153860056, "dur": 270, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/com.crashkonijn.goap.demos.turn-based.dll (+2 others)"}}, {"pid": 12345, "tid": 16, "ts": 1754737153860327, "dur": 53, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1754737153860389, "dur": 296870, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1754737154157260, "dur": 1459, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.RenderPipeline.Universal.ShaderLibrary.dll (+pdb)"}}, {"pid": 12345, "tid": 16, "ts": 1754737154158719, "dur": 56, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1754737154158781, "dur": 1291, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.VisualScripting.Core.dll (+pdb)"}}, {"pid": 12345, "tid": 16, "ts": 1754737154160072, "dur": 160, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1754737154160645, "dur": 243, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\GameSoft\\Unity\\Editor\\2022.3.38f1c1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Linq.Expressions.dll"}}, {"pid": 12345, "tid": 16, "ts": 1754737154161540, "dur": 306, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\GameSoft\\Unity\\Editor\\2022.3.38f1c1\\Editor\\Data\\Tools\\ilpp\\Unity.ILPP.Runner\\System.Data.dll"}}, {"pid": 12345, "tid": 16, "ts": 1754737154161874, "dur": 210, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\GameSoft\\Unity\\Editor\\2022.3.38f1c1\\Editor\\Data\\Tools\\ilpp\\Unity.ILPP.Runner\\System.IO.Compression.Native.dll"}}, {"pid": 12345, "tid": 16, "ts": 1754737154162117, "dur": 75, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\GameSoft\\Unity\\Editor\\2022.3.38f1c1\\Editor\\Data\\Tools\\ilpp\\Unity.ILPP.Runner\\System.Net.WebHeaderCollection.dll"}}, {"pid": 12345, "tid": 16, "ts": 1754737154162264, "dur": 96, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\GameSoft\\Unity\\Editor\\2022.3.38f1c1\\Editor\\Data\\Tools\\ilpp\\Unity.ILPP.Runner\\System.Security.Cryptography.Cng.dll"}}, {"pid": 12345, "tid": 16, "ts": 1754737154162578, "dur": 93, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\GameSoft\\Unity\\Editor\\2022.3.38f1c1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.UIModule.dll"}}, {"pid": 12345, "tid": 16, "ts": 1754737154162758, "dur": 135, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\GameSoft\\Unity\\Editor\\2022.3.38f1c1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Linq.Queryable.dll"}}, {"pid": 12345, "tid": 16, "ts": 1754737154160240, "dur": 2968, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.VisualScripting.SettingsProvider.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 16, "ts": 1754737154163320, "dur": 95, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\GameSoft\\Unity\\Editor\\2022.3.38f1c1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.InputLegacyModule.dll"}}, {"pid": 12345, "tid": 16, "ts": 1754737154163268, "dur": 2011, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.PlasticSCM.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 16, "ts": 1754737154165341, "dur": 31216, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1754737154203169, "dur": 1761, "ph": "X", "name": "ProfilerWriteOutput"}, {"pid": 3100, "tid": 24785, "ts": 1754737154206345, "dur": 2484, "ph": "X", "name": "Wait for external events", "args": {"First to finish": "backend1.traceevents"}}, {"pid": 3100, "tid": 24785, "ts": 1754737154210477, "dur": 944, "ph": "X", "name": "backend1.traceevents", "args": {}}, {"pid": 3100, "tid": 24785, "ts": 1754737154205334, "dur": 6125, "ph": "X", "name": "Write chrome-trace events", "args": {}}, {}]}