{"Instructions Readme": "1) Open Chrome or Edge, 2) go to chrome://tracing, 3) click Load, 4) navigate to this file.", "processInfo": {}, "traceEvents": [{"pid": 3100, "tid": -1, "ph": "M", "name": "process_name", "args": {"name": "BeeDriver"}}, {"pid": 3100, "tid": -1, "ph": "M", "name": "process_sort_index", "args": {"sort_index": "-2"}}, {"pid": 3100, "tid": 24781, "ph": "M", "name": "thread_name", "args": {"name": "Thread Pool Worker"}}, {"pid": 3100, "tid": 24781, "ts": 1754736429715972, "dur": 358, "ph": "X", "name": "ChromeTraceHeader", "args": {}}, {"pid": 3100, "tid": 24781, "ts": 1754736429719018, "dur": 490, "ph": "X", "name": "Thread Pool Worker", "args": {}}, {"pid": 3100, "tid": 21474836480, "ph": "M", "name": "thread_name", "args": {"name": "ReadEntireBinlogFromIpcAsync"}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736428480908, "dur": 143418, "ph": "X", "name": "WaitForConnectionAsync", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736428624327, "dur": 1087508, "ph": "X", "name": "UpdateFromStreamAsync", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736428624340, "dur": 34, "ph": "X", "name": "ReadAsync 0", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736428624376, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736428624378, "dur": 104215, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736428728605, "dur": 3, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736428728609, "dur": 47, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736428728660, "dur": 9, "ph": "X", "name": "ProcessMessages 41", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736428728670, "dur": 2169, "ph": "X", "name": "ReadAsync 41", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736428730844, "dur": 44, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736428730893, "dur": 2, "ph": "X", "name": "ProcessMessages 648", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736428730896, "dur": 45, "ph": "X", "name": "ReadAsync 648", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736428730944, "dur": 1, "ph": "X", "name": "ProcessMessages 692", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736428730946, "dur": 42, "ph": "X", "name": "ReadAsync 692", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736428730991, "dur": 1, "ph": "X", "name": "ProcessMessages 646", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736428730993, "dur": 50, "ph": "X", "name": "ReadAsync 646", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736428731046, "dur": 1, "ph": "X", "name": "ProcessMessages 687", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736428731049, "dur": 45, "ph": "X", "name": "ReadAsync 687", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736428731098, "dur": 1, "ph": "X", "name": "ProcessMessages 709", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736428731100, "dur": 45, "ph": "X", "name": "ReadAsync 709", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736428731148, "dur": 38, "ph": "X", "name": "ReadAsync 787", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736428731188, "dur": 26, "ph": "X", "name": "ReadAsync 629", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736428731217, "dur": 1, "ph": "X", "name": "ProcessMessages 344", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736428731218, "dur": 31, "ph": "X", "name": "ReadAsync 344", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736428731252, "dur": 1, "ph": "X", "name": "ProcessMessages 103", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736428731253, "dur": 41, "ph": "X", "name": "ReadAsync 103", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736428731295, "dur": 23, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736428731320, "dur": 2, "ph": "X", "name": "ProcessMessages 485", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736428731322, "dur": 50, "ph": "X", "name": "ReadAsync 485", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736428731374, "dur": 40, "ph": "X", "name": "ReadAsync 361", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736428731418, "dur": 1, "ph": "X", "name": "ProcessMessages 879", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736428731420, "dur": 42, "ph": "X", "name": "ReadAsync 879", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736428731463, "dur": 28, "ph": "X", "name": "ReadAsync 568", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736428731493, "dur": 24, "ph": "X", "name": "ReadAsync 338", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736428731519, "dur": 33, "ph": "X", "name": "ReadAsync 351", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736428731555, "dur": 1, "ph": "X", "name": "ProcessMessages 393", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736428731557, "dur": 41, "ph": "X", "name": "ReadAsync 393", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736428731601, "dur": 1, "ph": "X", "name": "ProcessMessages 711", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736428731602, "dur": 29, "ph": "X", "name": "ReadAsync 711", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736428731634, "dur": 26, "ph": "X", "name": "ReadAsync 497", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736428731662, "dur": 31, "ph": "X", "name": "ReadAsync 199", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736428731695, "dur": 1, "ph": "X", "name": "ProcessMessages 483", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736428731697, "dur": 33, "ph": "X", "name": "ReadAsync 483", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736428731732, "dur": 26, "ph": "X", "name": "ReadAsync 487", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736428731761, "dur": 26, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736428731788, "dur": 24, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736428731813, "dur": 21, "ph": "X", "name": "ReadAsync 371", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736428731836, "dur": 15, "ph": "X", "name": "ReadAsync 422", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736428731853, "dur": 20, "ph": "X", "name": "ReadAsync 323", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736428731874, "dur": 20, "ph": "X", "name": "ReadAsync 204", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736428731895, "dur": 22, "ph": "X", "name": "ReadAsync 245", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736428731919, "dur": 27, "ph": "X", "name": "ReadAsync 353", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736428731948, "dur": 25, "ph": "X", "name": "ReadAsync 414", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736428731974, "dur": 25, "ph": "X", "name": "ReadAsync 360", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736428732002, "dur": 1, "ph": "X", "name": "ProcessMessages 382", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736428732003, "dur": 34, "ph": "X", "name": "ReadAsync 382", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736428732039, "dur": 29, "ph": "X", "name": "ReadAsync 440", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736428732069, "dur": 22, "ph": "X", "name": "ReadAsync 485", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736428732094, "dur": 30, "ph": "X", "name": "ReadAsync 480", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736428732127, "dur": 1, "ph": "X", "name": "ProcessMessages 455", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736428732128, "dur": 36, "ph": "X", "name": "ReadAsync 455", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736428732167, "dur": 24, "ph": "X", "name": "ReadAsync 594", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736428732193, "dur": 21, "ph": "X", "name": "ReadAsync 341", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736428732215, "dur": 23, "ph": "X", "name": "ReadAsync 438", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736428732239, "dur": 20, "ph": "X", "name": "ReadAsync 314", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736428732261, "dur": 23, "ph": "X", "name": "ReadAsync 360", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736428732286, "dur": 21, "ph": "X", "name": "ReadAsync 385", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736428732308, "dur": 20, "ph": "X", "name": "ReadAsync 435", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736428732330, "dur": 20, "ph": "X", "name": "ReadAsync 343", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736428732352, "dur": 21, "ph": "X", "name": "ReadAsync 342", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736428732374, "dur": 22, "ph": "X", "name": "ReadAsync 342", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736428732398, "dur": 23, "ph": "X", "name": "ReadAsync 255", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736428732422, "dur": 21, "ph": "X", "name": "ReadAsync 449", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736428732445, "dur": 23, "ph": "X", "name": "ReadAsync 370", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736428732469, "dur": 20, "ph": "X", "name": "ReadAsync 364", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736428732491, "dur": 21, "ph": "X", "name": "ReadAsync 234", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736428732513, "dur": 19, "ph": "X", "name": "ReadAsync 364", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736428732534, "dur": 18, "ph": "X", "name": "ReadAsync 296", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736428732553, "dur": 17, "ph": "X", "name": "ReadAsync 333", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736428732571, "dur": 20, "ph": "X", "name": "ReadAsync 120", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736428732593, "dur": 20, "ph": "X", "name": "ReadAsync 241", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736428732615, "dur": 20, "ph": "X", "name": "ReadAsync 330", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736428732636, "dur": 22, "ph": "X", "name": "ReadAsync 347", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736428732660, "dur": 23, "ph": "X", "name": "ReadAsync 389", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736428732684, "dur": 21, "ph": "X", "name": "ReadAsync 439", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736428732707, "dur": 21, "ph": "X", "name": "ReadAsync 350", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736428732728, "dur": 20, "ph": "X", "name": "ReadAsync 499", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736428732751, "dur": 21, "ph": "X", "name": "ReadAsync 258", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736428732773, "dur": 18, "ph": "X", "name": "ReadAsync 380", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736428732793, "dur": 22, "ph": "X", "name": "ReadAsync 483", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736428732816, "dur": 21, "ph": "X", "name": "ReadAsync 373", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736428732838, "dur": 20, "ph": "X", "name": "ReadAsync 358", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736428732860, "dur": 21, "ph": "X", "name": "ReadAsync 395", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736428732882, "dur": 39, "ph": "X", "name": "ReadAsync 351", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736428732922, "dur": 26, "ph": "X", "name": "ReadAsync 403", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736428732950, "dur": 29, "ph": "X", "name": "ReadAsync 620", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736428732981, "dur": 31, "ph": "X", "name": "ReadAsync 353", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736428733014, "dur": 1, "ph": "X", "name": "ProcessMessages 661", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736428733017, "dur": 45, "ph": "X", "name": "ReadAsync 661", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736428733064, "dur": 1, "ph": "X", "name": "ProcessMessages 717", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736428733065, "dur": 45, "ph": "X", "name": "ReadAsync 717", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736428733112, "dur": 1, "ph": "X", "name": "ProcessMessages 729", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736428733114, "dur": 26, "ph": "X", "name": "ReadAsync 729", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736428733142, "dur": 20, "ph": "X", "name": "ReadAsync 735", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736428733164, "dur": 23, "ph": "X", "name": "ReadAsync 339", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736428733189, "dur": 22, "ph": "X", "name": "ReadAsync 315", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736428733213, "dur": 21, "ph": "X", "name": "ReadAsync 426", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736428733235, "dur": 22, "ph": "X", "name": "ReadAsync 334", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736428733258, "dur": 19, "ph": "X", "name": "ReadAsync 291", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736428733279, "dur": 17, "ph": "X", "name": "ReadAsync 202", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736428733298, "dur": 23, "ph": "X", "name": "ReadAsync 166", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736428733323, "dur": 25, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736428733350, "dur": 20, "ph": "X", "name": "ReadAsync 457", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736428733372, "dur": 24, "ph": "X", "name": "ReadAsync 314", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736428733397, "dur": 22, "ph": "X", "name": "ReadAsync 436", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736428733421, "dur": 20, "ph": "X", "name": "ReadAsync 319", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736428733442, "dur": 24, "ph": "X", "name": "ReadAsync 215", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736428733468, "dur": 21, "ph": "X", "name": "ReadAsync 294", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736428733490, "dur": 21, "ph": "X", "name": "ReadAsync 359", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736428733513, "dur": 20, "ph": "X", "name": "ReadAsync 315", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736428733534, "dur": 21, "ph": "X", "name": "ReadAsync 295", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736428733557, "dur": 21, "ph": "X", "name": "ReadAsync 324", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736428733579, "dur": 21, "ph": "X", "name": "ReadAsync 291", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736428733602, "dur": 21, "ph": "X", "name": "ReadAsync 176", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736428733624, "dur": 19, "ph": "X", "name": "ReadAsync 341", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736428733645, "dur": 35, "ph": "X", "name": "ReadAsync 155", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736428733681, "dur": 23, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736428733705, "dur": 21, "ph": "X", "name": "ReadAsync 347", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736428733728, "dur": 20, "ph": "X", "name": "ReadAsync 357", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736428733749, "dur": 20, "ph": "X", "name": "ReadAsync 281", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736428733773, "dur": 21, "ph": "X", "name": "ReadAsync 298", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736428733795, "dur": 21, "ph": "X", "name": "ReadAsync 275", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736428733818, "dur": 21, "ph": "X", "name": "ReadAsync 327", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736428733840, "dur": 21, "ph": "X", "name": "ReadAsync 232", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736428733863, "dur": 20, "ph": "X", "name": "ReadAsync 369", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736428733884, "dur": 22, "ph": "X", "name": "ReadAsync 271", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736428733908, "dur": 31, "ph": "X", "name": "ReadAsync 303", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736428733940, "dur": 30, "ph": "X", "name": "ReadAsync 235", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736428733972, "dur": 28, "ph": "X", "name": "ReadAsync 319", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736428734002, "dur": 1, "ph": "X", "name": "ProcessMessages 164", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736428734004, "dur": 41, "ph": "X", "name": "ReadAsync 164", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736428734047, "dur": 1, "ph": "X", "name": "ProcessMessages 503", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736428734049, "dur": 47, "ph": "X", "name": "ReadAsync 503", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736428734099, "dur": 1, "ph": "X", "name": "ProcessMessages 651", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736428734101, "dur": 40, "ph": "X", "name": "ReadAsync 651", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736428734144, "dur": 1, "ph": "X", "name": "ProcessMessages 790", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736428734145, "dur": 33, "ph": "X", "name": "ReadAsync 790", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736428734181, "dur": 25, "ph": "X", "name": "ReadAsync 502", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736428734209, "dur": 21, "ph": "X", "name": "ReadAsync 545", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736428734231, "dur": 17, "ph": "X", "name": "ReadAsync 261", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736428734250, "dur": 22, "ph": "X", "name": "ReadAsync 365", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736428734273, "dur": 17, "ph": "X", "name": "ReadAsync 210", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736428734292, "dur": 24, "ph": "X", "name": "ReadAsync 168", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736428734318, "dur": 17, "ph": "X", "name": "ReadAsync 324", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736428734336, "dur": 20, "ph": "X", "name": "ReadAsync 295", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736428734358, "dur": 17, "ph": "X", "name": "ReadAsync 301", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736428734376, "dur": 22, "ph": "X", "name": "ReadAsync 285", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736428734400, "dur": 21, "ph": "X", "name": "ReadAsync 240", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736428734423, "dur": 17, "ph": "X", "name": "ReadAsync 357", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736428734441, "dur": 24, "ph": "X", "name": "ReadAsync 161", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736428734467, "dur": 21, "ph": "X", "name": "ReadAsync 324", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736428734489, "dur": 17, "ph": "X", "name": "ReadAsync 420", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736428734507, "dur": 22, "ph": "X", "name": "ReadAsync 352", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736428734531, "dur": 21, "ph": "X", "name": "ReadAsync 176", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736428734554, "dur": 22, "ph": "X", "name": "ReadAsync 347", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736428734577, "dur": 17, "ph": "X", "name": "ReadAsync 323", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736428734596, "dur": 20, "ph": "X", "name": "ReadAsync 248", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736428734617, "dur": 17, "ph": "X", "name": "ReadAsync 207", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736428734635, "dur": 21, "ph": "X", "name": "ReadAsync 229", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736428734657, "dur": 21, "ph": "X", "name": "ReadAsync 269", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736428734679, "dur": 25, "ph": "X", "name": "ReadAsync 331", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736428734706, "dur": 25, "ph": "X", "name": "ReadAsync 346", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736428734732, "dur": 20, "ph": "X", "name": "ReadAsync 354", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736428734753, "dur": 17, "ph": "X", "name": "ReadAsync 295", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736428734771, "dur": 22, "ph": "X", "name": "ReadAsync 283", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736428734794, "dur": 21, "ph": "X", "name": "ReadAsync 241", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736428734817, "dur": 17, "ph": "X", "name": "ReadAsync 241", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736428734836, "dur": 21, "ph": "X", "name": "ReadAsync 161", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736428734858, "dur": 21, "ph": "X", "name": "ReadAsync 240", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736428734881, "dur": 50, "ph": "X", "name": "ReadAsync 364", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736428734934, "dur": 1, "ph": "X", "name": "ProcessMessages 193", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736428734936, "dur": 46, "ph": "X", "name": "ReadAsync 193", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736428734986, "dur": 2, "ph": "X", "name": "ProcessMessages 811", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736428734990, "dur": 61, "ph": "X", "name": "ReadAsync 811", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736428735053, "dur": 1, "ph": "X", "name": "ProcessMessages 517", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736428735056, "dur": 48, "ph": "X", "name": "ReadAsync 517", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736428735106, "dur": 45, "ph": "X", "name": "ReadAsync 243", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736428735155, "dur": 47, "ph": "X", "name": "ReadAsync 271", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736428735206, "dur": 50, "ph": "X", "name": "ReadAsync 276", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736428735260, "dur": 1, "ph": "X", "name": "ProcessMessages 454", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736428735262, "dur": 48, "ph": "X", "name": "ReadAsync 454", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736428735313, "dur": 1, "ph": "X", "name": "ProcessMessages 570", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736428735314, "dur": 40, "ph": "X", "name": "ReadAsync 570", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736428735357, "dur": 24, "ph": "X", "name": "ReadAsync 506", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736428735382, "dur": 29, "ph": "X", "name": "ReadAsync 342", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736428735413, "dur": 22, "ph": "X", "name": "ReadAsync 189", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736428735438, "dur": 25, "ph": "X", "name": "ReadAsync 460", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736428735464, "dur": 20, "ph": "X", "name": "ReadAsync 356", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736428735486, "dur": 22, "ph": "X", "name": "ReadAsync 194", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736428735509, "dur": 22, "ph": "X", "name": "ReadAsync 268", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736428735532, "dur": 21, "ph": "X", "name": "ReadAsync 315", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736428735555, "dur": 21, "ph": "X", "name": "ReadAsync 390", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736428735578, "dur": 23, "ph": "X", "name": "ReadAsync 178", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736428735602, "dur": 22, "ph": "X", "name": "ReadAsync 405", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736428735626, "dur": 21, "ph": "X", "name": "ReadAsync 447", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736428735648, "dur": 22, "ph": "X", "name": "ReadAsync 471", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736428735671, "dur": 22, "ph": "X", "name": "ReadAsync 422", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736428735694, "dur": 21, "ph": "X", "name": "ReadAsync 355", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736428735717, "dur": 18, "ph": "X", "name": "ReadAsync 366", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736428735736, "dur": 22, "ph": "X", "name": "ReadAsync 377", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736428735760, "dur": 21, "ph": "X", "name": "ReadAsync 366", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736428735782, "dur": 22, "ph": "X", "name": "ReadAsync 319", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736428735805, "dur": 22, "ph": "X", "name": "ReadAsync 378", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736428735828, "dur": 21, "ph": "X", "name": "ReadAsync 431", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736428735850, "dur": 35, "ph": "X", "name": "ReadAsync 366", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736428735887, "dur": 24, "ph": "X", "name": "ReadAsync 455", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736428735912, "dur": 22, "ph": "X", "name": "ReadAsync 352", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736428735936, "dur": 29, "ph": "X", "name": "ReadAsync 419", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736428735968, "dur": 1, "ph": "X", "name": "ProcessMessages 430", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736428735969, "dur": 46, "ph": "X", "name": "ReadAsync 430", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736428736018, "dur": 1, "ph": "X", "name": "ProcessMessages 504", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736428736020, "dur": 27, "ph": "X", "name": "ReadAsync 504", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736428736048, "dur": 28, "ph": "X", "name": "ReadAsync 589", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736428736078, "dur": 27, "ph": "X", "name": "ReadAsync 21", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736428736108, "dur": 3, "ph": "X", "name": "ProcessMessages 336", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736428736112, "dur": 37, "ph": "X", "name": "ReadAsync 336", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736428736151, "dur": 24, "ph": "X", "name": "ReadAsync 624", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736428736177, "dur": 22, "ph": "X", "name": "ReadAsync 509", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736428736202, "dur": 20, "ph": "X", "name": "ReadAsync 413", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736428736224, "dur": 24, "ph": "X", "name": "ReadAsync 169", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736428736249, "dur": 21, "ph": "X", "name": "ReadAsync 465", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736428736272, "dur": 21, "ph": "X", "name": "ReadAsync 360", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736428736295, "dur": 18, "ph": "X", "name": "ReadAsync 396", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736428736314, "dur": 22, "ph": "X", "name": "ReadAsync 279", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736428736337, "dur": 21, "ph": "X", "name": "ReadAsync 304", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736428736359, "dur": 19, "ph": "X", "name": "ReadAsync 299", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736428736379, "dur": 21, "ph": "X", "name": "ReadAsync 124", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736428736402, "dur": 21, "ph": "X", "name": "ReadAsync 328", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736428736424, "dur": 22, "ph": "X", "name": "ReadAsync 371", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736428736447, "dur": 24, "ph": "X", "name": "ReadAsync 454", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736428736473, "dur": 21, "ph": "X", "name": "ReadAsync 388", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736428736496, "dur": 21, "ph": "X", "name": "ReadAsync 327", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736428736518, "dur": 19, "ph": "X", "name": "ReadAsync 395", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736428736539, "dur": 123, "ph": "X", "name": "ReadAsync 8", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736428736666, "dur": 34, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736428736702, "dur": 21, "ph": "X", "name": "ReadAsync 615", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736428736725, "dur": 21, "ph": "X", "name": "ReadAsync 289", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736428736748, "dur": 20, "ph": "X", "name": "ReadAsync 298", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736428736769, "dur": 21, "ph": "X", "name": "ReadAsync 275", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736428736792, "dur": 21, "ph": "X", "name": "ReadAsync 353", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736428736815, "dur": 53, "ph": "X", "name": "ReadAsync 319", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736428736869, "dur": 21, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736428736892, "dur": 20, "ph": "X", "name": "ReadAsync 418", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736428736914, "dur": 30, "ph": "X", "name": "ReadAsync 383", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736428736947, "dur": 1, "ph": "X", "name": "ProcessMessages 449", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736428736949, "dur": 45, "ph": "X", "name": "ReadAsync 449", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736428736996, "dur": 36, "ph": "X", "name": "ReadAsync 633", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736428737035, "dur": 1, "ph": "X", "name": "ProcessMessages 552", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736428737037, "dur": 40, "ph": "X", "name": "ReadAsync 552", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736428737080, "dur": 1, "ph": "X", "name": "ProcessMessages 626", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736428737082, "dur": 36, "ph": "X", "name": "ReadAsync 626", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736428737121, "dur": 1, "ph": "X", "name": "ProcessMessages 656", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736428737123, "dur": 26, "ph": "X", "name": "ReadAsync 656", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736428737151, "dur": 19, "ph": "X", "name": "ReadAsync 540", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736428737171, "dur": 27, "ph": "X", "name": "ReadAsync 163", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736428737202, "dur": 32, "ph": "X", "name": "ReadAsync 134", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736428737235, "dur": 1, "ph": "X", "name": "ProcessMessages 578", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736428737237, "dur": 28, "ph": "X", "name": "ReadAsync 578", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736428737266, "dur": 17, "ph": "X", "name": "ReadAsync 473", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736428737285, "dur": 20, "ph": "X", "name": "ReadAsync 111", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736428737306, "dur": 18, "ph": "X", "name": "ReadAsync 213", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736428737325, "dur": 1, "ph": "X", "name": "ProcessMessages 296", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736428737326, "dur": 21, "ph": "X", "name": "ReadAsync 296", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736428737348, "dur": 16, "ph": "X", "name": "ReadAsync 241", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736428737366, "dur": 22, "ph": "X", "name": "ReadAsync 71", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736428737390, "dur": 21, "ph": "X", "name": "ReadAsync 337", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736428737412, "dur": 20, "ph": "X", "name": "ReadAsync 331", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736428737434, "dur": 19, "ph": "X", "name": "ReadAsync 362", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736428737455, "dur": 22, "ph": "X", "name": "ReadAsync 266", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736428737478, "dur": 20, "ph": "X", "name": "ReadAsync 261", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736428737499, "dur": 18, "ph": "X", "name": "ReadAsync 312", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736428737519, "dur": 28, "ph": "X", "name": "ReadAsync 248", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736428737549, "dur": 22, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736428737574, "dur": 23, "ph": "X", "name": "ReadAsync 372", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736428737598, "dur": 21, "ph": "X", "name": "ReadAsync 483", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736428737621, "dur": 21, "ph": "X", "name": "ReadAsync 329", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736428737644, "dur": 20, "ph": "X", "name": "ReadAsync 295", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736428737665, "dur": 21, "ph": "X", "name": "ReadAsync 214", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736428737687, "dur": 21, "ph": "X", "name": "ReadAsync 417", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736428737710, "dur": 21, "ph": "X", "name": "ReadAsync 193", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736428737732, "dur": 21, "ph": "X", "name": "ReadAsync 366", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736428737755, "dur": 20, "ph": "X", "name": "ReadAsync 335", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736428737777, "dur": 21, "ph": "X", "name": "ReadAsync 363", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736428737800, "dur": 21, "ph": "X", "name": "ReadAsync 338", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736428737822, "dur": 21, "ph": "X", "name": "ReadAsync 280", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736428737844, "dur": 18, "ph": "X", "name": "ReadAsync 321", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736428737864, "dur": 21, "ph": "X", "name": "ReadAsync 180", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736428737886, "dur": 20, "ph": "X", "name": "ReadAsync 275", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736428737908, "dur": 24, "ph": "X", "name": "ReadAsync 342", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736428737934, "dur": 24, "ph": "X", "name": "ReadAsync 412", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736428737961, "dur": 26, "ph": "X", "name": "ReadAsync 385", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736428737988, "dur": 29, "ph": "X", "name": "ReadAsync 327", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736428738020, "dur": 1, "ph": "X", "name": "ProcessMessages 482", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736428738022, "dur": 42, "ph": "X", "name": "ReadAsync 482", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736428738068, "dur": 1, "ph": "X", "name": "ProcessMessages 474", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736428738069, "dur": 33, "ph": "X", "name": "ReadAsync 474", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736428738104, "dur": 1, "ph": "X", "name": "ProcessMessages 478", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736428738106, "dur": 36, "ph": "X", "name": "ReadAsync 478", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736428738143, "dur": 22, "ph": "X", "name": "ReadAsync 565", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736428738168, "dur": 1, "ph": "X", "name": "ProcessMessages 285", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736428738170, "dur": 30, "ph": "X", "name": "ReadAsync 285", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736428738201, "dur": 21, "ph": "X", "name": "ReadAsync 415", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736428738223, "dur": 24, "ph": "X", "name": "ReadAsync 302", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736428738249, "dur": 24, "ph": "X", "name": "ReadAsync 145", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736428738275, "dur": 23, "ph": "X", "name": "ReadAsync 402", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736428738299, "dur": 21, "ph": "X", "name": "ReadAsync 344", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736428738322, "dur": 20, "ph": "X", "name": "ReadAsync 283", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736428738343, "dur": 17, "ph": "X", "name": "ReadAsync 256", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736428738362, "dur": 20, "ph": "X", "name": "ReadAsync 282", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736428738383, "dur": 16, "ph": "X", "name": "ReadAsync 280", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736428738402, "dur": 21, "ph": "X", "name": "ReadAsync 91", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736428738424, "dur": 21, "ph": "X", "name": "ReadAsync 330", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736428738446, "dur": 31, "ph": "X", "name": "ReadAsync 319", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736428738479, "dur": 21, "ph": "X", "name": "ReadAsync 359", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736428738501, "dur": 22, "ph": "X", "name": "ReadAsync 326", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736428738524, "dur": 20, "ph": "X", "name": "ReadAsync 273", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736428738547, "dur": 20, "ph": "X", "name": "ReadAsync 231", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736428738569, "dur": 20, "ph": "X", "name": "ReadAsync 245", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736428738590, "dur": 21, "ph": "X", "name": "ReadAsync 393", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736428738612, "dur": 17, "ph": "X", "name": "ReadAsync 427", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736428738630, "dur": 21, "ph": "X", "name": "ReadAsync 453", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736428738652, "dur": 21, "ph": "X", "name": "ReadAsync 227", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736428738675, "dur": 21, "ph": "X", "name": "ReadAsync 403", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736428738698, "dur": 21, "ph": "X", "name": "ReadAsync 365", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736428738720, "dur": 20, "ph": "X", "name": "ReadAsync 213", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736428738741, "dur": 17, "ph": "X", "name": "ReadAsync 429", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736428738760, "dur": 21, "ph": "X", "name": "ReadAsync 463", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736428738782, "dur": 21, "ph": "X", "name": "ReadAsync 366", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736428738804, "dur": 21, "ph": "X", "name": "ReadAsync 346", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736428738827, "dur": 21, "ph": "X", "name": "ReadAsync 393", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736428738849, "dur": 20, "ph": "X", "name": "ReadAsync 494", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736428738871, "dur": 18, "ph": "X", "name": "ReadAsync 242", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736428738890, "dur": 21, "ph": "X", "name": "ReadAsync 378", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736428738912, "dur": 22, "ph": "X", "name": "ReadAsync 383", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736428738936, "dur": 22, "ph": "X", "name": "ReadAsync 489", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736428738960, "dur": 37, "ph": "X", "name": "ReadAsync 318", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736428738999, "dur": 1, "ph": "X", "name": "ProcessMessages 435", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736428739001, "dur": 36, "ph": "X", "name": "ReadAsync 435", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736428739039, "dur": 1, "ph": "X", "name": "ProcessMessages 432", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736428739041, "dur": 35, "ph": "X", "name": "ReadAsync 432", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736428739078, "dur": 24, "ph": "X", "name": "ReadAsync 517", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736428739104, "dur": 1, "ph": "X", "name": "ProcessMessages 121", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736428739106, "dur": 40, "ph": "X", "name": "ReadAsync 121", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736428739148, "dur": 31, "ph": "X", "name": "ReadAsync 528", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736428739181, "dur": 22, "ph": "X", "name": "ReadAsync 255", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736428739204, "dur": 23, "ph": "X", "name": "ReadAsync 406", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736428739228, "dur": 23, "ph": "X", "name": "ReadAsync 158", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736428739253, "dur": 21, "ph": "X", "name": "ReadAsync 462", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736428739277, "dur": 21, "ph": "X", "name": "ReadAsync 437", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736428739299, "dur": 21, "ph": "X", "name": "ReadAsync 296", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736428739321, "dur": 20, "ph": "X", "name": "ReadAsync 302", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736428739343, "dur": 20, "ph": "X", "name": "ReadAsync 320", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736428739364, "dur": 18, "ph": "X", "name": "ReadAsync 300", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736428739383, "dur": 23, "ph": "X", "name": "ReadAsync 241", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736428739407, "dur": 21, "ph": "X", "name": "ReadAsync 280", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736428739429, "dur": 21, "ph": "X", "name": "ReadAsync 398", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736428739452, "dur": 21, "ph": "X", "name": "ReadAsync 268", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736428739474, "dur": 20, "ph": "X", "name": "ReadAsync 242", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736428739495, "dur": 18, "ph": "X", "name": "ReadAsync 293", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736428739515, "dur": 21, "ph": "X", "name": "ReadAsync 285", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736428739537, "dur": 28, "ph": "X", "name": "ReadAsync 387", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736428739567, "dur": 24, "ph": "X", "name": "ReadAsync 408", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736428739592, "dur": 21, "ph": "X", "name": "ReadAsync 405", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736428739614, "dur": 21, "ph": "X", "name": "ReadAsync 369", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736428739636, "dur": 18, "ph": "X", "name": "ReadAsync 419", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736428739656, "dur": 21, "ph": "X", "name": "ReadAsync 380", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736428739678, "dur": 23, "ph": "X", "name": "ReadAsync 191", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736428739703, "dur": 22, "ph": "X", "name": "ReadAsync 284", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736428739726, "dur": 21, "ph": "X", "name": "ReadAsync 342", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736428739748, "dur": 19, "ph": "X", "name": "ReadAsync 406", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736428739768, "dur": 21, "ph": "X", "name": "ReadAsync 100", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736428739791, "dur": 22, "ph": "X", "name": "ReadAsync 402", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736428739814, "dur": 22, "ph": "X", "name": "ReadAsync 308", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736428739838, "dur": 21, "ph": "X", "name": "ReadAsync 445", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736428739861, "dur": 22, "ph": "X", "name": "ReadAsync 222", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736428739884, "dur": 21, "ph": "X", "name": "ReadAsync 416", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736428739906, "dur": 24, "ph": "X", "name": "ReadAsync 366", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736428739932, "dur": 24, "ph": "X", "name": "ReadAsync 345", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736428739959, "dur": 71, "ph": "X", "name": "ReadAsync 288", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736428740033, "dur": 1, "ph": "X", "name": "ProcessMessages 512", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736428740035, "dur": 54, "ph": "X", "name": "ReadAsync 512", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736428740093, "dur": 1, "ph": "X", "name": "ProcessMessages 770", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736428740096, "dur": 240, "ph": "X", "name": "ReadAsync 770", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736428740338, "dur": 26, "ph": "X", "name": "ReadAsync 256", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736428740366, "dur": 43, "ph": "X", "name": "ReadAsync 316", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736428740412, "dur": 40, "ph": "X", "name": "ReadAsync 112", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736428740456, "dur": 1, "ph": "X", "name": "ProcessMessages 577", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736428740457, "dur": 28, "ph": "X", "name": "ReadAsync 577", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736428740488, "dur": 1, "ph": "X", "name": "ProcessMessages 387", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736428740490, "dur": 34, "ph": "X", "name": "ReadAsync 387", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736428740525, "dur": 23, "ph": "X", "name": "ReadAsync 483", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736428740550, "dur": 24, "ph": "X", "name": "ReadAsync 33", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736428740576, "dur": 21, "ph": "X", "name": "ReadAsync 124", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736428740598, "dur": 24, "ph": "X", "name": "ReadAsync 179", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736428740625, "dur": 23, "ph": "X", "name": "ReadAsync 149", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736428740650, "dur": 19, "ph": "X", "name": "ReadAsync 179", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736428740671, "dur": 18, "ph": "X", "name": "ReadAsync 190", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736428740690, "dur": 22, "ph": "X", "name": "ReadAsync 101", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736428740714, "dur": 27, "ph": "X", "name": "ReadAsync 149", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736428740743, "dur": 24, "ph": "X", "name": "ReadAsync 175", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736428740768, "dur": 30, "ph": "X", "name": "ReadAsync 215", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736428740802, "dur": 40, "ph": "X", "name": "ReadAsync 175", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736428740845, "dur": 44, "ph": "X", "name": "ReadAsync 154", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736428740892, "dur": 1, "ph": "X", "name": "ProcessMessages 197", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736428740894, "dur": 41, "ph": "X", "name": "ReadAsync 197", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736428740938, "dur": 34, "ph": "X", "name": "ReadAsync 237", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736428740974, "dur": 43, "ph": "X", "name": "ReadAsync 40", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736428741021, "dur": 1, "ph": "X", "name": "ProcessMessages 200", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736428741023, "dur": 41, "ph": "X", "name": "ReadAsync 200", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736428741068, "dur": 39, "ph": "X", "name": "ReadAsync 160", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736428741109, "dur": 2, "ph": "X", "name": "ProcessMessages 535", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736428741111, "dur": 28, "ph": "X", "name": "ReadAsync 535", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736428741141, "dur": 1, "ph": "X", "name": "ProcessMessages 294", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736428741142, "dur": 42, "ph": "X", "name": "ReadAsync 294", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736428741189, "dur": 37, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736428741229, "dur": 42, "ph": "X", "name": "ReadAsync 480", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736428741273, "dur": 1, "ph": "X", "name": "ProcessMessages 304", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736428741275, "dur": 39, "ph": "X", "name": "ReadAsync 304", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736428741316, "dur": 2, "ph": "X", "name": "ProcessMessages 817", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736428741318, "dur": 27, "ph": "X", "name": "ReadAsync 817", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736428741348, "dur": 1, "ph": "X", "name": "ProcessMessages 258", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736428741350, "dur": 36, "ph": "X", "name": "ReadAsync 258", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736428741387, "dur": 25, "ph": "X", "name": "ReadAsync 385", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736428741414, "dur": 22, "ph": "X", "name": "ReadAsync 274", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736428741437, "dur": 1, "ph": "X", "name": "ProcessMessages 237", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736428741438, "dur": 22, "ph": "X", "name": "ReadAsync 237", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736428741462, "dur": 23, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736428741486, "dur": 21, "ph": "X", "name": "ReadAsync 224", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736428741508, "dur": 22, "ph": "X", "name": "ReadAsync 159", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736428741532, "dur": 28, "ph": "X", "name": "ReadAsync 308", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736428741561, "dur": 23, "ph": "X", "name": "ReadAsync 281", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736428741585, "dur": 25, "ph": "X", "name": "ReadAsync 364", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736428741615, "dur": 29, "ph": "X", "name": "ReadAsync 125", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736428741647, "dur": 24, "ph": "X", "name": "ReadAsync 284", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736428741673, "dur": 21, "ph": "X", "name": "ReadAsync 373", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736428741695, "dur": 24, "ph": "X", "name": "ReadAsync 149", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736428741734, "dur": 37, "ph": "X", "name": "ReadAsync 89", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736428741773, "dur": 21, "ph": "X", "name": "ReadAsync 302", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736428741797, "dur": 24, "ph": "X", "name": "ReadAsync 278", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736428741822, "dur": 17, "ph": "X", "name": "ReadAsync 341", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736428741842, "dur": 20, "ph": "X", "name": "ReadAsync 277", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736428741864, "dur": 26, "ph": "X", "name": "ReadAsync 245", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736428741891, "dur": 20, "ph": "X", "name": "ReadAsync 202", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736428741913, "dur": 25, "ph": "X", "name": "ReadAsync 113", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736428741941, "dur": 1, "ph": "X", "name": "ProcessMessages 295", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736428741942, "dur": 57, "ph": "X", "name": "ReadAsync 295", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736428742002, "dur": 1, "ph": "X", "name": "ProcessMessages 367", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736428742004, "dur": 35, "ph": "X", "name": "ReadAsync 367", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736428742041, "dur": 32, "ph": "X", "name": "ReadAsync 61", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736428742075, "dur": 26, "ph": "X", "name": "ReadAsync 330", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736428742102, "dur": 26, "ph": "X", "name": "ReadAsync 373", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736428742132, "dur": 26, "ph": "X", "name": "ReadAsync 237", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736428742162, "dur": 32, "ph": "X", "name": "ReadAsync 155", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736428742195, "dur": 1, "ph": "X", "name": "ProcessMessages 313", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736428742197, "dur": 20, "ph": "X", "name": "ReadAsync 313", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736428742218, "dur": 20, "ph": "X", "name": "ReadAsync 463", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736428742240, "dur": 19, "ph": "X", "name": "ReadAsync 67", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736428742260, "dur": 21, "ph": "X", "name": "ReadAsync 95", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736428742283, "dur": 27, "ph": "X", "name": "ReadAsync 221", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736428742311, "dur": 21, "ph": "X", "name": "ReadAsync 247", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736428742334, "dur": 19, "ph": "X", "name": "ReadAsync 388", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736428742354, "dur": 20, "ph": "X", "name": "ReadAsync 61", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736428742375, "dur": 20, "ph": "X", "name": "ReadAsync 200", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736428742396, "dur": 19, "ph": "X", "name": "ReadAsync 239", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736428742416, "dur": 20, "ph": "X", "name": "ReadAsync 21", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736428742438, "dur": 20, "ph": "X", "name": "ReadAsync 351", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736428742459, "dur": 19, "ph": "X", "name": "ReadAsync 237", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736428742479, "dur": 20, "ph": "X", "name": "ReadAsync 40", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736428742501, "dur": 20, "ph": "X", "name": "ReadAsync 239", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736428742522, "dur": 21, "ph": "X", "name": "ReadAsync 250", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736428742544, "dur": 19, "ph": "X", "name": "ReadAsync 402", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736428742565, "dur": 17, "ph": "X", "name": "ReadAsync 237", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736428742583, "dur": 22, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736428742606, "dur": 20, "ph": "X", "name": "ReadAsync 318", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736428742628, "dur": 19, "ph": "X", "name": "ReadAsync 298", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736428742650, "dur": 20, "ph": "X", "name": "ReadAsync 427", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736428742671, "dur": 19, "ph": "X", "name": "ReadAsync 240", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736428742691, "dur": 21, "ph": "X", "name": "ReadAsync 40", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736428742713, "dur": 21, "ph": "X", "name": "ReadAsync 326", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736428742736, "dur": 21, "ph": "X", "name": "ReadAsync 287", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736428742758, "dur": 20, "ph": "X", "name": "ReadAsync 403", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736428742780, "dur": 19, "ph": "X", "name": "ReadAsync 193", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736428742800, "dur": 21, "ph": "X", "name": "ReadAsync 113", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736428742823, "dur": 19, "ph": "X", "name": "ReadAsync 274", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736428742844, "dur": 20, "ph": "X", "name": "ReadAsync 133", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736428742865, "dur": 21, "ph": "X", "name": "ReadAsync 338", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736428742888, "dur": 20, "ph": "X", "name": "ReadAsync 228", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736428742909, "dur": 20, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736428742930, "dur": 30, "ph": "X", "name": "ReadAsync 174", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736428742962, "dur": 57, "ph": "X", "name": "ReadAsync 245", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736428743021, "dur": 1, "ph": "X", "name": "ProcessMessages 527", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736428743023, "dur": 27, "ph": "X", "name": "ReadAsync 527", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736428743053, "dur": 1, "ph": "X", "name": "ProcessMessages 433", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736428743055, "dur": 27, "ph": "X", "name": "ReadAsync 433", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736428743084, "dur": 38, "ph": "X", "name": "ReadAsync 177", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736428743124, "dur": 1, "ph": "X", "name": "ProcessMessages 345", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736428743126, "dur": 32, "ph": "X", "name": "ReadAsync 345", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736428743160, "dur": 24, "ph": "X", "name": "ReadAsync 225", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736428743186, "dur": 21, "ph": "X", "name": "ReadAsync 265", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736428743208, "dur": 1, "ph": "X", "name": "ProcessMessages 280", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736428743210, "dur": 20, "ph": "X", "name": "ReadAsync 280", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736428743231, "dur": 22, "ph": "X", "name": "ReadAsync 306", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736428743255, "dur": 18, "ph": "X", "name": "ReadAsync 143", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736428743274, "dur": 28, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736428743304, "dur": 17, "ph": "X", "name": "ReadAsync 244", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736428743322, "dur": 36, "ph": "X", "name": "ReadAsync 201", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736428743362, "dur": 31, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736428743395, "dur": 23, "ph": "X", "name": "ReadAsync 456", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736428743421, "dur": 24, "ph": "X", "name": "ReadAsync 214", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736428743446, "dur": 15, "ph": "X", "name": "ReadAsync 443", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736428743462, "dur": 21, "ph": "X", "name": "ReadAsync 347", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736428743485, "dur": 23, "ph": "X", "name": "ReadAsync 188", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736428743510, "dur": 17, "ph": "X", "name": "ReadAsync 408", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736428743529, "dur": 18, "ph": "X", "name": "ReadAsync 169", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736428743548, "dur": 33, "ph": "X", "name": "ReadAsync 140", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736428743585, "dur": 30, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736428743617, "dur": 21, "ph": "X", "name": "ReadAsync 407", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736428743639, "dur": 25, "ph": "X", "name": "ReadAsync 103", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736428743666, "dur": 25, "ph": "X", "name": "ReadAsync 493", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736428743693, "dur": 23, "ph": "X", "name": "ReadAsync 355", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736428743718, "dur": 24, "ph": "X", "name": "ReadAsync 331", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736428743743, "dur": 19, "ph": "X", "name": "ReadAsync 111", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736428743764, "dur": 20, "ph": "X", "name": "ReadAsync 145", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736428743786, "dur": 21, "ph": "X", "name": "ReadAsync 311", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736428743808, "dur": 19, "ph": "X", "name": "ReadAsync 271", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736428743829, "dur": 17, "ph": "X", "name": "ReadAsync 187", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736428743847, "dur": 21, "ph": "X", "name": "ReadAsync 53", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736428743869, "dur": 39, "ph": "X", "name": "ReadAsync 40", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736428743910, "dur": 27, "ph": "X", "name": "ReadAsync 285", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736428743939, "dur": 22, "ph": "X", "name": "ReadAsync 520", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736428743962, "dur": 23, "ph": "X", "name": "ReadAsync 183", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736428743987, "dur": 22, "ph": "X", "name": "ReadAsync 324", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736428744010, "dur": 17, "ph": "X", "name": "ReadAsync 136", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736428744028, "dur": 17, "ph": "X", "name": "ReadAsync 101", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736428744047, "dur": 26, "ph": "X", "name": "ReadAsync 225", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736428744074, "dur": 30, "ph": "X", "name": "ReadAsync 184", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736428744106, "dur": 31, "ph": "X", "name": "ReadAsync 423", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736428744138, "dur": 26, "ph": "X", "name": "ReadAsync 446", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736428744166, "dur": 21, "ph": "X", "name": "ReadAsync 348", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736428744188, "dur": 16, "ph": "X", "name": "ReadAsync 379", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736428744206, "dur": 20, "ph": "X", "name": "ReadAsync 311", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736428744227, "dur": 19, "ph": "X", "name": "ReadAsync 175", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736428744248, "dur": 17, "ph": "X", "name": "ReadAsync 229", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736428744266, "dur": 18, "ph": "X", "name": "ReadAsync 8", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736428744286, "dur": 25, "ph": "X", "name": "ReadAsync 176", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736428744312, "dur": 21, "ph": "X", "name": "ReadAsync 223", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736428744334, "dur": 19, "ph": "X", "name": "ReadAsync 376", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736428744355, "dur": 23, "ph": "X", "name": "ReadAsync 238", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736428744379, "dur": 29, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736428744410, "dur": 1, "ph": "X", "name": "ProcessMessages 233", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736428744412, "dur": 39, "ph": "X", "name": "ReadAsync 233", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736428744452, "dur": 31, "ph": "X", "name": "ReadAsync 461", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736428744487, "dur": 1, "ph": "X", "name": "ProcessMessages 287", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736428744488, "dur": 34, "ph": "X", "name": "ReadAsync 287", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736428744524, "dur": 23, "ph": "X", "name": "ReadAsync 67", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736428744548, "dur": 22, "ph": "X", "name": "ReadAsync 428", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736428744572, "dur": 26, "ph": "X", "name": "ReadAsync 324", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736428744601, "dur": 1, "ph": "X", "name": "ProcessMessages 421", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736428744602, "dur": 29, "ph": "X", "name": "ReadAsync 421", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736428744633, "dur": 24, "ph": "X", "name": "ReadAsync 378", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736428744659, "dur": 22, "ph": "X", "name": "ReadAsync 316", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736428744683, "dur": 32, "ph": "X", "name": "ReadAsync 506", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736428744717, "dur": 22, "ph": "X", "name": "ReadAsync 326", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736428744740, "dur": 29, "ph": "X", "name": "ReadAsync 324", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736428744773, "dur": 39, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736428744815, "dur": 1, "ph": "X", "name": "ProcessMessages 436", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736428744816, "dur": 28, "ph": "X", "name": "ReadAsync 436", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736428744847, "dur": 28, "ph": "X", "name": "ReadAsync 412", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736428744876, "dur": 23, "ph": "X", "name": "ReadAsync 240", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736428744901, "dur": 20, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736428744924, "dur": 25, "ph": "X", "name": "ReadAsync 230", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736428744951, "dur": 38, "ph": "X", "name": "ReadAsync 284", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736428744992, "dur": 1, "ph": "X", "name": "ProcessMessages 552", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736428744994, "dur": 41, "ph": "X", "name": "ReadAsync 552", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736428745040, "dur": 39, "ph": "X", "name": "ReadAsync 164", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736428745082, "dur": 1, "ph": "X", "name": "ProcessMessages 165", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736428745084, "dur": 33, "ph": "X", "name": "ReadAsync 165", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736428745119, "dur": 38, "ph": "X", "name": "ReadAsync 597", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736428745160, "dur": 1, "ph": "X", "name": "ProcessMessages 240", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736428745161, "dur": 36, "ph": "X", "name": "ReadAsync 240", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736428745200, "dur": 25, "ph": "X", "name": "ReadAsync 242", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736428745226, "dur": 22, "ph": "X", "name": "ReadAsync 362", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736428745250, "dur": 18, "ph": "X", "name": "ReadAsync 310", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736428745270, "dur": 39, "ph": "X", "name": "ReadAsync 143", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736428745311, "dur": 22, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736428745335, "dur": 22, "ph": "X", "name": "ReadAsync 325", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736428745359, "dur": 22, "ph": "X", "name": "ReadAsync 278", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736428745383, "dur": 25, "ph": "X", "name": "ReadAsync 399", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736428745409, "dur": 2, "ph": "X", "name": "ProcessMessages 68", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736428745411, "dur": 22, "ph": "X", "name": "ReadAsync 68", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736428745434, "dur": 22, "ph": "X", "name": "ReadAsync 189", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736428745459, "dur": 23, "ph": "X", "name": "ReadAsync 304", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736428745483, "dur": 21, "ph": "X", "name": "ReadAsync 397", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736428745505, "dur": 24, "ph": "X", "name": "ReadAsync 73", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736428745531, "dur": 20, "ph": "X", "name": "ReadAsync 446", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736428745552, "dur": 22, "ph": "X", "name": "ReadAsync 93", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736428745576, "dur": 23, "ph": "X", "name": "ReadAsync 277", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736428745600, "dur": 22, "ph": "X", "name": "ReadAsync 375", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736428745624, "dur": 21, "ph": "X", "name": "ReadAsync 320", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736428745646, "dur": 17, "ph": "X", "name": "ReadAsync 261", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736428745664, "dur": 30, "ph": "X", "name": "ReadAsync 143", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736428745695, "dur": 23, "ph": "X", "name": "ReadAsync 98", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736428745720, "dur": 23, "ph": "X", "name": "ReadAsync 434", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736428745744, "dur": 22, "ph": "X", "name": "ReadAsync 440", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736428745767, "dur": 22, "ph": "X", "name": "ReadAsync 383", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736428745790, "dur": 28, "ph": "X", "name": "ReadAsync 450", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736428745820, "dur": 26, "ph": "X", "name": "ReadAsync 317", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736428745848, "dur": 18, "ph": "X", "name": "ReadAsync 522", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736428745868, "dur": 49, "ph": "X", "name": "ReadAsync 345", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736428745919, "dur": 23, "ph": "X", "name": "ReadAsync 240", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736428745946, "dur": 43, "ph": "X", "name": "ReadAsync 266", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736428745990, "dur": 39, "ph": "X", "name": "ReadAsync 534", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736428746032, "dur": 1, "ph": "X", "name": "ProcessMessages 353", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736428746034, "dur": 40, "ph": "X", "name": "ReadAsync 353", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736428746076, "dur": 1, "ph": "X", "name": "ProcessMessages 745", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736428746078, "dur": 41, "ph": "X", "name": "ReadAsync 745", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736428746121, "dur": 22, "ph": "X", "name": "ReadAsync 353", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736428746144, "dur": 20, "ph": "X", "name": "ReadAsync 413", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736428746166, "dur": 20, "ph": "X", "name": "ReadAsync 191", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736428746189, "dur": 29, "ph": "X", "name": "ReadAsync 237", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736428746220, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736428746222, "dur": 41, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736428746265, "dur": 31, "ph": "X", "name": "ReadAsync 474", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736428746298, "dur": 22, "ph": "X", "name": "ReadAsync 418", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736428746322, "dur": 20, "ph": "X", "name": "ReadAsync 256", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736428746343, "dur": 19, "ph": "X", "name": "ReadAsync 63", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736428746364, "dur": 22, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736428746387, "dur": 22, "ph": "X", "name": "ReadAsync 302", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736428746411, "dur": 27, "ph": "X", "name": "ReadAsync 388", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736428746439, "dur": 20, "ph": "X", "name": "ReadAsync 343", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736428746460, "dur": 20, "ph": "X", "name": "ReadAsync 144", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736428746482, "dur": 22, "ph": "X", "name": "ReadAsync 188", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736428746505, "dur": 21, "ph": "X", "name": "ReadAsync 301", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736428746528, "dur": 23, "ph": "X", "name": "ReadAsync 373", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736428746553, "dur": 18, "ph": "X", "name": "ReadAsync 323", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736428746572, "dur": 21, "ph": "X", "name": "ReadAsync 53", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736428746595, "dur": 21, "ph": "X", "name": "ReadAsync 300", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736428746616, "dur": 1, "ph": "X", "name": "ProcessMessages 277", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736428746618, "dur": 22, "ph": "X", "name": "ReadAsync 277", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736428746641, "dur": 21, "ph": "X", "name": "ReadAsync 393", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736428746664, "dur": 21, "ph": "X", "name": "ReadAsync 225", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736428746686, "dur": 30, "ph": "X", "name": "ReadAsync 188", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736428746719, "dur": 40, "ph": "X", "name": "ReadAsync 265", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736428746762, "dur": 3, "ph": "X", "name": "ProcessMessages 363", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736428746765, "dur": 39, "ph": "X", "name": "ReadAsync 363", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736428746807, "dur": 1, "ph": "X", "name": "ProcessMessages 298", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736428746809, "dur": 46, "ph": "X", "name": "ReadAsync 298", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736428746858, "dur": 1, "ph": "X", "name": "ProcessMessages 355", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736428746860, "dur": 39, "ph": "X", "name": "ReadAsync 355", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736428746902, "dur": 1, "ph": "X", "name": "ProcessMessages 759", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736428746904, "dur": 43, "ph": "X", "name": "ReadAsync 759", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736428746950, "dur": 1, "ph": "X", "name": "ProcessMessages 492", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736428746952, "dur": 94, "ph": "X", "name": "ReadAsync 492", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736428747049, "dur": 1, "ph": "X", "name": "ProcessMessages 633", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736428747051, "dur": 47, "ph": "X", "name": "ReadAsync 633", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736428747101, "dur": 1, "ph": "X", "name": "ProcessMessages 614", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736428747103, "dur": 45, "ph": "X", "name": "ReadAsync 614", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736428747151, "dur": 1, "ph": "X", "name": "ProcessMessages 791", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736428747153, "dur": 38, "ph": "X", "name": "ReadAsync 791", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736428747194, "dur": 1, "ph": "X", "name": "ProcessMessages 237", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736428747195, "dur": 25, "ph": "X", "name": "ReadAsync 237", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736428747222, "dur": 24, "ph": "X", "name": "ReadAsync 141", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736428747249, "dur": 1, "ph": "X", "name": "ProcessMessages 287", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736428747250, "dur": 38, "ph": "X", "name": "ReadAsync 287", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736428747291, "dur": 1, "ph": "X", "name": "ProcessMessages 533", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736428747293, "dur": 29, "ph": "X", "name": "ReadAsync 533", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736428747324, "dur": 41, "ph": "X", "name": "ReadAsync 300", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736428747368, "dur": 41, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736428747412, "dur": 1, "ph": "X", "name": "ProcessMessages 539", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736428747414, "dur": 31, "ph": "X", "name": "ReadAsync 539", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736428747447, "dur": 22, "ph": "X", "name": "ReadAsync 524", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736428747471, "dur": 17, "ph": "X", "name": "ReadAsync 58", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736428747490, "dur": 23, "ph": "X", "name": "ReadAsync 111", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736428747515, "dur": 22, "ph": "X", "name": "ReadAsync 259", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736428747538, "dur": 19, "ph": "X", "name": "ReadAsync 387", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736428747559, "dur": 22, "ph": "X", "name": "ReadAsync 296", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736428747583, "dur": 22, "ph": "X", "name": "ReadAsync 67", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736428747606, "dur": 26, "ph": "X", "name": "ReadAsync 230", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736428747634, "dur": 21, "ph": "X", "name": "ReadAsync 224", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736428747656, "dur": 21, "ph": "X", "name": "ReadAsync 315", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736428747678, "dur": 22, "ph": "X", "name": "ReadAsync 180", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736428747702, "dur": 22, "ph": "X", "name": "ReadAsync 310", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736428747725, "dur": 21, "ph": "X", "name": "ReadAsync 279", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736428747748, "dur": 27, "ph": "X", "name": "ReadAsync 246", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736428747777, "dur": 21, "ph": "X", "name": "ReadAsync 362", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736428747799, "dur": 20, "ph": "X", "name": "ReadAsync 242", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736428747821, "dur": 19, "ph": "X", "name": "ReadAsync 143", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736428747841, "dur": 22, "ph": "X", "name": "ReadAsync 149", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736428747865, "dur": 22, "ph": "X", "name": "ReadAsync 233", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736428747888, "dur": 25, "ph": "X", "name": "ReadAsync 391", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736428747914, "dur": 30, "ph": "X", "name": "ReadAsync 320", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736428747946, "dur": 26, "ph": "X", "name": "ReadAsync 532", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736428747974, "dur": 40, "ph": "X", "name": "ReadAsync 320", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736428748016, "dur": 1, "ph": "X", "name": "ProcessMessages 313", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736428748018, "dur": 41, "ph": "X", "name": "ReadAsync 313", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736428748061, "dur": 1, "ph": "X", "name": "ProcessMessages 529", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736428748063, "dur": 29, "ph": "X", "name": "ReadAsync 529", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736428748094, "dur": 35, "ph": "X", "name": "ReadAsync 384", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736428748133, "dur": 1, "ph": "X", "name": "ProcessMessages 318", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736428748135, "dur": 41, "ph": "X", "name": "ReadAsync 318", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736428748177, "dur": 22, "ph": "X", "name": "ReadAsync 576", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736428748201, "dur": 23, "ph": "X", "name": "ReadAsync 225", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736428748226, "dur": 21, "ph": "X", "name": "ReadAsync 264", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736428748249, "dur": 19, "ph": "X", "name": "ReadAsync 400", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736428748270, "dur": 20, "ph": "X", "name": "ReadAsync 106", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736428748291, "dur": 28, "ph": "X", "name": "ReadAsync 200", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736428748320, "dur": 20, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736428748341, "dur": 20, "ph": "X", "name": "ReadAsync 229", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736428748363, "dur": 21, "ph": "X", "name": "ReadAsync 284", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736428748386, "dur": 19, "ph": "X", "name": "ReadAsync 448", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736428748406, "dur": 29, "ph": "X", "name": "ReadAsync 145", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736428748437, "dur": 20, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736428748458, "dur": 19, "ph": "X", "name": "ReadAsync 284", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736428748479, "dur": 20, "ph": "X", "name": "ReadAsync 333", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736428748500, "dur": 19, "ph": "X", "name": "ReadAsync 417", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736428748521, "dur": 29, "ph": "X", "name": "ReadAsync 197", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736428748552, "dur": 20, "ph": "X", "name": "ReadAsync 213", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736428748574, "dur": 20, "ph": "X", "name": "ReadAsync 364", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736428748595, "dur": 20, "ph": "X", "name": "ReadAsync 384", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736428748616, "dur": 17, "ph": "X", "name": "ReadAsync 237", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736428748634, "dur": 21, "ph": "X", "name": "ReadAsync 112", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736428748656, "dur": 21, "ph": "X", "name": "ReadAsync 274", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736428748679, "dur": 20, "ph": "X", "name": "ReadAsync 164", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736428748700, "dur": 20, "ph": "X", "name": "ReadAsync 390", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736428748722, "dur": 21, "ph": "X", "name": "ReadAsync 383", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736428748744, "dur": 20, "ph": "X", "name": "ReadAsync 390", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736428748766, "dur": 19, "ph": "X", "name": "ReadAsync 374", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736428748787, "dur": 20, "ph": "X", "name": "ReadAsync 181", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736428748809, "dur": 19, "ph": "X", "name": "ReadAsync 356", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736428748829, "dur": 20, "ph": "X", "name": "ReadAsync 316", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736428748850, "dur": 20, "ph": "X", "name": "ReadAsync 185", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736428748873, "dur": 26, "ph": "X", "name": "ReadAsync 52", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736428748900, "dur": 20, "ph": "X", "name": "ReadAsync 290", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736428748922, "dur": 20, "ph": "X", "name": "ReadAsync 397", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736428748943, "dur": 33, "ph": "X", "name": "ReadAsync 366", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736428748979, "dur": 1, "ph": "X", "name": "ProcessMessages 387", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736428748980, "dur": 49, "ph": "X", "name": "ReadAsync 387", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736428749033, "dur": 1, "ph": "X", "name": "ProcessMessages 724", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736428749034, "dur": 29, "ph": "X", "name": "ReadAsync 724", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736428749065, "dur": 36, "ph": "X", "name": "ReadAsync 733", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736428749105, "dur": 43, "ph": "X", "name": "ReadAsync 240", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736428749150, "dur": 26, "ph": "X", "name": "ReadAsync 284", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736428749179, "dur": 23, "ph": "X", "name": "ReadAsync 706", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736428749204, "dur": 24, "ph": "X", "name": "ReadAsync 477", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736428749230, "dur": 1, "ph": "X", "name": "ProcessMessages 432", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736428749232, "dur": 29, "ph": "X", "name": "ReadAsync 432", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736428749263, "dur": 24, "ph": "X", "name": "ReadAsync 515", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736428749290, "dur": 23, "ph": "X", "name": "ReadAsync 457", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736428749314, "dur": 17, "ph": "X", "name": "ReadAsync 270", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736428749333, "dur": 20, "ph": "X", "name": "ReadAsync 61", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736428749355, "dur": 20, "ph": "X", "name": "ReadAsync 261", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736428749376, "dur": 20, "ph": "X", "name": "ReadAsync 241", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736428749398, "dur": 22, "ph": "X", "name": "ReadAsync 345", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736428749421, "dur": 18, "ph": "X", "name": "ReadAsync 323", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736428749441, "dur": 25, "ph": "X", "name": "ReadAsync 261", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736428749467, "dur": 20, "ph": "X", "name": "ReadAsync 241", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736428749489, "dur": 20, "ph": "X", "name": "ReadAsync 356", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736428749510, "dur": 22, "ph": "X", "name": "ReadAsync 291", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736428749534, "dur": 20, "ph": "X", "name": "ReadAsync 248", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736428749556, "dur": 27, "ph": "X", "name": "ReadAsync 144", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736428749584, "dur": 20, "ph": "X", "name": "ReadAsync 100", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736428749605, "dur": 20, "ph": "X", "name": "ReadAsync 235", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736428749626, "dur": 27, "ph": "X", "name": "ReadAsync 304", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736428749655, "dur": 19, "ph": "X", "name": "ReadAsync 419", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736428749675, "dur": 22, "ph": "X", "name": "ReadAsync 62", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736428749700, "dur": 21, "ph": "X", "name": "ReadAsync 287", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736428749722, "dur": 44, "ph": "X", "name": "ReadAsync 389", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736428749767, "dur": 21, "ph": "X", "name": "ReadAsync 249", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736428749789, "dur": 20, "ph": "X", "name": "ReadAsync 371", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736428749810, "dur": 34, "ph": "X", "name": "ReadAsync 337", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736428749845, "dur": 20, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736428749867, "dur": 20, "ph": "X", "name": "ReadAsync 317", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736428749889, "dur": 20, "ph": "X", "name": "ReadAsync 374", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736428749911, "dur": 23, "ph": "X", "name": "ReadAsync 345", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736428749935, "dur": 24, "ph": "X", "name": "ReadAsync 187", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736428749963, "dur": 48, "ph": "X", "name": "ReadAsync 237", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736428750015, "dur": 44, "ph": "X", "name": "ReadAsync 380", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736428750062, "dur": 1, "ph": "X", "name": "ProcessMessages 827", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736428750063, "dur": 29, "ph": "X", "name": "ReadAsync 827", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736428750094, "dur": 34, "ph": "X", "name": "ReadAsync 608", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736428750131, "dur": 20, "ph": "X", "name": "ReadAsync 290", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736428750152, "dur": 25, "ph": "X", "name": "ReadAsync 561", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736428750182, "dur": 34, "ph": "X", "name": "ReadAsync 71", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736428750219, "dur": 1, "ph": "X", "name": "ProcessMessages 237", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736428750221, "dur": 30, "ph": "X", "name": "ReadAsync 237", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736428750252, "dur": 22, "ph": "X", "name": "ReadAsync 469", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736428750276, "dur": 22, "ph": "X", "name": "ReadAsync 328", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736428750300, "dur": 21, "ph": "X", "name": "ReadAsync 331", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736428750323, "dur": 22, "ph": "X", "name": "ReadAsync 355", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736428750346, "dur": 39, "ph": "X", "name": "ReadAsync 305", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736428750387, "dur": 25, "ph": "X", "name": "ReadAsync 279", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736428750415, "dur": 21, "ph": "X", "name": "ReadAsync 264", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736428750437, "dur": 19, "ph": "X", "name": "ReadAsync 240", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736428750458, "dur": 21, "ph": "X", "name": "ReadAsync 67", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736428750483, "dur": 31, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736428750517, "dur": 27, "ph": "X", "name": "ReadAsync 370", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736428750545, "dur": 24, "ph": "X", "name": "ReadAsync 297", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736428750571, "dur": 21, "ph": "X", "name": "ReadAsync 313", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736428750594, "dur": 27, "ph": "X", "name": "ReadAsync 354", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736428750623, "dur": 23, "ph": "X", "name": "ReadAsync 84", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736428750647, "dur": 22, "ph": "X", "name": "ReadAsync 454", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736428750671, "dur": 21, "ph": "X", "name": "ReadAsync 343", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736428750693, "dur": 18, "ph": "X", "name": "ReadAsync 306", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736428750712, "dur": 16, "ph": "X", "name": "ReadAsync 185", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736428750730, "dur": 21, "ph": "X", "name": "ReadAsync 52", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736428750752, "dur": 21, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736428750775, "dur": 116, "ph": "X", "name": "ReadAsync 224", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736428750896, "dur": 28, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736428750926, "dur": 35, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736428750964, "dur": 44, "ph": "X", "name": "ReadAsync 76", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736428751011, "dur": 1, "ph": "X", "name": "ProcessMessages 68", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736428751013, "dur": 49, "ph": "X", "name": "ReadAsync 68", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736428751064, "dur": 1, "ph": "X", "name": "ProcessMessages 112", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736428751065, "dur": 34, "ph": "X", "name": "ReadAsync 112", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736428751103, "dur": 1, "ph": "X", "name": "ProcessMessages 124", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736428751105, "dur": 33, "ph": "X", "name": "ReadAsync 124", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736428751139, "dur": 1, "ph": "X", "name": "ProcessMessages 164", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736428751140, "dur": 34, "ph": "X", "name": "ReadAsync 164", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736428751178, "dur": 1, "ph": "X", "name": "ProcessMessages 140", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736428751180, "dur": 42, "ph": "X", "name": "ReadAsync 140", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736428751225, "dur": 1, "ph": "X", "name": "ProcessMessages 208", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736428751227, "dur": 45, "ph": "X", "name": "ReadAsync 208", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736428751276, "dur": 1, "ph": "X", "name": "ProcessMessages 192", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736428751278, "dur": 36, "ph": "X", "name": "ReadAsync 192", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736428751318, "dur": 1, "ph": "X", "name": "ProcessMessages 116", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736428751320, "dur": 33, "ph": "X", "name": "ReadAsync 116", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736428751355, "dur": 23, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736428751379, "dur": 1, "ph": "X", "name": "ProcessMessages 44", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736428751381, "dur": 28, "ph": "X", "name": "ReadAsync 44", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736428751412, "dur": 29, "ph": "X", "name": "ReadAsync 100", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736428751444, "dur": 26, "ph": "X", "name": "ReadAsync 160", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736428751472, "dur": 1, "ph": "X", "name": "ProcessMessages 140", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736428751474, "dur": 30, "ph": "X", "name": "ReadAsync 140", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736428751505, "dur": 1, "ph": "X", "name": "ProcessMessages 144", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736428751507, "dur": 27, "ph": "X", "name": "ReadAsync 144", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736428751536, "dur": 1, "ph": "X", "name": "ProcessMessages 132", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736428751538, "dur": 30, "ph": "X", "name": "ReadAsync 132", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736428751572, "dur": 1, "ph": "X", "name": "ProcessMessages 144", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736428751574, "dur": 28, "ph": "X", "name": "ReadAsync 144", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736428751605, "dur": 1, "ph": "X", "name": "ProcessMessages 172", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736428751607, "dur": 35, "ph": "X", "name": "ReadAsync 172", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736428751645, "dur": 1, "ph": "X", "name": "ProcessMessages 176", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736428751647, "dur": 35, "ph": "X", "name": "ReadAsync 176", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736428751685, "dur": 30, "ph": "X", "name": "ReadAsync 148", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736428751717, "dur": 26, "ph": "X", "name": "ReadAsync 128", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736428751746, "dur": 29, "ph": "X", "name": "ReadAsync 144", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736428751777, "dur": 1, "ph": "X", "name": "ProcessMessages 92", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736428751780, "dur": 30, "ph": "X", "name": "ReadAsync 92", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736428751812, "dur": 1, "ph": "X", "name": "ProcessMessages 160", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736428751814, "dur": 30, "ph": "X", "name": "ReadAsync 160", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736428751846, "dur": 29, "ph": "X", "name": "ReadAsync 96", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736428751876, "dur": 1, "ph": "X", "name": "ProcessMessages 148", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736428751877, "dur": 23, "ph": "X", "name": "ReadAsync 148", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736428751902, "dur": 1, "ph": "X", "name": "ProcessMessages 144", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736428751904, "dur": 29, "ph": "X", "name": "ReadAsync 144", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736428751936, "dur": 24, "ph": "X", "name": "ReadAsync 112", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736428751962, "dur": 1, "ph": "X", "name": "ProcessMessages 172", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736428751964, "dur": 35, "ph": "X", "name": "ReadAsync 172", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736428752002, "dur": 30, "ph": "X", "name": "ReadAsync 112", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736428752033, "dur": 1, "ph": "X", "name": "ProcessMessages 160", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736428752035, "dur": 22, "ph": "X", "name": "ReadAsync 160", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736428752060, "dur": 32, "ph": "X", "name": "ReadAsync 128", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736428752095, "dur": 1, "ph": "X", "name": "ProcessMessages 100", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736428752097, "dur": 47, "ph": "X", "name": "ReadAsync 100", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736428752146, "dur": 1, "ph": "X", "name": "ProcessMessages 160", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736428752148, "dur": 31, "ph": "X", "name": "ReadAsync 160", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736428752182, "dur": 1, "ph": "X", "name": "ProcessMessages 224", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736428752184, "dur": 39, "ph": "X", "name": "ReadAsync 224", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736428752225, "dur": 1, "ph": "X", "name": "ProcessMessages 192", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736428752227, "dur": 26, "ph": "X", "name": "ReadAsync 192", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736428752256, "dur": 23, "ph": "X", "name": "ReadAsync 172", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736428752281, "dur": 23, "ph": "X", "name": "ReadAsync 116", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736428752306, "dur": 25, "ph": "X", "name": "ReadAsync 108", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736428752334, "dur": 22, "ph": "X", "name": "ReadAsync 128", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736428752358, "dur": 23, "ph": "X", "name": "ReadAsync 68", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736428752384, "dur": 23, "ph": "X", "name": "ReadAsync 96", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736428752409, "dur": 26, "ph": "X", "name": "ReadAsync 112", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736428752437, "dur": 1, "ph": "X", "name": "ProcessMessages 64", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736428752439, "dur": 35, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736428752476, "dur": 28, "ph": "X", "name": "ReadAsync 124", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736428752506, "dur": 23, "ph": "X", "name": "ReadAsync 144", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736428752533, "dur": 32, "ph": "X", "name": "ReadAsync 96", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736428752568, "dur": 24, "ph": "X", "name": "ReadAsync 116", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736428752594, "dur": 108, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736428752707, "dur": 36, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736428752746, "dur": 29, "ph": "X", "name": "ReadAsync 52", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736428752780, "dur": 35, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736428752820, "dur": 28, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736428752853, "dur": 34, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736428752891, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736428752893, "dur": 27, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736428752923, "dur": 25, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736428752952, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736428752953, "dur": 76, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736428753034, "dur": 728, "ph": "X", "name": "ProcessMessages 52", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736428753764, "dur": 380, "ph": "X", "name": "ReadAsync 52", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736428754149, "dur": 28, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736428754179, "dur": 2329, "ph": "X", "name": "ProcessMessages 34", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736428756511, "dur": 45, "ph": "X", "name": "ReadAsync 34", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736428756559, "dur": 1, "ph": "X", "name": "ProcessMessages 112", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736428756561, "dur": 2006, "ph": "X", "name": "ReadAsync 112", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736428758574, "dur": 63, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736428758639, "dur": 32, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736428758676, "dur": 29, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736428758709, "dur": 275, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736428758990, "dur": 41, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736428759032, "dur": 70, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736428759107, "dur": 41, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736428759152, "dur": 31, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736428759188, "dur": 34, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736428759227, "dur": 35, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736428759264, "dur": 30, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736428759297, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736428759299, "dur": 128, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736428759430, "dur": 1, "ph": "X", "name": "ProcessMessages 64", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736428759432, "dur": 38, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736428759474, "dur": 31, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736428759509, "dur": 41, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736428759551, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736428759553, "dur": 21, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736428759576, "dur": 24, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736428759603, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736428759606, "dur": 47, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736428759655, "dur": 20, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736428759677, "dur": 21, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736428759701, "dur": 57, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736428759761, "dur": 41, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736428759806, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736428759808, "dur": 29, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736428759839, "dur": 113, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736428759954, "dur": 30, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736428759986, "dur": 117, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736428760108, "dur": 36, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736428760147, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736428760149, "dur": 42, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736428760194, "dur": 32, "ph": "X", "name": "ReadAsync 52", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736428760228, "dur": 1, "ph": "X", "name": "ProcessMessages 80", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736428760230, "dur": 27, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736428760259, "dur": 42, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736428760303, "dur": 22, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736428760327, "dur": 21, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736428760350, "dur": 26, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736428760379, "dur": 28, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736428760409, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736428760411, "dur": 35, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736428760449, "dur": 34, "ph": "X", "name": "ReadAsync 124", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736428760485, "dur": 1, "ph": "X", "name": "ProcessMessages 84", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736428760487, "dur": 31, "ph": "X", "name": "ReadAsync 84", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736428760522, "dur": 22, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736428760547, "dur": 1, "ph": "X", "name": "ProcessMessages 64", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736428760549, "dur": 32, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736428760583, "dur": 49, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736428760635, "dur": 25, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736428760661, "dur": 34, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736428760697, "dur": 25, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736428760724, "dur": 25, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736428760753, "dur": 27, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736428760783, "dur": 78, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736428760863, "dur": 31, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736428760897, "dur": 1, "ph": "X", "name": "ProcessMessages 36", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736428760898, "dur": 33, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736428760935, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736428760937, "dur": 24, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736428760963, "dur": 28, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736428760995, "dur": 48, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736428761047, "dur": 3, "ph": "X", "name": "ProcessMessages 56", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736428761051, "dur": 37, "ph": "X", "name": "ReadAsync 56", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736428761092, "dur": 1, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736428761095, "dur": 36, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736428761133, "dur": 71, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736428761207, "dur": 26, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736428761237, "dur": 28, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736428761267, "dur": 39, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736428761311, "dur": 28, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736428761341, "dur": 56, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736428761398, "dur": 21, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736428761421, "dur": 21, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736428761444, "dur": 40, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736428761487, "dur": 23, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736428761512, "dur": 22, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736428761539, "dur": 169, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736428761711, "dur": 29, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736428761743, "dur": 1, "ph": "X", "name": "ProcessMessages 36", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736428761745, "dur": 93, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736428761842, "dur": 20, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736428761864, "dur": 24, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736428761890, "dur": 30, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736428761925, "dur": 67, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736428761996, "dur": 41, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736428762041, "dur": 1, "ph": "X", "name": "ProcessMessages 36", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736428762042, "dur": 39, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736428762086, "dur": 31, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736428762119, "dur": 79, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736428762204, "dur": 33, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736428762239, "dur": 25, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736428762266, "dur": 30, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736428762299, "dur": 22, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736428762322, "dur": 80, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736428762404, "dur": 15, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736428762421, "dur": 97, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736428762519, "dur": 23, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736428762544, "dur": 93, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736428762639, "dur": 24, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736428762665, "dur": 41, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736428762707, "dur": 23, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736428762732, "dur": 37, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736428762770, "dur": 18, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736428762790, "dur": 47, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736428762839, "dur": 19, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736428762859, "dur": 51, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736428762912, "dur": 21, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736428762934, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736428762936, "dur": 22, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736428762959, "dur": 20, "ph": "X", "name": "ReadAsync 28", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736428762981, "dur": 32, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736428763018, "dur": 27, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736428763047, "dur": 136, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736428763184, "dur": 29, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736428763215, "dur": 36, "ph": "X", "name": "ReadAsync 52", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736428763254, "dur": 31, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736428763289, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736428763290, "dur": 26, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736428763318, "dur": 110, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736428763429, "dur": 17, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736428763447, "dur": 29, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736428763480, "dur": 1142, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736428764627, "dur": 31, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736428764660, "dur": 2, "ph": "X", "name": "ProcessMessages 8", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736428764663, "dur": 297449, "ph": "X", "name": "ReadAsync 8", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736429062122, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736429062126, "dur": 34, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736429062161, "dur": 27, "ph": "X", "name": "ProcessMessages 192", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736429062189, "dur": 3624, "ph": "X", "name": "ReadAsync 192", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736429065822, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736429065825, "dur": 48, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736429065878, "dur": 1, "ph": "X", "name": "ProcessMessages 36", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736429065880, "dur": 32, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736429065917, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736429065918, "dur": 125, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736429066048, "dur": 37, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736429066089, "dur": 41, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736429066133, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736429066135, "dur": 32, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736429066169, "dur": 24, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736429066196, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736429066198, "dur": 66, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736429066266, "dur": 22, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736429066291, "dur": 66, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736429066360, "dur": 20, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736429066383, "dur": 332, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736429066717, "dur": 30, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736429066751, "dur": 144, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736429066898, "dur": 33, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736429066936, "dur": 450, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736429067391, "dur": 36, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736429067430, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736429067432, "dur": 35, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736429067469, "dur": 23, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736429067497, "dur": 27, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736429067527, "dur": 35, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736429067565, "dur": 28, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736429067596, "dur": 143, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736429067742, "dur": 21, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736429067767, "dur": 34, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736429067804, "dur": 23, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736429067829, "dur": 21, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736429067852, "dur": 450, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736429068305, "dur": 22, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736429068330, "dur": 85, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736429068417, "dur": 21, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736429068441, "dur": 363, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736429068807, "dur": 32, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736429068840, "dur": 2, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736429068843, "dur": 22, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736429068867, "dur": 86, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736429068955, "dur": 20, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736429068977, "dur": 24, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736429069003, "dur": 29, "ph": "X", "name": "ReadAsync 44", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736429069035, "dur": 45, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736429069083, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736429069084, "dur": 28, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736429069115, "dur": 22, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736429069140, "dur": 75, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736429069217, "dur": 20, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736429069240, "dur": 332, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736429069574, "dur": 20, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736429069597, "dur": 20, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736429069619, "dur": 21, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736429069642, "dur": 35, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736429069680, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736429069684, "dur": 28, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736429069714, "dur": 30, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736429069745, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736429069747, "dur": 20, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736429069769, "dur": 20, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736429069791, "dur": 48, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736429069841, "dur": 21, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736429069864, "dur": 29, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736429069897, "dur": 23, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736429069922, "dur": 20, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736429069946, "dur": 20, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736429069969, "dur": 30, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736429070002, "dur": 36, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736429070043, "dur": 30, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736429070077, "dur": 42, "ph": "X", "name": "ReadAsync 76", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736429070123, "dur": 1, "ph": "X", "name": "ProcessMessages 36", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736429070125, "dur": 32, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736429070159, "dur": 24, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736429070185, "dur": 25, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736429070213, "dur": 22, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736429070238, "dur": 22, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736429070262, "dur": 25, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736429070300, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736429070302, "dur": 39, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736429070346, "dur": 40, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736429070389, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736429070391, "dur": 39, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736429070434, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736429070435, "dur": 35, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736429070473, "dur": 33, "ph": "X", "name": "ReadAsync 108", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736429070510, "dur": 1, "ph": "X", "name": "ProcessMessages 84", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736429070512, "dur": 28, "ph": "X", "name": "ReadAsync 84", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736429070542, "dur": 23, "ph": "X", "name": "ReadAsync 60", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736429070568, "dur": 23, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736429070593, "dur": 26, "ph": "X", "name": "ReadAsync 44", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736429070621, "dur": 23, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736429070646, "dur": 26, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736429070676, "dur": 35, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736429070714, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736429070716, "dur": 31, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736429070749, "dur": 22, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736429070776, "dur": 23, "ph": "X", "name": "ReadAsync 108", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736429070801, "dur": 22, "ph": "X", "name": "ReadAsync 68", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736429070825, "dur": 100, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736429070930, "dur": 41, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736429070975, "dur": 28, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736429071005, "dur": 24, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736429071032, "dur": 23, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736429071057, "dur": 23, "ph": "X", "name": "ReadAsync 52", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736429071082, "dur": 47, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736429071132, "dur": 1, "ph": "X", "name": "ProcessMessages 92", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736429071134, "dur": 43, "ph": "X", "name": "ReadAsync 92", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736429071180, "dur": 1, "ph": "X", "name": "ProcessMessages 100", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736429071182, "dur": 53, "ph": "X", "name": "ReadAsync 100", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736429071334, "dur": 38, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736429071375, "dur": 1, "ph": "X", "name": "ProcessMessages 116", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736429071377, "dur": 34587, "ph": "X", "name": "ReadAsync 116", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736429105975, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736429105979, "dur": 50, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736429106030, "dur": 3981, "ph": "X", "name": "ProcessMessages 979", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736429110016, "dur": 4473, "ph": "X", "name": "ReadAsync 979", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736429114496, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736429114498, "dur": 32, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736429114532, "dur": 2, "ph": "X", "name": "ProcessMessages 8", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736429114535, "dur": 75251, "ph": "X", "name": "ReadAsync 8", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736429189798, "dur": 3, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736429189802, "dur": 31, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736429189837, "dur": 3, "ph": "X", "name": "ProcessMessages 8", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736429189841, "dur": 253797, "ph": "X", "name": "ReadAsync 8", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736429443648, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736429443653, "dur": 32, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736429443686, "dur": 313, "ph": "X", "name": "ProcessMessages 353", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736429444001, "dur": 6812, "ph": "X", "name": "ReadAsync 353", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736429450823, "dur": 6, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736429450831, "dur": 38, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736429450870, "dur": 3, "ph": "X", "name": "ProcessMessages 8", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736429450873, "dur": 19425, "ph": "X", "name": "ReadAsync 8", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736429470311, "dur": 3, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736429470315, "dur": 72, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736429470392, "dur": 30, "ph": "X", "name": "ProcessMessages 416", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736429470423, "dur": 5517, "ph": "X", "name": "ReadAsync 416", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736429475946, "dur": 52, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736429476000, "dur": 1, "ph": "X", "name": "ProcessMessages 8", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736429476002, "dur": 1861, "ph": "X", "name": "ReadAsync 8", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736429477869, "dur": 19, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736429477888, "dur": 18, "ph": "X", "name": "ProcessMessages 34", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736429477908, "dur": 79668, "ph": "X", "name": "ReadAsync 34", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736429557587, "dur": 3, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736429557591, "dur": 52, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736429557645, "dur": 2, "ph": "X", "name": "ProcessMessages 8", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736429557648, "dur": 674, "ph": "X", "name": "ReadAsync 8", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736429558329, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736429558331, "dur": 38, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736429558373, "dur": 25, "ph": "X", "name": "ProcessMessages 34", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736429558400, "dur": 127915, "ph": "X", "name": "ReadAsync 34", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736429686328, "dur": 3, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736429686334, "dur": 78, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736429686416, "dur": 36, "ph": "X", "name": "ProcessMessages 422", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736429686453, "dur": 5279, "ph": "X", "name": "ReadAsync 422", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736429691738, "dur": 56, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736429691799, "dur": 3, "ph": "X", "name": "ProcessMessages 8", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736429691803, "dur": 614, "ph": "X", "name": "ReadAsync 8", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736429692420, "dur": 41, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736429692464, "dur": 23, "ph": "X", "name": "ProcessMessages 34", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736429692488, "dur": 11112, "ph": "X", "name": "ReadAsync 34", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736429703607, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736429703610, "dur": 44, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736429703658, "dur": 2, "ph": "X", "name": "ProcessMessages 8", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736429703661, "dur": 546, "ph": "X", "name": "ReadAsync 8", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736429704213, "dur": 42, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736429704259, "dur": 23, "ph": "X", "name": "ProcessMessages 50", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736429704283, "dur": 471, "ph": "X", "name": "ReadAsync 50", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736429704758, "dur": 46, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736429704808, "dur": 7020, "ph": "X", "name": "ReadAsync 13", "args": {}}, {"pid": 3100, "tid": 24781, "ts": 1754736429719511, "dur": 974, "ph": "X", "name": "ReadEntireBinlogFromIpcAsync", "args": {}}, {"pid": 3100, "tid": 17179869184, "ph": "M", "name": "thread_name", "args": {"name": "WaitForBuildProgramInputDataBeingWrittenAndSendDagReadyMessageAsync"}}, {"pid": 3100, "tid": 17179869184, "ts": 1754736428480874, "dur": 6, "ph": "X", "name": "await writeBuildProgramInputDataTask", "args": {}}, {"pid": 3100, "tid": 17179869184, "ts": 1754736428480881, "dur": 143444, "ph": "X", "name": "WritePipe.WaitConnectionAsync", "args": {}}, {"pid": 3100, "tid": 17179869184, "ts": 1754736428624327, "dur": 43, "ph": "X", "name": "WriteDagReadyMessage", "args": {}}, {"pid": 3100, "tid": 24781, "ts": 1754736429720493, "dur": 3, "ph": "X", "name": "WaitForBuildProgramInputDataBeingWrittenAndSendDagReadyMessageAsync", "args": {}}, {"pid": 3100, "tid": 1, "ph": "M", "name": "thread_name", "args": {"name": ""}}, {"pid": 3100, "tid": 1, "ts": 1754736427543564, "dur": 3189, "ph": "X", "name": "<Add>b__0", "args": {}}, {"pid": 3100, "tid": 1, "ts": 1754736427546758, "dur": 21104, "ph": "X", "name": "<Add>b__0", "args": {}}, {"pid": 3100, "tid": 1, "ts": 1754736427567871, "dur": 32099, "ph": "X", "name": "WriteJson", "args": {}}, {"pid": 3100, "tid": 24781, "ts": 1754736429720497, "dur": 3, "ph": "X", "name": "", "args": {}}, {"pid": 3100, "tid": 12884901888, "ph": "M", "name": "thread_name", "args": {"name": "ReadEntireBinlogFromIpcAsync"}}, {"pid": 3100, "tid": 12884901888, "ts": 1754736427542455, "dur": 144669, "ph": "X", "name": "WaitForConnectionAsync", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754736427687126, "dur": 31191, "ph": "X", "name": "UpdateFromStreamAsync", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754736427687920, "dur": 851, "ph": "X", "name": "ReadAsync 0", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754736427688778, "dur": 588, "ph": "X", "name": "ProcessMessages 57", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754736427689369, "dur": 92, "ph": "X", "name": "ReadAsync 57", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754736427689467, "dur": 350, "ph": "X", "name": "ProcessMessages 7985", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754736427689818, "dur": 81, "ph": "X", "name": "ReadAsync 7985", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754736427689902, "dur": 2, "ph": "X", "name": "ProcessMessages 6452", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754736427689905, "dur": 38, "ph": "X", "name": "ReadAsync 6452", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754736427689946, "dur": 1, "ph": "X", "name": "ProcessMessages 600", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754736427689947, "dur": 37, "ph": "X", "name": "ReadAsync 600", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754736427689988, "dur": 29, "ph": "X", "name": "ReadAsync 616", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754736427690019, "dur": 23, "ph": "X", "name": "ReadAsync 524", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754736427690044, "dur": 28, "ph": "X", "name": "ReadAsync 340", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754736427690076, "dur": 1, "ph": "X", "name": "ProcessMessages 365", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754736427690077, "dur": 33, "ph": "X", "name": "ReadAsync 365", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754736427690113, "dur": 23, "ph": "X", "name": "ReadAsync 478", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754736427690137, "dur": 25, "ph": "X", "name": "ReadAsync 452", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754736427690164, "dur": 27, "ph": "X", "name": "ReadAsync 360", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754736427690192, "dur": 27, "ph": "X", "name": "ReadAsync 460", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754736427690222, "dur": 1, "ph": "X", "name": "ProcessMessages 228", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754736427690223, "dur": 32, "ph": "X", "name": "ReadAsync 228", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754736427690258, "dur": 25, "ph": "X", "name": "ReadAsync 551", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754736427690284, "dur": 22, "ph": "X", "name": "ReadAsync 581", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754736427690308, "dur": 22, "ph": "X", "name": "ReadAsync 436", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754736427690332, "dur": 23, "ph": "X", "name": "ReadAsync 390", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754736427690357, "dur": 21, "ph": "X", "name": "ReadAsync 506", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754736427690380, "dur": 21, "ph": "X", "name": "ReadAsync 457", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754736427690402, "dur": 22, "ph": "X", "name": "ReadAsync 333", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754736427690426, "dur": 20, "ph": "X", "name": "ReadAsync 412", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754736427690449, "dur": 23, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754736427690474, "dur": 21, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754736427690497, "dur": 23, "ph": "X", "name": "ReadAsync 433", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754736427690521, "dur": 23, "ph": "X", "name": "ReadAsync 467", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754736427690547, "dur": 1, "ph": "X", "name": "ProcessMessages 450", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754736427690549, "dur": 33, "ph": "X", "name": "ReadAsync 450", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754736427690583, "dur": 22, "ph": "X", "name": "ReadAsync 612", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754736427690607, "dur": 24, "ph": "X", "name": "ReadAsync 237", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754736427690632, "dur": 23, "ph": "X", "name": "ReadAsync 380", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754736427690656, "dur": 29, "ph": "X", "name": "ReadAsync 505", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754736427690689, "dur": 1, "ph": "X", "name": "ProcessMessages 252", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754736427690690, "dur": 34, "ph": "X", "name": "ReadAsync 252", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754736427690727, "dur": 22, "ph": "X", "name": "ReadAsync 739", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754736427690750, "dur": 22, "ph": "X", "name": "ReadAsync 359", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754736427690774, "dur": 24, "ph": "X", "name": "ReadAsync 335", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754736427690799, "dur": 25, "ph": "X", "name": "ReadAsync 331", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754736427690826, "dur": 21, "ph": "X", "name": "ReadAsync 460", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754736427690848, "dur": 18, "ph": "X", "name": "ReadAsync 335", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754736427690868, "dur": 21, "ph": "X", "name": "ReadAsync 213", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754736427690891, "dur": 23, "ph": "X", "name": "ReadAsync 244", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754736427690916, "dur": 26, "ph": "X", "name": "ReadAsync 261", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754736427690944, "dur": 23, "ph": "X", "name": "ReadAsync 413", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754736427690969, "dur": 24, "ph": "X", "name": "ReadAsync 463", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754736427690994, "dur": 18, "ph": "X", "name": "ReadAsync 258", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754736427691014, "dur": 23, "ph": "X", "name": "ReadAsync 369", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754736427691039, "dur": 21, "ph": "X", "name": "ReadAsync 351", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754736427691061, "dur": 22, "ph": "X", "name": "ReadAsync 317", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754736427691084, "dur": 23, "ph": "X", "name": "ReadAsync 406", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754736427691109, "dur": 20, "ph": "X", "name": "ReadAsync 456", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754736427691131, "dur": 22, "ph": "X", "name": "ReadAsync 224", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754736427691155, "dur": 22, "ph": "X", "name": "ReadAsync 303", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754736427691178, "dur": 21, "ph": "X", "name": "ReadAsync 356", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754736427691200, "dur": 22, "ph": "X", "name": "ReadAsync 260", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754736427691223, "dur": 21, "ph": "X", "name": "ReadAsync 255", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754736427691246, "dur": 22, "ph": "X", "name": "ReadAsync 262", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754736427691269, "dur": 31, "ph": "X", "name": "ReadAsync 286", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754736427691302, "dur": 23, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754736427691327, "dur": 22, "ph": "X", "name": "ReadAsync 333", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754736427691351, "dur": 24, "ph": "X", "name": "ReadAsync 367", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754736427691378, "dur": 36, "ph": "X", "name": "ReadAsync 400", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754736427691416, "dur": 21, "ph": "X", "name": "ReadAsync 392", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754736427691438, "dur": 22, "ph": "X", "name": "ReadAsync 314", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754736427691462, "dur": 22, "ph": "X", "name": "ReadAsync 346", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754736427691486, "dur": 22, "ph": "X", "name": "ReadAsync 242", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754736427691509, "dur": 23, "ph": "X", "name": "ReadAsync 315", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754736427691533, "dur": 22, "ph": "X", "name": "ReadAsync 323", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754736427691557, "dur": 24, "ph": "X", "name": "ReadAsync 355", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754736427691583, "dur": 23, "ph": "X", "name": "ReadAsync 312", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754736427691607, "dur": 22, "ph": "X", "name": "ReadAsync 260", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754736427691631, "dur": 20, "ph": "X", "name": "ReadAsync 312", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754736427691652, "dur": 53, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754736427691707, "dur": 22, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754736427691731, "dur": 23, "ph": "X", "name": "ReadAsync 343", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754736427691755, "dur": 21, "ph": "X", "name": "ReadAsync 365", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754736427691778, "dur": 22, "ph": "X", "name": "ReadAsync 210", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754736427691801, "dur": 21, "ph": "X", "name": "ReadAsync 295", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754736427691824, "dur": 22, "ph": "X", "name": "ReadAsync 331", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754736427691848, "dur": 20, "ph": "X", "name": "ReadAsync 280", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754736427691870, "dur": 22, "ph": "X", "name": "ReadAsync 61", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754736427691893, "dur": 22, "ph": "X", "name": "ReadAsync 315", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754736427691916, "dur": 23, "ph": "X", "name": "ReadAsync 346", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754736427691940, "dur": 21, "ph": "X", "name": "ReadAsync 275", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754736427691963, "dur": 22, "ph": "X", "name": "ReadAsync 339", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754736427691987, "dur": 22, "ph": "X", "name": "ReadAsync 294", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754736427692010, "dur": 19, "ph": "X", "name": "ReadAsync 303", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754736427692031, "dur": 18, "ph": "X", "name": "ReadAsync 21", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754736427692051, "dur": 21, "ph": "X", "name": "ReadAsync 104", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754736427692072, "dur": 23, "ph": "X", "name": "ReadAsync 149", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754736427692097, "dur": 29, "ph": "X", "name": "ReadAsync 249", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754736427692127, "dur": 29, "ph": "X", "name": "ReadAsync 399", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754736427692158, "dur": 20, "ph": "X", "name": "ReadAsync 582", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754736427692179, "dur": 18, "ph": "X", "name": "ReadAsync 87", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754736427692199, "dur": 21, "ph": "X", "name": "ReadAsync 330", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754736427692221, "dur": 22, "ph": "X", "name": "ReadAsync 342", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754736427692245, "dur": 22, "ph": "X", "name": "ReadAsync 359", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754736427692268, "dur": 27, "ph": "X", "name": "ReadAsync 390", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754736427692297, "dur": 1, "ph": "X", "name": "ProcessMessages 339", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754736427692299, "dur": 34, "ph": "X", "name": "ReadAsync 339", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754736427692335, "dur": 22, "ph": "X", "name": "ReadAsync 420", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754736427692358, "dur": 26, "ph": "X", "name": "ReadAsync 245", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754736427692387, "dur": 24, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754736427692412, "dur": 22, "ph": "X", "name": "ReadAsync 499", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754736427692435, "dur": 22, "ph": "X", "name": "ReadAsync 234", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754736427692459, "dur": 21, "ph": "X", "name": "ReadAsync 357", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754736427692482, "dur": 21, "ph": "X", "name": "ReadAsync 283", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754736427692504, "dur": 20, "ph": "X", "name": "ReadAsync 307", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754736427692526, "dur": 21, "ph": "X", "name": "ReadAsync 271", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754736427692548, "dur": 24, "ph": "X", "name": "ReadAsync 131", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754736427692574, "dur": 21, "ph": "X", "name": "ReadAsync 294", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754736427692597, "dur": 21, "ph": "X", "name": "ReadAsync 410", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754736427692620, "dur": 21, "ph": "X", "name": "ReadAsync 266", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754736427692643, "dur": 21, "ph": "X", "name": "ReadAsync 283", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754736427692665, "dur": 21, "ph": "X", "name": "ReadAsync 226", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754736427692688, "dur": 23, "ph": "X", "name": "ReadAsync 288", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754736427692713, "dur": 23, "ph": "X", "name": "ReadAsync 65", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754736427692737, "dur": 21, "ph": "X", "name": "ReadAsync 324", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754736427692759, "dur": 1, "ph": "X", "name": "ProcessMessages 295", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754736427692761, "dur": 23, "ph": "X", "name": "ReadAsync 295", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754736427692785, "dur": 22, "ph": "X", "name": "ReadAsync 280", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754736427692809, "dur": 23, "ph": "X", "name": "ReadAsync 262", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754736427692833, "dur": 22, "ph": "X", "name": "ReadAsync 355", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754736427692857, "dur": 18, "ph": "X", "name": "ReadAsync 373", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754736427692878, "dur": 19, "ph": "X", "name": "ReadAsync 74", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754736427692898, "dur": 23, "ph": "X", "name": "ReadAsync 226", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754736427692923, "dur": 22, "ph": "X", "name": "ReadAsync 329", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754736427692946, "dur": 26, "ph": "X", "name": "ReadAsync 314", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754736427692974, "dur": 22, "ph": "X", "name": "ReadAsync 253", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754736427692997, "dur": 18, "ph": "X", "name": "ReadAsync 439", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754736427693017, "dur": 22, "ph": "X", "name": "ReadAsync 74", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754736427693040, "dur": 22, "ph": "X", "name": "ReadAsync 262", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754736427693063, "dur": 21, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754736427693086, "dur": 23, "ph": "X", "name": "ReadAsync 312", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754736427693111, "dur": 22, "ph": "X", "name": "ReadAsync 295", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754736427693134, "dur": 21, "ph": "X", "name": "ReadAsync 301", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754736427693156, "dur": 21, "ph": "X", "name": "ReadAsync 182", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754736427693179, "dur": 22, "ph": "X", "name": "ReadAsync 243", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754736427693203, "dur": 22, "ph": "X", "name": "ReadAsync 270", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754736427693227, "dur": 20, "ph": "X", "name": "ReadAsync 274", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754736427693248, "dur": 21, "ph": "X", "name": "ReadAsync 74", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754736427693270, "dur": 21, "ph": "X", "name": "ReadAsync 355", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754736427693293, "dur": 21, "ph": "X", "name": "ReadAsync 362", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754736427693316, "dur": 21, "ph": "X", "name": "ReadAsync 353", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754736427693338, "dur": 21, "ph": "X", "name": "ReadAsync 312", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754736427693361, "dur": 21, "ph": "X", "name": "ReadAsync 307", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754736427693384, "dur": 1, "ph": "X", "name": "ProcessMessages 338", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754736427693385, "dur": 21, "ph": "X", "name": "ReadAsync 338", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754736427693407, "dur": 14, "ph": "X", "name": "ReadAsync 310", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754736427693422, "dur": 18, "ph": "X", "name": "ReadAsync 131", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754736427693442, "dur": 22, "ph": "X", "name": "ReadAsync 220", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754736427693465, "dur": 21, "ph": "X", "name": "ReadAsync 331", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754736427693488, "dur": 21, "ph": "X", "name": "ReadAsync 328", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754736427693510, "dur": 22, "ph": "X", "name": "ReadAsync 268", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754736427693534, "dur": 21, "ph": "X", "name": "ReadAsync 311", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754736427693556, "dur": 14, "ph": "X", "name": "ReadAsync 318", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754736427693572, "dur": 23, "ph": "X", "name": "ReadAsync 254", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754736427693596, "dur": 110, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754736427693708, "dur": 1, "ph": "X", "name": "ProcessMessages 393", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754736427693711, "dur": 67, "ph": "X", "name": "ReadAsync 393", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754736427693783, "dur": 3, "ph": "X", "name": "ProcessMessages 2152", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754736427693787, "dur": 70, "ph": "X", "name": "ReadAsync 2152", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754736427693859, "dur": 1, "ph": "X", "name": "ProcessMessages 822", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754736427693862, "dur": 50, "ph": "X", "name": "ReadAsync 822", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754736427693914, "dur": 1, "ph": "X", "name": "ProcessMessages 958", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754736427693916, "dur": 31, "ph": "X", "name": "ReadAsync 958", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754736427693948, "dur": 28, "ph": "X", "name": "ReadAsync 475", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754736427693979, "dur": 28, "ph": "X", "name": "ReadAsync 346", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754736427694008, "dur": 50, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754736427694061, "dur": 1, "ph": "X", "name": "ProcessMessages 372", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754736427694063, "dur": 46, "ph": "X", "name": "ReadAsync 372", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754736427694113, "dur": 1, "ph": "X", "name": "ProcessMessages 905", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754736427694114, "dur": 38, "ph": "X", "name": "ReadAsync 905", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754736427694155, "dur": 1, "ph": "X", "name": "ProcessMessages 412", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754736427694157, "dur": 30, "ph": "X", "name": "ReadAsync 412", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754736427694188, "dur": 20, "ph": "X", "name": "ReadAsync 534", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754736427694210, "dur": 23, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754736427694236, "dur": 22, "ph": "X", "name": "ReadAsync 241", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754736427694259, "dur": 22, "ph": "X", "name": "ReadAsync 95", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754736427694283, "dur": 21, "ph": "X", "name": "ReadAsync 311", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754736427694305, "dur": 16, "ph": "X", "name": "ReadAsync 313", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754736427694323, "dur": 17, "ph": "X", "name": "ReadAsync 253", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754736427694341, "dur": 24, "ph": "X", "name": "ReadAsync 236", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754736427694370, "dur": 31, "ph": "X", "name": "ReadAsync 237", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754736427694403, "dur": 30, "ph": "X", "name": "ReadAsync 365", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754736427694434, "dur": 24, "ph": "X", "name": "ReadAsync 252", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754736427694461, "dur": 31, "ph": "X", "name": "ReadAsync 437", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754736427694494, "dur": 32, "ph": "X", "name": "ReadAsync 447", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754736427694528, "dur": 1, "ph": "X", "name": "ProcessMessages 429", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754736427694530, "dur": 70, "ph": "X", "name": "ReadAsync 429", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754736427694602, "dur": 37, "ph": "X", "name": "ReadAsync 618", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754736427694642, "dur": 1, "ph": "X", "name": "ProcessMessages 1060", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754736427694644, "dur": 30, "ph": "X", "name": "ReadAsync 1060", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754736427694676, "dur": 27, "ph": "X", "name": "ReadAsync 432", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754736427694705, "dur": 24, "ph": "X", "name": "ReadAsync 169", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754736427694731, "dur": 14, "ph": "X", "name": "ReadAsync 461", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754736427694746, "dur": 127, "ph": "X", "name": "ReadAsync 193", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754736427694875, "dur": 31, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754736427694908, "dur": 1, "ph": "X", "name": "ProcessMessages 339", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754736427694909, "dur": 33, "ph": "X", "name": "ReadAsync 339", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754736427694944, "dur": 24, "ph": "X", "name": "ReadAsync 659", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754736427694971, "dur": 29, "ph": "X", "name": "ReadAsync 383", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754736427695001, "dur": 27, "ph": "X", "name": "ReadAsync 388", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754736427695031, "dur": 1, "ph": "X", "name": "ProcessMessages 380", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754736427695032, "dur": 124, "ph": "X", "name": "ReadAsync 380", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754736427695160, "dur": 42, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754736427695204, "dur": 1, "ph": "X", "name": "ProcessMessages 761", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754736427695205, "dur": 30, "ph": "X", "name": "ReadAsync 761", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754736427695236, "dur": 30, "ph": "X", "name": "ReadAsync 495", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754736427695269, "dur": 1, "ph": "X", "name": "ProcessMessages 416", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754736427695271, "dur": 29, "ph": "X", "name": "ReadAsync 416", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754736427695302, "dur": 28, "ph": "X", "name": "ReadAsync 681", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754736427695333, "dur": 1, "ph": "X", "name": "ProcessMessages 82", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754736427695334, "dur": 33, "ph": "X", "name": "ReadAsync 82", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754736427695370, "dur": 26, "ph": "X", "name": "ReadAsync 434", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754736427695398, "dur": 21, "ph": "X", "name": "ReadAsync 425", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754736427695420, "dur": 24, "ph": "X", "name": "ReadAsync 255", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754736427695446, "dur": 20, "ph": "X", "name": "ReadAsync 251", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754736427695467, "dur": 25, "ph": "X", "name": "ReadAsync 277", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754736427695493, "dur": 22, "ph": "X", "name": "ReadAsync 323", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754736427695516, "dur": 21, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754736427695538, "dur": 22, "ph": "X", "name": "ReadAsync 252", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754736427695562, "dur": 23, "ph": "X", "name": "ReadAsync 295", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754736427695587, "dur": 23, "ph": "X", "name": "ReadAsync 366", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754736427695612, "dur": 26, "ph": "X", "name": "ReadAsync 339", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754736427695639, "dur": 19, "ph": "X", "name": "ReadAsync 383", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754736427695660, "dur": 20, "ph": "X", "name": "ReadAsync 313", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754736427695682, "dur": 34, "ph": "X", "name": "ReadAsync 169", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754736427695717, "dur": 22, "ph": "X", "name": "ReadAsync 357", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754736427695742, "dur": 22, "ph": "X", "name": "ReadAsync 311", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754736427695765, "dur": 22, "ph": "X", "name": "ReadAsync 434", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754736427695788, "dur": 30, "ph": "X", "name": "ReadAsync 342", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754736427695821, "dur": 1, "ph": "X", "name": "ProcessMessages 291", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754736427695822, "dur": 30, "ph": "X", "name": "ReadAsync 291", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754736427695854, "dur": 1, "ph": "X", "name": "ProcessMessages 382", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754736427695856, "dur": 26, "ph": "X", "name": "ReadAsync 382", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754736427695884, "dur": 24, "ph": "X", "name": "ReadAsync 264", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754736427695910, "dur": 26, "ph": "X", "name": "ReadAsync 332", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754736427695939, "dur": 38, "ph": "X", "name": "ReadAsync 476", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754736427695979, "dur": 22, "ph": "X", "name": "ReadAsync 581", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754736427696002, "dur": 21, "ph": "X", "name": "ReadAsync 255", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754736427696025, "dur": 20, "ph": "X", "name": "ReadAsync 224", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754736427696046, "dur": 20, "ph": "X", "name": "ReadAsync 183", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754736427696068, "dur": 19, "ph": "X", "name": "ReadAsync 248", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754736427696089, "dur": 22, "ph": "X", "name": "ReadAsync 222", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754736427696112, "dur": 19, "ph": "X", "name": "ReadAsync 455", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754736427696133, "dur": 25, "ph": "X", "name": "ReadAsync 297", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754736427696159, "dur": 22, "ph": "X", "name": "ReadAsync 323", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754736427696184, "dur": 21, "ph": "X", "name": "ReadAsync 349", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754736427696207, "dur": 26, "ph": "X", "name": "ReadAsync 289", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754736427696235, "dur": 22, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754736427696258, "dur": 24, "ph": "X", "name": "ReadAsync 497", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754736427696284, "dur": 22, "ph": "X", "name": "ReadAsync 520", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754736427696308, "dur": 21, "ph": "X", "name": "ReadAsync 384", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754736427696331, "dur": 26, "ph": "X", "name": "ReadAsync 328", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754736427696358, "dur": 1, "ph": "X", "name": "ProcessMessages 482", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754736427696360, "dur": 24, "ph": "X", "name": "ReadAsync 482", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754736427696385, "dur": 24, "ph": "X", "name": "ReadAsync 202", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754736427696411, "dur": 1, "ph": "X", "name": "ProcessMessages 375", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754736427696413, "dur": 29, "ph": "X", "name": "ReadAsync 375", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754736427696443, "dur": 22, "ph": "X", "name": "ReadAsync 694", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754736427696466, "dur": 23, "ph": "X", "name": "ReadAsync 292", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754736427696494, "dur": 22, "ph": "X", "name": "ReadAsync 316", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754736427696517, "dur": 19, "ph": "X", "name": "ReadAsync 382", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754736427696538, "dur": 18, "ph": "X", "name": "ReadAsync 278", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754736427696556, "dur": 18, "ph": "X", "name": "ReadAsync 135", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754736427696576, "dur": 22, "ph": "X", "name": "ReadAsync 315", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754736427696601, "dur": 23, "ph": "X", "name": "ReadAsync 331", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754736427696625, "dur": 24, "ph": "X", "name": "ReadAsync 373", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754736427696650, "dur": 34, "ph": "X", "name": "ReadAsync 315", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754736427696685, "dur": 21, "ph": "X", "name": "ReadAsync 418", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754736427696707, "dur": 26, "ph": "X", "name": "ReadAsync 208", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754736427696735, "dur": 24, "ph": "X", "name": "ReadAsync 410", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754736427696761, "dur": 21, "ph": "X", "name": "ReadAsync 505", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754736427696783, "dur": 22, "ph": "X", "name": "ReadAsync 280", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754736427696807, "dur": 34, "ph": "X", "name": "ReadAsync 306", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754736427696846, "dur": 39, "ph": "X", "name": "ReadAsync 301", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754736427696887, "dur": 1, "ph": "X", "name": "ProcessMessages 394", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754736427696889, "dur": 36, "ph": "X", "name": "ReadAsync 394", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754736427696928, "dur": 24, "ph": "X", "name": "ReadAsync 897", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754736427696953, "dur": 22, "ph": "X", "name": "ReadAsync 231", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754736427696977, "dur": 23, "ph": "X", "name": "ReadAsync 412", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754736427697001, "dur": 22, "ph": "X", "name": "ReadAsync 348", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754736427697025, "dur": 17, "ph": "X", "name": "ReadAsync 428", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754736427697044, "dur": 79, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754736427697125, "dur": 1, "ph": "X", "name": "ProcessMessages 641", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754736427697127, "dur": 29, "ph": "X", "name": "ReadAsync 641", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754736427697157, "dur": 21, "ph": "X", "name": "ReadAsync 730", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754736427697179, "dur": 23, "ph": "X", "name": "ReadAsync 245", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754736427697205, "dur": 24, "ph": "X", "name": "ReadAsync 450", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754736427697231, "dur": 17, "ph": "X", "name": "ReadAsync 527", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754736427697250, "dur": 22, "ph": "X", "name": "ReadAsync 100", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754736427697273, "dur": 26, "ph": "X", "name": "ReadAsync 402", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754736427697302, "dur": 1, "ph": "X", "name": "ProcessMessages 518", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754736427697304, "dur": 36, "ph": "X", "name": "ReadAsync 518", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754736427697341, "dur": 24, "ph": "X", "name": "ReadAsync 650", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754736427697366, "dur": 18, "ph": "X", "name": "ReadAsync 450", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754736427697385, "dur": 17, "ph": "X", "name": "ReadAsync 427", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754736427697404, "dur": 26, "ph": "X", "name": "ReadAsync 123", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754736427697432, "dur": 23, "ph": "X", "name": "ReadAsync 393", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754736427697456, "dur": 21, "ph": "X", "name": "ReadAsync 394", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754736427697478, "dur": 21, "ph": "X", "name": "ReadAsync 264", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754736427697501, "dur": 22, "ph": "X", "name": "ReadAsync 247", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754736427697525, "dur": 18, "ph": "X", "name": "ReadAsync 402", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754736427697544, "dur": 21, "ph": "X", "name": "ReadAsync 162", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754736427697567, "dur": 25, "ph": "X", "name": "ReadAsync 247", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754736427697594, "dur": 25, "ph": "X", "name": "ReadAsync 401", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754736427697622, "dur": 39, "ph": "X", "name": "ReadAsync 455", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754736427697664, "dur": 23, "ph": "X", "name": "ReadAsync 414", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754736427697688, "dur": 18, "ph": "X", "name": "ReadAsync 425", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754736427697707, "dur": 20, "ph": "X", "name": "ReadAsync 175", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754736427697729, "dur": 22, "ph": "X", "name": "ReadAsync 237", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754736427697753, "dur": 22, "ph": "X", "name": "ReadAsync 376", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754736427697776, "dur": 21, "ph": "X", "name": "ReadAsync 327", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754736427697799, "dur": 22, "ph": "X", "name": "ReadAsync 247", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754736427697822, "dur": 22, "ph": "X", "name": "ReadAsync 358", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754736427697845, "dur": 20, "ph": "X", "name": "ReadAsync 303", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754736427697867, "dur": 21, "ph": "X", "name": "ReadAsync 159", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754736427697889, "dur": 25, "ph": "X", "name": "ReadAsync 289", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754736427697915, "dur": 21, "ph": "X", "name": "ReadAsync 518", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754736427697938, "dur": 21, "ph": "X", "name": "ReadAsync 393", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754736427697961, "dur": 22, "ph": "X", "name": "ReadAsync 454", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754736427697984, "dur": 23, "ph": "X", "name": "ReadAsync 343", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754736427698008, "dur": 19, "ph": "X", "name": "ReadAsync 489", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754736427698029, "dur": 23, "ph": "X", "name": "ReadAsync 93", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754736427698053, "dur": 22, "ph": "X", "name": "ReadAsync 390", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754736427698077, "dur": 23, "ph": "X", "name": "ReadAsync 498", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754736427698102, "dur": 23, "ph": "X", "name": "ReadAsync 331", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754736427698127, "dur": 23, "ph": "X", "name": "ReadAsync 396", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754736427698152, "dur": 22, "ph": "X", "name": "ReadAsync 411", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754736427698175, "dur": 21, "ph": "X", "name": "ReadAsync 333", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754736427698198, "dur": 22, "ph": "X", "name": "ReadAsync 258", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754736427698222, "dur": 21, "ph": "X", "name": "ReadAsync 409", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754736427698246, "dur": 22, "ph": "X", "name": "ReadAsync 377", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754736427698269, "dur": 26, "ph": "X", "name": "ReadAsync 336", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754736427698298, "dur": 22, "ph": "X", "name": "ReadAsync 388", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754736427698321, "dur": 26, "ph": "X", "name": "ReadAsync 350", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754736427698348, "dur": 24, "ph": "X", "name": "ReadAsync 33", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754736427698374, "dur": 23, "ph": "X", "name": "ReadAsync 401", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754736427698400, "dur": 1, "ph": "X", "name": "ProcessMessages 235", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754736427698402, "dur": 31, "ph": "X", "name": "ReadAsync 235", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754736427698435, "dur": 21, "ph": "X", "name": "ReadAsync 388", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754736427698457, "dur": 24, "ph": "X", "name": "ReadAsync 233", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754736427698484, "dur": 96, "ph": "X", "name": "ReadAsync 284", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754736427698583, "dur": 1, "ph": "X", "name": "ProcessMessages 273", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754736427698585, "dur": 46, "ph": "X", "name": "ReadAsync 273", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754736427698633, "dur": 1, "ph": "X", "name": "ProcessMessages 1234", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754736427698634, "dur": 29, "ph": "X", "name": "ReadAsync 1234", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754736427698665, "dur": 26, "ph": "X", "name": "ReadAsync 248", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754736427698694, "dur": 34, "ph": "X", "name": "ReadAsync 277", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754736427698731, "dur": 1, "ph": "X", "name": "ProcessMessages 248", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754736427698733, "dur": 33, "ph": "X", "name": "ReadAsync 248", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754736427698768, "dur": 24, "ph": "X", "name": "ReadAsync 361", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754736427698794, "dur": 22, "ph": "X", "name": "ReadAsync 279", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754736427698817, "dur": 25, "ph": "X", "name": "ReadAsync 77", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754736427698846, "dur": 39, "ph": "X", "name": "ReadAsync 231", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754736427698887, "dur": 28, "ph": "X", "name": "ReadAsync 295", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754736427698918, "dur": 37, "ph": "X", "name": "ReadAsync 100", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754736427698957, "dur": 35, "ph": "X", "name": "ReadAsync 140", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754736427698993, "dur": 23, "ph": "X", "name": "ReadAsync 150", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754736427699018, "dur": 19, "ph": "X", "name": "ReadAsync 386", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754736427699040, "dur": 22, "ph": "X", "name": "ReadAsync 104", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754736427699063, "dur": 1, "ph": "X", "name": "ProcessMessages 121", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754736427699065, "dur": 32, "ph": "X", "name": "ReadAsync 121", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754736427699099, "dur": 1, "ph": "X", "name": "ProcessMessages 228", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754736427699101, "dur": 93, "ph": "X", "name": "ReadAsync 228", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754736427699198, "dur": 40, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754736427699240, "dur": 25, "ph": "X", "name": "ReadAsync 415", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754736427699267, "dur": 1, "ph": "X", "name": "ProcessMessages 348", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754736427699268, "dur": 28, "ph": "X", "name": "ReadAsync 348", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754736427699299, "dur": 1, "ph": "X", "name": "ProcessMessages 336", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754736427699301, "dur": 30, "ph": "X", "name": "ReadAsync 336", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754736427699333, "dur": 24, "ph": "X", "name": "ReadAsync 328", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754736427699360, "dur": 23, "ph": "X", "name": "ReadAsync 284", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754736427699384, "dur": 31, "ph": "X", "name": "ReadAsync 335", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754736427699421, "dur": 1, "ph": "X", "name": "ProcessMessages 311", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754736427699423, "dur": 159, "ph": "X", "name": "ReadAsync 311", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754736427699585, "dur": 1, "ph": "X", "name": "ProcessMessages 331", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754736427699587, "dur": 46, "ph": "X", "name": "ReadAsync 331", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754736427699636, "dur": 1, "ph": "X", "name": "ProcessMessages 801", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754736427699638, "dur": 46, "ph": "X", "name": "ReadAsync 801", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754736427699687, "dur": 1, "ph": "X", "name": "ProcessMessages 411", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754736427699689, "dur": 41, "ph": "X", "name": "ReadAsync 411", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754736427699733, "dur": 1, "ph": "X", "name": "ProcessMessages 426", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754736427699734, "dur": 38, "ph": "X", "name": "ReadAsync 426", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754736427699775, "dur": 24, "ph": "X", "name": "ReadAsync 434", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754736427699801, "dur": 22, "ph": "X", "name": "ReadAsync 253", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754736427699825, "dur": 28, "ph": "X", "name": "ReadAsync 21", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754736427699865, "dur": 39, "ph": "X", "name": "ReadAsync 89", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754736427699907, "dur": 1, "ph": "X", "name": "ProcessMessages 148", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754736427699909, "dur": 41, "ph": "X", "name": "ReadAsync 148", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754736427699954, "dur": 37, "ph": "X", "name": "ReadAsync 90", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754736427699993, "dur": 1, "ph": "X", "name": "ProcessMessages 310", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754736427699995, "dur": 20, "ph": "X", "name": "ReadAsync 310", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754736427700017, "dur": 33, "ph": "X", "name": "ReadAsync 241", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754736427700053, "dur": 1, "ph": "X", "name": "ProcessMessages 133", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754736427700054, "dur": 44, "ph": "X", "name": "ReadAsync 133", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754736427700101, "dur": 44, "ph": "X", "name": "ReadAsync 365", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754736427700147, "dur": 1, "ph": "X", "name": "ProcessMessages 263", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754736427700149, "dur": 40, "ph": "X", "name": "ReadAsync 263", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754736427700191, "dur": 1, "ph": "X", "name": "ProcessMessages 393", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754736427700193, "dur": 36, "ph": "X", "name": "ReadAsync 393", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754736427700231, "dur": 1, "ph": "X", "name": "ProcessMessages 301", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754736427700232, "dur": 25, "ph": "X", "name": "ReadAsync 301", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754736427700258, "dur": 23, "ph": "X", "name": "ReadAsync 185", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754736427700284, "dur": 70, "ph": "X", "name": "ReadAsync 52", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754736427700358, "dur": 33, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754736427700393, "dur": 1, "ph": "X", "name": "ProcessMessages 368", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754736427700395, "dur": 22, "ph": "X", "name": "ReadAsync 368", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754736427700419, "dur": 27, "ph": "X", "name": "ReadAsync 323", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754736427700448, "dur": 1, "ph": "X", "name": "ProcessMessages 237", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754736427700450, "dur": 69, "ph": "X", "name": "ReadAsync 237", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754736427700521, "dur": 27, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754736427700550, "dur": 43, "ph": "X", "name": "ReadAsync 348", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754736427700595, "dur": 33, "ph": "X", "name": "ReadAsync 398", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754736427700630, "dur": 52, "ph": "X", "name": "ReadAsync 240", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754736427700684, "dur": 35, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754736427700722, "dur": 1, "ph": "X", "name": "ProcessMessages 305", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754736427700723, "dur": 32, "ph": "X", "name": "ReadAsync 305", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754736427700758, "dur": 1, "ph": "X", "name": "ProcessMessages 458", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754736427700760, "dur": 29, "ph": "X", "name": "ReadAsync 458", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754736427700791, "dur": 31, "ph": "X", "name": "ReadAsync 216", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754736427700825, "dur": 58, "ph": "X", "name": "ReadAsync 21", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754736427700885, "dur": 40, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754736427700927, "dur": 23, "ph": "X", "name": "ReadAsync 382", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754736427700952, "dur": 27, "ph": "X", "name": "ReadAsync 78", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754736427700980, "dur": 23, "ph": "X", "name": "ReadAsync 339", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754736427701006, "dur": 1, "ph": "X", "name": "ProcessMessages 237", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754736427701007, "dur": 66, "ph": "X", "name": "ReadAsync 237", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754736427701077, "dur": 40, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754736427701120, "dur": 1, "ph": "X", "name": "ProcessMessages 444", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754736427701122, "dur": 40, "ph": "X", "name": "ReadAsync 444", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754736427701165, "dur": 1, "ph": "X", "name": "ProcessMessages 578", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754736427701168, "dur": 40, "ph": "X", "name": "ReadAsync 578", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754736427701210, "dur": 31, "ph": "X", "name": "ReadAsync 134", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754736427701243, "dur": 25, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754736427701270, "dur": 22, "ph": "X", "name": "ReadAsync 505", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754736427701294, "dur": 23, "ph": "X", "name": "ReadAsync 391", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754736427701319, "dur": 20, "ph": "X", "name": "ReadAsync 329", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754736427701340, "dur": 50, "ph": "X", "name": "ReadAsync 58", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754736427701392, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754736427701394, "dur": 26, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754736427701423, "dur": 25, "ph": "X", "name": "ReadAsync 514", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754736427701450, "dur": 25, "ph": "X", "name": "ReadAsync 465", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754736427701478, "dur": 58, "ph": "X", "name": "ReadAsync 258", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754736427701538, "dur": 23, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754736427701563, "dur": 20, "ph": "X", "name": "ReadAsync 375", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754736427701584, "dur": 1, "ph": "X", "name": "ProcessMessages 173", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754736427701586, "dur": 21, "ph": "X", "name": "ReadAsync 173", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754736427701609, "dur": 14, "ph": "X", "name": "ReadAsync 318", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754736427701625, "dur": 60, "ph": "X", "name": "ReadAsync 228", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754736427701686, "dur": 22, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754736427701710, "dur": 22, "ph": "X", "name": "ReadAsync 265", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754736427701734, "dur": 22, "ph": "X", "name": "ReadAsync 338", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754736427701757, "dur": 20, "ph": "X", "name": "ReadAsync 403", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754736427701779, "dur": 48, "ph": "X", "name": "ReadAsync 66", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754736427701828, "dur": 21, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754736427701851, "dur": 22, "ph": "X", "name": "ReadAsync 283", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754736427701875, "dur": 22, "ph": "X", "name": "ReadAsync 292", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754736427701898, "dur": 20, "ph": "X", "name": "ReadAsync 317", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754736427701920, "dur": 54, "ph": "X", "name": "ReadAsync 138", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754736427701975, "dur": 22, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754736427701999, "dur": 22, "ph": "X", "name": "ReadAsync 296", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754736427702022, "dur": 21, "ph": "X", "name": "ReadAsync 243", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754736427702045, "dur": 17, "ph": "X", "name": "ReadAsync 377", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754736427702063, "dur": 2, "ph": "X", "name": "ProcessMessages 66", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754736427702065, "dur": 51, "ph": "X", "name": "ReadAsync 66", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754736427702118, "dur": 21, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754736427702140, "dur": 22, "ph": "X", "name": "ReadAsync 240", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754736427702164, "dur": 33, "ph": "X", "name": "ReadAsync 205", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754736427702198, "dur": 22, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754736427702222, "dur": 21, "ph": "X", "name": "ReadAsync 343", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754736427702244, "dur": 20, "ph": "X", "name": "ReadAsync 367", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754736427702265, "dur": 21, "ph": "X", "name": "ReadAsync 394", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754736427702288, "dur": 22, "ph": "X", "name": "ReadAsync 271", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754736427702312, "dur": 32, "ph": "X", "name": "ReadAsync 373", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754736427702346, "dur": 21, "ph": "X", "name": "ReadAsync 318", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754736427702368, "dur": 20, "ph": "X", "name": "ReadAsync 235", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754736427702390, "dur": 56, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754736427702447, "dur": 24, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754736427702472, "dur": 28, "ph": "X", "name": "ReadAsync 328", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754736427702502, "dur": 21, "ph": "X", "name": "ReadAsync 464", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754736427702525, "dur": 22, "ph": "X", "name": "ReadAsync 211", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754736427702549, "dur": 21, "ph": "X", "name": "ReadAsync 349", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754736427702571, "dur": 21, "ph": "X", "name": "ReadAsync 278", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754736427702593, "dur": 27, "ph": "X", "name": "ReadAsync 260", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754736427702623, "dur": 2, "ph": "X", "name": "ProcessMessages 55", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754736427702625, "dur": 26, "ph": "X", "name": "ReadAsync 55", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754736427702655, "dur": 25, "ph": "X", "name": "ReadAsync 218", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754736427702682, "dur": 20, "ph": "X", "name": "ReadAsync 364", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754736427702704, "dur": 17, "ph": "X", "name": "ReadAsync 95", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754736427702722, "dur": 68, "ph": "X", "name": "ReadAsync 145", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754736427702792, "dur": 22, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754736427702815, "dur": 22, "ph": "X", "name": "ReadAsync 265", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754736427702839, "dur": 21, "ph": "X", "name": "ReadAsync 276", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754736427702862, "dur": 22, "ph": "X", "name": "ReadAsync 230", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754736427702885, "dur": 21, "ph": "X", "name": "ReadAsync 254", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754736427702907, "dur": 1, "ph": "X", "name": "ProcessMessages 311", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754736427702908, "dur": 22, "ph": "X", "name": "ReadAsync 311", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754736427702933, "dur": 51, "ph": "X", "name": "ReadAsync 140", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754736427702986, "dur": 21, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754736427703009, "dur": 26, "ph": "X", "name": "ReadAsync 256", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754736427703039, "dur": 1, "ph": "X", "name": "ProcessMessages 210", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754736427703040, "dur": 34, "ph": "X", "name": "ReadAsync 210", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754736427703077, "dur": 24, "ph": "X", "name": "ReadAsync 612", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754736427703103, "dur": 43, "ph": "X", "name": "ReadAsync 355", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754736427703149, "dur": 44, "ph": "X", "name": "ReadAsync 282", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754736427703195, "dur": 23, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754736427703220, "dur": 21, "ph": "X", "name": "ReadAsync 293", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754736427703243, "dur": 21, "ph": "X", "name": "ReadAsync 386", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754736427703265, "dur": 18, "ph": "X", "name": "ReadAsync 174", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754736427703285, "dur": 19, "ph": "X", "name": "ReadAsync 181", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754736427703305, "dur": 67, "ph": "X", "name": "ReadAsync 56", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754736427703373, "dur": 24, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754736427703399, "dur": 20, "ph": "X", "name": "ReadAsync 320", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754736427703421, "dur": 23, "ph": "X", "name": "ReadAsync 256", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754736427703445, "dur": 17, "ph": "X", "name": "ReadAsync 379", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754736427703464, "dur": 52, "ph": "X", "name": "ReadAsync 66", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754736427703517, "dur": 21, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754736427703539, "dur": 21, "ph": "X", "name": "ReadAsync 253", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754736427703563, "dur": 25, "ph": "X", "name": "ReadAsync 321", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754736427703590, "dur": 1, "ph": "X", "name": "ProcessMessages 323", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754736427703592, "dur": 28, "ph": "X", "name": "ReadAsync 323", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754736427703622, "dur": 42, "ph": "X", "name": "ReadAsync 139", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754736427703665, "dur": 20, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754736427703686, "dur": 23, "ph": "X", "name": "ReadAsync 239", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754736427703712, "dur": 32, "ph": "X", "name": "ReadAsync 295", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754736427703747, "dur": 1, "ph": "X", "name": "ProcessMessages 456", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754736427703749, "dur": 30, "ph": "X", "name": "ReadAsync 456", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754736427703782, "dur": 38, "ph": "X", "name": "ReadAsync 366", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754736427703821, "dur": 20, "ph": "X", "name": "ReadAsync 487", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754736427703843, "dur": 21, "ph": "X", "name": "ReadAsync 412", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754736427703865, "dur": 22, "ph": "X", "name": "ReadAsync 337", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754736427703889, "dur": 23, "ph": "X", "name": "ReadAsync 179", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754736427703913, "dur": 57, "ph": "X", "name": "ReadAsync 240", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754736427703972, "dur": 29, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754736427704002, "dur": 1, "ph": "X", "name": "ProcessMessages 291", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754736427704003, "dur": 35, "ph": "X", "name": "ReadAsync 291", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754736427704041, "dur": 19, "ph": "X", "name": "ReadAsync 601", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754736427704061, "dur": 59, "ph": "X", "name": "ReadAsync 196", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754736427704122, "dur": 23, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754736427704146, "dur": 23, "ph": "X", "name": "ReadAsync 342", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754736427704171, "dur": 22, "ph": "X", "name": "ReadAsync 411", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754736427704194, "dur": 21, "ph": "X", "name": "ReadAsync 309", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754736427704217, "dur": 54, "ph": "X", "name": "ReadAsync 61", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754736427704274, "dur": 30, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754736427704306, "dur": 24, "ph": "X", "name": "ReadAsync 362", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754736427704332, "dur": 23, "ph": "X", "name": "ReadAsync 381", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754736427704357, "dur": 20, "ph": "X", "name": "ReadAsync 297", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754736427704379, "dur": 45, "ph": "X", "name": "ReadAsync 57", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754736427704425, "dur": 22, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754736427704449, "dur": 26, "ph": "X", "name": "ReadAsync 274", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754736427704476, "dur": 21, "ph": "X", "name": "ReadAsync 419", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754736427704499, "dur": 20, "ph": "X", "name": "ReadAsync 300", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754736427704520, "dur": 46, "ph": "X", "name": "ReadAsync 52", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754736427704568, "dur": 28, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754736427704598, "dur": 23, "ph": "X", "name": "ReadAsync 167", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754736427704622, "dur": 22, "ph": "X", "name": "ReadAsync 431", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754736427704646, "dur": 18, "ph": "X", "name": "ReadAsync 405", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754736427704665, "dur": 53, "ph": "X", "name": "ReadAsync 67", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754736427704720, "dur": 25, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754736427704746, "dur": 23, "ph": "X", "name": "ReadAsync 339", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754736427704770, "dur": 21, "ph": "X", "name": "ReadAsync 380", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754736427704793, "dur": 22, "ph": "X", "name": "ReadAsync 391", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754736427704816, "dur": 21, "ph": "X", "name": "ReadAsync 359", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754736427704839, "dur": 22, "ph": "X", "name": "ReadAsync 214", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754736427704863, "dur": 23, "ph": "X", "name": "ReadAsync 277", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754736427704887, "dur": 21, "ph": "X", "name": "ReadAsync 366", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754736427704909, "dur": 20, "ph": "X", "name": "ReadAsync 235", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754736427704930, "dur": 21, "ph": "X", "name": "ReadAsync 108", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754736427704953, "dur": 58, "ph": "X", "name": "ReadAsync 197", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754736427705012, "dur": 23, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754736427705036, "dur": 30, "ph": "X", "name": "ReadAsync 346", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754736427705068, "dur": 18, "ph": "X", "name": "ReadAsync 566", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754736427705088, "dur": 21, "ph": "X", "name": "ReadAsync 327", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754736427705110, "dur": 26, "ph": "X", "name": "ReadAsync 339", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754736427705138, "dur": 1, "ph": "X", "name": "ProcessMessages 399", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754736427705140, "dur": 29, "ph": "X", "name": "ReadAsync 399", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754736427705170, "dur": 22, "ph": "X", "name": "ReadAsync 643", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754736427705195, "dur": 21, "ph": "X", "name": "ReadAsync 357", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754736427705217, "dur": 17, "ph": "X", "name": "ReadAsync 44", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754736427705235, "dur": 56, "ph": "X", "name": "ReadAsync 196", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754736427705293, "dur": 23, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754736427705319, "dur": 22, "ph": "X", "name": "ReadAsync 319", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754736427705342, "dur": 22, "ph": "X", "name": "ReadAsync 362", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754736427705366, "dur": 25, "ph": "X", "name": "ReadAsync 455", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754736427705393, "dur": 22, "ph": "X", "name": "ReadAsync 395", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754736427705417, "dur": 23, "ph": "X", "name": "ReadAsync 415", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754736427705442, "dur": 23, "ph": "X", "name": "ReadAsync 376", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754736427705466, "dur": 42, "ph": "X", "name": "ReadAsync 437", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754736427705513, "dur": 2, "ph": "X", "name": "ProcessMessages 84", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754736427705517, "dur": 66, "ph": "X", "name": "ReadAsync 84", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754736427705586, "dur": 1, "ph": "X", "name": "ProcessMessages 237", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754736427705588, "dur": 61, "ph": "X", "name": "ReadAsync 237", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754736427705654, "dur": 1, "ph": "X", "name": "ProcessMessages 266", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754736427705657, "dur": 57, "ph": "X", "name": "ReadAsync 266", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754736427705718, "dur": 1, "ph": "X", "name": "ProcessMessages 957", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754736427705720, "dur": 58, "ph": "X", "name": "ReadAsync 957", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754736427705780, "dur": 55, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754736427705838, "dur": 1, "ph": "X", "name": "ProcessMessages 538", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754736427705839, "dur": 43, "ph": "X", "name": "ReadAsync 538", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754736427705884, "dur": 1, "ph": "X", "name": "ProcessMessages 496", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754736427705886, "dur": 35, "ph": "X", "name": "ReadAsync 496", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754736427705923, "dur": 48, "ph": "X", "name": "ReadAsync 143", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754736427705975, "dur": 31, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754736427706009, "dur": 24, "ph": "X", "name": "ReadAsync 565", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754736427706035, "dur": 21, "ph": "X", "name": "ReadAsync 525", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754736427706059, "dur": 123, "ph": "X", "name": "ReadAsync 136", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754736427706187, "dur": 31, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754736427706220, "dur": 28, "ph": "X", "name": "ReadAsync 1183", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754736427706249, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754736427706251, "dur": 29, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754736427706284, "dur": 37, "ph": "X", "name": "ReadAsync 432", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754736427706323, "dur": 24, "ph": "X", "name": "ReadAsync 198", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754736427706350, "dur": 26, "ph": "X", "name": "ReadAsync 415", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754736427706378, "dur": 129, "ph": "X", "name": "ReadAsync 57", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754736427706512, "dur": 41, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754736427706555, "dur": 1, "ph": "X", "name": "ProcessMessages 1489", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754736427706557, "dur": 35, "ph": "X", "name": "ReadAsync 1489", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754736427706595, "dur": 29, "ph": "X", "name": "ReadAsync 495", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754736427706626, "dur": 22, "ph": "X", "name": "ReadAsync 372", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754736427706650, "dur": 179, "ph": "X", "name": "ReadAsync 248", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754736427706832, "dur": 1, "ph": "X", "name": "ProcessMessages 237", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754736427706833, "dur": 38, "ph": "X", "name": "ReadAsync 237", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754736427706874, "dur": 1, "ph": "X", "name": "ProcessMessages 1169", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754736427706875, "dur": 28, "ph": "X", "name": "ReadAsync 1169", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754736427706904, "dur": 23, "ph": "X", "name": "ReadAsync 206", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754736427706929, "dur": 22, "ph": "X", "name": "ReadAsync 323", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754736427706953, "dur": 21, "ph": "X", "name": "ReadAsync 448", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754736427706975, "dur": 52, "ph": "X", "name": "ReadAsync 143", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754736427707029, "dur": 22, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754736427707052, "dur": 26, "ph": "X", "name": "ReadAsync 274", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754736427707081, "dur": 1, "ph": "X", "name": "ProcessMessages 366", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754736427707082, "dur": 33, "ph": "X", "name": "ReadAsync 366", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754736427707118, "dur": 46, "ph": "X", "name": "ReadAsync 481", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754736427707166, "dur": 31, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754736427707199, "dur": 25, "ph": "X", "name": "ReadAsync 444", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754736427707226, "dur": 24, "ph": "X", "name": "ReadAsync 190", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754736427707252, "dur": 20, "ph": "X", "name": "ReadAsync 412", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754736427707274, "dur": 63, "ph": "X", "name": "ReadAsync 62", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754736427707338, "dur": 19, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754736427707358, "dur": 21, "ph": "X", "name": "ReadAsync 279", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754736427707381, "dur": 18, "ph": "X", "name": "ReadAsync 203", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754736427707400, "dur": 21, "ph": "X", "name": "ReadAsync 275", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754736427707423, "dur": 22, "ph": "X", "name": "ReadAsync 175", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754736427707447, "dur": 23, "ph": "X", "name": "ReadAsync 315", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754736427707473, "dur": 1, "ph": "X", "name": "ProcessMessages 279", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754736427707475, "dur": 30, "ph": "X", "name": "ReadAsync 279", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754736427707507, "dur": 23, "ph": "X", "name": "ReadAsync 432", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754736427707532, "dur": 18, "ph": "X", "name": "ReadAsync 176", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754736427707551, "dur": 21, "ph": "X", "name": "ReadAsync 148", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754736427707574, "dur": 18, "ph": "X", "name": "ReadAsync 93", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754736427707593, "dur": 71, "ph": "X", "name": "ReadAsync 144", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754736427707666, "dur": 24, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754736427707693, "dur": 23, "ph": "X", "name": "ReadAsync 231", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754736427707717, "dur": 23, "ph": "X", "name": "ReadAsync 325", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754736427707742, "dur": 23, "ph": "X", "name": "ReadAsync 332", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754736427707766, "dur": 17, "ph": "X", "name": "ReadAsync 340", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754736427707785, "dur": 21, "ph": "X", "name": "ReadAsync 299", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754736427707808, "dur": 23, "ph": "X", "name": "ReadAsync 152", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754736427707833, "dur": 42, "ph": "X", "name": "ReadAsync 242", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754736427707878, "dur": 1, "ph": "X", "name": "ProcessMessages 325", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754736427707879, "dur": 34, "ph": "X", "name": "ReadAsync 325", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754736427707917, "dur": 33, "ph": "X", "name": "ReadAsync 289", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754736427707952, "dur": 1, "ph": "X", "name": "ProcessMessages 240", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754736427707953, "dur": 43, "ph": "X", "name": "ReadAsync 240", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754736427707998, "dur": 24, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754736427708024, "dur": 22, "ph": "X", "name": "ReadAsync 309", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754736427708048, "dur": 23, "ph": "X", "name": "ReadAsync 227", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754736427708072, "dur": 22, "ph": "X", "name": "ReadAsync 300", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754736427708095, "dur": 23, "ph": "X", "name": "ReadAsync 283", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754736427708119, "dur": 1, "ph": "X", "name": "ProcessMessages 315", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754736427708121, "dur": 23, "ph": "X", "name": "ReadAsync 315", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754736427708145, "dur": 22, "ph": "X", "name": "ReadAsync 225", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754736427708169, "dur": 23, "ph": "X", "name": "ReadAsync 250", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754736427708194, "dur": 22, "ph": "X", "name": "ReadAsync 260", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754736427708217, "dur": 21, "ph": "X", "name": "ReadAsync 96", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754736427708240, "dur": 55, "ph": "X", "name": "ReadAsync 196", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754736427708297, "dur": 30, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754736427708329, "dur": 1, "ph": "X", "name": "ProcessMessages 296", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754736427708331, "dur": 34, "ph": "X", "name": "ReadAsync 296", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754736427708367, "dur": 23, "ph": "X", "name": "ReadAsync 550", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754736427708392, "dur": 20, "ph": "X", "name": "ReadAsync 198", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754736427708414, "dur": 88, "ph": "X", "name": "ReadAsync 62", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754736427708506, "dur": 28, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754736427708536, "dur": 26, "ph": "X", "name": "ReadAsync 490", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754736427708566, "dur": 26, "ph": "X", "name": "ReadAsync 231", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754736427708594, "dur": 17, "ph": "X", "name": "ReadAsync 490", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754736427708613, "dur": 60, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754736427708675, "dur": 22, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754736427708699, "dur": 24, "ph": "X", "name": "ReadAsync 405", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754736427708724, "dur": 22, "ph": "X", "name": "ReadAsync 411", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754736427708748, "dur": 20, "ph": "X", "name": "ReadAsync 313", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754736427708770, "dur": 53, "ph": "X", "name": "ReadAsync 57", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754736427708824, "dur": 22, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754736427708847, "dur": 23, "ph": "X", "name": "ReadAsync 304", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754736427708871, "dur": 22, "ph": "X", "name": "ReadAsync 373", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754736427708895, "dur": 22, "ph": "X", "name": "ReadAsync 472", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754736427708919, "dur": 23, "ph": "X", "name": "ReadAsync 410", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754736427708943, "dur": 18, "ph": "X", "name": "ReadAsync 375", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754736427708963, "dur": 21, "ph": "X", "name": "ReadAsync 322", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754736427708986, "dur": 22, "ph": "X", "name": "ReadAsync 131", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754736427709009, "dur": 21, "ph": "X", "name": "ReadAsync 453", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754736427709032, "dur": 22, "ph": "X", "name": "ReadAsync 88", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754736427709055, "dur": 60, "ph": "X", "name": "ReadAsync 237", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754736427709116, "dur": 33, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754736427709150, "dur": 1, "ph": "X", "name": "ProcessMessages 310", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754736427709151, "dur": 34, "ph": "X", "name": "ReadAsync 310", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754736427709187, "dur": 23, "ph": "X", "name": "ReadAsync 489", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754736427709211, "dur": 22, "ph": "X", "name": "ReadAsync 428", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754736427709235, "dur": 22, "ph": "X", "name": "ReadAsync 403", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754736427709258, "dur": 22, "ph": "X", "name": "ReadAsync 300", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754736427709281, "dur": 22, "ph": "X", "name": "ReadAsync 339", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754736427709305, "dur": 20, "ph": "X", "name": "ReadAsync 427", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754736427709327, "dur": 18, "ph": "X", "name": "ReadAsync 189", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754736427709346, "dur": 71, "ph": "X", "name": "ReadAsync 240", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754736427709419, "dur": 21, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754736427709442, "dur": 24, "ph": "X", "name": "ReadAsync 304", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754736427709467, "dur": 21, "ph": "X", "name": "ReadAsync 382", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754736427709489, "dur": 22, "ph": "X", "name": "ReadAsync 475", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754736427709513, "dur": 23, "ph": "X", "name": "ReadAsync 414", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754736427709537, "dur": 22, "ph": "X", "name": "ReadAsync 376", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754736427709561, "dur": 22, "ph": "X", "name": "ReadAsync 336", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754736427709584, "dur": 21, "ph": "X", "name": "ReadAsync 470", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754736427709607, "dur": 18, "ph": "X", "name": "ReadAsync 196", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754736427709626, "dur": 59, "ph": "X", "name": "ReadAsync 237", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754736427709687, "dur": 22, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754736427709710, "dur": 24, "ph": "X", "name": "ReadAsync 359", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754736427709736, "dur": 22, "ph": "X", "name": "ReadAsync 348", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754736427709759, "dur": 24, "ph": "X", "name": "ReadAsync 304", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754736427709784, "dur": 18, "ph": "X", "name": "ReadAsync 387", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754736427709804, "dur": 21, "ph": "X", "name": "ReadAsync 208", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754736427709826, "dur": 22, "ph": "X", "name": "ReadAsync 277", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754736427709851, "dur": 22, "ph": "X", "name": "ReadAsync 308", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754736427709874, "dur": 22, "ph": "X", "name": "ReadAsync 271", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754736427709897, "dur": 20, "ph": "X", "name": "ReadAsync 217", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754736427709919, "dur": 51, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754736427709971, "dur": 23, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754736427709995, "dur": 24, "ph": "X", "name": "ReadAsync 318", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754736427710021, "dur": 21, "ph": "X", "name": "ReadAsync 420", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754736427710044, "dur": 17, "ph": "X", "name": "ReadAsync 313", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754736427710062, "dur": 46, "ph": "X", "name": "ReadAsync 57", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754736427710110, "dur": 23, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754736427710135, "dur": 21, "ph": "X", "name": "ReadAsync 369", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754736427710158, "dur": 22, "ph": "X", "name": "ReadAsync 175", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754736427710181, "dur": 22, "ph": "X", "name": "ReadAsync 409", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754736427710205, "dur": 42, "ph": "X", "name": "ReadAsync 456", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754736427710248, "dur": 22, "ph": "X", "name": "ReadAsync 430", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754736427710271, "dur": 18, "ph": "X", "name": "ReadAsync 342", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754736427710290, "dur": 21, "ph": "X", "name": "ReadAsync 267", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754736427710313, "dur": 17, "ph": "X", "name": "ReadAsync 327", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754736427710332, "dur": 21, "ph": "X", "name": "ReadAsync 81", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754736427710355, "dur": 59, "ph": "X", "name": "ReadAsync 237", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754736427710415, "dur": 21, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754736427710437, "dur": 26, "ph": "X", "name": "ReadAsync 301", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754736427710465, "dur": 22, "ph": "X", "name": "ReadAsync 454", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754736427710488, "dur": 22, "ph": "X", "name": "ReadAsync 403", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754736427710512, "dur": 22, "ph": "X", "name": "ReadAsync 372", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754736427710536, "dur": 22, "ph": "X", "name": "ReadAsync 294", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754736427710559, "dur": 21, "ph": "X", "name": "ReadAsync 395", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754736427710582, "dur": 17, "ph": "X", "name": "ReadAsync 333", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754736427710600, "dur": 22, "ph": "X", "name": "ReadAsync 173", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754736427710624, "dur": 18, "ph": "X", "name": "ReadAsync 170", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754736427710642, "dur": 49, "ph": "X", "name": "ReadAsync 67", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754736427710693, "dur": 23, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754736427710718, "dur": 23, "ph": "X", "name": "ReadAsync 330", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754736427710742, "dur": 22, "ph": "X", "name": "ReadAsync 340", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754736427710766, "dur": 22, "ph": "X", "name": "ReadAsync 303", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754736427710789, "dur": 22, "ph": "X", "name": "ReadAsync 373", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754736427710812, "dur": 22, "ph": "X", "name": "ReadAsync 256", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754736427710836, "dur": 22, "ph": "X", "name": "ReadAsync 343", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754736427710859, "dur": 21, "ph": "X", "name": "ReadAsync 294", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754736427710881, "dur": 22, "ph": "X", "name": "ReadAsync 147", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754736427710905, "dur": 54, "ph": "X", "name": "ReadAsync 240", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754736427710960, "dur": 23, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754736427710985, "dur": 23, "ph": "X", "name": "ReadAsync 298", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754736427711010, "dur": 21, "ph": "X", "name": "ReadAsync 368", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754736427711032, "dur": 24, "ph": "X", "name": "ReadAsync 323", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754736427711058, "dur": 17, "ph": "X", "name": "ReadAsync 408", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754736427711076, "dur": 1, "ph": "X", "name": "ProcessMessages 132", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754736427711078, "dur": 21, "ph": "X", "name": "ReadAsync 132", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754736427711100, "dur": 22, "ph": "X", "name": "ReadAsync 333", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754736427711123, "dur": 26, "ph": "X", "name": "ReadAsync 290", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754736427711151, "dur": 17, "ph": "X", "name": "ReadAsync 300", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754736427711169, "dur": 22, "ph": "X", "name": "ReadAsync 69", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754736427711193, "dur": 57, "ph": "X", "name": "ReadAsync 237", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754736427711252, "dur": 106, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754736427711361, "dur": 50, "ph": "X", "name": "ReadAsync 224", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754736427711417, "dur": 594, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754736427712013, "dur": 47, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754736427712061, "dur": 1, "ph": "X", "name": "ProcessMessages 464", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754736427712063, "dur": 122, "ph": "X", "name": "ReadAsync 464", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754736427712190, "dur": 27, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754736427712219, "dur": 187, "ph": "X", "name": "ProcessMessages 13", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754736427712408, "dur": 5853, "ph": "X", "name": "ReadAsync 13", "args": {}}, {"pid": 3100, "tid": 24781, "ts": 1754736429720502, "dur": 551, "ph": "X", "name": "ReadEntireBinlogFromIpcAsync", "args": {}}, {"pid": 3100, "tid": 8589934592, "ph": "M", "name": "thread_name", "args": {"name": "WaitForBuildProgramInputDataBeingWrittenAndSendDagReadyMessageAsync"}}, {"pid": 3100, "tid": 8589934592, "ts": 1754736427540756, "dur": 59380, "ph": "X", "name": "await writeBuildProgramInputDataTask", "args": {}}, {"pid": 3100, "tid": 8589934592, "ts": 1754736427600140, "dur": 86974, "ph": "X", "name": "WritePipe.WaitConnectionAsync", "args": {}}, {"pid": 3100, "tid": 8589934592, "ts": 1754736427687117, "dur": 1305, "ph": "X", "name": "WriteDagReadyMessage", "args": {}}, {"pid": 3100, "tid": 24781, "ts": 1754736429721054, "dur": 2, "ph": "X", "name": "WaitForBuildProgramInputDataBeingWrittenAndSendDagReadyMessageAsync", "args": {}}, {"pid": 3100, "tid": 4294967296, "ph": "M", "name": "thread_name", "args": {"name": "BuildAsync"}}, {"pid": 3100, "tid": 4294967296, "ts": 1754736427414711, "dur": 304332, "ph": "X", "name": "RunBackend", "args": {}}, {"pid": 3100, "tid": 4294967296, "ts": 1754736427416976, "dur": 120416, "ph": "X", "name": "BackendProgram.Start", "args": {}}, {"pid": 3100, "tid": 4294967296, "ts": 1754736427719134, "dur": 640851, "ph": "X", "name": "await ExecuteBuildProgram", "args": {}}, {"pid": 3100, "tid": 4294967296, "ts": 1754736428360137, "dur": 1351749, "ph": "X", "name": "RunBackend", "args": {}}, {"pid": 3100, "tid": 4294967296, "ts": 1754736428360239, "dur": 120600, "ph": "X", "name": "BackendProgram.Start", "args": {}}, {"pid": 3100, "tid": 4294967296, "ts": 1754736429711902, "dur": 2615, "ph": "X", "name": "await WaitForAndApplyScriptUpdaters", "args": {}}, {"pid": 3100, "tid": 4294967296, "ts": 1754736429713859, "dur": 28, "ph": "X", "name": "await <PERSON><PERSON>tUp<PERSON><PERSON>", "args": {}}, {"pid": 3100, "tid": 4294967296, "ts": 1754736429714521, "dur": 7, "ph": "X", "name": "await taskToReadBuildProgramOutput", "args": {}}, {"pid": 3100, "tid": 24781, "ts": 1754736429721057, "dur": 7, "ph": "X", "name": "BuildAsync", "args": {}}, {"cat": "", "pid": 12345, "tid": 0, "ts": 0, "ph": "M", "name": "process_name", "args": {"name": "bee_backend"}}, {"pid": 12345, "tid": 0, "ts": 1754736428623939, "dur": 105666, "ph": "X", "name": "DriverInitData", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1754736428729611, "dur": 261, "ph": "X", "name": "RemoveStaleOutputs", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1754736428729967, "dur": 52, "ph": "X", "name": "<PERSON><PERSON>", "args": {"detail": "PrepareNodes"}}, {"pid": 12345, "tid": 0, "ts": 1754736428730019, "dur": 400, "ph": "X", "name": "BuildQueueInit", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1754736428734650, "dur": 65, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.Collections.dll.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1754736428734825, "dur": 55, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Core.Runtime.dll (+2 others)"}}, {"pid": 12345, "tid": 0, "ts": 1754736428740131, "dur": 53, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/UnityEngine.UI.dll (+pdb)"}}, {"pid": 12345, "tid": 0, "ts": 1754736428749343, "dur": 51, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/com.crashkonijn.goap.demos.simple.dll (+pdb)"}}, {"pid": 12345, "tid": 0, "ts": 1754736428730436, "dur": 19961, "ph": "X", "name": "EnqueueRequestedNodes", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1754736428750411, "dur": 953489, "ph": "X", "name": "WaitForBuildFinished", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1754736429703902, "dur": 270, "ph": "X", "name": "JoinBuildThread", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1754736429704312, "dur": 55, "ph": "X", "name": "BuildQueueDestroyTail", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1754736429704388, "dur": 1094, "ph": "X", "name": "<PERSON><PERSON>", "args": {"detail": "Write AllBuiltNodes"}}, {"pid": 12345, "tid": 1, "ts": 1754736428731075, "dur": 19421, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1754736428750526, "dur": 58, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\GameSoft\\Unity\\Editor\\2022.3.38f1c1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.VehiclesModule.dll"}}, {"pid": 12345, "tid": 1, "ts": 1754736428750511, "dur": 77, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.VehiclesModule.dll_B4028357F84AC7DF.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1754736428750646, "dur": 50, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\GameSoft\\Unity\\Editor\\2022.3.38f1c1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.AccessibilityModule.dll"}}, {"pid": 12345, "tid": 1, "ts": 1754736428750645, "dur": 54, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.AccessibilityModule.dll_4B3448FA7561224A.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1754736428750773, "dur": 61, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.GameCenterModule.dll_09D119D0E0AA1ABF.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1754736428750917, "dur": 95, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1754736428751116, "dur": 161, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Mathematics.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 1, "ts": 1754736428751340, "dur": 137, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Core.Editor.rsp"}}, {"pid": 12345, "tid": 1, "ts": 1754736428751565, "dur": 135, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/CrashKonijn.Goap.Runtime.rsp"}}, {"pid": 12345, "tid": 1, "ts": 1754736428751819, "dur": 199, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/com.crashkonijn.docs.getting_started.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 1, "ts": 1754736428752176, "dur": 137, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1754736428752341, "dur": 61, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1754736428752402, "dur": 187, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/18381044871219080784.rsp"}}, {"pid": 12345, "tid": 1, "ts": 1754736428752590, "dur": 188, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1754736428752779, "dur": 165, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1754736428752944, "dur": 171, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1754736428753115, "dur": 202, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1754736428753317, "dur": 180, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1754736428753497, "dur": 202, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1754736428753700, "dur": 159, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1754736428753860, "dur": 153, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1754736428754013, "dur": 755, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1754736428754769, "dur": 210, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1754736428754979, "dur": 228, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1754736428755207, "dur": 218, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1754736428755425, "dur": 212, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1754736428755637, "dur": 198, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1754736428755835, "dur": 231, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1754736428756066, "dur": 186, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1754736428756369, "dur": 512, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.render-pipelines.core@14.0.11\\Editor\\Lighting\\ProbeVolume\\ProbeVolumeUI.Skin.cs"}}, {"pid": 12345, "tid": 1, "ts": 1754736428756252, "dur": 752, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1754736428757004, "dur": 188, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1754736428757192, "dur": 240, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1754736428757432, "dur": 202, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1754736428757634, "dur": 172, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1754736428757865, "dur": 83, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1754736428757948, "dur": 281, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1754736428758229, "dur": 501, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1754736428758740, "dur": 552, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.ShaderGraph.Utilities.dll.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1754736428759317, "dur": 567, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.ShaderGraph.Utilities.dll (+2 others)"}}, {"pid": 12345, "tid": 1, "ts": 1754736428759964, "dur": 114, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/com.crashkonijn.goap.demos.turn-based.dll.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1754736428760097, "dur": 57, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/com.crashkonijn.goap.demos.turn-based.dll.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1754736428760155, "dur": 387, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1754736428760542, "dur": 229, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1754736428760771, "dur": 598, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1754736428761369, "dur": 1463, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1754736428762833, "dur": 54, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Assembly-CSharp.dll.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1754736428762912, "dur": 301020, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1754736429064440, "dur": 359, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.collections@1.2.4\\Unity.Collections.LowLevel.ILSupport\\Unity.Collections.LowLevel.ILSupport.dll"}}, {"pid": 12345, "tid": 1, "ts": 1754736429063934, "dur": 1902, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.RenderPipelines.ShaderGraph.ShaderGraphLibrary.dll (+pdb)"}}, {"pid": 12345, "tid": 1, "ts": 1754736429065837, "dur": 52, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1754736429065899, "dur": 1460, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Mathematics.dll (+pdb)"}}, {"pid": 12345, "tid": 1, "ts": 1754736429067361, "dur": 66, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1754736429067437, "dur": 1376, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Collections.BurstCompatibilityGen.dll (+pdb)"}}, {"pid": 12345, "tid": 1, "ts": 1754736429069872, "dur": 88, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\GameSoft\\Unity\\Editor\\2022.3.38f1c1\\Editor\\Data\\Tools\\ilpp\\Unity.ILPP.Runner\\System.Net.WebSockets.dll"}}, {"pid": 12345, "tid": 1, "ts": 1754736429069981, "dur": 269, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\GameSoft\\Unity\\Editor\\2022.3.38f1c1\\Editor\\Data\\Tools\\ilpp\\Unity.ILPP.Runner\\System.Runtime.CompilerServices.Unsafe.dll"}}, {"pid": 12345, "tid": 1, "ts": 1754736429070780, "dur": 81, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\GameSoft\\Unity\\Editor\\2022.3.38f1c1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Threading.Thread.dll"}}, {"pid": 12345, "tid": 1, "ts": 1754736429068850, "dur": 2041, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Mathematics.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 1, "ts": 1754736429070943, "dur": 633039, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1754736428731045, "dur": 19429, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1754736428750482, "dur": 63, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityWebRequestWWWModule.dll_BA3DE9AF0772CF8F.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1754736428750651, "dur": 63, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\GameSoft\\Unity\\Editor\\2022.3.38f1c1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.AndroidJNIModule.dll"}}, {"pid": 12345, "tid": 2, "ts": 1754736428750650, "dur": 66, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.AndroidJNIModule.dll_DF83CFB61A6929CE.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1754736428750739, "dur": 70, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.AndroidJNIModule.dll_DF83CFB61A6929CE.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1754736428750836, "dur": 102, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.StreamingModule.dll_706C4E83917515AE.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1754736428750939, "dur": 82, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.collections@1.2.4\\Unity.Collections.LowLevel.ILSupport\\Unity.Collections.LowLevel.ILSupport.dll"}}, {"pid": 12345, "tid": 2, "ts": 1754736428750939, "dur": 83, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.Collections.LowLevel.ILSupport.dll_2E40CE80E145E521.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1754736428751034, "dur": 165, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.Collections.LowLevel.ILSupport.dll_2E40CE80E145E521.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1754736428751204, "dur": 59, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/UnityEngine.UI.dll.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1754736428752177, "dur": 199, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\GameSoft\\Unity\\Editor\\2022.3.38f1c1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Reflection.Primitives.dll"}}, {"pid": 12345, "tid": 2, "ts": 1754736428752468, "dur": 55, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\GameSoft\\Unity\\Editor\\2022.3.38f1c1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Runtime.InteropServices.dll"}}, {"pid": 12345, "tid": 2, "ts": 1754736428752527, "dur": 545, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\GameSoft\\Unity\\Editor\\2022.3.38f1c1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Runtime.Numerics.dll"}}, {"pid": 12345, "tid": 2, "ts": 1754736428753351, "dur": 75, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\GameSoft\\Unity\\Editor\\2022.3.38f1c1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Xml.XmlDocument.dll"}}, {"pid": 12345, "tid": 2, "ts": 1754736428754215, "dur": 347, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.ugui@1.0.0\\Runtime\\UI\\Core\\SpecializedCollections\\IndexedSet.cs"}}, {"pid": 12345, "tid": 2, "ts": 1754736428754612, "dur": 66, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.ugui@1.0.0\\Runtime\\UI\\Core\\ToggleGroup.cs"}}, {"pid": 12345, "tid": 2, "ts": 1754736428751278, "dur": 3504, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/UnityEngine.UI.dll (+2 others)"}}, {"pid": 12345, "tid": 2, "ts": 1754736428754846, "dur": 192, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1754736428755038, "dur": 183, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1754736428755221, "dur": 236, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1754736428755457, "dur": 314, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1754736428755771, "dur": 216, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1754736428755988, "dur": 186, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1754736428756174, "dur": 172, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1754736428756346, "dur": 222, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1754736428756568, "dur": 198, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1754736428756766, "dur": 240, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1754736428757006, "dur": 218, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1754736428757224, "dur": 264, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1754736428757488, "dur": 175, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1754736428757663, "dur": 219, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1754736428757941, "dur": 270, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1754736428758262, "dur": 434, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1754736428758705, "dur": 58, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/CrashKonijn.Agent.Core.dll.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1754736428758764, "dur": 78, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1754736428758848, "dur": 276, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/CrashKonijn.Agent.Core.dll (+2 others)"}}, {"pid": 12345, "tid": 2, "ts": 1754736428759125, "dur": 75, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1754736428759653, "dur": 113, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.crashkonijn.goap@601802d6e1\\Runtime\\CrashKonijn.Goap.Core\\Interfaces\\IWorldSensorConfig.cs"}}, {"pid": 12345, "tid": 2, "ts": 1754736428759209, "dur": 563, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/CrashKonijn.Goap.Core.dll (+2 others)"}}, {"pid": 12345, "tid": 2, "ts": 1754736428759772, "dur": 55, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1754736428759886, "dur": 167, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.TextMeshPro.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1754736428760072, "dur": 408, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.TextMeshPro.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 2, "ts": 1754736428760560, "dur": 208, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1754736428760769, "dur": 71, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Core.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1754736428760869, "dur": 425, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Core.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 2, "ts": 1754736428761372, "dur": 66, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.ShaderGraph.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1754736428761466, "dur": 905, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.ShaderGraph.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 2, "ts": 1754736428762470, "dur": 320, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Universal.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 2, "ts": 1754736428762879, "dur": 192, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/com.unity.cinemachine.editor.dll (+2 others)"}}, {"pid": 12345, "tid": 2, "ts": 1754736428763251, "dur": 935, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\Bee\\artifacts\\1900b0aE.dag\\Assembly-CSharp.rsp"}}, {"pid": 12345, "tid": 2, "ts": 1754736428763093, "dur": 1094, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Assembly-CSharp.dll (+2 others)"}}, {"pid": 12345, "tid": 2, "ts": 1754736428764246, "dur": 341318, "ph": "X", "name": "Csc", "args": {"detail": "Library/Bee/artifacts/1900b0aE.dag/Assembly-CSharp.dll (+2 others)"}}, {"pid": 12345, "tid": 2, "ts": 1754736429107397, "dur": 6265, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\Bee\\artifacts\\1900b0aE.dag\\Assembly-CSharp.ref.dll"}}, {"pid": 12345, "tid": 2, "ts": 1754736429107152, "dur": 6568, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Assembly-CSharp-Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 2, "ts": 1754736429114023, "dur": 59, "ph": "X", "name": "StoreTimestampsOfNonGeneratedInputFiles", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1754736429114095, "dur": 329140, "ph": "X", "name": "Csc", "args": {"detail": "Library/Bee/artifacts/1900b0aE.dag/Assembly-CSharp-Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 2, "ts": 1754736429444506, "dur": 4005, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\Bee\\artifacts\\1900b0aE.dag\\Assembly-CSharp-Editor.dll"}}, {"pid": 12345, "tid": 2, "ts": 1754736429444504, "dur": 4957, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Assembly-CSharp-Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 2, "ts": 1754736429450266, "dur": 122, "ph": "X", "name": "StoreTimestampsOfNonGeneratedInputFiles", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1754736429450403, "dur": 235513, "ph": "X", "name": "ILPostProcess", "args": {"detail": "Library/Bee/artifacts/1900b0aE.dag/post-processed/Assembly-CSharp-Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 2, "ts": 1754736429691223, "dur": 92, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\Bee\\artifacts\\1900b0aE.dag\\post-processed\\Assembly-CSharp-Editor.pdb"}}, {"pid": 12345, "tid": 2, "ts": 1754736429691221, "dur": 95, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Assembly-CSharp-Editor.pdb"}}, {"pid": 12345, "tid": 2, "ts": 1754736429691333, "dur": 740, "ph": "X", "name": "CopyFiles", "args": {"detail": "Library/ScriptAssemblies/Assembly-CSharp-Editor.pdb"}}, {"pid": 12345, "tid": 2, "ts": 1754736429692077, "dur": 11825, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1754736428731272, "dur": 19358, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1754736428750650, "dur": 91, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\GameSoft\\Unity\\Editor\\2022.3.38f1c1\\Editor\\Data\\Managed\\UnityEngine\\UnityEditor.UIElementsModule.dll"}}, {"pid": 12345, "tid": 3, "ts": 1754736428750639, "dur": 105, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.UIElementsModule.dll_613AE6E2D149BAF8.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1754736428750870, "dur": 124, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1754736428751184, "dur": 134, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/CrashKonijn.Agent.Core.rsp2"}}, {"pid": 12345, "tid": 3, "ts": 1754736428751411, "dur": 73, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.ShaderGraph.Editor.rsp2"}}, {"pid": 12345, "tid": 3, "ts": 1754736428751635, "dur": 64, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Shared.Editor.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 3, "ts": 1754736428751718, "dur": 103, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.SettingsProvider.Editor.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 3, "ts": 1754736428752180, "dur": 138, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1754736428752340, "dur": 54, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1754736428752395, "dur": 184, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/9321109326917711913.rsp"}}, {"pid": 12345, "tid": 3, "ts": 1754736428752580, "dur": 199, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1754736428752780, "dur": 266, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1754736428753046, "dur": 195, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1754736428753241, "dur": 185, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1754736428753426, "dur": 198, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1754736428753624, "dur": 172, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1754736428753797, "dur": 177, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1754736428753974, "dur": 214, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1754736428754188, "dur": 588, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1754736428754776, "dur": 199, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1754736428754975, "dur": 286, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1754736428755262, "dur": 189, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1754736428755452, "dur": 213, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1754736428755665, "dur": 198, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1754736428755863, "dur": 221, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1754736428756084, "dur": 213, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1754736428756297, "dur": 226, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1754736428756524, "dur": 188, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1754736428756712, "dur": 241, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1754736428756954, "dur": 176, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1754736428757130, "dur": 188, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1754736428757318, "dur": 256, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1754736428757574, "dur": 205, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1754736428757779, "dur": 113, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1754736428757938, "dur": 285, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1754736428758223, "dur": 620, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1754736428758844, "dur": 70, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Universal.Shaders.dll.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1754736428758936, "dur": 305, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Universal.Shaders.dll (+2 others)"}}, {"pid": 12345, "tid": 3, "ts": 1754736428759398, "dur": 78, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\GameSoft\\Unity\\Editor\\2022.3.38f1c1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.VRModule.dll"}}, {"pid": 12345, "tid": 3, "ts": 1754736428759288, "dur": 469, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/CrashKonijn.Agent.Runtime.dll (+2 others)"}}, {"pid": 12345, "tid": 3, "ts": 1754736428759757, "dur": 217, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1754736428760142, "dur": 184, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1754736428760344, "dur": 194, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1754736428760543, "dur": 80, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.Collections.BurstCompatibilityGen.dll.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1754736428760650, "dur": 210, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Collections.BurstCompatibilityGen.dll (+2 others)"}}, {"pid": 12345, "tid": 3, "ts": 1754736428760890, "dur": 483, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1754736428761373, "dur": 1463, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1754736428762837, "dur": 65, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Assembly-CSharp-Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1754736428762925, "dur": 300997, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1754736429064058, "dur": 61, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\GameSoft\\Unity\\Editor\\2022.3.38f1c1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.TilemapModule.dll"}}, {"pid": 12345, "tid": 3, "ts": 1754736429064265, "dur": 189, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\GameSoft\\Unity\\Editor\\2022.3.38f1c1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netfx\\System.Xml.Linq.dll"}}, {"pid": 12345, "tid": 3, "ts": 1754736429064816, "dur": 175, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\GameSoft\\Unity\\Editor\\2022.3.38f1c1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Buffers.dll"}}, {"pid": 12345, "tid": 3, "ts": 1754736429064998, "dur": 131, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\GameSoft\\Unity\\Editor\\2022.3.38f1c1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.ComponentModel.EventBasedAsync.dll"}}, {"pid": 12345, "tid": 3, "ts": 1754736429063936, "dur": 2536, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.RenderPipeline.Universal.ShaderLibrary.dll (+pdb)"}}, {"pid": 12345, "tid": 3, "ts": 1754736429066637, "dur": 59, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\GameSoft\\Unity\\Editor\\2022.3.38f1c1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netfx\\System.Web.dll"}}, {"pid": 12345, "tid": 3, "ts": 1754736429067169, "dur": 60, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\GameSoft\\Unity\\Editor\\2022.3.38f1c1\\Editor\\Data\\Tools\\ilpp\\Unity.ILPP.Runner\\Microsoft.AspNetCore.Hosting.dll"}}, {"pid": 12345, "tid": 3, "ts": 1754736429066525, "dur": 1496, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Timeline.dll (+pdb)"}}, {"pid": 12345, "tid": 3, "ts": 1754736429068022, "dur": 559, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1754736429069650, "dur": 129, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\GameSoft\\Unity\\Editor\\2022.3.38f1c1\\Editor\\Data\\Tools\\ilpp\\Unity.ILPP.Runner\\System.ServiceModel.Web.dll"}}, {"pid": 12345, "tid": 3, "ts": 1754736429069895, "dur": 63, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\GameSoft\\Unity\\Editor\\2022.3.38f1c1\\Editor\\Data\\Tools\\ilpp\\Unity.ILPP.Runner\\System.Windows.dll"}}, {"pid": 12345, "tid": 3, "ts": 1754736429070240, "dur": 75, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\GameSoft\\Unity\\Editor\\2022.3.38f1c1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netfx\\System.Windows.dll"}}, {"pid": 12345, "tid": 3, "ts": 1754736429070357, "dur": 58, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\GameSoft\\Unity\\Editor\\2022.3.38f1c1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Drawing.Primitives.dll"}}, {"pid": 12345, "tid": 3, "ts": 1754736429068588, "dur": 2143, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/URPWizard.dll (+pdb)"}}, {"pid": 12345, "tid": 3, "ts": 1754736429070771, "dur": 633247, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1754736428730974, "dur": 19449, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1754736428750432, "dur": 3900, "ph": "X", "name": "CheckDagSignatures", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1754736428754333, "dur": 560, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1754736428754894, "dur": 265, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1754736428755159, "dur": 193, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1754736428755352, "dur": 240, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1754736428755592, "dur": 232, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1754736428755824, "dur": 185, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1754736428756009, "dur": 173, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1754736428756182, "dur": 188, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1754736428756370, "dur": 205, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1754736428756576, "dur": 196, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1754736428756772, "dur": 282, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1754736428757055, "dur": 200, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1754736428757255, "dur": 200, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1754736428757456, "dur": 186, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1754736428757642, "dur": 233, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1754736428757876, "dur": 73, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1754736428757949, "dur": 263, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1754736428758212, "dur": 614, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1754736428758833, "dur": 94, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Core.ShaderLibrary.dll.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1754736428758942, "dur": 252, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Core.ShaderLibrary.dll (+2 others)"}}, {"pid": 12345, "tid": 4, "ts": 1754736428759194, "dur": 183, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1754736428759389, "dur": 190, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1754736428759984, "dur": 79, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\GameSoft\\Unity\\Editor\\2022.3.38f1c1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Xml.XPath.XDocument.dll"}}, {"pid": 12345, "tid": 4, "ts": 1754736428759586, "dur": 511, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/GlassSystem.dll (+2 others)"}}, {"pid": 12345, "tid": 4, "ts": 1754736428760098, "dur": 281, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1754736428760384, "dur": 156, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1754736428760540, "dur": 80, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.Collections.DocCodeSamples.dll.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1754736428760656, "dur": 242, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Collections.DocCodeSamples.dll (+2 others)"}}, {"pid": 12345, "tid": 4, "ts": 1754736428760935, "dur": 442, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1754736428761378, "dur": 302657, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1754736429064036, "dur": 1627, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.RenderPipelines.Universal.Shaders.dll (+pdb)"}}, {"pid": 12345, "tid": 4, "ts": 1754736429065664, "dur": 126, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1754736429065796, "dur": 1334, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Cinemachine.dll (+pdb)"}}, {"pid": 12345, "tid": 4, "ts": 1754736429067165, "dur": 1552, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.PlasticSCM.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 4, "ts": 1754736429069896, "dur": 73, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\GameSoft\\Unity\\Editor\\2022.3.38f1c1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.CrashReportingModule.dll"}}, {"pid": 12345, "tid": 4, "ts": 1754736429069981, "dur": 156, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\GameSoft\\Unity\\Editor\\2022.3.38f1c1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.JSONSerializeModule.dll"}}, {"pid": 12345, "tid": 4, "ts": 1754736429068757, "dur": 1894, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.VisualScripting.Flow.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 4, "ts": 1754736429070720, "dur": 217, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1754736429070963, "dur": 632970, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1754736428731141, "dur": 19402, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1754736428750574, "dur": 96, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\GameSoft\\Unity\\Editor\\2022.3.38f1c1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.WindModule.dll"}}, {"pid": 12345, "tid": 5, "ts": 1754736428750558, "dur": 115, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.WindModule.dll_504B268BBB0FC6D0.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1754736428750675, "dur": 58, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1754736428750842, "dur": 66, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.SubsystemsModule.dll_5DAAF2C2D061984C.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1754736428750911, "dur": 94, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\GameSoft\\Unity\\Editor\\2022.3.38f1c1\\Editor\\Data\\PlaybackEngines\\WindowsStandaloneSupport\\UnityEditor.WindowsStandalone.Extensions.dll"}}, {"pid": 12345, "tid": 5, "ts": 1754736428750909, "dur": 98, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.WindowsStandalone.Extensions.dll_35385077F0CEC424.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1754736428751192, "dur": 148, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Flow.rsp2"}}, {"pid": 12345, "tid": 5, "ts": 1754736428751351, "dur": 161, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.State.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 5, "ts": 1754736428751596, "dur": 72, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Rider.Editor.rsp2"}}, {"pid": 12345, "tid": 5, "ts": 1754736428751784, "dur": 77, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/com.crashkonijn.goap.demos.complex.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 5, "ts": 1754736428751937, "dur": 70, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Assembly-CSharp-Editor.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 5, "ts": 1754736428752072, "dur": 55, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/13686715218353603589.rsp"}}, {"pid": 12345, "tid": 5, "ts": 1754736428752181, "dur": 206, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1754736428752387, "dur": 180, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/3388064732626934676.rsp"}}, {"pid": 12345, "tid": 5, "ts": 1754736428752568, "dur": 368, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1754736428752936, "dur": 195, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1754736428753131, "dur": 513, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1754736428753644, "dur": 232, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1754736428753876, "dur": 211, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1754736428754088, "dur": 591, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1754736428754679, "dur": 224, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1754736428754904, "dur": 208, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1754736428755112, "dur": 177, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1754736428755327, "dur": 337, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1754736428755664, "dur": 164, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1754736428755828, "dur": 430, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1754736428756258, "dur": 211, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1754736428756470, "dur": 202, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1754736428756673, "dur": 299, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1754736428756972, "dur": 169, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1754736428757141, "dur": 220, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1754736428757361, "dur": 194, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1754736428757555, "dur": 167, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1754736428757723, "dur": 54, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1754736428757777, "dur": 190, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1754736428757967, "dur": 260, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1754736428758227, "dur": 517, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1754736428758754, "dur": 95, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/CrashKonijn.Agent.Runtime.dll.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1754736428758850, "dur": 75, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1754736428758929, "dur": 107, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.ShaderGraph.ShaderGraphLibrary.dll.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1754736428759037, "dur": 70, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1754736428759183, "dur": 52, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\GameSoft\\Unity\\Editor\\2022.3.38f1c1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.Physics2DModule.dll"}}, {"pid": 12345, "tid": 5, "ts": 1754736428759112, "dur": 431, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.ShaderGraph.ShaderGraphLibrary.dll (+2 others)"}}, {"pid": 12345, "tid": 5, "ts": 1754736428759543, "dur": 273, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1754736428759827, "dur": 175, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/CrashKonijn.Goap.Runtime.dll.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1754736428760002, "dur": 126, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1754736428760152, "dur": 404, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1754736428760556, "dur": 216, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1754736428760773, "dur": 590, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1754736428761363, "dur": 272, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/com.crashkonijn.docs.getting_started.dll (+2 others)"}}, {"pid": 12345, "tid": 5, "ts": 1754736428761675, "dur": 303025, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1754736429065395, "dur": 73, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\GameSoft\\Unity\\Editor\\2022.3.38f1c1\\Editor\\Data\\Tools\\ilpp\\Unity.ILPP.Runner\\Microsoft.AspNetCore.Mvc.DataAnnotations.dll"}}, {"pid": 12345, "tid": 5, "ts": 1754736429064702, "dur": 1609, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/CrashKonijn.Goap.Core.dll (+pdb)"}}, {"pid": 12345, "tid": 5, "ts": 1754736429066685, "dur": 97, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\GameSoft\\Unity\\Editor\\2022.3.38f1c1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Reflection.Primitives.dll"}}, {"pid": 12345, "tid": 5, "ts": 1754736429066347, "dur": 1423, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/GlassSystem.dll (+pdb)"}}, {"pid": 12345, "tid": 5, "ts": 1754736429067770, "dur": 157, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1754736429068651, "dur": 71, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\GameSoft\\Unity\\Editor\\2022.3.38f1c1\\Editor\\Data\\Tools\\ilpp\\Unity.ILPP.Runner\\mscordbi.dll"}}, {"pid": 12345, "tid": 5, "ts": 1754736429067934, "dur": 1517, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.VisualScripting.State.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 5, "ts": 1754736429069727, "dur": 115, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1754736429069886, "dur": 170, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1754736429070119, "dur": 108, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\Bee\\artifacts\\1900b0aE.dag\\post-processed\\UnityEngine.UI.pdb"}}, {"pid": 12345, "tid": 5, "ts": 1754736429070119, "dur": 109, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "CopyFiles Library/ScriptAssemblies/UnityEngine.UI.pdb"}}, {"pid": 12345, "tid": 5, "ts": 1754736429070263, "dur": 121, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1754736429070409, "dur": 144, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1754736429070556, "dur": 73, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1754736429070653, "dur": 54, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1754736429070744, "dur": 633147, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1754736428731003, "dur": 19447, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1754736428750656, "dur": 76, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\GameSoft\\Unity\\Editor\\2022.3.38f1c1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.ARModule.dll"}}, {"pid": 12345, "tid": 6, "ts": 1754736428750655, "dur": 79, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ARModule.dll_969BEA2E4A09BAD9.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1754736428750913, "dur": 93, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\GameSoft\\Unity\\Editor\\2022.3.38f1c1\\Editor\\Data\\Managed\\UnityEditor.Graphs.dll"}}, {"pid": 12345, "tid": 6, "ts": 1754736428750912, "dur": 95, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.Graphs.dll_34C604C212D62B6E.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1754736428751034, "dur": 102, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.Graphs.dll_34C604C212D62B6E.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1754736428751151, "dur": 54, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/UnityEngine.TestRunner.dll.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1754736428751217, "dur": 72, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/UnityEngine.TestRunner.dll.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1754736428752174, "dur": 132, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\GameSoft\\Unity\\Editor\\2022.3.38f1c1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Reflection.Primitives.dll"}}, {"pid": 12345, "tid": 6, "ts": 1754736428752307, "dur": 98, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\GameSoft\\Unity\\Editor\\2022.3.38f1c1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Resources.Reader.dll"}}, {"pid": 12345, "tid": 6, "ts": 1754736428752514, "dur": 555, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\GameSoft\\Unity\\Editor\\2022.3.38f1c1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Runtime.Numerics.dll"}}, {"pid": 12345, "tid": 6, "ts": 1754736428754216, "dur": 331, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.test-framework@1.1.33\\UnityEngine.TestRunner\\TestRunner\\PlaymodeTestsControllerSettings.cs"}}, {"pid": 12345, "tid": 6, "ts": 1754736428754548, "dur": 207, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.test-framework@1.1.33\\UnityEngine.TestRunner\\TestRunner\\RemoteHelpers\\IRemoteTestResultDataFactory.cs"}}, {"pid": 12345, "tid": 6, "ts": 1754736428751292, "dur": 3947, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/UnityEngine.TestRunner.dll (+2 others)"}}, {"pid": 12345, "tid": 6, "ts": 1754736428755293, "dur": 139, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/UnityEditor.TestRunner.dll.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1754736428755498, "dur": 57, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\GameSoft\\Unity\\Editor\\2022.3.38f1c1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.AccessibilityModule.dll"}}, {"pid": 12345, "tid": 6, "ts": 1754736428757323, "dur": 54, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.test-framework@1.1.33\\UnityEditor.TestRunner\\Api\\ITestRunnerApi.cs"}}, {"pid": 12345, "tid": 6, "ts": 1754736428757387, "dur": 67, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.test-framework@1.1.33\\UnityEditor.TestRunner\\Api\\ITestTreeRebuildCallbacks.cs"}}, {"pid": 12345, "tid": 6, "ts": 1754736428755444, "dur": 2711, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/UnityEditor.TestRunner.dll (+2 others)"}}, {"pid": 12345, "tid": 6, "ts": 1754736428758313, "dur": 259, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/UnityEditor.UI.dll (+2 others)"}}, {"pid": 12345, "tid": 6, "ts": 1754736428758721, "dur": 355, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Burst.dll (+2 others)"}}, {"pid": 12345, "tid": 6, "ts": 1754736428759077, "dur": 98, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1754736428759204, "dur": 77, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.Burst.CodeGen.dll.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1754736428759468, "dur": 154, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\GameSoft\\Unity\\Editor\\2022.3.38f1c1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Console.dll"}}, {"pid": 12345, "tid": 6, "ts": 1754736428759822, "dur": 86, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\GameSoft\\Unity\\Editor\\2022.3.38f1c1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\System.Xml.Linq.dll"}}, {"pid": 12345, "tid": 6, "ts": 1754736428759293, "dur": 653, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Burst.CodeGen.dll (+2 others)"}}, {"pid": 12345, "tid": 6, "ts": 1754736428759947, "dur": 105, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1754736428760062, "dur": 108, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Core.dll.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1754736428760205, "dur": 806, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Core.dll (+2 others)"}}, {"pid": 12345, "tid": 6, "ts": 1754736428761118, "dur": 676, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Flow.dll (+2 others)"}}, {"pid": 12345, "tid": 6, "ts": 1754736428761927, "dur": 183, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.State.dll (+2 others)"}}, {"pid": 12345, "tid": 6, "ts": 1754736428762162, "dur": 301781, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1754736429063945, "dur": 1815, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/CrashKonijn.Agent.Core.dll (+pdb)"}}, {"pid": 12345, "tid": 6, "ts": 1754736429065806, "dur": 1484, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Burst.dll (+pdb)"}}, {"pid": 12345, "tid": 6, "ts": 1754736429067291, "dur": 752, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1754736429068050, "dur": 1650, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.ShaderGraph.Utilities.dll (+pdb)"}}, {"pid": 12345, "tid": 6, "ts": 1754736429069700, "dur": 105, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1754736429069854, "dur": 115, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1754736429069978, "dur": 85, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1754736429070242, "dur": 61, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\Bee\\artifacts\\1900b0aE.dag\\post-processed\\Unity.VisualScripting.Flow.pdb"}}, {"pid": 12345, "tid": 6, "ts": 1754736429070242, "dur": 62, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.VisualScripting.Flow.pdb"}}, {"pid": 12345, "tid": 6, "ts": 1754736429070431, "dur": 293, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1754736429070724, "dur": 404582, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1754736429475309, "dur": 200, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\Bee\\artifacts\\1900b0aE.dag\\post-processed\\Assembly-CSharp.pdb"}}, {"pid": 12345, "tid": 6, "ts": 1754736429475308, "dur": 202, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Assembly-CSharp.pdb"}}, {"pid": 12345, "tid": 6, "ts": 1754736429475551, "dur": 1967, "ph": "X", "name": "CopyFiles", "args": {"detail": "Library/ScriptAssemblies/Assembly-CSharp.pdb"}}, {"pid": 12345, "tid": 6, "ts": 1754736429477521, "dur": 226399, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1754736428730989, "dur": 19447, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1754736428750624, "dur": 57, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1754736428750897, "dur": 105, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1754736428751063, "dur": 93, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.VisualScripting.TextureAssets.dll_E1772EF4FEB70289.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1754736428751174, "dur": 127, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Mathematics.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 7, "ts": 1754736428751538, "dur": 58, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.TextMeshPro.rsp"}}, {"pid": 12345, "tid": 7, "ts": 1754736428751971, "dur": 76, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Collections.BurstCompatibilityGen.rsp"}}, {"pid": 12345, "tid": 7, "ts": 1754736428752073, "dur": 75, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/8899139255040401798.rsp"}}, {"pid": 12345, "tid": 7, "ts": 1754736428752196, "dur": 251, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1754736428752453, "dur": 242, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1754736428752696, "dur": 232, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1754736428752928, "dur": 456, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1754736428753385, "dur": 142, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1754736428753527, "dur": 212, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1754736428753739, "dur": 165, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1754736428753904, "dur": 171, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1754736428754076, "dur": 551, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1754736428754627, "dur": 206, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1754736428754923, "dur": 253, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1754736428755176, "dur": 202, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1754736428755378, "dur": 237, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1754736428755615, "dur": 167, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1754736428755782, "dur": 248, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1754736428756030, "dur": 183, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1754736428756213, "dur": 220, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1754736428756433, "dur": 175, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1754736428756608, "dur": 186, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1754736428756794, "dur": 307, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1754736428757101, "dur": 183, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1754736428757284, "dur": 204, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1754736428757488, "dur": 251, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1754736428757739, "dur": 347, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1754736428758086, "dur": 140, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1754736428758226, "dur": 491, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1754736428758722, "dur": 55, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.Searcher.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1754736428758796, "dur": 422, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Searcher.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 7, "ts": 1754736428759218, "dur": 597, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1754736428759843, "dur": 71, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\Bee\\artifacts\\1900b0aE.dag\\Unity.Searcher.Editor.ref.dll"}}, {"pid": 12345, "tid": 7, "ts": 1754736428759842, "dur": 73, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.Searcher.Editor.ref.dll_12B7E1785E41BE0E.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1754736428759934, "dur": 91, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/com.crashkonijn.goap.demos.simple.dll.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1754736428760096, "dur": 93, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.InputSystem.TestFramework.dll.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1754736428760190, "dur": 131, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1754736428760328, "dur": 263, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.InputSystem.TestFramework.dll (+2 others)"}}, {"pid": 12345, "tid": 7, "ts": 1754736428760622, "dur": 183, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1754736428760805, "dur": 560, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1754736428761366, "dur": 330, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/com.crashkonijn.goap.demos.complex.dll (+2 others)"}}, {"pid": 12345, "tid": 7, "ts": 1754736428761728, "dur": 302209, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1754736429063938, "dur": 1431, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/CrashKonijn.Goap.Runtime.dll (+pdb)"}}, {"pid": 12345, "tid": 7, "ts": 1754736429065370, "dur": 317, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1754736429065695, "dur": 1363, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.InputSystem.dll (+pdb)"}}, {"pid": 12345, "tid": 7, "ts": 1754736429067059, "dur": 140, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1754736429067206, "dur": 1376, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.CollabProxy.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 7, "ts": 1754736429069696, "dur": 109, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\GameSoft\\Unity\\Editor\\2022.3.38f1c1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.ARModule.dll"}}, {"pid": 12345, "tid": 7, "ts": 1754736429070125, "dur": 60, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\GameSoft\\Unity\\Editor\\2022.3.38f1c1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Xml.XmlSerializer.dll"}}, {"pid": 12345, "tid": 7, "ts": 1754736429070192, "dur": 54, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\GameSoft\\Unity\\Editor\\2022.3.38f1c1\\Editor\\Data\\PlaybackEngines\\WindowsStandaloneSupport\\UnityEditor.WindowsStandalone.Extensions.dll"}}, {"pid": 12345, "tid": 7, "ts": 1754736429070247, "dur": 130, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.ext.nunit@1.0.6\\net35\\unity-custom\\nunit.framework.dll"}}, {"pid": 12345, "tid": 7, "ts": 1754736429068614, "dur": 2054, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Collections.DocCodeSamples.dll (+pdb)"}}, {"pid": 12345, "tid": 7, "ts": 1754736429070742, "dur": 633148, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1754736428731029, "dur": 19432, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1754736428750482, "dur": 73, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\GameSoft\\Unity\\Editor\\2022.3.38f1c1\\Editor\\Data\\Managed\\UnityEngine\\UnityEditor.UIElementsSamplesModule.dll"}}, {"pid": 12345, "tid": 8, "ts": 1754736428750469, "dur": 89, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.UIElementsSamplesModule.dll_623E7535F4BD3DF5.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1754736428750715, "dur": 81, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.AssetBundleModule.dll_022E81689B98A39E.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1754736428750858, "dur": 75, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.TextRenderingModule.dll_EE72F1C640402F2B.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1754736428750935, "dur": 88, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.collab-proxy@2.8.2\\Lib\\Editor\\Unity.Plastic.Newtonsoft.Json.dll"}}, {"pid": 12345, "tid": 8, "ts": 1754736428750934, "dur": 91, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.Plastic.Newtonsoft.Json.dll_CF17D4CA2A31CD8C.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1754736428751116, "dur": 72, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Core.Runtime.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 8, "ts": 1754736428751265, "dur": 147, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Universal.Config.Runtime.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 8, "ts": 1754736428751492, "dur": 60, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.ShaderGraph.Editor.rsp"}}, {"pid": 12345, "tid": 8, "ts": 1754736428751564, "dur": 258, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.ShaderGraph.Editor.rsp"}}, {"pid": 12345, "tid": 8, "ts": 1754736428751902, "dur": 172, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.CollabProxy.Editor.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 8, "ts": 1754736428752194, "dur": 216, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1754736428752411, "dur": 183, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/9703144790800738880.rsp"}}, {"pid": 12345, "tid": 8, "ts": 1754736428752595, "dur": 231, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1754736428752826, "dur": 238, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1754736428753064, "dur": 173, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1754736428753429, "dur": 515, "ph": "X", "name": "File", "args": {"detail": "D:\\GameSoft\\Unity\\Editor\\2022.3.38f1c1\\Editor\\Data\\Tools\\ilpp\\Unity.ILPP.Runner\\Microsoft.Extensions.Configuration.KeyPerFile.dll"}}, {"pid": 12345, "tid": 8, "ts": 1754736428753237, "dur": 934, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1754736428754172, "dur": 530, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1754736428754702, "dur": 311, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1754736428755013, "dur": 192, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1754736428755205, "dur": 195, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1754736428755401, "dur": 253, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1754736428755654, "dur": 197, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1754736428755851, "dur": 447, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1754736428756298, "dur": 230, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1754736428756528, "dur": 187, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1754736428756812, "dur": 579, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.render-pipelines.core@14.0.11\\Runtime\\Lighting\\ProbeVolume\\ProbeVolumeSceneData.cs"}}, {"pid": 12345, "tid": 8, "ts": 1754736428756715, "dur": 760, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1754736428757475, "dur": 271, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1754736428757746, "dur": 563, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1754736428758309, "dur": 514, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1754736428758824, "dur": 571, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.Rider.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1754736428759467, "dur": 160, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\GameSoft\\Unity\\Editor\\2022.3.38f1c1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.ClothModule.dll"}}, {"pid": 12345, "tid": 8, "ts": 1754736428759423, "dur": 602, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Rider.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 8, "ts": 1754736428760067, "dur": 95, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.InputSystem.ForUI.dll.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1754736428760176, "dur": 273, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.InputSystem.ForUI.dll (+2 others)"}}, {"pid": 12345, "tid": 8, "ts": 1754736428760547, "dur": 220, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1754736428760768, "dur": 53, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Universal.Config.Runtime.dll.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1754736428760834, "dur": 170, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Universal.Config.Runtime.dll (+2 others)"}}, {"pid": 12345, "tid": 8, "ts": 1754736428761043, "dur": 331, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1754736428761374, "dur": 302544, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1754736429063920, "dur": 1434, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Tests.dll (+pdb)"}}, {"pid": 12345, "tid": 8, "ts": 1754736429065355, "dur": 76, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1754736429065432, "dur": 78, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Tests.dll (+pdb)"}}, {"pid": 12345, "tid": 8, "ts": 1754736429065513, "dur": 1591, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/com.crashkonijn.docs.getting_started.dll (+pdb)"}}, {"pid": 12345, "tid": 8, "ts": 1754736429067141, "dur": 1452, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.RenderPipelines.Universal.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 8, "ts": 1754736429069797, "dur": 195, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\GameSoft\\Unity\\Editor\\2022.3.38f1c1\\Editor\\Data\\Tools\\ilpp\\Unity.ILPP.Runner\\System.IO.FileSystem.DriveInfo.dll"}}, {"pid": 12345, "tid": 8, "ts": 1754736429070125, "dur": 127, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\GameSoft\\Unity\\Editor\\2022.3.38f1c1\\Editor\\Data\\Tools\\ilpp\\Unity.ILPP.Runner\\System.Xml.dll"}}, {"pid": 12345, "tid": 8, "ts": 1754736429070780, "dur": 80, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\GameSoft\\Unity\\Editor\\2022.3.38f1c1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Runtime.Serialization.Xml.dll"}}, {"pid": 12345, "tid": 8, "ts": 1754736429068632, "dur": 2276, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/com.unity.cinemachine.editor.dll (+pdb)"}}, {"pid": 12345, "tid": 8, "ts": 1754736429070946, "dur": 633017, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1754736428731110, "dur": 19420, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1754736428750881, "dur": 108, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\GameSoft\\Unity\\Editor\\2022.3.38f1c1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.UnityWebRequestAudioModule.dll"}}, {"pid": 12345, "tid": 9, "ts": 1754736428750880, "dur": 110, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityWebRequestAudioModule.dll_180837F1985FCEBE.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1754736428751067, "dur": 87, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.NVIDIAModule.dll_3B326C6243819FB3.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1754736428751429, "dur": 145, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.TextMeshPro.rsp2"}}, {"pid": 12345, "tid": 9, "ts": 1754736428751678, "dur": 87, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Rider.Editor.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 9, "ts": 1754736428751920, "dur": 75, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Assembly-CSharp-Editor.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 9, "ts": 1754736428752185, "dur": 192, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1754736428752382, "dur": 236, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1754736428752618, "dur": 162, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1754736428752780, "dur": 171, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1754736428752951, "dur": 221, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1754736428753172, "dur": 166, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1754736428753338, "dur": 240, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1754736428753578, "dur": 190, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1754736428753769, "dur": 160, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1754736428753929, "dur": 161, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1754736428754090, "dur": 537, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1754736428754627, "dur": 183, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1754736428754810, "dur": 226, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1754736428755037, "dur": 195, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1754736428755232, "dur": 200, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1754736428755433, "dur": 700, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1754736428756133, "dur": 213, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1754736428756365, "dur": 660, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.visualscripting@1.9.4\\Runtime\\VisualScripting.Core\\Reflection\\Optimization\\ReflectionFieldAccessor.cs"}}, {"pid": 12345, "tid": 9, "ts": 1754736428756346, "dur": 861, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1754736428757208, "dur": 225, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1754736428757434, "dur": 235, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1754736428757669, "dur": 214, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1754736428757904, "dur": 81, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1754736428757986, "dur": 245, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1754736428758231, "dur": 477, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1754736428758714, "dur": 54, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/CrashKonijn.Goap.Core.dll.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1754736428758800, "dur": 123, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.VisualStudio.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1754736428758924, "dur": 481, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1754736428759409, "dur": 468, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.VisualStudio.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 9, "ts": 1754736428759877, "dur": 54, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1754736428759957, "dur": 117, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/com.crashkonijn.goap.demos.complex.dll.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1754736428760106, "dur": 275, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Tests.dll (+2 others)"}}, {"pid": 12345, "tid": 9, "ts": 1754736428760381, "dur": 300, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1754736428760685, "dur": 99, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1754736428760784, "dur": 597, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1754736428761381, "dur": 302547, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1754736429063930, "dur": 1423, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/com.crashkonijn.goap.demos.simple.dll (+pdb)"}}, {"pid": 12345, "tid": 9, "ts": 1754736429065354, "dur": 76, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1754736429065430, "dur": 58, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/com.crashkonijn.goap.demos.simple.dll (+pdb)"}}, {"pid": 12345, "tid": 9, "ts": 1754736429065491, "dur": 1751, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.RenderPipelines.Core.Runtime.dll (+pdb)"}}, {"pid": 12345, "tid": 9, "ts": 1754736429067242, "dur": 221, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1754736429067468, "dur": 1413, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Searcher.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 9, "ts": 1754736429068881, "dur": 316, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1754736429069411, "dur": 57, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1754736429069714, "dur": 80, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1754736429069799, "dur": 53, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\Bee\\artifacts\\1900b0aE.dag\\post-processed\\Unity.TextMeshPro.Editor.pdb"}}, {"pid": 12345, "tid": 9, "ts": 1754736429069798, "dur": 54, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.TextMeshPro.Editor.pdb"}}, {"pid": 12345, "tid": 9, "ts": 1754736429070259, "dur": 105, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1754736429070420, "dur": 251, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1754736429070671, "dur": 108, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1754736429070780, "dur": 79, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\Bee\\artifacts\\1900b0aE.dag\\post-processed\\UnityEditor.UI.dll"}}, {"pid": 12345, "tid": 9, "ts": 1754736429070780, "dur": 79, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "CopyFiles Library/ScriptAssemblies/UnityEditor.UI.dll"}}, {"pid": 12345, "tid": 9, "ts": 1754736429070870, "dur": 633134, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1754736428731096, "dur": 19419, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1754736428750617, "dur": 61, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.VirtualTexturingModule.dll_38C1DD3B59418845.mvfrm"}}, {"pid": 12345, "tid": 10, "ts": 1754736428750755, "dur": 119, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.DSPGraphModule.dll_4190EB41F081C965.mvfrm"}}, {"pid": 12345, "tid": 10, "ts": 1754736428750877, "dur": 98, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\GameSoft\\Unity\\Editor\\2022.3.38f1c1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.UnityTestProtocolModule.dll"}}, {"pid": 12345, "tid": 10, "ts": 1754736428750876, "dur": 135, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityTestProtocolModule.dll_1E012B284EB8934B.mvfrm"}}, {"pid": 12345, "tid": 10, "ts": 1754736428751036, "dur": 131, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/UnityEditor.TestRunner.rsp2"}}, {"pid": 12345, "tid": 10, "ts": 1754736428751202, "dur": 120, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Mathematics.rsp"}}, {"pid": 12345, "tid": 10, "ts": 1754736428751335, "dur": 62, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/CrashKonijn.Agent.Runtime.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 10, "ts": 1754736428752648, "dur": 1148, "ph": "X", "name": "WriteText", "args": {"detail": "Library/Bee/artifacts/1900b0aE.dag/Assembly-CSharp.rsp"}}, {"pid": 12345, "tid": 10, "ts": 1754736428753797, "dur": 389, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1754736428754187, "dur": 712, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1754736428754899, "dur": 250, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1754736428755149, "dur": 196, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1754736428755345, "dur": 217, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1754736428755563, "dur": 294, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1754736428755858, "dur": 217, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1754736428756076, "dur": 176, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1754736428756252, "dur": 222, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1754736428756475, "dur": 298, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1754736428756773, "dur": 349, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1754736428757122, "dur": 188, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1754736428757310, "dur": 204, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1754736428757514, "dur": 298, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1754736428757940, "dur": 278, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1754736428758218, "dur": 546, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1754736428758769, "dur": 115, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.TextMeshPro.dll.mvfrm"}}, {"pid": 12345, "tid": 10, "ts": 1754736428759307, "dur": 189, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\GameSoft\\Unity\\Editor\\2022.3.38f1c1\\Editor\\Data\\Tools\\Unity.SourceGenerators\\Unity.Properties.SourceGenerator.dll"}}, {"pid": 12345, "tid": 10, "ts": 1754736428758911, "dur": 588, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.TextMeshPro.dll (+2 others)"}}, {"pid": 12345, "tid": 10, "ts": 1754736428759500, "dur": 314, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1754736428759819, "dur": 161, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.Mathematics.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 10, "ts": 1754736428759993, "dur": 304, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Mathematics.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 10, "ts": 1754736428760380, "dur": 171, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1754736428760551, "dur": 219, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1754736428760770, "dur": 308, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1754736428761148, "dur": 663, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Core.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 10, "ts": 1754736428761812, "dur": 52, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1754736428761882, "dur": 52, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Flow.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 10, "ts": 1754736428761943, "dur": 303, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Flow.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 10, "ts": 1754736428762339, "dur": 211, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.State.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 10, "ts": 1754736428762640, "dur": 146, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Shared.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 10, "ts": 1754736428762882, "dur": 157, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/PPv2URPConverters.dll (+2 others)"}}, {"pid": 12345, "tid": 10, "ts": 1754736428763061, "dur": 300859, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1754736429063921, "dur": 1638, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.RenderPipelines.Universal.Config.Runtime.dll (+pdb)"}}, {"pid": 12345, "tid": 10, "ts": 1754736429065560, "dur": 175, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1754736429065741, "dur": 1409, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.VisualScripting.Core.dll (+pdb)"}}, {"pid": 12345, "tid": 10, "ts": 1754736429067150, "dur": 50, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1754736429067209, "dur": 1409, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Burst.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 10, "ts": 1754736429068618, "dur": 83, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1754736429069617, "dur": 59, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\GameSoft\\Unity\\Editor\\2022.3.38f1c1\\Editor\\Data\\Tools\\ilpp\\Unity.ILPP.Runner\\System.Diagnostics.Process.dll"}}, {"pid": 12345, "tid": 10, "ts": 1754736429069981, "dur": 162, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\GameSoft\\Unity\\Editor\\2022.3.38f1c1\\Editor\\Data\\Tools\\ilpp\\Unity.ILPP.Runner\\WindowsBase.dll"}}, {"pid": 12345, "tid": 10, "ts": 1754736429068708, "dur": 2050, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/UnityEditor.UI.dll (+pdb)"}}, {"pid": 12345, "tid": 10, "ts": 1754736429070786, "dur": 633266, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1754736428731179, "dur": 19394, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1754736428750729, "dur": 50, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ContentLoadModule.dll_9B44DAA96A1B4B8B.mvfrm"}}, {"pid": 12345, "tid": 11, "ts": 1754736428750895, "dur": 108, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1754736428751158, "dur": 68, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.Burst.Cecil.Rocks.dll_8573C5AEBE7FAC0E.mvfrm"}}, {"pid": 12345, "tid": 11, "ts": 1754736428751415, "dur": 131, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Mathematics.Editor.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 11, "ts": 1754736428751646, "dur": 65, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.TextMeshPro.Editor.rsp2"}}, {"pid": 12345, "tid": 11, "ts": 1754736428751901, "dur": 53, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Tests.rsp2"}}, {"pid": 12345, "tid": 11, "ts": 1754736428752012, "dur": 50, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/9870516708743257357.rsp"}}, {"pid": 12345, "tid": 11, "ts": 1754736428752159, "dur": 196, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1754736428752363, "dur": 504, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1754736428752867, "dur": 224, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1754736428753091, "dur": 160, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1754736428753251, "dur": 737, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1754736428753989, "dur": 210, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1754736428754199, "dur": 533, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1754736428754732, "dur": 288, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1754736428755021, "dur": 196, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1754736428755217, "dur": 220, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1754736428755437, "dur": 239, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1754736428755676, "dur": 169, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1754736428755845, "dur": 228, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1754736428756073, "dur": 195, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1754736428756268, "dur": 245, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1754736428756513, "dur": 295, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1754736428756809, "dur": 281, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1754736428757090, "dur": 179, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1754736428757269, "dur": 220, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1754736428757490, "dur": 172, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1754736428757662, "dur": 407, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1754736428758069, "dur": 152, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1754736428758221, "dur": 535, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1754736428758761, "dur": 332, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.Timeline.dll.mvfrm"}}, {"pid": 12345, "tid": 11, "ts": 1754736428759188, "dur": 118, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\GameSoft\\Unity\\Editor\\2022.3.38f1c1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.StreamingModule.dll"}}, {"pid": 12345, "tid": 11, "ts": 1754736428759397, "dur": 50, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\GameSoft\\Unity\\Editor\\2022.3.38f1c1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netfx\\System.Xml.dll"}}, {"pid": 12345, "tid": 11, "ts": 1754736428759119, "dur": 622, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Timeline.dll (+2 others)"}}, {"pid": 12345, "tid": 11, "ts": 1754736428759852, "dur": 111, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.Timeline.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 11, "ts": 1754736428760007, "dur": 673, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Timeline.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 11, "ts": 1754736428760777, "dur": 589, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1754736428761367, "dur": 164, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1754736428761532, "dur": 63, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/URPWizard.dll.mvfrm"}}, {"pid": 12345, "tid": 11, "ts": 1754736428761622, "dur": 231, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/URPWizard.dll (+2 others)"}}, {"pid": 12345, "tid": 11, "ts": 1754736428761895, "dur": 302058, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1754736429063955, "dur": 1421, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.InputSystem.ForUI.dll (+pdb)"}}, {"pid": 12345, "tid": 11, "ts": 1754736429065376, "dur": 287, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1754736429065725, "dur": 81, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\GameSoft\\Unity\\Editor\\2022.3.38f1c1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.AssetBundleModule.dll"}}, {"pid": 12345, "tid": 11, "ts": 1754736429066575, "dur": 61, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\GameSoft\\Unity\\Editor\\2022.3.38f1c1\\Editor\\Data\\Tools\\ilpp\\Unity.ILPP.Runner\\System.Runtime.Intrinsics.dll"}}, {"pid": 12345, "tid": 11, "ts": 1754736429065671, "dur": 1662, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.InputSystem.TestFramework.dll (+pdb)"}}, {"pid": 12345, "tid": 11, "ts": 1754736429067372, "dur": 1371, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.VisualScripting.Core.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 11, "ts": 1754736429069796, "dur": 121, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\GameSoft\\Unity\\Editor\\2022.3.38f1c1\\Editor\\Data\\Tools\\ilpp\\Unity.ILPP.Runner\\System.Security.dll"}}, {"pid": 12345, "tid": 11, "ts": 1754736429069952, "dur": 68, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\GameSoft\\Unity\\Editor\\2022.3.38f1c1\\Editor\\Data\\Tools\\ilpp\\Unity.ILPP.Runner\\System.Threading.Tasks.dll"}}, {"pid": 12345, "tid": 11, "ts": 1754736429070240, "dur": 63, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\GameSoft\\Unity\\Editor\\2022.3.38f1c1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.IO.MemoryMappedFiles.dll"}}, {"pid": 12345, "tid": 11, "ts": 1754736429068768, "dur": 1826, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.ShaderGraph.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 11, "ts": 1754736429070723, "dur": 36439, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1754736429107165, "dur": 79850, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\Bee\\artifacts\\1900b0aE.dag\\Assembly-CSharp.dll"}}, {"pid": 12345, "tid": 11, "ts": 1754736429107164, "dur": 81131, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Assembly-CSharp.dll (+pdb)"}}, {"pid": 12345, "tid": 11, "ts": 1754736429189216, "dur": 126, "ph": "X", "name": "StoreTimestampsOfNonGeneratedInputFiles", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1754736429189401, "dur": 280512, "ph": "X", "name": "ILPostProcess", "args": {"detail": "Library/Bee/artifacts/1900b0aE.dag/post-processed/Assembly-CSharp.dll (+pdb)"}}, {"pid": 12345, "tid": 11, "ts": 1754736429475291, "dur": 81807, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\Bee\\artifacts\\1900b0aE.dag\\post-processed\\Assembly-CSharp.dll"}}, {"pid": 12345, "tid": 11, "ts": 1754736429475290, "dur": 81811, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Assembly-CSharp.dll"}}, {"pid": 12345, "tid": 11, "ts": 1754736429557134, "dur": 819, "ph": "X", "name": "CopyFiles", "args": {"detail": "Library/ScriptAssemblies/Assembly-CSharp.dll"}}, {"pid": 12345, "tid": 11, "ts": 1754736429557959, "dur": 145984, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1754736428731165, "dur": 19396, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1754736428750898, "dur": 92, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\GameSoft\\Unity\\Editor\\2022.3.38f1c1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.UnityWebRequestTextureModule.dll"}}, {"pid": 12345, "tid": 12, "ts": 1754736428750897, "dur": 94, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityWebRequestTextureModule.dll_3AB3AEE512D0C853.mvfrm"}}, {"pid": 12345, "tid": 12, "ts": 1754736428751252, "dur": 54, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Core.Editor.rsp"}}, {"pid": 12345, "tid": 12, "ts": 1754736428751593, "dur": 159, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Cinemachine.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 12, "ts": 1754736428751876, "dur": 88, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/URPWizard.rsp"}}, {"pid": 12345, "tid": 12, "ts": 1754736428752059, "dur": 57, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/9562792982806824100.rsp"}}, {"pid": 12345, "tid": 12, "ts": 1754736428752187, "dur": 191, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1754736428752425, "dur": 215, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1754736428752640, "dur": 183, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1754736428752823, "dur": 186, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1754736428753009, "dur": 167, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1754736428753176, "dur": 182, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1754736428753359, "dur": 142, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1754736428753501, "dur": 212, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1754736428753713, "dur": 156, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1754736428753869, "dur": 170, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1754736428754039, "dur": 546, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1754736428754585, "dur": 214, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1754736428754933, "dur": 542, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.shadergraph@14.0.11\\Editor\\Drawing\\Views\\Slots\\MultiFloatSlotControlView.cs"}}, {"pid": 12345, "tid": 12, "ts": 1754736428754799, "dur": 736, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1754736428755535, "dur": 174, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1754736428755709, "dur": 187, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1754736428755896, "dur": 659, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1754736428756555, "dur": 188, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1754736428756743, "dur": 251, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1754736428756995, "dur": 183, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1754736428757179, "dur": 217, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1754736428757397, "dur": 197, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1754736428757594, "dur": 202, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1754736428757796, "dur": 91, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1754736428757934, "dur": 281, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1754736428758215, "dur": 574, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1754736428758791, "dur": 97, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.VSCode.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 12, "ts": 1754736428758888, "dur": 236, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1754736428759130, "dur": 506, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.VSCode.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 12, "ts": 1754736428759637, "dur": 86, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1754736428759788, "dur": 260, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Tests.dll.mvfrm"}}, {"pid": 12345, "tid": 12, "ts": 1754736428760065, "dur": 415, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPP-Configuration Library/ilpp-configuration.nevergeneratedoutput"}}, {"pid": 12345, "tid": 12, "ts": 1754736428760507, "dur": 104, "ph": "X", "name": "StoreTimestampsOfNonGeneratedInputFiles", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1754736428760621, "dur": 301088, "ph": "X", "name": "ILPP-Configuration", "args": {"detail": "Library/ilpp-configuration.nevergeneratedoutput"}}, {"pid": 12345, "tid": 12, "ts": 1754736429063923, "dur": 1451, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/com.crashkonijn.goap.demos.complex.dll (+pdb)"}}, {"pid": 12345, "tid": 12, "ts": 1754736429065374, "dur": 54, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1754736429065477, "dur": 1308, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.VisualScripting.Flow.dll (+pdb)"}}, {"pid": 12345, "tid": 12, "ts": 1754736429066786, "dur": 306, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1754736429067102, "dur": 1261, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.VisualScripting.SettingsProvider.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 12, "ts": 1754736429068363, "dur": 111, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1754736429069896, "dur": 63, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\GameSoft\\Unity\\Editor\\2022.3.38f1c1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.ComponentModel.Primitives.dll"}}, {"pid": 12345, "tid": 12, "ts": 1754736429068478, "dur": 1703, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/CrashKonijn.Goap.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 12, "ts": 1754736429070423, "dur": 258, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1754736429070731, "dur": 633165, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1754736428731228, "dur": 19366, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1754736428750633, "dur": 52, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\GameSoft\\Unity\\Editor\\2022.3.38f1c1\\Editor\\Data\\Managed\\UnityEngine\\UnityEditor.SceneTemplateModule.dll"}}, {"pid": 12345, "tid": 13, "ts": 1754736428750607, "dur": 80, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.SceneTemplateModule.dll_188EDD6374EAFAAD.mvfrm"}}, {"pid": 12345, "tid": 13, "ts": 1754736428750790, "dur": 51, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1754736428750842, "dur": 89, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.XRModule.dll_5E2DACE274283492.mvfrm"}}, {"pid": 12345, "tid": 13, "ts": 1754736428750958, "dur": 59, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1754736428751309, "dur": 62, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Universal.Runtime.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 13, "ts": 1754736428751682, "dur": 59, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualStudio.Editor.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 13, "ts": 1754736428752058, "dur": 456, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/3733166401979772732.rsp"}}, {"pid": 12345, "tid": 13, "ts": 1754736428752515, "dur": 183, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1754736428752698, "dur": 189, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1754736428752887, "dur": 279, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1754736428753166, "dur": 820, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1754736428753986, "dur": 205, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1754736428754192, "dur": 514, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1754736428754706, "dur": 280, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1754736428754987, "dur": 184, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1754736428755171, "dur": 173, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1754736428755344, "dur": 248, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1754736428755592, "dur": 287, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1754736428755879, "dur": 241, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1754736428756120, "dur": 189, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1754736428756309, "dur": 231, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1754736428756540, "dur": 160, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1754736428756700, "dur": 257, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1754736428756957, "dur": 176, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1754736428757133, "dur": 221, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1754736428757355, "dur": 186, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1754736428757542, "dur": 147, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1754736428757717, "dur": 58, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1754736428757775, "dur": 249, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1754736428758025, "dur": 192, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1754736428758217, "dur": 447, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1754736428759728, "dur": 50, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.inputsystem@1.7.0\\InputSystem\\Plugins\\XInput\\XboxGamepadMacOS.cs"}}, {"pid": 12345, "tid": 13, "ts": 1754736428758743, "dur": 1264, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.InputSystem.dll (+2 others)"}}, {"pid": 12345, "tid": 13, "ts": 1754736428760055, "dur": 111, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Core.Runtime.dll.mvfrm"}}, {"pid": 12345, "tid": 13, "ts": 1754736428760193, "dur": 486, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Core.Runtime.dll (+2 others)"}}, {"pid": 12345, "tid": 13, "ts": 1754736428760771, "dur": 53, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipeline.Universal.ShaderLibrary.dll.mvfrm"}}, {"pid": 12345, "tid": 13, "ts": 1754736428760845, "dur": 170, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipeline.Universal.ShaderLibrary.dll (+2 others)"}}, {"pid": 12345, "tid": 13, "ts": 1754736428761116, "dur": 348, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Universal.Runtime.dll (+2 others)"}}, {"pid": 12345, "tid": 13, "ts": 1754736428761526, "dur": 69, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Cinemachine.dll.mvfrm"}}, {"pid": 12345, "tid": 13, "ts": 1754736428761617, "dur": 286, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Cinemachine.dll (+2 others)"}}, {"pid": 12345, "tid": 13, "ts": 1754736428761937, "dur": 302642, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1754736429065677, "dur": 77, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\GameSoft\\Unity\\Editor\\2022.3.38f1c1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Xml.XmlDocument.dll"}}, {"pid": 12345, "tid": 13, "ts": 1754736429064581, "dur": 1371, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.RenderPipelines.Universal.Runtime.dll (+pdb)"}}, {"pid": 12345, "tid": 13, "ts": 1754736429065992, "dur": 1361, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.TextMeshPro.dll (+pdb)"}}, {"pid": 12345, "tid": 13, "ts": 1754736429067387, "dur": 1587, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.TextMeshPro.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 13, "ts": 1754736429068975, "dur": 796, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1754736429069822, "dur": 227, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1754736429070413, "dur": 238, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1754736429070670, "dur": 92, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1754736429070764, "dur": 90, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1754736429070860, "dur": 633131, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1754736428731262, "dur": 19350, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1754736428750626, "dur": 52, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\GameSoft\\Unity\\Editor\\2022.3.38f1c1\\Editor\\Data\\Managed\\UnityEngine\\UnityEditor.UIBuilderModule.dll"}}, {"pid": 12345, "tid": 14, "ts": 1754736428750619, "dur": 62, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.UIBuilderModule.dll_41DA9226DA4902FE.mvfrm"}}, {"pid": 12345, "tid": 14, "ts": 1754736428750845, "dur": 73, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.SharedInternalsModule.dll_C8666D03B273DA0D.mvfrm"}}, {"pid": 12345, "tid": 14, "ts": 1754736428750923, "dur": 86, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1754736428751010, "dur": 52, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/UnityEngine.UI.rsp2"}}, {"pid": 12345, "tid": 14, "ts": 1754736428751314, "dur": 126, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Collections.rsp"}}, {"pid": 12345, "tid": 14, "ts": 1754736428751521, "dur": 101, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Universal.Editor.rsp2"}}, {"pid": 12345, "tid": 14, "ts": 1754736428751763, "dur": 95, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.TextMeshPro.Editor.rsp"}}, {"pid": 12345, "tid": 14, "ts": 1754736428752188, "dur": 263, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1754736428752456, "dur": 326, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1754736428752782, "dur": 208, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1754736428752990, "dur": 195, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1754736428753185, "dur": 523, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1754736428753708, "dur": 174, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1754736428753882, "dur": 205, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1754736428754087, "dur": 563, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1754736428754650, "dur": 233, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1754736428754883, "dur": 373, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1754736428755256, "dur": 181, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1754736428755437, "dur": 355, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1754736428755792, "dur": 205, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1754736428755997, "dur": 234, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1754736428756231, "dur": 223, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1754736428756454, "dur": 172, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1754736428756626, "dur": 186, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1754736428756812, "dur": 239, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1754736428757052, "dur": 182, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1754736428757234, "dur": 228, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1754736428757462, "dur": 217, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1754736428757737, "dur": 263, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1754736428758000, "dur": 220, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1754736428758220, "dur": 559, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1754736428758796, "dur": 115, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.PlasticSCM.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 14, "ts": 1754736428759300, "dur": 59, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.collab-proxy@2.8.2\\Editor\\Developer\\UpdateReport\\UpdateReportLineListViewItem.cs"}}, {"pid": 12345, "tid": 14, "ts": 1754736428759597, "dur": 94, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.collab-proxy@2.8.2\\Editor\\Views\\Changesets\\ChangesetsListHeaderState.cs"}}, {"pid": 12345, "tid": 14, "ts": 1754736428758945, "dur": 1019, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.PlasticSCM.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 14, "ts": 1754736428759964, "dur": 107, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1754736428760092, "dur": 69, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.PlasticSCM.Editor.ref.dll_257AEB342BE77856.mvfrm"}}, {"pid": 12345, "tid": 14, "ts": 1754736428760162, "dur": 76, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.CollabProxy.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 14, "ts": 1754736428760260, "dur": 231, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.CollabProxy.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 14, "ts": 1754736428760492, "dur": 178, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1754736428760675, "dur": 120, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1754736428760796, "dur": 572, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1754736428761368, "dur": 922, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1754736428762349, "dur": 169, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.SettingsProvider.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 14, "ts": 1754736428762554, "dur": 301361, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1754736429065725, "dur": 479, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.visualscripting@1.9.4\\Editor\\VisualScripting.Core\\Dependencies\\DotNetZip\\Unity.VisualScripting.IonicZip.dll"}}, {"pid": 12345, "tid": 14, "ts": 1754736429063920, "dur": 2295, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.RenderPipelines.Core.ShaderLibrary.dll (+pdb)"}}, {"pid": 12345, "tid": 14, "ts": 1754736429066216, "dur": 775, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1754736429067001, "dur": 1167, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/UnityEngine.UI.dll (+pdb)"}}, {"pid": 12345, "tid": 14, "ts": 1754736429068168, "dur": 262, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1754736429069505, "dur": 60, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\GameSoft\\Unity\\Editor\\2022.3.38f1c1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.AndroidJNIModule.dll"}}, {"pid": 12345, "tid": 14, "ts": 1754736429069943, "dur": 51, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\GameSoft\\Unity\\Editor\\2022.3.38f1c1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Reflection.Emit.Lightweight.dll"}}, {"pid": 12345, "tid": 14, "ts": 1754736429068438, "dur": 2002, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/PPv2URPConverters.dll (+pdb)"}}, {"pid": 12345, "tid": 14, "ts": 1754736429070441, "dur": 93, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1754736429070548, "dur": 180, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1754736429070753, "dur": 633135, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1754736428731312, "dur": 19331, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1754736428750655, "dur": 88, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\GameSoft\\Unity\\Editor\\2022.3.38f1c1\\Editor\\Data\\Managed\\UnityEngine\\UnityEditor.UnityConnectModule.dll"}}, {"pid": 12345, "tid": 15, "ts": 1754736428750649, "dur": 96, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.UnityConnectModule.dll_591A98B8662B29F2.mvfrm"}}, {"pid": 12345, "tid": 15, "ts": 1754736428750932, "dur": 79, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1754736428751264, "dur": 61, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Core.Editor.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 15, "ts": 1754736428751374, "dur": 77, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.ShaderGraph.Utilities.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 15, "ts": 1754736428751519, "dur": 107, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.State.Editor.rsp2"}}, {"pid": 12345, "tid": 15, "ts": 1754736428751772, "dur": 116, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/URPWizard.rsp2"}}, {"pid": 12345, "tid": 15, "ts": 1754736428751971, "dur": 65, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.InputSystem.TestFramework.rsp"}}, {"pid": 12345, "tid": 15, "ts": 1754736428752202, "dur": 179, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1754736428752382, "dur": 147, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/6841316898876630997.rsp"}}, {"pid": 12345, "tid": 15, "ts": 1754736428752530, "dur": 216, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1754736428752746, "dur": 177, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1754736428752923, "dur": 174, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1754736428753097, "dur": 166, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1754736428753263, "dur": 309, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1754736428753572, "dur": 156, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1754736428753728, "dur": 160, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1754736428753888, "dur": 173, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1754736428754062, "dur": 506, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1754736428754568, "dur": 200, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1754736428754768, "dur": 265, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1754736428755033, "dur": 188, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1754736428755221, "dur": 219, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1754736428755440, "dur": 292, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1754736428755732, "dur": 195, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1754736428755927, "dur": 211, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1754736428756138, "dur": 277, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1754736428756416, "dur": 184, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1754736428756600, "dur": 320, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1754736428756920, "dur": 218, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1754736428757138, "dur": 184, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1754736428757322, "dur": 223, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1754736428757545, "dur": 246, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1754736428757835, "dur": 56, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1754736428757943, "dur": 270, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1754736428758214, "dur": 588, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1754736428758803, "dur": 174, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/CrashKonijn.Goap.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 15, "ts": 1754736428758978, "dur": 97, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1754736428759081, "dur": 92, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/com.crashkonijn.docs.getting_started.dll.mvfrm"}}, {"pid": 12345, "tid": 15, "ts": 1754736428759200, "dur": 69, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/GlassSystem.dll.mvfrm"}}, {"pid": 12345, "tid": 15, "ts": 1754736428759291, "dur": 139, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.Burst.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 15, "ts": 1754736428759878, "dur": 71, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.burst@1.8.16\\Editor\\BurstDisassembler.Core.Wasm.cs"}}, {"pid": 12345, "tid": 15, "ts": 1754736428759456, "dur": 522, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Burst.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 15, "ts": 1754736428759978, "dur": 103, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1754736428760155, "dur": 386, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1754736428760541, "dur": 234, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1754736428760775, "dur": 589, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1754736428761365, "dur": 235, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/com.crashkonijn.goap.demos.turn-based.dll (+2 others)"}}, {"pid": 12345, "tid": 15, "ts": 1754736428761648, "dur": 302265, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1754736429063915, "dur": 1426, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/CrashKonijn.Goap.Resolver.dll (+pdb)"}}, {"pid": 12345, "tid": 15, "ts": 1754736429065344, "dur": 68, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1754736429065460, "dur": 1389, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/com.crashkonijn.goap.demos.turn-based.dll (+pdb)"}}, {"pid": 12345, "tid": 15, "ts": 1754736429066850, "dur": 281, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1754736429067138, "dur": 1434, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.RenderPipelines.Core.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 15, "ts": 1754736429069793, "dur": 58, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\GameSoft\\Unity\\Editor\\2022.3.38f1c1\\Editor\\Data\\Managed\\UnityEngine\\UnityEditor.dll"}}, {"pid": 12345, "tid": 15, "ts": 1754736429069981, "dur": 159, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\GameSoft\\Unity\\Editor\\2022.3.38f1c1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.UnityCurlModule.dll"}}, {"pid": 12345, "tid": 15, "ts": 1754736429070357, "dur": 69, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\GameSoft\\Unity\\Editor\\2022.3.38f1c1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Security.Cryptography.Primitives.dll"}}, {"pid": 12345, "tid": 15, "ts": 1754736429068628, "dur": 2032, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Timeline.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 15, "ts": 1754736429070716, "dur": 219, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1754736429070951, "dur": 633003, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1754736428731347, "dur": 19306, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1754736428750654, "dur": 52, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.AnimationModule.dll_BBD7613341ED5E70.mvfrm"}}, {"pid": 12345, "tid": 16, "ts": 1754736428750718, "dur": 89, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.AnimationModule.dll_BBD7613341ED5E70.mvfrm"}}, {"pid": 12345, "tid": 16, "ts": 1754736428750885, "dur": 111, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1754736428751095, "dur": 99, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Burst.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 16, "ts": 1754736428751243, "dur": 51, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Flow.rsp"}}, {"pid": 12345, "tid": 16, "ts": 1754736428751305, "dur": 157, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Flow.rsp"}}, {"pid": 12345, "tid": 16, "ts": 1754736428751585, "dur": 74, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/CrashKonijn.Goap.Editor.rsp2"}}, {"pid": 12345, "tid": 16, "ts": 1754736428751753, "dur": 83, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VSCode.Editor.rsp"}}, {"pid": 12345, "tid": 16, "ts": 1754736428751886, "dur": 56, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Tests.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 16, "ts": 1754736428752128, "dur": 63, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/18218310762646611085.rsp"}}, {"pid": 12345, "tid": 16, "ts": 1754736428752208, "dur": 146, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1754736428752384, "dur": 157, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/2781082588993088484.rsp"}}, {"pid": 12345, "tid": 16, "ts": 1754736428752542, "dur": 218, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1754736428752760, "dur": 179, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1754736428752939, "dur": 192, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1754736428753131, "dur": 176, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1754736428753455, "dur": 505, "ph": "X", "name": "File", "args": {"detail": "D:\\GameSoft\\Unity\\Editor\\2022.3.38f1c1\\Editor\\Data\\Tools\\ilpp\\Unity.ILPP.Runner\\Microsoft.AspNetCore.Cryptography.Internal.dll"}}, {"pid": 12345, "tid": 16, "ts": 1754736428753307, "dur": 736, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1754736428754043, "dur": 641, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1754736428754684, "dur": 172, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1754736428754932, "dur": 1043, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.shadergraph@14.0.11\\Editor\\Drawing\\Inspector\\PropertyDrawers\\Vector4PropertyDrawer.cs"}}, {"pid": 12345, "tid": 16, "ts": 1754736428754856, "dur": 1246, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1754736428756103, "dur": 191, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1754736428756294, "dur": 485, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1754736428756780, "dur": 258, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1754736428757039, "dur": 175, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1754736428757343, "dur": 629, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.inputsystem@1.7.0\\InputSystem\\Devices\\Commands\\IInputDeviceCommandInfo.cs"}}, {"pid": 12345, "tid": 16, "ts": 1754736428757214, "dur": 810, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1754736428758024, "dur": 185, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1754736428758256, "dur": 425, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1754736428758690, "dur": 69, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.Mathematics.dll.mvfrm"}}, {"pid": 12345, "tid": 16, "ts": 1754736428758819, "dur": 335, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\GameSoft\\Unity\\Editor\\2022.3.38f1c1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.ClusterRendererModule.dll"}}, {"pid": 12345, "tid": 16, "ts": 1754736428759397, "dur": 80, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\GameSoft\\Unity\\Editor\\2022.3.38f1c1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Xml.XmlDocument.dll"}}, {"pid": 12345, "tid": 16, "ts": 1754736428758787, "dur": 922, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Mathematics.dll (+2 others)"}}, {"pid": 12345, "tid": 16, "ts": 1754736428759710, "dur": 67, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1754736428759816, "dur": 151, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.Collections.dll.mvfrm"}}, {"pid": 12345, "tid": 16, "ts": 1754736428759996, "dur": 411, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Collections.dll (+2 others)"}}, {"pid": 12345, "tid": 16, "ts": 1754736428760408, "dur": 99, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1754736428760538, "dur": 60, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/CrashKonijn.Goap.Resolver.dll.mvfrm"}}, {"pid": 12345, "tid": 16, "ts": 1754736428760632, "dur": 216, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/CrashKonijn.Goap.Resolver.dll (+2 others)"}}, {"pid": 12345, "tid": 16, "ts": 1754736428760887, "dur": 406, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/CrashKonijn.Goap.Runtime.dll (+2 others)"}}, {"pid": 12345, "tid": 16, "ts": 1754736428761372, "dur": 245, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/CrashKonijn.Goap.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 16, "ts": 1754736428761617, "dur": 197, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1754736428761819, "dur": 181, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/com.crashkonijn.goap.demos.simple.dll (+2 others)"}}, {"pid": 12345, "tid": 16, "ts": 1754736428762030, "dur": 301894, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1754736429063926, "dur": 1425, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.VisualScripting.State.dll (+pdb)"}}, {"pid": 12345, "tid": 16, "ts": 1754736429065352, "dur": 359, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1754736429065716, "dur": 1293, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Collections.dll (+pdb)"}}, {"pid": 12345, "tid": 16, "ts": 1754736429067038, "dur": 1376, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/CrashKonijn.Agent.Runtime.dll (+pdb)"}}, {"pid": 12345, "tid": 16, "ts": 1754736429068415, "dur": 320, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1754736429069896, "dur": 80, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\GameSoft\\Unity\\Editor\\2022.3.38f1c1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.GameCenterModule.dll"}}, {"pid": 12345, "tid": 16, "ts": 1754736429068740, "dur": 1853, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.VisualScripting.Shared.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 16, "ts": 1754736429070594, "dur": 57, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1754736429070726, "dur": 620510, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1754736429691239, "dur": 11936, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\Bee\\artifacts\\1900b0aE.dag\\post-processed\\Assembly-CSharp-Editor.dll"}}, {"pid": 12345, "tid": 16, "ts": 1754736429691238, "dur": 11938, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Assembly-CSharp-Editor.dll"}}, {"pid": 12345, "tid": 16, "ts": 1754736429703198, "dur": 633, "ph": "X", "name": "CopyFiles", "args": {"detail": "Library/ScriptAssemblies/Assembly-CSharp-Editor.dll"}}, {"pid": 12345, "tid": 0, "ts": 1754736429709427, "dur": 1576, "ph": "X", "name": "ProfilerWriteOutput"}, {"pid": 35942, "tid": -1, "ph": "M", "name": "process_name", "args": {"name": "netcorerun.dll"}}, {"pid": 35942, "tid": -1, "ph": "M", "name": "process_sort_index", "args": {"sort_index": "-1"}}, {"pid": 35942, "tid": 1, "ph": "M", "name": "thread_name", "args": {"name": ""}}, {"pid": 35942, "tid": 1, "ts": 1754736428034173, "dur": 305429, "ph": "X", "name": "BuildProgram", "args": {}}, {"pid": 35942, "tid": 1, "ts": 1754736428035017, "dur": 49906, "ph": "X", "name": "BuildProgramContextConstructor", "args": {}}, {"pid": 35942, "tid": 1, "ts": 1754736428284830, "dur": 2684, "ph": "X", "name": "OutputData.Write", "args": {}}, {"pid": 35942, "tid": 1, "ts": 1754736428287517, "dur": 52071, "ph": "X", "name": "Backend.Write", "args": {}}, {"pid": 35942, "tid": 1, "ts": 1754736428289068, "dur": 30792, "ph": "X", "name": "JsonToString", "args": {}}, {"pid": 35942, "tid": 1, "ts": 1754736428344580, "dur": 853, "ph": "X", "name": "", "args": {}}, {"pid": 35942, "tid": 1, "ts": 1754736428344213, "dur": 1390, "ph": "X", "name": "Write chrome-trace events", "args": {}}, {"cat": "", "pid": 12345, "tid": 0, "ts": 0, "ph": "M", "name": "process_name", "args": {"name": "bee_backend"}}, {"pid": 12345, "tid": 0, "ts": 1754736427686121, "dur": 1546, "ph": "X", "name": "DriverInitData", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1754736427687679, "dur": 870, "ph": "X", "name": "RemoveStaleOutputs", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1754736427688691, "dur": 58, "ph": "X", "name": "<PERSON><PERSON>", "args": {"detail": "PrepareNodes"}}, {"pid": 12345, "tid": 0, "ts": 1754736427688749, "dur": 389, "ph": "X", "name": "BuildQueueInit", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1754736427689729, "dur": 71, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.AssetBundleModule.dll_022E81689B98A39E.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1754736427690199, "dur": 63, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.LocalizationModule.dll_1703D820AF2EEFB1.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1754736427697409, "dur": 66, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.ShaderGraph.ShaderGraphLibrary.dll (+2 others)"}}, {"pid": 12345, "tid": 0, "ts": 1754736427689156, "dur": 22461, "ph": "X", "name": "EnqueueRequestedNodes", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1754736427711630, "dur": 400, "ph": "X", "name": "WaitForBuildFinished", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1754736427712031, "dur": 67, "ph": "X", "name": "JoinBuildThread", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1754736427712112, "dur": 98, "ph": "X", "name": "JoinBuildThread", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1754736427712439, "dur": 80, "ph": "X", "name": "BuildQueueDestroyTail", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1754736427712535, "dur": 1082, "ph": "X", "name": "<PERSON><PERSON>", "args": {"detail": "Write AllBuiltNodes"}}, {"pid": 12345, "tid": 1, "ts": 1754736427689338, "dur": 22304, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1754736427711649, "dur": 325, "ph": "X", "name": "CheckDagSignatures", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1754736427689415, "dur": 22274, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1754736427711730, "dur": 83, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\GameSoft\\Unity\\Editor\\2022.3.38f1c1\\Editor\\Data\\Managed\\UnityEngine\\UnityEditor.UIElementsSamplesModule.dll"}}, {"pid": 12345, "tid": 2, "ts": 1754736427711705, "dur": 112, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.UIElementsSamplesModule.dll_623E7535F4BD3DF5.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1754736427711944, "dur": 150, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.SceneTemplateModule.dll_188EDD6374EAFAAD.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1754736427689367, "dur": 22288, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1754736427711734, "dur": 55, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.VFXModule.dll_7FCD2258FCB2D3F4.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1754736427711967, "dur": 110, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.AIModule.dll_DD29252AECEC5C10.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1754736427689394, "dur": 22274, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1754736427711731, "dur": 58, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.VehiclesModule.dll_B4028357F84AC7DF.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1754736427711945, "dur": 55, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.UIElementsModule.dll_613AE6E2D149BAF8.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1754736427689441, "dur": 22271, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1754736427711743, "dur": 73, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\GameSoft\\Unity\\Editor\\2022.3.38f1c1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.UnityWebRequestWWWModule.dll"}}, {"pid": 12345, "tid": 5, "ts": 1754736427711727, "dur": 94, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityWebRequestWWWModule.dll_BA3DE9AF0772CF8F.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1754736427711953, "dur": 153, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.SceneViewModule.dll_2C01C8125CB356D8.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1754736427689479, "dur": 22256, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1754736427711768, "dur": 59, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\GameSoft\\Unity\\Editor\\2022.3.38f1c1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.VideoModule.dll"}}, {"pid": 12345, "tid": 6, "ts": 1754736427711750, "dur": 80, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.VideoModule.dll_1DBF74AC9293BF07.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1754736427689504, "dur": 22248, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1754736427711980, "dur": 134, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.AnimationModule.dll_BBD7613341ED5E70.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1754736427689528, "dur": 22254, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1754736427711825, "dur": 66, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.VRModule.dll_2B6EE9DDEAEDE734.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1754736427711930, "dur": 50, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.UnityConnectModule.dll_591A98B8662B29F2.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1754736427689561, "dur": 22264, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1754736427689593, "dur": 22268, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1754736427711896, "dur": 57, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\GameSoft\\Unity\\Editor\\2022.3.38f1c1\\Editor\\Data\\Managed\\UnityEngine\\UnityEditor.PresetsUIModule.dll"}}, {"pid": 12345, "tid": 10, "ts": 1754736427711879, "dur": 78, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.PresetsUIModule.dll_A8B81DEC69E8E1CF.mvfrm"}}, {"pid": 12345, "tid": 11, "ts": 1754736427689621, "dur": 22312, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1754736427711968, "dur": 52, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\GameSoft\\Unity\\Editor\\2022.3.38f1c1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.AccessibilityModule.dll"}}, {"pid": 12345, "tid": 11, "ts": 1754736427711952, "dur": 70, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.AccessibilityModule.dll_4B3448FA7561224A.mvfrm"}}, {"pid": 12345, "tid": 12, "ts": 1754736427689659, "dur": 22302, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1754736427711969, "dur": 74, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ARModule.dll_969BEA2E4A09BAD9.mvfrm"}}, {"pid": 12345, "tid": 13, "ts": 1754736427689692, "dur": 22285, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1754736427689721, "dur": 22270, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1754736427689756, "dur": 22248, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1754736427689787, "dur": 22236, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1754736427717662, "dur": 526, "ph": "X", "name": "ProfilerWriteOutput"}, {"pid": 3100, "tid": 24781, "ts": 1754736429721393, "dur": 1531, "ph": "X", "name": "Wait for external events", "args": {"First to finish": "backend2.traceevents"}}, {"pid": 3100, "tid": 24781, "ts": 1754736429724717, "dur": 34, "ph": "X", "name": "Wait for external events", "args": {"First to finish": "buildprogram0.traceevents"}}, {"pid": 3100, "tid": 24781, "ts": 1754736429724911, "dur": 13, "ph": "X", "name": "Wait for external events", "args": {"First to finish": "backend1.traceevents"}}, {"pid": 3100, "tid": 24781, "ts": 1754736429723037, "dur": 1678, "ph": "X", "name": "backend2.traceevents", "args": {}}, {"pid": 3100, "tid": 24781, "ts": 1754736429724810, "dur": 100, "ph": "X", "name": "buildprogram0.traceevents", "args": {}}, {"pid": 3100, "tid": 24781, "ts": 1754736429724952, "dur": 109, "ph": "X", "name": "backend1.traceevents", "args": {}}, {"pid": 3100, "tid": 24781, "ts": 1754736429718083, "dur": 7573, "ph": "X", "name": "Write chrome-trace events", "args": {}}, {}]}