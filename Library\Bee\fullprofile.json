{"Instructions Readme": "1) Open Chrome or Edge, 2) go to chrome://tracing, 3) click Load, 4) navigate to this file.", "processInfo": {}, "traceEvents": [{"pid": 3100, "tid": -1, "ph": "M", "name": "process_name", "args": {"name": "BeeDriver"}}, {"pid": 3100, "tid": -1, "ph": "M", "name": "process_sort_index", "args": {"sort_index": "-2"}}, {"pid": 3100, "tid": 24786, "ph": "M", "name": "thread_name", "args": {"name": "Thread Pool Worker"}}, {"pid": 3100, "tid": 24786, "ts": 1754735841951766, "dur": 628, "ph": "X", "name": "ChromeTraceHeader", "args": {}}, {"pid": 3100, "tid": 24786, "ts": 1754735841955091, "dur": 558, "ph": "X", "name": "Thread Pool Worker", "args": {}}, {"pid": 3100, "tid": 21474836480, "ph": "M", "name": "thread_name", "args": {"name": "ReadEntireBinlogFromIpcAsync"}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735840537547, "dur": 157607, "ph": "X", "name": "WaitForConnectionAsync", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735840695156, "dur": 1250772, "ph": "X", "name": "UpdateFromStreamAsync", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735840695166, "dur": 30, "ph": "X", "name": "ReadAsync 0", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735840695198, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735840695199, "dur": 103958, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735840799162, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735840799166, "dur": 53, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735840799221, "dur": 6, "ph": "X", "name": "ProcessMessages 41", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735840799228, "dur": 2034, "ph": "X", "name": "ReadAsync 41", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735840801268, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735840801271, "dur": 41, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735840801313, "dur": 1, "ph": "X", "name": "ProcessMessages 644", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735840801315, "dur": 42, "ph": "X", "name": "ReadAsync 644", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735840801360, "dur": 1, "ph": "X", "name": "ProcessMessages 225", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735840801362, "dur": 55, "ph": "X", "name": "ReadAsync 225", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735840801421, "dur": 1, "ph": "X", "name": "ProcessMessages 686", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735840801423, "dur": 58, "ph": "X", "name": "ReadAsync 686", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735840801484, "dur": 1, "ph": "X", "name": "ProcessMessages 787", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735840801487, "dur": 62, "ph": "X", "name": "ReadAsync 787", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735840801551, "dur": 31, "ph": "X", "name": "ReadAsync 224", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735840801584, "dur": 35, "ph": "X", "name": "ReadAsync 453", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735840801621, "dur": 1, "ph": "X", "name": "ProcessMessages 353", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735840801623, "dur": 41, "ph": "X", "name": "ReadAsync 353", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735840801665, "dur": 34, "ph": "X", "name": "ReadAsync 594", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735840801702, "dur": 31, "ph": "X", "name": "ReadAsync 572", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735840801737, "dur": 1, "ph": "X", "name": "ProcessMessages 495", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735840801740, "dur": 36, "ph": "X", "name": "ReadAsync 495", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735840801779, "dur": 1, "ph": "X", "name": "ProcessMessages 212", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735840801781, "dur": 43, "ph": "X", "name": "ReadAsync 212", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735840801827, "dur": 39, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735840801870, "dur": 1, "ph": "X", "name": "ProcessMessages 705", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735840801871, "dur": 38, "ph": "X", "name": "ReadAsync 705", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735840801912, "dur": 28, "ph": "X", "name": "ReadAsync 719", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735840801941, "dur": 26, "ph": "X", "name": "ReadAsync 541", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735840801969, "dur": 34, "ph": "X", "name": "ReadAsync 333", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735840802005, "dur": 1, "ph": "X", "name": "ProcessMessages 438", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735840802006, "dur": 34, "ph": "X", "name": "ReadAsync 438", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735840802042, "dur": 30, "ph": "X", "name": "ReadAsync 660", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735840802075, "dur": 1, "ph": "X", "name": "ProcessMessages 489", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735840802077, "dur": 32, "ph": "X", "name": "ReadAsync 489", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735840802111, "dur": 1, "ph": "X", "name": "ProcessMessages 582", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735840802112, "dur": 43, "ph": "X", "name": "ReadAsync 582", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735840802158, "dur": 1, "ph": "X", "name": "ProcessMessages 564", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735840802160, "dur": 36, "ph": "X", "name": "ReadAsync 564", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735840802197, "dur": 28, "ph": "X", "name": "ReadAsync 654", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735840802228, "dur": 1, "ph": "X", "name": "ProcessMessages 87", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735840802230, "dur": 34, "ph": "X", "name": "ReadAsync 87", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735840802266, "dur": 26, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735840802294, "dur": 24, "ph": "X", "name": "ReadAsync 455", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735840802319, "dur": 21, "ph": "X", "name": "ReadAsync 261", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735840802342, "dur": 22, "ph": "X", "name": "ReadAsync 400", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735840802365, "dur": 25, "ph": "X", "name": "ReadAsync 336", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735840802392, "dur": 26, "ph": "X", "name": "ReadAsync 466", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735840802420, "dur": 1, "ph": "X", "name": "ProcessMessages 346", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735840802422, "dur": 42, "ph": "X", "name": "ReadAsync 346", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735840802466, "dur": 1, "ph": "X", "name": "ProcessMessages 476", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735840802468, "dur": 28, "ph": "X", "name": "ReadAsync 476", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735840802498, "dur": 30, "ph": "X", "name": "ReadAsync 684", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735840802532, "dur": 1, "ph": "X", "name": "ProcessMessages 323", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735840802534, "dur": 42, "ph": "X", "name": "ReadAsync 323", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735840802577, "dur": 28, "ph": "X", "name": "ReadAsync 760", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735840802608, "dur": 26, "ph": "X", "name": "ReadAsync 638", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735840802635, "dur": 23, "ph": "X", "name": "ReadAsync 467", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735840802660, "dur": 27, "ph": "X", "name": "ReadAsync 217", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735840802689, "dur": 21, "ph": "X", "name": "ReadAsync 575", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735840802712, "dur": 25, "ph": "X", "name": "ReadAsync 483", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735840802738, "dur": 23, "ph": "X", "name": "ReadAsync 418", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735840802762, "dur": 25, "ph": "X", "name": "ReadAsync 369", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735840802790, "dur": 36, "ph": "X", "name": "ReadAsync 470", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735840802829, "dur": 27, "ph": "X", "name": "ReadAsync 194", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735840802858, "dur": 22, "ph": "X", "name": "ReadAsync 600", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735840802881, "dur": 23, "ph": "X", "name": "ReadAsync 101", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735840802906, "dur": 22, "ph": "X", "name": "ReadAsync 369", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735840802930, "dur": 178, "ph": "X", "name": "ReadAsync 397", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735840803110, "dur": 33, "ph": "X", "name": "ReadAsync 369", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735840803145, "dur": 28, "ph": "X", "name": "ReadAsync 1877", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735840803176, "dur": 30, "ph": "X", "name": "ReadAsync 237", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735840803210, "dur": 34, "ph": "X", "name": "ReadAsync 491", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735840803245, "dur": 24, "ph": "X", "name": "ReadAsync 440", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735840803271, "dur": 33, "ph": "X", "name": "ReadAsync 495", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735840803306, "dur": 30, "ph": "X", "name": "ReadAsync 505", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735840803338, "dur": 1, "ph": "X", "name": "ProcessMessages 248", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735840803340, "dur": 76, "ph": "X", "name": "ReadAsync 248", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735840803420, "dur": 1, "ph": "X", "name": "ProcessMessages 1239", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735840803422, "dur": 49, "ph": "X", "name": "ReadAsync 1239", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735840803474, "dur": 1, "ph": "X", "name": "ProcessMessages 355", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735840803475, "dur": 32, "ph": "X", "name": "ReadAsync 355", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735840803509, "dur": 39, "ph": "X", "name": "ReadAsync 757", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735840803552, "dur": 41, "ph": "X", "name": "ReadAsync 363", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735840803594, "dur": 36, "ph": "X", "name": "ReadAsync 553", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735840803634, "dur": 1, "ph": "X", "name": "ProcessMessages 550", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735840803635, "dur": 44, "ph": "X", "name": "ReadAsync 550", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735840803682, "dur": 1, "ph": "X", "name": "ProcessMessages 719", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735840803684, "dur": 40, "ph": "X", "name": "ReadAsync 719", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735840803726, "dur": 1, "ph": "X", "name": "ProcessMessages 823", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735840803727, "dur": 30, "ph": "X", "name": "ReadAsync 823", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735840803759, "dur": 28, "ph": "X", "name": "ReadAsync 455", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735840803790, "dur": 30, "ph": "X", "name": "ReadAsync 520", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735840803822, "dur": 1, "ph": "X", "name": "ProcessMessages 355", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735840803824, "dur": 44, "ph": "X", "name": "ReadAsync 355", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735840803871, "dur": 1, "ph": "X", "name": "ProcessMessages 409", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735840803873, "dur": 38, "ph": "X", "name": "ReadAsync 409", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735840803914, "dur": 1, "ph": "X", "name": "ProcessMessages 429", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735840803915, "dur": 204, "ph": "X", "name": "ReadAsync 429", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735840804122, "dur": 1, "ph": "X", "name": "ProcessMessages 626", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735840804123, "dur": 23, "ph": "X", "name": "ReadAsync 626", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735840804147, "dur": 30, "ph": "X", "name": "ReadAsync 191", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735840804181, "dur": 29, "ph": "X", "name": "ReadAsync 216", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735840804212, "dur": 24, "ph": "X", "name": "ReadAsync 114", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735840804238, "dur": 25, "ph": "X", "name": "ReadAsync 329", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735840804265, "dur": 23, "ph": "X", "name": "ReadAsync 404", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735840804289, "dur": 41, "ph": "X", "name": "ReadAsync 345", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735840804332, "dur": 25, "ph": "X", "name": "ReadAsync 367", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735840804360, "dur": 48, "ph": "X", "name": "ReadAsync 544", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735840804412, "dur": 45, "ph": "X", "name": "ReadAsync 352", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735840804458, "dur": 32, "ph": "X", "name": "ReadAsync 599", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735840804491, "dur": 36, "ph": "X", "name": "ReadAsync 410", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735840804531, "dur": 1, "ph": "X", "name": "ProcessMessages 237", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735840804533, "dur": 34, "ph": "X", "name": "ReadAsync 237", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735840804568, "dur": 23, "ph": "X", "name": "ReadAsync 450", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735840804594, "dur": 50, "ph": "X", "name": "ReadAsync 222", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735840804648, "dur": 30, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735840804680, "dur": 25, "ph": "X", "name": "ReadAsync 543", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735840804708, "dur": 32, "ph": "X", "name": "ReadAsync 258", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735840804743, "dur": 25, "ph": "X", "name": "ReadAsync 89", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735840804770, "dur": 23, "ph": "X", "name": "ReadAsync 323", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735840804796, "dur": 22, "ph": "X", "name": "ReadAsync 330", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735840804820, "dur": 19, "ph": "X", "name": "ReadAsync 280", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735840804840, "dur": 21, "ph": "X", "name": "ReadAsync 74", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735840804863, "dur": 23, "ph": "X", "name": "ReadAsync 303", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735840804889, "dur": 1, "ph": "X", "name": "ProcessMessages 306", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735840804890, "dur": 50, "ph": "X", "name": "ReadAsync 306", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735840804941, "dur": 26, "ph": "X", "name": "ReadAsync 263", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735840804969, "dur": 23, "ph": "X", "name": "ReadAsync 465", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735840804994, "dur": 24, "ph": "X", "name": "ReadAsync 220", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735840805020, "dur": 20, "ph": "X", "name": "ReadAsync 35", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735840805042, "dur": 36, "ph": "X", "name": "ReadAsync 289", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735840805081, "dur": 33, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735840805115, "dur": 21, "ph": "X", "name": "ReadAsync 241", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735840805138, "dur": 23, "ph": "X", "name": "ReadAsync 475", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735840805162, "dur": 20, "ph": "X", "name": "ReadAsync 321", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735840805184, "dur": 23, "ph": "X", "name": "ReadAsync 425", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735840805209, "dur": 25, "ph": "X", "name": "ReadAsync 333", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735840805235, "dur": 22, "ph": "X", "name": "ReadAsync 343", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735840805259, "dur": 24, "ph": "X", "name": "ReadAsync 390", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735840805285, "dur": 1, "ph": "X", "name": "ProcessMessages 340", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735840805286, "dur": 24, "ph": "X", "name": "ReadAsync 340", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735840805312, "dur": 21, "ph": "X", "name": "ReadAsync 372", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735840805335, "dur": 23, "ph": "X", "name": "ReadAsync 365", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735840805361, "dur": 25, "ph": "X", "name": "ReadAsync 297", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735840805387, "dur": 30, "ph": "X", "name": "ReadAsync 81", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735840805421, "dur": 39, "ph": "X", "name": "ReadAsync 425", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735840805462, "dur": 1, "ph": "X", "name": "ProcessMessages 425", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735840805463, "dur": 27, "ph": "X", "name": "ReadAsync 425", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735840805492, "dur": 23, "ph": "X", "name": "ReadAsync 422", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735840805517, "dur": 27, "ph": "X", "name": "ReadAsync 342", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735840805545, "dur": 24, "ph": "X", "name": "ReadAsync 279", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735840805570, "dur": 27, "ph": "X", "name": "ReadAsync 297", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735840805599, "dur": 28, "ph": "X", "name": "ReadAsync 345", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735840805631, "dur": 1, "ph": "X", "name": "ProcessMessages 388", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735840805633, "dur": 31, "ph": "X", "name": "ReadAsync 388", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735840805666, "dur": 57, "ph": "X", "name": "ReadAsync 485", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735840805724, "dur": 29, "ph": "X", "name": "ReadAsync 239", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735840805755, "dur": 1, "ph": "X", "name": "ProcessMessages 406", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735840805757, "dur": 35, "ph": "X", "name": "ReadAsync 406", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735840805794, "dur": 29, "ph": "X", "name": "ReadAsync 445", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735840805825, "dur": 25, "ph": "X", "name": "ReadAsync 269", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735840805852, "dur": 28, "ph": "X", "name": "ReadAsync 332", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735840805882, "dur": 27, "ph": "X", "name": "ReadAsync 224", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735840805911, "dur": 1, "ph": "X", "name": "ProcessMessages 491", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735840805913, "dur": 40, "ph": "X", "name": "ReadAsync 491", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735840805956, "dur": 38, "ph": "X", "name": "ReadAsync 75", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735840805997, "dur": 1, "ph": "X", "name": "ProcessMessages 417", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735840805999, "dur": 42, "ph": "X", "name": "ReadAsync 417", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735840806043, "dur": 1, "ph": "X", "name": "ProcessMessages 473", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735840806045, "dur": 33, "ph": "X", "name": "ReadAsync 473", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735840806080, "dur": 27, "ph": "X", "name": "ReadAsync 507", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735840806109, "dur": 1, "ph": "X", "name": "ProcessMessages 346", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735840806111, "dur": 34, "ph": "X", "name": "ReadAsync 346", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735840806147, "dur": 23, "ph": "X", "name": "ReadAsync 382", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735840806172, "dur": 29, "ph": "X", "name": "ReadAsync 283", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735840806203, "dur": 32, "ph": "X", "name": "ReadAsync 330", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735840806237, "dur": 1, "ph": "X", "name": "ProcessMessages 256", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735840806239, "dur": 39, "ph": "X", "name": "ReadAsync 256", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735840806279, "dur": 26, "ph": "X", "name": "ReadAsync 522", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735840806307, "dur": 1, "ph": "X", "name": "ProcessMessages 336", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735840806310, "dur": 24, "ph": "X", "name": "ReadAsync 336", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735840806336, "dur": 24, "ph": "X", "name": "ReadAsync 383", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735840806361, "dur": 32, "ph": "X", "name": "ReadAsync 407", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735840806396, "dur": 41, "ph": "X", "name": "ReadAsync 430", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735840806440, "dur": 1, "ph": "X", "name": "ProcessMessages 393", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735840806442, "dur": 23, "ph": "X", "name": "ReadAsync 393", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735840806466, "dur": 28, "ph": "X", "name": "ReadAsync 454", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735840806496, "dur": 21, "ph": "X", "name": "ReadAsync 197", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735840806519, "dur": 24, "ph": "X", "name": "ReadAsync 121", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735840806545, "dur": 33, "ph": "X", "name": "ReadAsync 323", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735840806580, "dur": 1, "ph": "X", "name": "ProcessMessages 363", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735840806582, "dur": 35, "ph": "X", "name": "ReadAsync 363", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735840806620, "dur": 29, "ph": "X", "name": "ReadAsync 403", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735840806651, "dur": 23, "ph": "X", "name": "ReadAsync 420", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735840806676, "dur": 22, "ph": "X", "name": "ReadAsync 528", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735840806700, "dur": 35, "ph": "X", "name": "ReadAsync 84", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735840806737, "dur": 35, "ph": "X", "name": "ReadAsync 405", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735840806774, "dur": 54, "ph": "X", "name": "ReadAsync 688", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735840806830, "dur": 1, "ph": "X", "name": "ProcessMessages 398", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735840806832, "dur": 39, "ph": "X", "name": "ReadAsync 398", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735840806874, "dur": 1, "ph": "X", "name": "ProcessMessages 484", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735840806875, "dur": 98, "ph": "X", "name": "ReadAsync 484", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735840806976, "dur": 65, "ph": "X", "name": "ReadAsync 594", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735840807042, "dur": 55, "ph": "X", "name": "ReadAsync 810", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735840807100, "dur": 36, "ph": "X", "name": "ReadAsync 716", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735840807140, "dur": 1, "ph": "X", "name": "ProcessMessages 375", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735840807141, "dur": 31, "ph": "X", "name": "ReadAsync 375", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735840807177, "dur": 1, "ph": "X", "name": "ProcessMessages 688", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735840807179, "dur": 36, "ph": "X", "name": "ReadAsync 688", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735840807217, "dur": 25, "ph": "X", "name": "ReadAsync 274", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735840807244, "dur": 29, "ph": "X", "name": "ReadAsync 457", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735840807274, "dur": 27, "ph": "X", "name": "ReadAsync 142", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735840807304, "dur": 34, "ph": "X", "name": "ReadAsync 329", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735840807340, "dur": 29, "ph": "X", "name": "ReadAsync 451", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735840807371, "dur": 25, "ph": "X", "name": "ReadAsync 245", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735840807398, "dur": 38, "ph": "X", "name": "ReadAsync 396", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735840807439, "dur": 1, "ph": "X", "name": "ProcessMessages 147", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735840807441, "dur": 47, "ph": "X", "name": "ReadAsync 147", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735840807490, "dur": 1, "ph": "X", "name": "ProcessMessages 401", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735840807491, "dur": 28, "ph": "X", "name": "ReadAsync 401", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735840807521, "dur": 1, "ph": "X", "name": "ProcessMessages 344", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735840807522, "dur": 33, "ph": "X", "name": "ReadAsync 344", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735840807558, "dur": 1, "ph": "X", "name": "ProcessMessages 321", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735840807560, "dur": 45, "ph": "X", "name": "ReadAsync 321", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735840807608, "dur": 1, "ph": "X", "name": "ProcessMessages 500", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735840807609, "dur": 31, "ph": "X", "name": "ReadAsync 500", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735840807642, "dur": 29, "ph": "X", "name": "ReadAsync 490", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735840807674, "dur": 44, "ph": "X", "name": "ReadAsync 430", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735840807720, "dur": 34, "ph": "X", "name": "ReadAsync 570", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735840807756, "dur": 1, "ph": "X", "name": "ProcessMessages 478", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735840807758, "dur": 35, "ph": "X", "name": "ReadAsync 478", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735840807795, "dur": 24, "ph": "X", "name": "ReadAsync 495", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735840807821, "dur": 31, "ph": "X", "name": "ReadAsync 216", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735840807855, "dur": 1, "ph": "X", "name": "ProcessMessages 356", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735840807856, "dur": 32, "ph": "X", "name": "ReadAsync 356", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735840807890, "dur": 1, "ph": "X", "name": "ProcessMessages 704", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735840807891, "dur": 24, "ph": "X", "name": "ReadAsync 704", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735840807917, "dur": 23, "ph": "X", "name": "ReadAsync 383", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735840807942, "dur": 25, "ph": "X", "name": "ReadAsync 332", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735840807969, "dur": 136, "ph": "X", "name": "ReadAsync 403", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735840808107, "dur": 25, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735840808135, "dur": 23, "ph": "X", "name": "ReadAsync 453", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735840808160, "dur": 23, "ph": "X", "name": "ReadAsync 452", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735840808185, "dur": 22, "ph": "X", "name": "ReadAsync 302", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735840808209, "dur": 20, "ph": "X", "name": "ReadAsync 330", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735840808230, "dur": 29, "ph": "X", "name": "ReadAsync 433", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735840808262, "dur": 63, "ph": "X", "name": "ReadAsync 179", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735840808327, "dur": 46, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735840808376, "dur": 1, "ph": "X", "name": "ProcessMessages 761", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735840808378, "dur": 37, "ph": "X", "name": "ReadAsync 761", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735840808416, "dur": 1, "ph": "X", "name": "ProcessMessages 797", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735840808418, "dur": 28, "ph": "X", "name": "ReadAsync 797", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735840808448, "dur": 27, "ph": "X", "name": "ReadAsync 441", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735840808476, "dur": 29, "ph": "X", "name": "ReadAsync 354", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735840808506, "dur": 24, "ph": "X", "name": "ReadAsync 122", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735840808531, "dur": 26, "ph": "X", "name": "ReadAsync 476", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735840808561, "dur": 1, "ph": "X", "name": "ProcessMessages 338", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735840808562, "dur": 35, "ph": "X", "name": "ReadAsync 338", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735840808599, "dur": 1, "ph": "X", "name": "ProcessMessages 505", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735840808602, "dur": 24, "ph": "X", "name": "ReadAsync 505", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735840808627, "dur": 21, "ph": "X", "name": "ReadAsync 352", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735840808650, "dur": 22, "ph": "X", "name": "ReadAsync 274", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735840808674, "dur": 24, "ph": "X", "name": "ReadAsync 128", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735840808700, "dur": 26, "ph": "X", "name": "ReadAsync 413", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735840808728, "dur": 23, "ph": "X", "name": "ReadAsync 429", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735840808753, "dur": 22, "ph": "X", "name": "ReadAsync 322", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735840808776, "dur": 22, "ph": "X", "name": "ReadAsync 343", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735840808800, "dur": 21, "ph": "X", "name": "ReadAsync 317", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735840808822, "dur": 23, "ph": "X", "name": "ReadAsync 165", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735840808846, "dur": 22, "ph": "X", "name": "ReadAsync 452", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735840808869, "dur": 22, "ph": "X", "name": "ReadAsync 349", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735840808892, "dur": 21, "ph": "X", "name": "ReadAsync 385", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735840808915, "dur": 23, "ph": "X", "name": "ReadAsync 310", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735840808940, "dur": 1, "ph": "X", "name": "ProcessMessages 309", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735840808942, "dur": 38, "ph": "X", "name": "ReadAsync 309", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735840808983, "dur": 1, "ph": "X", "name": "ProcessMessages 312", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735840808985, "dur": 38, "ph": "X", "name": "ReadAsync 312", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735840809026, "dur": 34, "ph": "X", "name": "ReadAsync 494", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735840809063, "dur": 1, "ph": "X", "name": "ProcessMessages 383", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735840809065, "dur": 40, "ph": "X", "name": "ReadAsync 383", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735840809108, "dur": 38, "ph": "X", "name": "ReadAsync 203", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735840809147, "dur": 34, "ph": "X", "name": "ReadAsync 504", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735840809184, "dur": 1, "ph": "X", "name": "ProcessMessages 412", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735840809186, "dur": 47, "ph": "X", "name": "ReadAsync 412", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735840809236, "dur": 1, "ph": "X", "name": "ProcessMessages 451", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735840809239, "dur": 38, "ph": "X", "name": "ReadAsync 451", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735840809279, "dur": 1, "ph": "X", "name": "ProcessMessages 557", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735840809281, "dur": 46, "ph": "X", "name": "ReadAsync 557", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735840809328, "dur": 46, "ph": "X", "name": "ReadAsync 136", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735840809377, "dur": 1, "ph": "X", "name": "ProcessMessages 506", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735840809379, "dur": 38, "ph": "X", "name": "ReadAsync 506", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735840809419, "dur": 35, "ph": "X", "name": "ReadAsync 531", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735840809458, "dur": 42, "ph": "X", "name": "ReadAsync 237", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735840809503, "dur": 1, "ph": "X", "name": "ProcessMessages 84", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735840809505, "dur": 35, "ph": "X", "name": "ReadAsync 84", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735840809542, "dur": 28, "ph": "X", "name": "ReadAsync 508", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735840809571, "dur": 23, "ph": "X", "name": "ReadAsync 298", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735840809596, "dur": 25, "ph": "X", "name": "ReadAsync 450", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735840809623, "dur": 24, "ph": "X", "name": "ReadAsync 306", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735840809649, "dur": 32, "ph": "X", "name": "ReadAsync 435", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735840809684, "dur": 1, "ph": "X", "name": "ProcessMessages 428", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735840809686, "dur": 35, "ph": "X", "name": "ReadAsync 428", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735840809723, "dur": 1, "ph": "X", "name": "ProcessMessages 375", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735840809725, "dur": 31, "ph": "X", "name": "ReadAsync 375", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735840809758, "dur": 29, "ph": "X", "name": "ReadAsync 505", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735840809790, "dur": 1, "ph": "X", "name": "ProcessMessages 210", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735840809791, "dur": 66, "ph": "X", "name": "ReadAsync 210", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735840809858, "dur": 35, "ph": "X", "name": "ReadAsync 566", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735840809896, "dur": 1, "ph": "X", "name": "ProcessMessages 487", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735840809897, "dur": 34, "ph": "X", "name": "ReadAsync 487", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735840809934, "dur": 40, "ph": "X", "name": "ReadAsync 320", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735840809975, "dur": 1, "ph": "X", "name": "ProcessMessages 521", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735840809977, "dur": 27, "ph": "X", "name": "ReadAsync 521", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735840810006, "dur": 34, "ph": "X", "name": "ReadAsync 507", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735840810043, "dur": 61, "ph": "X", "name": "ReadAsync 260", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735840810107, "dur": 31, "ph": "X", "name": "ReadAsync 563", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735840810141, "dur": 1, "ph": "X", "name": "ProcessMessages 615", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735840810142, "dur": 38, "ph": "X", "name": "ReadAsync 615", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735840810183, "dur": 1, "ph": "X", "name": "ProcessMessages 479", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735840810185, "dur": 30, "ph": "X", "name": "ReadAsync 479", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735840810217, "dur": 23, "ph": "X", "name": "ReadAsync 481", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735840810243, "dur": 23, "ph": "X", "name": "ReadAsync 305", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735840810269, "dur": 23, "ph": "X", "name": "ReadAsync 289", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735840810293, "dur": 24, "ph": "X", "name": "ReadAsync 393", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735840810318, "dur": 23, "ph": "X", "name": "ReadAsync 431", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735840810343, "dur": 22, "ph": "X", "name": "ReadAsync 549", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735840810368, "dur": 24, "ph": "X", "name": "ReadAsync 322", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735840810393, "dur": 18, "ph": "X", "name": "ReadAsync 434", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735840810413, "dur": 22, "ph": "X", "name": "ReadAsync 340", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735840810436, "dur": 24, "ph": "X", "name": "ReadAsync 409", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735840810462, "dur": 27, "ph": "X", "name": "ReadAsync 455", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735840810492, "dur": 24, "ph": "X", "name": "ReadAsync 547", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735840810518, "dur": 22, "ph": "X", "name": "ReadAsync 454", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735840810542, "dur": 23, "ph": "X", "name": "ReadAsync 385", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735840810566, "dur": 23, "ph": "X", "name": "ReadAsync 462", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735840810590, "dur": 1, "ph": "X", "name": "ProcessMessages 293", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735840810591, "dur": 24, "ph": "X", "name": "ReadAsync 293", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735840810617, "dur": 20, "ph": "X", "name": "ReadAsync 433", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735840810639, "dur": 22, "ph": "X", "name": "ReadAsync 427", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735840810662, "dur": 26, "ph": "X", "name": "ReadAsync 315", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735840810690, "dur": 23, "ph": "X", "name": "ReadAsync 350", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735840810715, "dur": 30, "ph": "X", "name": "ReadAsync 558", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735840810747, "dur": 1, "ph": "X", "name": "ProcessMessages 321", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735840810748, "dur": 44, "ph": "X", "name": "ReadAsync 321", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735840810795, "dur": 35, "ph": "X", "name": "ReadAsync 397", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735840810833, "dur": 1, "ph": "X", "name": "ProcessMessages 411", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735840810834, "dur": 41, "ph": "X", "name": "ReadAsync 411", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735840810878, "dur": 1, "ph": "X", "name": "ProcessMessages 590", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735840810879, "dur": 38, "ph": "X", "name": "ReadAsync 590", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735840810919, "dur": 36, "ph": "X", "name": "ReadAsync 358", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735840810958, "dur": 1, "ph": "X", "name": "ProcessMessages 647", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735840810959, "dur": 32, "ph": "X", "name": "ReadAsync 647", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735840810993, "dur": 38, "ph": "X", "name": "ReadAsync 605", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735840811036, "dur": 44, "ph": "X", "name": "ReadAsync 383", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735840811082, "dur": 1, "ph": "X", "name": "ProcessMessages 723", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735840811084, "dur": 30, "ph": "X", "name": "ReadAsync 723", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735840811116, "dur": 22, "ph": "X", "name": "ReadAsync 392", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735840811140, "dur": 23, "ph": "X", "name": "ReadAsync 487", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735840811165, "dur": 40, "ph": "X", "name": "ReadAsync 67", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735840811208, "dur": 1, "ph": "X", "name": "ProcessMessages 458", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735840811209, "dur": 30, "ph": "X", "name": "ReadAsync 458", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735840811242, "dur": 26, "ph": "X", "name": "ReadAsync 516", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735840811269, "dur": 22, "ph": "X", "name": "ReadAsync 388", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735840811292, "dur": 49, "ph": "X", "name": "ReadAsync 555", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735840811343, "dur": 31, "ph": "X", "name": "ReadAsync 350", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735840811376, "dur": 24, "ph": "X", "name": "ReadAsync 921", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735840811404, "dur": 31, "ph": "X", "name": "ReadAsync 251", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735840811437, "dur": 24, "ph": "X", "name": "ReadAsync 645", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735840811463, "dur": 24, "ph": "X", "name": "ReadAsync 415", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735840811488, "dur": 34, "ph": "X", "name": "ReadAsync 307", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735840811526, "dur": 1, "ph": "X", "name": "ProcessMessages 420", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735840811527, "dur": 44, "ph": "X", "name": "ReadAsync 420", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735840811574, "dur": 1, "ph": "X", "name": "ProcessMessages 572", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735840811576, "dur": 45, "ph": "X", "name": "ReadAsync 572", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735840811623, "dur": 33, "ph": "X", "name": "ReadAsync 581", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735840811658, "dur": 1, "ph": "X", "name": "ProcessMessages 371", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735840811660, "dur": 40, "ph": "X", "name": "ReadAsync 371", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735840811702, "dur": 29, "ph": "X", "name": "ReadAsync 482", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735840811734, "dur": 1, "ph": "X", "name": "ProcessMessages 349", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735840811736, "dur": 37, "ph": "X", "name": "ReadAsync 349", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735840811775, "dur": 30, "ph": "X", "name": "ReadAsync 316", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735840811808, "dur": 1, "ph": "X", "name": "ProcessMessages 417", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735840811810, "dur": 34, "ph": "X", "name": "ReadAsync 417", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735840811847, "dur": 24, "ph": "X", "name": "ReadAsync 331", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735840811873, "dur": 26, "ph": "X", "name": "ReadAsync 196", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735840811901, "dur": 23, "ph": "X", "name": "ReadAsync 169", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735840811925, "dur": 24, "ph": "X", "name": "ReadAsync 228", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735840811951, "dur": 23, "ph": "X", "name": "ReadAsync 258", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735840811978, "dur": 30, "ph": "X", "name": "ReadAsync 247", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735840812010, "dur": 1, "ph": "X", "name": "ProcessMessages 349", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735840812012, "dur": 35, "ph": "X", "name": "ReadAsync 349", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735840812049, "dur": 24, "ph": "X", "name": "ReadAsync 465", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735840812075, "dur": 23, "ph": "X", "name": "ReadAsync 213", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735840812100, "dur": 28, "ph": "X", "name": "ReadAsync 347", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735840812130, "dur": 29, "ph": "X", "name": "ReadAsync 366", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735840812162, "dur": 32, "ph": "X", "name": "ReadAsync 341", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735840812197, "dur": 23, "ph": "X", "name": "ReadAsync 459", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735840812221, "dur": 22, "ph": "X", "name": "ReadAsync 164", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735840812244, "dur": 21, "ph": "X", "name": "ReadAsync 281", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735840812267, "dur": 23, "ph": "X", "name": "ReadAsync 245", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735840812291, "dur": 41, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735840812333, "dur": 1, "ph": "X", "name": "ProcessMessages 228", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735840812335, "dur": 24, "ph": "X", "name": "ReadAsync 228", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735840812360, "dur": 23, "ph": "X", "name": "ReadAsync 304", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735840812384, "dur": 27, "ph": "X", "name": "ReadAsync 336", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735840812414, "dur": 1, "ph": "X", "name": "ProcessMessages 292", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735840812416, "dur": 38, "ph": "X", "name": "ReadAsync 292", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735840812458, "dur": 43, "ph": "X", "name": "ReadAsync 57", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735840812504, "dur": 1, "ph": "X", "name": "ProcessMessages 448", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735840812505, "dur": 40, "ph": "X", "name": "ReadAsync 448", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735840812549, "dur": 36, "ph": "X", "name": "ReadAsync 291", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735840812588, "dur": 1, "ph": "X", "name": "ProcessMessages 688", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735840812590, "dur": 35, "ph": "X", "name": "ReadAsync 688", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735840812628, "dur": 24, "ph": "X", "name": "ReadAsync 368", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735840812654, "dur": 23, "ph": "X", "name": "ReadAsync 367", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735840812678, "dur": 25, "ph": "X", "name": "ReadAsync 348", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735840812705, "dur": 21, "ph": "X", "name": "ReadAsync 201", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735840812726, "dur": 1, "ph": "X", "name": "ProcessMessages 56", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735840812728, "dur": 25, "ph": "X", "name": "ReadAsync 56", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735840812755, "dur": 23, "ph": "X", "name": "ReadAsync 236", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735840812779, "dur": 23, "ph": "X", "name": "ReadAsync 313", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735840812803, "dur": 22, "ph": "X", "name": "ReadAsync 375", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735840812827, "dur": 22, "ph": "X", "name": "ReadAsync 311", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735840812851, "dur": 22, "ph": "X", "name": "ReadAsync 275", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735840812875, "dur": 23, "ph": "X", "name": "ReadAsync 247", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735840812900, "dur": 27, "ph": "X", "name": "ReadAsync 373", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735840812930, "dur": 1, "ph": "X", "name": "ProcessMessages 149", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735840812932, "dur": 29, "ph": "X", "name": "ReadAsync 149", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735840812972, "dur": 27, "ph": "X", "name": "ReadAsync 89", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735840813001, "dur": 24, "ph": "X", "name": "ReadAsync 430", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735840813027, "dur": 24, "ph": "X", "name": "ReadAsync 215", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735840813052, "dur": 25, "ph": "X", "name": "ReadAsync 352", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735840813079, "dur": 23, "ph": "X", "name": "ReadAsync 348", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735840813104, "dur": 36, "ph": "X", "name": "ReadAsync 299", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735840813142, "dur": 37, "ph": "X", "name": "ReadAsync 214", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735840813180, "dur": 29, "ph": "X", "name": "ReadAsync 250", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735840813211, "dur": 19, "ph": "X", "name": "ReadAsync 373", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735840813231, "dur": 23, "ph": "X", "name": "ReadAsync 133", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735840813255, "dur": 27, "ph": "X", "name": "ReadAsync 202", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735840813284, "dur": 27, "ph": "X", "name": "ReadAsync 368", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735840813313, "dur": 31, "ph": "X", "name": "ReadAsync 237", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735840813346, "dur": 26, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735840813374, "dur": 29, "ph": "X", "name": "ReadAsync 347", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735840813406, "dur": 1, "ph": "X", "name": "ProcessMessages 283", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735840813408, "dur": 40, "ph": "X", "name": "ReadAsync 283", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735840813451, "dur": 33, "ph": "X", "name": "ReadAsync 356", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735840813485, "dur": 27, "ph": "X", "name": "ReadAsync 378", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735840813514, "dur": 21, "ph": "X", "name": "ReadAsync 491", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735840813535, "dur": 2, "ph": "X", "name": "ProcessMessages 143", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735840813538, "dur": 21, "ph": "X", "name": "ReadAsync 143", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735840813560, "dur": 37, "ph": "X", "name": "ReadAsync 171", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735840813600, "dur": 1, "ph": "X", "name": "ProcessMessages 289", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735840813602, "dur": 31, "ph": "X", "name": "ReadAsync 289", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735840813635, "dur": 22, "ph": "X", "name": "ReadAsync 588", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735840813658, "dur": 23, "ph": "X", "name": "ReadAsync 183", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735840813682, "dur": 22, "ph": "X", "name": "ReadAsync 346", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735840813706, "dur": 22, "ph": "X", "name": "ReadAsync 495", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735840813732, "dur": 32, "ph": "X", "name": "ReadAsync 144", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735840813766, "dur": 24, "ph": "X", "name": "ReadAsync 330", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735840813792, "dur": 24, "ph": "X", "name": "ReadAsync 266", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735840813819, "dur": 26, "ph": "X", "name": "ReadAsync 451", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735840813847, "dur": 39, "ph": "X", "name": "ReadAsync 248", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735840813890, "dur": 34, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735840813926, "dur": 23, "ph": "X", "name": "ReadAsync 581", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735840813952, "dur": 26, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735840813980, "dur": 27, "ph": "X", "name": "ReadAsync 40", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735840814008, "dur": 23, "ph": "X", "name": "ReadAsync 458", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735840814032, "dur": 36, "ph": "X", "name": "ReadAsync 138", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735840814070, "dur": 20, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735840814092, "dur": 21, "ph": "X", "name": "ReadAsync 315", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735840814114, "dur": 23, "ph": "X", "name": "ReadAsync 193", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735840814140, "dur": 26, "ph": "X", "name": "ReadAsync 225", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735840814168, "dur": 21, "ph": "X", "name": "ReadAsync 304", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735840814190, "dur": 20, "ph": "X", "name": "ReadAsync 57", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735840814212, "dur": 18, "ph": "X", "name": "ReadAsync 170", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735840814232, "dur": 17, "ph": "X", "name": "ReadAsync 249", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735840814251, "dur": 23, "ph": "X", "name": "ReadAsync 307", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735840814275, "dur": 22, "ph": "X", "name": "ReadAsync 224", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735840814299, "dur": 27, "ph": "X", "name": "ReadAsync 134", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735840814330, "dur": 48, "ph": "X", "name": "ReadAsync 181", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735840814380, "dur": 29, "ph": "X", "name": "ReadAsync 616", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735840814411, "dur": 1, "ph": "X", "name": "ProcessMessages 237", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735840814412, "dur": 22, "ph": "X", "name": "ReadAsync 237", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735840814435, "dur": 26, "ph": "X", "name": "ReadAsync 8", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735840814462, "dur": 26, "ph": "X", "name": "ReadAsync 162", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735840814491, "dur": 1, "ph": "X", "name": "ProcessMessages 263", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735840814493, "dur": 32, "ph": "X", "name": "ReadAsync 263", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735840814528, "dur": 25, "ph": "X", "name": "ReadAsync 503", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735840814555, "dur": 21, "ph": "X", "name": "ReadAsync 66", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735840814577, "dur": 24, "ph": "X", "name": "ReadAsync 192", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735840814604, "dur": 1, "ph": "X", "name": "ProcessMessages 257", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735840814605, "dur": 28, "ph": "X", "name": "ReadAsync 257", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735840814636, "dur": 25, "ph": "X", "name": "ReadAsync 8", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735840814662, "dur": 23, "ph": "X", "name": "ReadAsync 239", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735840814687, "dur": 25, "ph": "X", "name": "ReadAsync 388", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735840814714, "dur": 39, "ph": "X", "name": "ReadAsync 359", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735840814755, "dur": 22, "ph": "X", "name": "ReadAsync 396", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735840814779, "dur": 23, "ph": "X", "name": "ReadAsync 282", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735840814803, "dur": 26, "ph": "X", "name": "ReadAsync 399", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735840814831, "dur": 25, "ph": "X", "name": "ReadAsync 174", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735840814860, "dur": 35, "ph": "X", "name": "ReadAsync 140", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735840814896, "dur": 29, "ph": "X", "name": "ReadAsync 177", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735840814929, "dur": 30, "ph": "X", "name": "ReadAsync 301", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735840814961, "dur": 26, "ph": "X", "name": "ReadAsync 456", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735840814989, "dur": 25, "ph": "X", "name": "ReadAsync 493", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735840815016, "dur": 26, "ph": "X", "name": "ReadAsync 126", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735840815044, "dur": 29, "ph": "X", "name": "ReadAsync 340", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735840815077, "dur": 44, "ph": "X", "name": "ReadAsync 76", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735840815125, "dur": 1, "ph": "X", "name": "ProcessMessages 570", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735840815127, "dur": 31, "ph": "X", "name": "ReadAsync 570", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735840815161, "dur": 28, "ph": "X", "name": "ReadAsync 240", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735840815192, "dur": 39, "ph": "X", "name": "ReadAsync 169", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735840815234, "dur": 23, "ph": "X", "name": "ReadAsync 452", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735840815260, "dur": 23, "ph": "X", "name": "ReadAsync 229", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735840815284, "dur": 23, "ph": "X", "name": "ReadAsync 377", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735840815309, "dur": 52, "ph": "X", "name": "ReadAsync 261", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735840815366, "dur": 47, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735840815416, "dur": 1, "ph": "X", "name": "ProcessMessages 309", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735840815418, "dur": 46, "ph": "X", "name": "ReadAsync 309", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735840815467, "dur": 1, "ph": "X", "name": "ProcessMessages 478", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735840815468, "dur": 44, "ph": "X", "name": "ReadAsync 478", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735840815515, "dur": 1, "ph": "X", "name": "ProcessMessages 472", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735840815517, "dur": 38, "ph": "X", "name": "ReadAsync 472", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735840815558, "dur": 41, "ph": "X", "name": "ReadAsync 456", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735840815602, "dur": 26, "ph": "X", "name": "ReadAsync 484", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735840815631, "dur": 21, "ph": "X", "name": "ReadAsync 381", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735840815653, "dur": 34, "ph": "X", "name": "ReadAsync 237", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735840815688, "dur": 25, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735840815715, "dur": 22, "ph": "X", "name": "ReadAsync 321", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735840815738, "dur": 23, "ph": "X", "name": "ReadAsync 348", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735840815762, "dur": 19, "ph": "X", "name": "ReadAsync 295", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735840815784, "dur": 29, "ph": "X", "name": "ReadAsync 57", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735840815818, "dur": 32, "ph": "X", "name": "ReadAsync 162", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735840815852, "dur": 26, "ph": "X", "name": "ReadAsync 227", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735840815881, "dur": 29, "ph": "X", "name": "ReadAsync 364", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735840815913, "dur": 1, "ph": "X", "name": "ProcessMessages 295", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735840815914, "dur": 27, "ph": "X", "name": "ReadAsync 295", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735840815943, "dur": 21, "ph": "X", "name": "ReadAsync 168", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735840815966, "dur": 23, "ph": "X", "name": "ReadAsync 266", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735840815990, "dur": 22, "ph": "X", "name": "ReadAsync 374", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735840816014, "dur": 24, "ph": "X", "name": "ReadAsync 401", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735840816041, "dur": 24, "ph": "X", "name": "ReadAsync 155", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735840816066, "dur": 39, "ph": "X", "name": "ReadAsync 422", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735840816107, "dur": 27, "ph": "X", "name": "ReadAsync 355", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735840816136, "dur": 21, "ph": "X", "name": "ReadAsync 459", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735840816159, "dur": 26, "ph": "X", "name": "ReadAsync 278", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735840816187, "dur": 24, "ph": "X", "name": "ReadAsync 145", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735840816214, "dur": 29, "ph": "X", "name": "ReadAsync 109", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735840816246, "dur": 26, "ph": "X", "name": "ReadAsync 334", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735840816274, "dur": 22, "ph": "X", "name": "ReadAsync 429", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735840816297, "dur": 1, "ph": "X", "name": "ProcessMessages 228", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735840816299, "dur": 22, "ph": "X", "name": "ReadAsync 228", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735840816323, "dur": 22, "ph": "X", "name": "ReadAsync 171", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735840816348, "dur": 1, "ph": "X", "name": "ProcessMessages 343", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735840816349, "dur": 35, "ph": "X", "name": "ReadAsync 343", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735840816386, "dur": 24, "ph": "X", "name": "ReadAsync 555", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735840816412, "dur": 22, "ph": "X", "name": "ReadAsync 66", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735840816436, "dur": 41, "ph": "X", "name": "ReadAsync 117", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735840816481, "dur": 27, "ph": "X", "name": "ReadAsync 155", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735840816510, "dur": 31, "ph": "X", "name": "ReadAsync 369", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735840816545, "dur": 36, "ph": "X", "name": "ReadAsync 400", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735840816584, "dur": 29, "ph": "X", "name": "ReadAsync 68", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735840816614, "dur": 52, "ph": "X", "name": "ReadAsync 454", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735840816668, "dur": 24, "ph": "X", "name": "ReadAsync 1", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735840816696, "dur": 34, "ph": "X", "name": "ReadAsync 255", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735840816731, "dur": 26, "ph": "X", "name": "ReadAsync 347", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735840816758, "dur": 22, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735840816783, "dur": 29, "ph": "X", "name": "ReadAsync 233", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735840816816, "dur": 29, "ph": "X", "name": "ReadAsync 168", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735840816847, "dur": 24, "ph": "X", "name": "ReadAsync 524", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735840816874, "dur": 1, "ph": "X", "name": "ProcessMessages 145", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735840816875, "dur": 33, "ph": "X", "name": "ReadAsync 145", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735840816911, "dur": 24, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735840816937, "dur": 18, "ph": "X", "name": "ReadAsync 409", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735840816957, "dur": 22, "ph": "X", "name": "ReadAsync 51", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735840816981, "dur": 20, "ph": "X", "name": "ReadAsync 318", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735840817004, "dur": 24, "ph": "X", "name": "ReadAsync 367", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735840817029, "dur": 23, "ph": "X", "name": "ReadAsync 345", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735840817053, "dur": 25, "ph": "X", "name": "ReadAsync 202", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735840817080, "dur": 26, "ph": "X", "name": "ReadAsync 331", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735840817109, "dur": 25, "ph": "X", "name": "ReadAsync 435", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735840817139, "dur": 33, "ph": "X", "name": "ReadAsync 342", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735840817174, "dur": 30, "ph": "X", "name": "ReadAsync 66", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735840817206, "dur": 23, "ph": "X", "name": "ReadAsync 460", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735840817231, "dur": 26, "ph": "X", "name": "ReadAsync 339", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735840817260, "dur": 23, "ph": "X", "name": "ReadAsync 516", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735840817284, "dur": 22, "ph": "X", "name": "ReadAsync 490", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735840817308, "dur": 23, "ph": "X", "name": "ReadAsync 407", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735840817332, "dur": 19, "ph": "X", "name": "ReadAsync 345", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735840817353, "dur": 20, "ph": "X", "name": "ReadAsync 412", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735840817375, "dur": 26, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735840817404, "dur": 31, "ph": "X", "name": "ReadAsync 240", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735840817437, "dur": 23, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735840817462, "dur": 24, "ph": "X", "name": "ReadAsync 381", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735840817488, "dur": 23, "ph": "X", "name": "ReadAsync 408", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735840817512, "dur": 30, "ph": "X", "name": "ReadAsync 352", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735840817544, "dur": 1, "ph": "X", "name": "ProcessMessages 459", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735840817546, "dur": 32, "ph": "X", "name": "ReadAsync 459", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735840817580, "dur": 23, "ph": "X", "name": "ReadAsync 587", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735840817605, "dur": 32, "ph": "X", "name": "ReadAsync 135", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735840817639, "dur": 30, "ph": "X", "name": "ReadAsync 442", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735840817673, "dur": 34, "ph": "X", "name": "ReadAsync 296", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735840817711, "dur": 49, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735840817764, "dur": 1, "ph": "X", "name": "ProcessMessages 516", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735840817766, "dur": 41, "ph": "X", "name": "ReadAsync 516", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735840817809, "dur": 32, "ph": "X", "name": "ReadAsync 423", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735840817843, "dur": 1, "ph": "X", "name": "ProcessMessages 240", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735840817845, "dur": 24, "ph": "X", "name": "ReadAsync 240", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735840817872, "dur": 31, "ph": "X", "name": "ReadAsync 215", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735840817905, "dur": 24, "ph": "X", "name": "ReadAsync 356", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735840817931, "dur": 24, "ph": "X", "name": "ReadAsync 479", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735840817956, "dur": 2, "ph": "X", "name": "ProcessMessages 139", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735840817959, "dur": 25, "ph": "X", "name": "ReadAsync 139", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735840817985, "dur": 23, "ph": "X", "name": "ReadAsync 194", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735840818010, "dur": 23, "ph": "X", "name": "ReadAsync 363", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735840818035, "dur": 23, "ph": "X", "name": "ReadAsync 441", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735840818059, "dur": 22, "ph": "X", "name": "ReadAsync 240", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735840818083, "dur": 23, "ph": "X", "name": "ReadAsync 219", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735840818107, "dur": 23, "ph": "X", "name": "ReadAsync 288", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735840818131, "dur": 28, "ph": "X", "name": "ReadAsync 430", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735840818161, "dur": 20, "ph": "X", "name": "ReadAsync 258", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735840818183, "dur": 22, "ph": "X", "name": "ReadAsync 189", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735840818207, "dur": 17, "ph": "X", "name": "ReadAsync 265", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735840818226, "dur": 24, "ph": "X", "name": "ReadAsync 63", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735840818251, "dur": 21, "ph": "X", "name": "ReadAsync 360", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735840818273, "dur": 20, "ph": "X", "name": "ReadAsync 237", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735840818295, "dur": 24, "ph": "X", "name": "ReadAsync 99", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735840818322, "dur": 23, "ph": "X", "name": "ReadAsync 324", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735840818346, "dur": 16, "ph": "X", "name": "ReadAsync 362", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735840818364, "dur": 33, "ph": "X", "name": "ReadAsync 362", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735840818401, "dur": 34, "ph": "X", "name": "ReadAsync 321", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735840818437, "dur": 26, "ph": "X", "name": "ReadAsync 548", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735840818465, "dur": 1, "ph": "X", "name": "ProcessMessages 519", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735840818467, "dur": 38, "ph": "X", "name": "ReadAsync 519", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735840818508, "dur": 1, "ph": "X", "name": "ProcessMessages 310", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735840818510, "dur": 36, "ph": "X", "name": "ReadAsync 310", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735840818550, "dur": 31, "ph": "X", "name": "ReadAsync 8", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735840818583, "dur": 25, "ph": "X", "name": "ReadAsync 242", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735840818611, "dur": 23, "ph": "X", "name": "ReadAsync 425", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735840818636, "dur": 21, "ph": "X", "name": "ReadAsync 262", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735840818659, "dur": 23, "ph": "X", "name": "ReadAsync 232", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735840818684, "dur": 35, "ph": "X", "name": "ReadAsync 8", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735840818721, "dur": 1, "ph": "X", "name": "ProcessMessages 331", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735840818723, "dur": 44, "ph": "X", "name": "ReadAsync 331", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735840818770, "dur": 1, "ph": "X", "name": "ProcessMessages 321", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735840818772, "dur": 35, "ph": "X", "name": "ReadAsync 321", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735840818810, "dur": 23, "ph": "X", "name": "ReadAsync 412", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735840818835, "dur": 26, "ph": "X", "name": "ReadAsync 56", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735840818865, "dur": 28, "ph": "X", "name": "ReadAsync 107", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735840818894, "dur": 29, "ph": "X", "name": "ReadAsync 536", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735840818925, "dur": 24, "ph": "X", "name": "ReadAsync 423", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735840818951, "dur": 24, "ph": "X", "name": "ReadAsync 67", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735840818979, "dur": 28, "ph": "X", "name": "ReadAsync 199", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735840819009, "dur": 26, "ph": "X", "name": "ReadAsync 451", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735840819038, "dur": 32, "ph": "X", "name": "ReadAsync 408", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735840819071, "dur": 23, "ph": "X", "name": "ReadAsync 102", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735840819095, "dur": 22, "ph": "X", "name": "ReadAsync 370", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735840819119, "dur": 21, "ph": "X", "name": "ReadAsync 339", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735840819142, "dur": 23, "ph": "X", "name": "ReadAsync 114", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735840819167, "dur": 22, "ph": "X", "name": "ReadAsync 381", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735840819190, "dur": 1, "ph": "X", "name": "ProcessMessages 254", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735840819191, "dur": 22, "ph": "X", "name": "ReadAsync 254", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735840819215, "dur": 19, "ph": "X", "name": "ReadAsync 266", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735840819235, "dur": 17, "ph": "X", "name": "ReadAsync 277", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735840819254, "dur": 25, "ph": "X", "name": "ReadAsync 253", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735840819281, "dur": 40, "ph": "X", "name": "ReadAsync 181", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735840819322, "dur": 22, "ph": "X", "name": "ReadAsync 68", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735840819347, "dur": 1, "ph": "X", "name": "ProcessMessages 422", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735840819348, "dur": 30, "ph": "X", "name": "ReadAsync 422", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735840819380, "dur": 24, "ph": "X", "name": "ReadAsync 243", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735840819406, "dur": 20, "ph": "X", "name": "ReadAsync 436", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735840819428, "dur": 24, "ph": "X", "name": "ReadAsync 319", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735840819453, "dur": 24, "ph": "X", "name": "ReadAsync 271", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735840819479, "dur": 25, "ph": "X", "name": "ReadAsync 294", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735840819506, "dur": 24, "ph": "X", "name": "ReadAsync 261", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735840819531, "dur": 23, "ph": "X", "name": "ReadAsync 289", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735840819556, "dur": 23, "ph": "X", "name": "ReadAsync 219", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735840819581, "dur": 30, "ph": "X", "name": "ReadAsync 21", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735840819615, "dur": 46, "ph": "X", "name": "ReadAsync 101", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735840819663, "dur": 26, "ph": "X", "name": "ReadAsync 541", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735840819691, "dur": 31, "ph": "X", "name": "ReadAsync 346", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735840819724, "dur": 1, "ph": "X", "name": "ProcessMessages 310", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735840819726, "dur": 45, "ph": "X", "name": "ReadAsync 310", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735840819773, "dur": 31, "ph": "X", "name": "ReadAsync 524", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735840819806, "dur": 29, "ph": "X", "name": "ReadAsync 411", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735840819838, "dur": 1, "ph": "X", "name": "ProcessMessages 240", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735840819840, "dur": 36, "ph": "X", "name": "ReadAsync 240", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735840819877, "dur": 29, "ph": "X", "name": "ReadAsync 151", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735840819908, "dur": 35, "ph": "X", "name": "ReadAsync 466", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735840819945, "dur": 1, "ph": "X", "name": "ProcessMessages 444", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735840819946, "dur": 28, "ph": "X", "name": "ReadAsync 444", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735840819978, "dur": 21, "ph": "X", "name": "ReadAsync 57", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735840820000, "dur": 27, "ph": "X", "name": "ReadAsync 44", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735840820031, "dur": 30, "ph": "X", "name": "ReadAsync 363", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735840820063, "dur": 21, "ph": "X", "name": "ReadAsync 639", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735840820086, "dur": 27, "ph": "X", "name": "ReadAsync 197", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735840820117, "dur": 32, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735840820151, "dur": 23, "ph": "X", "name": "ReadAsync 483", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735840820176, "dur": 21, "ph": "X", "name": "ReadAsync 478", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735840820199, "dur": 21, "ph": "X", "name": "ReadAsync 225", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735840820221, "dur": 23, "ph": "X", "name": "ReadAsync 200", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735840820246, "dur": 21, "ph": "X", "name": "ReadAsync 330", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735840820269, "dur": 24, "ph": "X", "name": "ReadAsync 410", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735840820296, "dur": 25, "ph": "X", "name": "ReadAsync 510", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735840820322, "dur": 24, "ph": "X", "name": "ReadAsync 439", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735840820347, "dur": 23, "ph": "X", "name": "ReadAsync 371", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735840820374, "dur": 40, "ph": "X", "name": "ReadAsync 261", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735840820417, "dur": 1, "ph": "X", "name": "ProcessMessages 419", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735840820419, "dur": 39, "ph": "X", "name": "ReadAsync 419", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735840820461, "dur": 37, "ph": "X", "name": "ReadAsync 237", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735840820501, "dur": 31, "ph": "X", "name": "ReadAsync 258", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735840820534, "dur": 1, "ph": "X", "name": "ProcessMessages 429", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735840820536, "dur": 37, "ph": "X", "name": "ReadAsync 429", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735840820576, "dur": 169, "ph": "X", "name": "ReadAsync 464", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735840820747, "dur": 41, "ph": "X", "name": "ReadAsync 284", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735840820791, "dur": 1, "ph": "X", "name": "ProcessMessages 1702", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735840820794, "dur": 42, "ph": "X", "name": "ReadAsync 1702", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735840820839, "dur": 34, "ph": "X", "name": "ReadAsync 387", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735840820875, "dur": 1, "ph": "X", "name": "ProcessMessages 206", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735840820877, "dur": 44, "ph": "X", "name": "ReadAsync 206", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735840820924, "dur": 1, "ph": "X", "name": "ProcessMessages 728", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735840820925, "dur": 58, "ph": "X", "name": "ReadAsync 728", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735840820986, "dur": 1, "ph": "X", "name": "ProcessMessages 578", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735840820988, "dur": 54, "ph": "X", "name": "ReadAsync 578", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735840821046, "dur": 1, "ph": "X", "name": "ProcessMessages 870", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735840821048, "dur": 64, "ph": "X", "name": "ReadAsync 870", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735840821117, "dur": 25, "ph": "X", "name": "ReadAsync 1", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735840821144, "dur": 1, "ph": "X", "name": "ProcessMessages 195", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735840821145, "dur": 34, "ph": "X", "name": "ReadAsync 195", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735840821183, "dur": 1, "ph": "X", "name": "ProcessMessages 229", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735840821185, "dur": 39, "ph": "X", "name": "ReadAsync 229", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735840821227, "dur": 1, "ph": "X", "name": "ProcessMessages 8", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735840821228, "dur": 53, "ph": "X", "name": "ReadAsync 8", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735840821284, "dur": 1, "ph": "X", "name": "ProcessMessages 502", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735840821286, "dur": 45, "ph": "X", "name": "ReadAsync 502", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735840821336, "dur": 76, "ph": "X", "name": "ReadAsync 325", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735840821415, "dur": 1, "ph": "X", "name": "ProcessMessages 303", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735840821417, "dur": 37, "ph": "X", "name": "ReadAsync 303", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735840821457, "dur": 1, "ph": "X", "name": "ProcessMessages 301", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735840821459, "dur": 35, "ph": "X", "name": "ReadAsync 301", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735840821497, "dur": 1, "ph": "X", "name": "ProcessMessages 241", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735840821499, "dur": 42, "ph": "X", "name": "ReadAsync 241", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735840821544, "dur": 67, "ph": "X", "name": "ReadAsync 463", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735840821614, "dur": 1, "ph": "X", "name": "ProcessMessages 274", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735840821616, "dur": 45, "ph": "X", "name": "ReadAsync 274", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735840821665, "dur": 1, "ph": "X", "name": "ProcessMessages 302", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735840821667, "dur": 47, "ph": "X", "name": "ReadAsync 302", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735840821716, "dur": 1, "ph": "X", "name": "ProcessMessages 339", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735840821718, "dur": 44, "ph": "X", "name": "ReadAsync 339", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735840821765, "dur": 1, "ph": "X", "name": "ProcessMessages 544", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735840821766, "dur": 43, "ph": "X", "name": "ReadAsync 544", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735840821813, "dur": 33, "ph": "X", "name": "ReadAsync 281", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735840821849, "dur": 1, "ph": "X", "name": "ProcessMessages 632", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735840821850, "dur": 32, "ph": "X", "name": "ReadAsync 632", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735840821885, "dur": 25, "ph": "X", "name": "ReadAsync 469", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735840821912, "dur": 26, "ph": "X", "name": "ReadAsync 285", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735840821941, "dur": 1, "ph": "X", "name": "ProcessMessages 203", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735840821943, "dur": 37, "ph": "X", "name": "ReadAsync 203", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735840821981, "dur": 27, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735840822011, "dur": 1, "ph": "X", "name": "ProcessMessages 277", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735840822013, "dur": 38, "ph": "X", "name": "ReadAsync 277", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735840822053, "dur": 1, "ph": "X", "name": "ProcessMessages 538", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735840822055, "dur": 29, "ph": "X", "name": "ReadAsync 538", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735840822087, "dur": 38, "ph": "X", "name": "ReadAsync 408", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735840822128, "dur": 1, "ph": "X", "name": "ProcessMessages 237", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735840822129, "dur": 33, "ph": "X", "name": "ReadAsync 237", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735840822166, "dur": 30, "ph": "X", "name": "ReadAsync 248", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735840822198, "dur": 28, "ph": "X", "name": "ReadAsync 407", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735840822228, "dur": 27, "ph": "X", "name": "ReadAsync 346", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735840822257, "dur": 25, "ph": "X", "name": "ReadAsync 475", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735840822285, "dur": 1, "ph": "X", "name": "ProcessMessages 203", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735840822287, "dur": 37, "ph": "X", "name": "ReadAsync 203", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735840822326, "dur": 1, "ph": "X", "name": "ProcessMessages 478", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735840822328, "dur": 27, "ph": "X", "name": "ReadAsync 478", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735840822358, "dur": 52, "ph": "X", "name": "ReadAsync 320", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735840822412, "dur": 38, "ph": "X", "name": "ReadAsync 260", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735840822455, "dur": 44, "ph": "X", "name": "ReadAsync 237", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735840822501, "dur": 26, "ph": "X", "name": "ReadAsync 412", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735840822531, "dur": 1, "ph": "X", "name": "ProcessMessages 365", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735840822532, "dur": 32, "ph": "X", "name": "ReadAsync 365", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735840822567, "dur": 31, "ph": "X", "name": "ReadAsync 430", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735840822600, "dur": 27, "ph": "X", "name": "ReadAsync 172", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735840822630, "dur": 1, "ph": "X", "name": "ProcessMessages 414", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735840822631, "dur": 35, "ph": "X", "name": "ReadAsync 414", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735840822668, "dur": 30, "ph": "X", "name": "ReadAsync 384", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735840822700, "dur": 1, "ph": "X", "name": "ProcessMessages 221", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735840822702, "dur": 31, "ph": "X", "name": "ReadAsync 221", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735840822735, "dur": 1, "ph": "X", "name": "ProcessMessages 240", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735840822737, "dur": 37, "ph": "X", "name": "ReadAsync 240", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735840822779, "dur": 34, "ph": "X", "name": "ReadAsync 167", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735840822816, "dur": 24, "ph": "X", "name": "ReadAsync 512", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735840822842, "dur": 22, "ph": "X", "name": "ReadAsync 322", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735840822866, "dur": 33, "ph": "X", "name": "ReadAsync 345", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735840822902, "dur": 1, "ph": "X", "name": "ProcessMessages 84", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735840822903, "dur": 38, "ph": "X", "name": "ReadAsync 84", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735840822943, "dur": 28, "ph": "X", "name": "ReadAsync 515", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735840822973, "dur": 1, "ph": "X", "name": "ProcessMessages 514", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735840822975, "dur": 30, "ph": "X", "name": "ReadAsync 514", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735840823007, "dur": 1, "ph": "X", "name": "ProcessMessages 311", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735840823009, "dur": 31, "ph": "X", "name": "ReadAsync 311", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735840823043, "dur": 22, "ph": "X", "name": "ReadAsync 176", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735840823066, "dur": 89, "ph": "X", "name": "ReadAsync 60", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735840823157, "dur": 30, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735840823188, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735840823190, "dur": 31, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735840823225, "dur": 33, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735840823261, "dur": 34, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735840823298, "dur": 1, "ph": "X", "name": "ProcessMessages 96", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735840823300, "dur": 39, "ph": "X", "name": "ReadAsync 96", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735840823342, "dur": 1, "ph": "X", "name": "ProcessMessages 60", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735840823344, "dur": 32, "ph": "X", "name": "ReadAsync 60", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735840823378, "dur": 1, "ph": "X", "name": "ProcessMessages 164", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735840823379, "dur": 23, "ph": "X", "name": "ReadAsync 164", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735840823405, "dur": 27, "ph": "X", "name": "ReadAsync 128", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735840823435, "dur": 25, "ph": "X", "name": "ReadAsync 96", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735840823462, "dur": 27, "ph": "X", "name": "ReadAsync 124", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735840823492, "dur": 2, "ph": "X", "name": "ProcessMessages 100", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735840823495, "dur": 42, "ph": "X", "name": "ReadAsync 100", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735840823540, "dur": 1, "ph": "X", "name": "ProcessMessages 176", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735840823542, "dur": 32, "ph": "X", "name": "ReadAsync 176", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735840823577, "dur": 25, "ph": "X", "name": "ReadAsync 128", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735840823605, "dur": 28, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735840823637, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735840823640, "dur": 30, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735840823673, "dur": 19, "ph": "X", "name": "ReadAsync 124", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735840823693, "dur": 1, "ph": "X", "name": "ProcessMessages 96", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735840823695, "dur": 30, "ph": "X", "name": "ReadAsync 96", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735840823727, "dur": 24, "ph": "X", "name": "ReadAsync 68", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735840823753, "dur": 25, "ph": "X", "name": "ReadAsync 140", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735840823781, "dur": 33, "ph": "X", "name": "ReadAsync 112", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735840823818, "dur": 33, "ph": "X", "name": "ReadAsync 132", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735840823854, "dur": 1, "ph": "X", "name": "ProcessMessages 176", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735840823856, "dur": 38, "ph": "X", "name": "ReadAsync 176", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735840823895, "dur": 1, "ph": "X", "name": "ProcessMessages 208", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735840823897, "dur": 33, "ph": "X", "name": "ReadAsync 208", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735840823933, "dur": 1, "ph": "X", "name": "ProcessMessages 76", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735840823934, "dur": 45, "ph": "X", "name": "ReadAsync 76", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735840823983, "dur": 1, "ph": "X", "name": "ProcessMessages 164", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735840823985, "dur": 43, "ph": "X", "name": "ReadAsync 164", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735840824029, "dur": 1, "ph": "X", "name": "ProcessMessages 240", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735840824031, "dur": 33, "ph": "X", "name": "ReadAsync 240", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735840824067, "dur": 30, "ph": "X", "name": "ReadAsync 140", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735840824099, "dur": 1, "ph": "X", "name": "ProcessMessages 160", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735840824101, "dur": 30, "ph": "X", "name": "ReadAsync 160", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735840824134, "dur": 1, "ph": "X", "name": "ProcessMessages 84", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735840824138, "dur": 38, "ph": "X", "name": "ReadAsync 84", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735840824179, "dur": 1, "ph": "X", "name": "ProcessMessages 176", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735840824181, "dur": 36, "ph": "X", "name": "ReadAsync 176", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735840824220, "dur": 1, "ph": "X", "name": "ProcessMessages 128", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735840824222, "dur": 41, "ph": "X", "name": "ReadAsync 128", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735840824266, "dur": 1, "ph": "X", "name": "ProcessMessages 144", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735840824269, "dur": 46, "ph": "X", "name": "ReadAsync 144", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735840824316, "dur": 1, "ph": "X", "name": "ProcessMessages 224", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735840824319, "dur": 29, "ph": "X", "name": "ReadAsync 224", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735840824350, "dur": 1, "ph": "X", "name": "ProcessMessages 176", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735840824351, "dur": 29, "ph": "X", "name": "ReadAsync 176", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735840824383, "dur": 1, "ph": "X", "name": "ProcessMessages 140", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735840824385, "dur": 36, "ph": "X", "name": "ReadAsync 140", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735840824424, "dur": 1, "ph": "X", "name": "ProcessMessages 164", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735840824426, "dur": 33, "ph": "X", "name": "ReadAsync 164", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735840824460, "dur": 1, "ph": "X", "name": "ProcessMessages 172", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735840824462, "dur": 35, "ph": "X", "name": "ReadAsync 172", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735840824501, "dur": 1, "ph": "X", "name": "ProcessMessages 100", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735840824503, "dur": 33, "ph": "X", "name": "ReadAsync 100", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735840824538, "dur": 1, "ph": "X", "name": "ProcessMessages 208", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735840824541, "dur": 41, "ph": "X", "name": "ReadAsync 208", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735840824585, "dur": 1, "ph": "X", "name": "ProcessMessages 160", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735840824587, "dur": 98, "ph": "X", "name": "ReadAsync 160", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735840824689, "dur": 1, "ph": "X", "name": "ProcessMessages 192", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735840824691, "dur": 41, "ph": "X", "name": "ReadAsync 192", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735840824733, "dur": 2, "ph": "X", "name": "ProcessMessages 112", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735840824736, "dur": 31, "ph": "X", "name": "ReadAsync 112", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735840824770, "dur": 1, "ph": "X", "name": "ProcessMessages 144", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735840824773, "dur": 34, "ph": "X", "name": "ReadAsync 144", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735840824809, "dur": 1, "ph": "X", "name": "ProcessMessages 144", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735840824811, "dur": 29, "ph": "X", "name": "ReadAsync 144", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735840824843, "dur": 1, "ph": "X", "name": "ProcessMessages 112", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735840824845, "dur": 47, "ph": "X", "name": "ReadAsync 112", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735840824894, "dur": 1, "ph": "X", "name": "ProcessMessages 144", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735840824895, "dur": 26, "ph": "X", "name": "ReadAsync 144", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735840824923, "dur": 1, "ph": "X", "name": "ProcessMessages 144", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735840824924, "dur": 28, "ph": "X", "name": "ReadAsync 144", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735840824956, "dur": 1, "ph": "X", "name": "ProcessMessages 92", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735840824958, "dur": 30, "ph": "X", "name": "ReadAsync 92", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735840824991, "dur": 1, "ph": "X", "name": "ProcessMessages 148", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735840824993, "dur": 39, "ph": "X", "name": "ReadAsync 148", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735840825035, "dur": 1, "ph": "X", "name": "ProcessMessages 124", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735840825037, "dur": 31, "ph": "X", "name": "ReadAsync 124", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735840825070, "dur": 78, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735840825152, "dur": 30, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735840825184, "dur": 25, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735840825212, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735840825214, "dur": 31, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735840825249, "dur": 27, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735840825278, "dur": 118, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735840825402, "dur": 29, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735840825433, "dur": 788, "ph": "X", "name": "ProcessMessages 8", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735840826223, "dur": 39, "ph": "X", "name": "ReadAsync 8", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735840826266, "dur": 3195, "ph": "X", "name": "ProcessMessages 62", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735840829466, "dur": 40, "ph": "X", "name": "ReadAsync 62", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735840829509, "dur": 1, "ph": "X", "name": "ProcessMessages 112", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735840829511, "dur": 2046, "ph": "X", "name": "ReadAsync 112", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735840831561, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735840831563, "dur": 52, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735840831619, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735840831620, "dur": 36, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735840831661, "dur": 246, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735840831912, "dur": 31, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735840831946, "dur": 75, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735840832026, "dur": 35, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735840832064, "dur": 1, "ph": "X", "name": "ProcessMessages 80", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735840832066, "dur": 27, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735840832095, "dur": 1, "ph": "X", "name": "ProcessMessages 36", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735840832096, "dur": 26, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735840832125, "dur": 29, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735840832156, "dur": 29, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735840832188, "dur": 30, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735840832222, "dur": 29, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735840832255, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735840832256, "dur": 28, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735840832286, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735840832288, "dur": 61, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735840832352, "dur": 136, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735840832491, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735840832493, "dur": 101, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735840832597, "dur": 1, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735840832599, "dur": 47, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735840832649, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735840832651, "dur": 53, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735840832708, "dur": 39, "ph": "X", "name": "ReadAsync 28", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735840832750, "dur": 1, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735840832752, "dur": 38, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735840832793, "dur": 1, "ph": "X", "name": "ProcessMessages 36", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735840832794, "dur": 32, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735840832830, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735840832831, "dur": 45, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735840832881, "dur": 30, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735840832914, "dur": 24, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735840832941, "dur": 1, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735840832943, "dur": 29, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735840832974, "dur": 61, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735840833038, "dur": 30, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735840833071, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735840833073, "dur": 81, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735840833156, "dur": 70, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735840833230, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735840833231, "dur": 38, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735840833273, "dur": 1, "ph": "X", "name": "ProcessMessages 128", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735840833275, "dur": 73, "ph": "X", "name": "ReadAsync 128", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735840833350, "dur": 1, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735840833352, "dur": 33, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735840833388, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735840833391, "dur": 32, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735840833426, "dur": 45, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735840833475, "dur": 30, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735840833507, "dur": 24, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735840833535, "dur": 2, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735840833538, "dur": 29, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735840833570, "dur": 29, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735840833601, "dur": 33, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735840833637, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735840833639, "dur": 38, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735840833680, "dur": 1, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735840833682, "dur": 40, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735840833728, "dur": 29, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735840833759, "dur": 29, "ph": "X", "name": "ReadAsync 52", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735840833791, "dur": 1, "ph": "X", "name": "ProcessMessages 64", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735840833793, "dur": 34, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735840833830, "dur": 1, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735840833832, "dur": 46, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735840833883, "dur": 34, "ph": "X", "name": "ReadAsync 28", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735840833919, "dur": 102, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735840834026, "dur": 98, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735840834127, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735840834129, "dur": 47, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735840834179, "dur": 26, "ph": "X", "name": "ReadAsync 92", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735840834206, "dur": 25, "ph": "X", "name": "ReadAsync 84", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735840834233, "dur": 48, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735840834284, "dur": 35, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735840834321, "dur": 1, "ph": "X", "name": "ProcessMessages 36", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735840834323, "dur": 42, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735840834367, "dur": 29, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735840834398, "dur": 3, "ph": "X", "name": "ProcessMessages 40", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735840834401, "dur": 70, "ph": "X", "name": "ReadAsync 40", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735840834474, "dur": 27, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735840834504, "dur": 24, "ph": "X", "name": "ReadAsync 52", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735840834530, "dur": 60, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735840834594, "dur": 34, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735840834632, "dur": 29, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735840834666, "dur": 34, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735840834704, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735840834705, "dur": 50, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735840834759, "dur": 37, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735840834800, "dur": 40, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735840834844, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735840834846, "dur": 85, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735840834935, "dur": 33, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735840834972, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735840834974, "dur": 135, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735840835112, "dur": 41, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735840835156, "dur": 102, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735840835260, "dur": 27, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735840835288, "dur": 1, "ph": "X", "name": "ProcessMessages 52", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735840835290, "dur": 24, "ph": "X", "name": "ReadAsync 52", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735840835316, "dur": 21, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735840835339, "dur": 23, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735840835364, "dur": 22, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735840835388, "dur": 137, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735840835529, "dur": 35, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735840835570, "dur": 49, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735840835624, "dur": 34, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735840835662, "dur": 1, "ph": "X", "name": "ProcessMessages 68", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735840835664, "dur": 33, "ph": "X", "name": "ReadAsync 68", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735840835699, "dur": 145, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735840835848, "dur": 17, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735840835867, "dur": 166, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735840836037, "dur": 28, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735840836068, "dur": 40, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735840836113, "dur": 38, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735840836153, "dur": 55, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735840836210, "dur": 27, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735840836241, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735840836242, "dur": 68, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735840836315, "dur": 23, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735840836340, "dur": 89, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735840836434, "dur": 50, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735840836487, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735840836488, "dur": 35, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735840836529, "dur": 46, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735840836579, "dur": 33, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735840836617, "dur": 215, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735840836836, "dur": 41, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735840836880, "dur": 1, "ph": "X", "name": "ProcessMessages 36", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735840836881, "dur": 27, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735840836910, "dur": 26, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735840836938, "dur": 27, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735840836968, "dur": 210, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735840837182, "dur": 33, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735840837218, "dur": 22, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735840837243, "dur": 527, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735840837773, "dur": 39, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735840837815, "dur": 2, "ph": "X", "name": "ProcessMessages 8", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735840837819, "dur": 339508, "ph": "X", "name": "ReadAsync 8", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735841177335, "dur": 3, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735841177341, "dur": 54, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735841177398, "dur": 20, "ph": "X", "name": "ProcessMessages 192", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735841177422, "dur": 3772, "ph": "X", "name": "ReadAsync 192", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735841181199, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735841181201, "dur": 29, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735841181231, "dur": 1, "ph": "X", "name": "ProcessMessages 36", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735841181233, "dur": 170, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735841181409, "dur": 43, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735841181455, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735841181458, "dur": 30, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735841181492, "dur": 114, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735841181611, "dur": 40, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735841181655, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735841181657, "dur": 142, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735841181802, "dur": 25, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735841181831, "dur": 387, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735841182223, "dur": 29, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735841182256, "dur": 230, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735841182491, "dur": 32, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735841182527, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735841182529, "dur": 257, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735841182790, "dur": 26, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735841182821, "dur": 111, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735841182937, "dur": 29, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735841182970, "dur": 34, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735841183007, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735841183009, "dur": 43, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735841183055, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735841183056, "dur": 99, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735841183158, "dur": 21, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735841183182, "dur": 83, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735841183267, "dur": 33, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735841183304, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735841183306, "dur": 235, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735841183546, "dur": 48, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735841183598, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735841183600, "dur": 35, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735841183638, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735841183640, "dur": 33, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735841183676, "dur": 142, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735841183823, "dur": 33, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735841183859, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735841183861, "dur": 624, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735841184490, "dur": 36, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735841184530, "dur": 2, "ph": "X", "name": "ProcessMessages 36", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735841184534, "dur": 81, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735841184620, "dur": 27, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735841184649, "dur": 353, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735841185005, "dur": 36, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735841185045, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735841185047, "dur": 155, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735841185204, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735841185207, "dur": 31, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735841185242, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735841185244, "dur": 29, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735841185275, "dur": 143, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735841185422, "dur": 36, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735841185462, "dur": 44, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735841185510, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735841185511, "dur": 157, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735841185674, "dur": 59, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735841185736, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735841185738, "dur": 98, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735841185842, "dur": 41, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735841185885, "dur": 2, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735841185889, "dur": 77, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735841185970, "dur": 37, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735841186011, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735841186013, "dur": 58, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735841186076, "dur": 39, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735841186118, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735841186120, "dur": 49, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735841186172, "dur": 25, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735841186200, "dur": 44, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735841186246, "dur": 25, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735841186274, "dur": 132, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735841186412, "dur": 46, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735841186462, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735841186464, "dur": 39, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735841186505, "dur": 1, "ph": "X", "name": "ProcessMessages 36", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735841186507, "dur": 56, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735841186568, "dur": 38, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735841186609, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735841186611, "dur": 39, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735841186654, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735841186655, "dur": 34, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735841186693, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735841186694, "dur": 43, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735841186741, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735841186743, "dur": 41, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735841186787, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735841186788, "dur": 38, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735841186829, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735841186831, "dur": 33, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735841186867, "dur": 25, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735841186894, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735841186896, "dur": 23, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735841186921, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735841186923, "dur": 146, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735841187072, "dur": 24, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735841187098, "dur": 30, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735841187132, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735841187134, "dur": 30, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735841187167, "dur": 22, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735841187192, "dur": 1, "ph": "X", "name": "ProcessMessages 64", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735841187194, "dur": 31, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735841187227, "dur": 1, "ph": "X", "name": "ProcessMessages 64", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735841187228, "dur": 24, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735841187254, "dur": 22, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735841187278, "dur": 25, "ph": "X", "name": "ReadAsync 76", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735841187306, "dur": 1, "ph": "X", "name": "ProcessMessages 52", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735841187308, "dur": 22, "ph": "X", "name": "ReadAsync 52", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735841187331, "dur": 1, "ph": "X", "name": "ProcessMessages 64", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735841187333, "dur": 23, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735841187360, "dur": 26, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735841187390, "dur": 24, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735841187417, "dur": 31, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735841187451, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735841187453, "dur": 30, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735841187485, "dur": 35, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735841187525, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735841187527, "dur": 30, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735841187560, "dur": 27, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735841187591, "dur": 32, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735841187626, "dur": 23, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735841187652, "dur": 23, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735841187677, "dur": 24, "ph": "X", "name": "ReadAsync 76", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735841187704, "dur": 1, "ph": "X", "name": "ProcessMessages 52", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735841187705, "dur": 27, "ph": "X", "name": "ReadAsync 52", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735841187735, "dur": 26, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735841187764, "dur": 1, "ph": "X", "name": "ProcessMessages 64", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735841187765, "dur": 28, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735841187796, "dur": 27, "ph": "X", "name": "ReadAsync 128", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735841187825, "dur": 23, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735841187851, "dur": 23, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735841187875, "dur": 22, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735841187900, "dur": 25, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735841187928, "dur": 28, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735841187957, "dur": 57, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735841188017, "dur": 27, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735841188046, "dur": 35, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735841188084, "dur": 1, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735841188086, "dur": 27, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735841188115, "dur": 83, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735841188202, "dur": 30, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735841188235, "dur": 80578, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735841268821, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735841268825, "dur": 57, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735841268886, "dur": 3826, "ph": "X", "name": "ProcessMessages 979", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735841272714, "dur": 56, "ph": "X", "name": "ReadAsync 979", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735841272772, "dur": 2, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735841272775, "dur": 49334, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735841322120, "dur": 3, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735841322125, "dur": 55, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735841322181, "dur": 3, "ph": "X", "name": "ProcessMessages 8", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735841322185, "dur": 304076, "ph": "X", "name": "ReadAsync 8", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735841626268, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735841626272, "dur": 53, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735841626329, "dur": 291, "ph": "X", "name": "ProcessMessages 353", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735841626621, "dur": 20441, "ph": "X", "name": "ReadAsync 353", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735841647071, "dur": 3, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735841647076, "dur": 60, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735841647140, "dur": 3, "ph": "X", "name": "ProcessMessages 8", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735841647144, "dur": 9692, "ph": "X", "name": "ReadAsync 8", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735841656844, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735841656849, "dur": 57, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735841656909, "dur": 23, "ph": "X", "name": "ProcessMessages 416", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735841656933, "dur": 6408, "ph": "X", "name": "ReadAsync 416", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735841663348, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735841663352, "dur": 53, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735841663408, "dur": 2, "ph": "X", "name": "ProcessMessages 8", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735841663411, "dur": 1697, "ph": "X", "name": "ReadAsync 8", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735841665113, "dur": 36, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735841665152, "dur": 20, "ph": "X", "name": "ProcessMessages 34", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735841665173, "dur": 71482, "ph": "X", "name": "ReadAsync 34", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735841736662, "dur": 3, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735841736666, "dur": 31, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735841736699, "dur": 2, "ph": "X", "name": "ProcessMessages 8", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735841736701, "dur": 713, "ph": "X", "name": "ReadAsync 8", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735841737422, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735841737425, "dur": 49, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735841737477, "dur": 22, "ph": "X", "name": "ProcessMessages 34", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735841737500, "dur": 175500, "ph": "X", "name": "ReadAsync 34", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735841913009, "dur": 3, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735841913013, "dur": 59, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735841913076, "dur": 29, "ph": "X", "name": "ProcessMessages 422", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735841913107, "dur": 6490, "ph": "X", "name": "ReadAsync 422", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735841919606, "dur": 3, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735841919611, "dur": 59, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735841919672, "dur": 2, "ph": "X", "name": "ProcessMessages 8", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735841919675, "dur": 673, "ph": "X", "name": "ReadAsync 8", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735841920355, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735841920357, "dur": 37, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735841920397, "dur": 23, "ph": "X", "name": "ProcessMessages 34", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735841920420, "dur": 14430, "ph": "X", "name": "ReadAsync 34", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735841934860, "dur": 3, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735841934865, "dur": 65, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735841934933, "dur": 3, "ph": "X", "name": "ProcessMessages 8", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735841934938, "dur": 727, "ph": "X", "name": "ReadAsync 8", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735841935672, "dur": 37, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735841935712, "dur": 27, "ph": "X", "name": "ProcessMessages 50", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735841935740, "dur": 542, "ph": "X", "name": "ReadAsync 50", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735841936286, "dur": 46, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735841936335, "dur": 2, "ph": "X", "name": "ProcessMessages 13", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735841936339, "dur": 9583, "ph": "X", "name": "ReadAsync 13", "args": {}}, {"pid": 3100, "tid": 24786, "ts": 1754735841955653, "dur": 1441, "ph": "X", "name": "ReadEntireBinlogFromIpcAsync", "args": {}}, {"pid": 3100, "tid": 17179869184, "ph": "M", "name": "thread_name", "args": {"name": "WaitForBuildProgramInputDataBeingWrittenAndSendDagReadyMessageAsync"}}, {"pid": 3100, "tid": 17179869184, "ts": 1754735840537512, "dur": 7, "ph": "X", "name": "await writeBuildProgramInputDataTask", "args": {}}, {"pid": 3100, "tid": 17179869184, "ts": 1754735840537520, "dur": 157635, "ph": "X", "name": "WritePipe.WaitConnectionAsync", "args": {}}, {"pid": 3100, "tid": 17179869184, "ts": 1754735840695156, "dur": 37, "ph": "X", "name": "WriteDagReadyMessage", "args": {}}, {"pid": 3100, "tid": 24786, "ts": 1754735841957097, "dur": 5, "ph": "X", "name": "WaitForBuildProgramInputDataBeingWrittenAndSendDagReadyMessageAsync", "args": {}}, {"pid": 3100, "tid": 1, "ph": "M", "name": "thread_name", "args": {"name": ""}}, {"pid": 3100, "tid": 1, "ts": 1754735839540465, "dur": 3233, "ph": "X", "name": "<Add>b__0", "args": {}}, {"pid": 3100, "tid": 1, "ts": 1754735839543702, "dur": 25545, "ph": "X", "name": "<Add>b__0", "args": {}}, {"pid": 3100, "tid": 1, "ts": 1754735839569257, "dur": 30729, "ph": "X", "name": "WriteJson", "args": {}}, {"pid": 3100, "tid": 24786, "ts": 1754735841957104, "dur": 3, "ph": "X", "name": "", "args": {}}, {"pid": 3100, "tid": 12884901888, "ph": "M", "name": "thread_name", "args": {"name": "ReadEntireBinlogFromIpcAsync"}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735839539419, "dur": 149461, "ph": "X", "name": "WaitForConnectionAsync", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735839688882, "dur": 29933, "ph": "X", "name": "UpdateFromStreamAsync", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735839689797, "dur": 1156, "ph": "X", "name": "ReadAsync 0", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735839690958, "dur": 1152, "ph": "X", "name": "ProcessMessages 8195", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735839692114, "dur": 206, "ph": "X", "name": "ReadAsync 8195", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735839692322, "dur": 6, "ph": "X", "name": "ProcessMessages 20527", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735839692330, "dur": 32, "ph": "X", "name": "ReadAsync 20527", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735839692365, "dur": 27, "ph": "X", "name": "ReadAsync 326", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735839692395, "dur": 24, "ph": "X", "name": "ReadAsync 240", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735839692421, "dur": 28, "ph": "X", "name": "ReadAsync 479", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735839692453, "dur": 41, "ph": "X", "name": "ReadAsync 207", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735839692497, "dur": 24, "ph": "X", "name": "ReadAsync 610", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735839692523, "dur": 37, "ph": "X", "name": "ReadAsync 458", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735839692561, "dur": 23, "ph": "X", "name": "ReadAsync 623", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735839692585, "dur": 22, "ph": "X", "name": "ReadAsync 367", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735839692609, "dur": 23, "ph": "X", "name": "ReadAsync 350", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735839692634, "dur": 23, "ph": "X", "name": "ReadAsync 284", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735839692658, "dur": 22, "ph": "X", "name": "ReadAsync 353", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735839692681, "dur": 22, "ph": "X", "name": "ReadAsync 276", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735839692704, "dur": 21, "ph": "X", "name": "ReadAsync 259", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735839692727, "dur": 24, "ph": "X", "name": "ReadAsync 285", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735839692752, "dur": 22, "ph": "X", "name": "ReadAsync 82", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735839692775, "dur": 22, "ph": "X", "name": "ReadAsync 212", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735839692799, "dur": 27, "ph": "X", "name": "ReadAsync 74", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735839692829, "dur": 33, "ph": "X", "name": "ReadAsync 124", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735839692864, "dur": 21, "ph": "X", "name": "ReadAsync 548", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735839692887, "dur": 23, "ph": "X", "name": "ReadAsync 315", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735839692911, "dur": 23, "ph": "X", "name": "ReadAsync 421", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735839692937, "dur": 25, "ph": "X", "name": "ReadAsync 258", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735839692963, "dur": 23, "ph": "X", "name": "ReadAsync 273", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735839692987, "dur": 26, "ph": "X", "name": "ReadAsync 393", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735839693014, "dur": 18, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735839693033, "dur": 20, "ph": "X", "name": "ReadAsync 70", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735839693055, "dur": 23, "ph": "X", "name": "ReadAsync 294", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735839693080, "dur": 22, "ph": "X", "name": "ReadAsync 261", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735839693104, "dur": 21, "ph": "X", "name": "ReadAsync 268", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735839693126, "dur": 21, "ph": "X", "name": "ReadAsync 252", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735839693148, "dur": 23, "ph": "X", "name": "ReadAsync 234", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735839693173, "dur": 21, "ph": "X", "name": "ReadAsync 246", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735839693196, "dur": 22, "ph": "X", "name": "ReadAsync 270", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735839693220, "dur": 88, "ph": "X", "name": "ReadAsync 72", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735839693310, "dur": 24, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735839693336, "dur": 37, "ph": "X", "name": "ReadAsync 578", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735839693377, "dur": 36, "ph": "X", "name": "ReadAsync 229", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735839693415, "dur": 28, "ph": "X", "name": "ReadAsync 538", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735839693445, "dur": 35, "ph": "X", "name": "ReadAsync 278", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735839693483, "dur": 36, "ph": "X", "name": "ReadAsync 262", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735839693521, "dur": 24, "ph": "X", "name": "ReadAsync 531", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735839693548, "dur": 22, "ph": "X", "name": "ReadAsync 321", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735839693571, "dur": 22, "ph": "X", "name": "ReadAsync 259", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735839693595, "dur": 21, "ph": "X", "name": "ReadAsync 279", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735839693618, "dur": 23, "ph": "X", "name": "ReadAsync 234", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735839693642, "dur": 32, "ph": "X", "name": "ReadAsync 269", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735839693678, "dur": 27, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735839693706, "dur": 22, "ph": "X", "name": "ReadAsync 241", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735839693730, "dur": 22, "ph": "X", "name": "ReadAsync 349", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735839693753, "dur": 21, "ph": "X", "name": "ReadAsync 332", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735839693776, "dur": 23, "ph": "X", "name": "ReadAsync 356", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735839693801, "dur": 23, "ph": "X", "name": "ReadAsync 404", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735839693826, "dur": 22, "ph": "X", "name": "ReadAsync 446", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735839693850, "dur": 22, "ph": "X", "name": "ReadAsync 302", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735839693873, "dur": 23, "ph": "X", "name": "ReadAsync 357", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735839693898, "dur": 1, "ph": "X", "name": "ProcessMessages 320", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735839693899, "dur": 21, "ph": "X", "name": "ReadAsync 320", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735839693922, "dur": 25, "ph": "X", "name": "ReadAsync 282", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735839693948, "dur": 21, "ph": "X", "name": "ReadAsync 421", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735839693970, "dur": 21, "ph": "X", "name": "ReadAsync 173", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735839693993, "dur": 22, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735839694016, "dur": 21, "ph": "X", "name": "ReadAsync 356", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735839694039, "dur": 21, "ph": "X", "name": "ReadAsync 251", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735839694061, "dur": 22, "ph": "X", "name": "ReadAsync 301", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735839694085, "dur": 24, "ph": "X", "name": "ReadAsync 285", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735839694110, "dur": 21, "ph": "X", "name": "ReadAsync 316", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735839694133, "dur": 21, "ph": "X", "name": "ReadAsync 291", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735839694155, "dur": 22, "ph": "X", "name": "ReadAsync 151", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735839694180, "dur": 23, "ph": "X", "name": "ReadAsync 303", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735839694204, "dur": 23, "ph": "X", "name": "ReadAsync 316", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735839694229, "dur": 22, "ph": "X", "name": "ReadAsync 313", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735839694252, "dur": 2, "ph": "X", "name": "ProcessMessages 330", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735839694254, "dur": 21, "ph": "X", "name": "ReadAsync 330", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735839694277, "dur": 44, "ph": "X", "name": "ReadAsync 347", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735839694322, "dur": 23, "ph": "X", "name": "ReadAsync 280", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735839694347, "dur": 30, "ph": "X", "name": "ReadAsync 377", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735839694380, "dur": 1, "ph": "X", "name": "ProcessMessages 296", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735839694381, "dur": 44, "ph": "X", "name": "ReadAsync 296", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735839694428, "dur": 1, "ph": "X", "name": "ProcessMessages 431", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735839694430, "dur": 41, "ph": "X", "name": "ReadAsync 431", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735839694474, "dur": 1, "ph": "X", "name": "ProcessMessages 585", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735839694475, "dur": 27, "ph": "X", "name": "ReadAsync 585", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735839694504, "dur": 25, "ph": "X", "name": "ReadAsync 348", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735839694531, "dur": 25, "ph": "X", "name": "ReadAsync 236", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735839694558, "dur": 22, "ph": "X", "name": "ReadAsync 362", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735839694581, "dur": 24, "ph": "X", "name": "ReadAsync 390", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735839694608, "dur": 24, "ph": "X", "name": "ReadAsync 238", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735839694633, "dur": 21, "ph": "X", "name": "ReadAsync 340", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735839694656, "dur": 20, "ph": "X", "name": "ReadAsync 264", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735839694677, "dur": 19, "ph": "X", "name": "ReadAsync 67", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735839694697, "dur": 21, "ph": "X", "name": "ReadAsync 324", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735839694719, "dur": 29, "ph": "X", "name": "ReadAsync 295", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735839694750, "dur": 23, "ph": "X", "name": "ReadAsync 227", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735839694774, "dur": 24, "ph": "X", "name": "ReadAsync 294", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735839694799, "dur": 22, "ph": "X", "name": "ReadAsync 265", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735839694822, "dur": 21, "ph": "X", "name": "ReadAsync 331", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735839694844, "dur": 21, "ph": "X", "name": "ReadAsync 227", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735839694867, "dur": 24, "ph": "X", "name": "ReadAsync 272", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735839694893, "dur": 24, "ph": "X", "name": "ReadAsync 446", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735839694918, "dur": 21, "ph": "X", "name": "ReadAsync 384", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735839694940, "dur": 22, "ph": "X", "name": "ReadAsync 399", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735839694963, "dur": 21, "ph": "X", "name": "ReadAsync 305", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735839694986, "dur": 27, "ph": "X", "name": "ReadAsync 346", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735839695015, "dur": 1, "ph": "X", "name": "ProcessMessages 185", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735839695017, "dur": 29, "ph": "X", "name": "ReadAsync 185", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735839695049, "dur": 25, "ph": "X", "name": "ReadAsync 460", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735839695075, "dur": 21, "ph": "X", "name": "ReadAsync 466", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735839695098, "dur": 22, "ph": "X", "name": "ReadAsync 203", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735839695120, "dur": 1, "ph": "X", "name": "ProcessMessages 265", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735839695122, "dur": 22, "ph": "X", "name": "ReadAsync 265", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735839695145, "dur": 22, "ph": "X", "name": "ReadAsync 383", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735839695168, "dur": 23, "ph": "X", "name": "ReadAsync 315", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735839695193, "dur": 22, "ph": "X", "name": "ReadAsync 69", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735839695217, "dur": 22, "ph": "X", "name": "ReadAsync 405", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735839695240, "dur": 22, "ph": "X", "name": "ReadAsync 443", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735839695264, "dur": 21, "ph": "X", "name": "ReadAsync 474", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735839695286, "dur": 21, "ph": "X", "name": "ReadAsync 422", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735839695308, "dur": 23, "ph": "X", "name": "ReadAsync 357", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735839695334, "dur": 22, "ph": "X", "name": "ReadAsync 369", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735839695357, "dur": 22, "ph": "X", "name": "ReadAsync 243", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735839695380, "dur": 21, "ph": "X", "name": "ReadAsync 495", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735839695402, "dur": 21, "ph": "X", "name": "ReadAsync 320", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735839695425, "dur": 21, "ph": "X", "name": "ReadAsync 378", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735839695448, "dur": 37, "ph": "X", "name": "ReadAsync 430", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735839695488, "dur": 25, "ph": "X", "name": "ReadAsync 358", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735839695514, "dur": 22, "ph": "X", "name": "ReadAsync 464", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735839695537, "dur": 23, "ph": "X", "name": "ReadAsync 351", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735839695562, "dur": 23, "ph": "X", "name": "ReadAsync 380", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735839695587, "dur": 22, "ph": "X", "name": "ReadAsync 466", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735839695610, "dur": 22, "ph": "X", "name": "ReadAsync 383", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735839695633, "dur": 39, "ph": "X", "name": "ReadAsync 323", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735839695675, "dur": 1, "ph": "X", "name": "ProcessMessages 332", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735839695677, "dur": 32, "ph": "X", "name": "ReadAsync 332", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735839695710, "dur": 23, "ph": "X", "name": "ReadAsync 416", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735839695735, "dur": 22, "ph": "X", "name": "ReadAsync 445", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735839695758, "dur": 23, "ph": "X", "name": "ReadAsync 373", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735839695783, "dur": 27, "ph": "X", "name": "ReadAsync 307", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735839695814, "dur": 32, "ph": "X", "name": "ReadAsync 307", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735839695848, "dur": 26, "ph": "X", "name": "ReadAsync 295", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735839695876, "dur": 22, "ph": "X", "name": "ReadAsync 550", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735839695899, "dur": 21, "ph": "X", "name": "ReadAsync 373", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735839695922, "dur": 21, "ph": "X", "name": "ReadAsync 335", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735839695944, "dur": 23, "ph": "X", "name": "ReadAsync 295", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735839695969, "dur": 22, "ph": "X", "name": "ReadAsync 361", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735839695993, "dur": 21, "ph": "X", "name": "ReadAsync 257", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735839696015, "dur": 21, "ph": "X", "name": "ReadAsync 372", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735839696037, "dur": 22, "ph": "X", "name": "ReadAsync 351", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735839696061, "dur": 22, "ph": "X", "name": "ReadAsync 369", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735839696084, "dur": 22, "ph": "X", "name": "ReadAsync 399", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735839696108, "dur": 26, "ph": "X", "name": "ReadAsync 406", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735839696135, "dur": 20, "ph": "X", "name": "ReadAsync 410", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735839696157, "dur": 122, "ph": "X", "name": "ReadAsync 8", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735839696283, "dur": 34, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735839696319, "dur": 1, "ph": "X", "name": "ProcessMessages 649", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735839696321, "dur": 39, "ph": "X", "name": "ReadAsync 649", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735839696361, "dur": 26, "ph": "X", "name": "ReadAsync 549", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735839696390, "dur": 34, "ph": "X", "name": "ReadAsync 383", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735839696426, "dur": 1, "ph": "X", "name": "ProcessMessages 490", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735839696428, "dur": 32, "ph": "X", "name": "ReadAsync 490", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735839696463, "dur": 38, "ph": "X", "name": "ReadAsync 78", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735839696503, "dur": 1, "ph": "X", "name": "ProcessMessages 430", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735839696505, "dur": 33, "ph": "X", "name": "ReadAsync 430", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735839696540, "dur": 34, "ph": "X", "name": "ReadAsync 931", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735839696576, "dur": 24, "ph": "X", "name": "ReadAsync 313", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735839696602, "dur": 23, "ph": "X", "name": "ReadAsync 342", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735839696627, "dur": 21, "ph": "X", "name": "ReadAsync 431", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735839696649, "dur": 22, "ph": "X", "name": "ReadAsync 123", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735839696672, "dur": 21, "ph": "X", "name": "ReadAsync 311", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735839696695, "dur": 21, "ph": "X", "name": "ReadAsync 319", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735839696718, "dur": 22, "ph": "X", "name": "ReadAsync 356", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735839696741, "dur": 22, "ph": "X", "name": "ReadAsync 255", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735839696765, "dur": 21, "ph": "X", "name": "ReadAsync 278", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735839696788, "dur": 23, "ph": "X", "name": "ReadAsync 343", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735839696812, "dur": 21, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735839696835, "dur": 22, "ph": "X", "name": "ReadAsync 377", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735839696858, "dur": 23, "ph": "X", "name": "ReadAsync 407", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735839696883, "dur": 22, "ph": "X", "name": "ReadAsync 385", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735839696907, "dur": 21, "ph": "X", "name": "ReadAsync 269", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735839696929, "dur": 21, "ph": "X", "name": "ReadAsync 354", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735839696952, "dur": 17, "ph": "X", "name": "ReadAsync 305", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735839696971, "dur": 22, "ph": "X", "name": "ReadAsync 136", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735839696994, "dur": 22, "ph": "X", "name": "ReadAsync 257", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735839697018, "dur": 26, "ph": "X", "name": "ReadAsync 410", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735839697045, "dur": 25, "ph": "X", "name": "ReadAsync 385", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735839697072, "dur": 33, "ph": "X", "name": "ReadAsync 329", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735839697107, "dur": 29, "ph": "X", "name": "ReadAsync 328", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735839697139, "dur": 43, "ph": "X", "name": "ReadAsync 280", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735839697184, "dur": 30, "ph": "X", "name": "ReadAsync 264", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735839697216, "dur": 26, "ph": "X", "name": "ReadAsync 725", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735839697245, "dur": 23, "ph": "X", "name": "ReadAsync 392", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735839697270, "dur": 25, "ph": "X", "name": "ReadAsync 412", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735839697297, "dur": 25, "ph": "X", "name": "ReadAsync 437", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735839697323, "dur": 29, "ph": "X", "name": "ReadAsync 85", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735839697353, "dur": 1, "ph": "X", "name": "ProcessMessages 469", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735839697355, "dur": 28, "ph": "X", "name": "ReadAsync 469", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735839697384, "dur": 39, "ph": "X", "name": "ReadAsync 543", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735839697427, "dur": 1, "ph": "X", "name": "ProcessMessages 390", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735839697429, "dur": 47, "ph": "X", "name": "ReadAsync 390", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735839697480, "dur": 1, "ph": "X", "name": "ProcessMessages 781", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735839697481, "dur": 38, "ph": "X", "name": "ReadAsync 781", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735839697521, "dur": 33, "ph": "X", "name": "ReadAsync 427", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735839697556, "dur": 22, "ph": "X", "name": "ReadAsync 683", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735839697579, "dur": 29, "ph": "X", "name": "ReadAsync 303", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735839697611, "dur": 113, "ph": "X", "name": "ReadAsync 328", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735839697727, "dur": 34, "ph": "X", "name": "ReadAsync 676", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735839697763, "dur": 1, "ph": "X", "name": "ProcessMessages 1665", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735839697764, "dur": 23, "ph": "X", "name": "ReadAsync 1665", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735839697789, "dur": 23, "ph": "X", "name": "ReadAsync 337", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735839697814, "dur": 20, "ph": "X", "name": "ReadAsync 262", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735839697835, "dur": 23, "ph": "X", "name": "ReadAsync 81", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735839697860, "dur": 22, "ph": "X", "name": "ReadAsync 380", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735839697885, "dur": 22, "ph": "X", "name": "ReadAsync 397", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735839697908, "dur": 25, "ph": "X", "name": "ReadAsync 372", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735839697935, "dur": 22, "ph": "X", "name": "ReadAsync 320", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735839697958, "dur": 20, "ph": "X", "name": "ReadAsync 343", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735839697980, "dur": 23, "ph": "X", "name": "ReadAsync 239", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735839698004, "dur": 22, "ph": "X", "name": "ReadAsync 239", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735839698028, "dur": 21, "ph": "X", "name": "ReadAsync 369", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735839698051, "dur": 21, "ph": "X", "name": "ReadAsync 352", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735839698074, "dur": 22, "ph": "X", "name": "ReadAsync 279", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735839698097, "dur": 22, "ph": "X", "name": "ReadAsync 374", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735839698120, "dur": 21, "ph": "X", "name": "ReadAsync 205", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735839698143, "dur": 21, "ph": "X", "name": "ReadAsync 277", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735839698165, "dur": 22, "ph": "X", "name": "ReadAsync 284", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735839698188, "dur": 21, "ph": "X", "name": "ReadAsync 475", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735839698211, "dur": 21, "ph": "X", "name": "ReadAsync 391", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735839698233, "dur": 22, "ph": "X", "name": "ReadAsync 354", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735839698257, "dur": 23, "ph": "X", "name": "ReadAsync 398", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735839698281, "dur": 21, "ph": "X", "name": "ReadAsync 370", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735839698303, "dur": 21, "ph": "X", "name": "ReadAsync 197", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735839698326, "dur": 22, "ph": "X", "name": "ReadAsync 429", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735839698349, "dur": 22, "ph": "X", "name": "ReadAsync 479", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735839698373, "dur": 22, "ph": "X", "name": "ReadAsync 498", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735839698396, "dur": 22, "ph": "X", "name": "ReadAsync 254", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735839698419, "dur": 2, "ph": "X", "name": "ProcessMessages 447", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735839698421, "dur": 22, "ph": "X", "name": "ReadAsync 447", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735839698445, "dur": 21, "ph": "X", "name": "ReadAsync 517", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735839698467, "dur": 22, "ph": "X", "name": "ReadAsync 109", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735839698490, "dur": 22, "ph": "X", "name": "ReadAsync 390", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735839698514, "dur": 21, "ph": "X", "name": "ReadAsync 525", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735839698536, "dur": 22, "ph": "X", "name": "ReadAsync 205", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735839698560, "dur": 21, "ph": "X", "name": "ReadAsync 339", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735839698583, "dur": 21, "ph": "X", "name": "ReadAsync 426", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735839698605, "dur": 17, "ph": "X", "name": "ReadAsync 363", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735839698623, "dur": 98, "ph": "X", "name": "ReadAsync 199", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735839698723, "dur": 22, "ph": "X", "name": "ReadAsync 235", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735839698747, "dur": 22, "ph": "X", "name": "ReadAsync 391", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735839698770, "dur": 21, "ph": "X", "name": "ReadAsync 406", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735839698793, "dur": 22, "ph": "X", "name": "ReadAsync 186", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735839698817, "dur": 21, "ph": "X", "name": "ReadAsync 315", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735839698839, "dur": 20, "ph": "X", "name": "ReadAsync 376", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735839698860, "dur": 30, "ph": "X", "name": "ReadAsync 76", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735839698891, "dur": 22, "ph": "X", "name": "ReadAsync 462", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735839698915, "dur": 22, "ph": "X", "name": "ReadAsync 441", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735839698939, "dur": 20, "ph": "X", "name": "ReadAsync 329", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735839698960, "dur": 22, "ph": "X", "name": "ReadAsync 285", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735839698984, "dur": 20, "ph": "X", "name": "ReadAsync 352", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735839699006, "dur": 21, "ph": "X", "name": "ReadAsync 248", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735839699028, "dur": 23, "ph": "X", "name": "ReadAsync 242", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735839699052, "dur": 21, "ph": "X", "name": "ReadAsync 371", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735839699075, "dur": 20, "ph": "X", "name": "ReadAsync 367", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735839699097, "dur": 18, "ph": "X", "name": "ReadAsync 207", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735839699116, "dur": 21, "ph": "X", "name": "ReadAsync 290", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735839699139, "dur": 21, "ph": "X", "name": "ReadAsync 265", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735839699161, "dur": 21, "ph": "X", "name": "ReadAsync 265", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735839699184, "dur": 21, "ph": "X", "name": "ReadAsync 387", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735839699207, "dur": 21, "ph": "X", "name": "ReadAsync 420", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735839699229, "dur": 15, "ph": "X", "name": "ReadAsync 393", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735839699247, "dur": 20, "ph": "X", "name": "ReadAsync 365", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735839699268, "dur": 25, "ph": "X", "name": "ReadAsync 288", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735839699295, "dur": 19, "ph": "X", "name": "ReadAsync 415", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735839699317, "dur": 1, "ph": "X", "name": "ProcessMessages 311", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735839699318, "dur": 28, "ph": "X", "name": "ReadAsync 311", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735839699348, "dur": 27, "ph": "X", "name": "ReadAsync 418", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735839699377, "dur": 25, "ph": "X", "name": "ReadAsync 188", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735839699404, "dur": 1, "ph": "X", "name": "ProcessMessages 494", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735839699406, "dur": 49, "ph": "X", "name": "ReadAsync 494", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735839699458, "dur": 1, "ph": "X", "name": "ProcessMessages 573", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735839699459, "dur": 29, "ph": "X", "name": "ReadAsync 573", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735839699490, "dur": 26, "ph": "X", "name": "ReadAsync 686", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735839699518, "dur": 26, "ph": "X", "name": "ReadAsync 328", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735839699545, "dur": 28, "ph": "X", "name": "ReadAsync 339", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735839699575, "dur": 1, "ph": "X", "name": "ProcessMessages 373", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735839699576, "dur": 36, "ph": "X", "name": "ReadAsync 373", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735839699615, "dur": 24, "ph": "X", "name": "ReadAsync 533", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735839699641, "dur": 20, "ph": "X", "name": "ReadAsync 488", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735839699662, "dur": 21, "ph": "X", "name": "ReadAsync 78", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735839699685, "dur": 24, "ph": "X", "name": "ReadAsync 280", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735839699711, "dur": 23, "ph": "X", "name": "ReadAsync 297", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735839699735, "dur": 22, "ph": "X", "name": "ReadAsync 307", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735839699759, "dur": 23, "ph": "X", "name": "ReadAsync 223", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735839699783, "dur": 21, "ph": "X", "name": "ReadAsync 265", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735839699806, "dur": 23, "ph": "X", "name": "ReadAsync 259", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735839699830, "dur": 21, "ph": "X", "name": "ReadAsync 318", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735839699853, "dur": 23, "ph": "X", "name": "ReadAsync 315", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735839699878, "dur": 23, "ph": "X", "name": "ReadAsync 233", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735839699902, "dur": 22, "ph": "X", "name": "ReadAsync 356", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735839699925, "dur": 21, "ph": "X", "name": "ReadAsync 323", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735839699948, "dur": 21, "ph": "X", "name": "ReadAsync 409", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735839699971, "dur": 24, "ph": "X", "name": "ReadAsync 258", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735839699997, "dur": 23, "ph": "X", "name": "ReadAsync 356", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735839700021, "dur": 21, "ph": "X", "name": "ReadAsync 351", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735839700043, "dur": 22, "ph": "X", "name": "ReadAsync 167", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735839700067, "dur": 26, "ph": "X", "name": "ReadAsync 298", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735839700095, "dur": 1, "ph": "X", "name": "ProcessMessages 61", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735839700096, "dur": 27, "ph": "X", "name": "ReadAsync 61", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735839700125, "dur": 34, "ph": "X", "name": "ReadAsync 240", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735839700160, "dur": 22, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735839700184, "dur": 22, "ph": "X", "name": "ReadAsync 287", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735839700208, "dur": 21, "ph": "X", "name": "ReadAsync 247", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735839700231, "dur": 20, "ph": "X", "name": "ReadAsync 380", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735839700252, "dur": 67, "ph": "X", "name": "ReadAsync 63", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735839700321, "dur": 22, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735839700345, "dur": 23, "ph": "X", "name": "ReadAsync 295", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735839700369, "dur": 27, "ph": "X", "name": "ReadAsync 266", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735839700398, "dur": 39, "ph": "X", "name": "ReadAsync 416", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735839700439, "dur": 28, "ph": "X", "name": "ReadAsync 438", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735839700468, "dur": 25, "ph": "X", "name": "ReadAsync 559", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735839700495, "dur": 23, "ph": "X", "name": "ReadAsync 378", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735839700520, "dur": 28, "ph": "X", "name": "ReadAsync 166", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735839700550, "dur": 52, "ph": "X", "name": "ReadAsync 237", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735839700605, "dur": 28, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735839700636, "dur": 25, "ph": "X", "name": "ReadAsync 224", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735839700663, "dur": 23, "ph": "X", "name": "ReadAsync 317", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735839700688, "dur": 22, "ph": "X", "name": "ReadAsync 377", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735839700712, "dur": 21, "ph": "X", "name": "ReadAsync 357", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735839700734, "dur": 22, "ph": "X", "name": "ReadAsync 244", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735839700757, "dur": 22, "ph": "X", "name": "ReadAsync 327", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735839700780, "dur": 20, "ph": "X", "name": "ReadAsync 282", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735839700802, "dur": 21, "ph": "X", "name": "ReadAsync 139", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735839700834, "dur": 21, "ph": "X", "name": "ReadAsync 89", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735839700857, "dur": 28, "ph": "X", "name": "ReadAsync 148", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735839700887, "dur": 23, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735839700910, "dur": 1, "ph": "X", "name": "ProcessMessages 269", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735839700912, "dur": 21, "ph": "X", "name": "ReadAsync 269", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735839700935, "dur": 22, "ph": "X", "name": "ReadAsync 300", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735839700958, "dur": 22, "ph": "X", "name": "ReadAsync 267", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735839700982, "dur": 21, "ph": "X", "name": "ReadAsync 291", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735839701005, "dur": 22, "ph": "X", "name": "ReadAsync 263", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735839701028, "dur": 22, "ph": "X", "name": "ReadAsync 208", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735839701051, "dur": 22, "ph": "X", "name": "ReadAsync 295", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735839701074, "dur": 21, "ph": "X", "name": "ReadAsync 191", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735839701098, "dur": 65, "ph": "X", "name": "ReadAsync 237", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735839701167, "dur": 30, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735839701201, "dur": 22, "ph": "X", "name": "ReadAsync 355", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735839701225, "dur": 21, "ph": "X", "name": "ReadAsync 336", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735839701248, "dur": 58, "ph": "X", "name": "ReadAsync 237", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735839701307, "dur": 25, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735839701334, "dur": 33, "ph": "X", "name": "ReadAsync 282", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735839701370, "dur": 1, "ph": "X", "name": "ProcessMessages 407", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735839701372, "dur": 40, "ph": "X", "name": "ReadAsync 407", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735839701415, "dur": 1, "ph": "X", "name": "ProcessMessages 297", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735839701417, "dur": 38, "ph": "X", "name": "ReadAsync 297", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735839701457, "dur": 24, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735839701483, "dur": 23, "ph": "X", "name": "ReadAsync 365", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735839701508, "dur": 25, "ph": "X", "name": "ReadAsync 346", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735839701535, "dur": 52, "ph": "X", "name": "ReadAsync 289", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735839701589, "dur": 31, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735839701622, "dur": 1, "ph": "X", "name": "ProcessMessages 305", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735839701623, "dur": 33, "ph": "X", "name": "ReadAsync 305", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735839701658, "dur": 18, "ph": "X", "name": "ReadAsync 494", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735839701678, "dur": 54, "ph": "X", "name": "ReadAsync 237", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735839701733, "dur": 28, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735839701763, "dur": 25, "ph": "X", "name": "ReadAsync 340", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735839701789, "dur": 21, "ph": "X", "name": "ReadAsync 499", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735839701812, "dur": 19, "ph": "X", "name": "ReadAsync 309", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735839701833, "dur": 47, "ph": "X", "name": "ReadAsync 8", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735839701881, "dur": 22, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735839701904, "dur": 22, "ph": "X", "name": "ReadAsync 318", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735839701928, "dur": 23, "ph": "X", "name": "ReadAsync 265", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735839701952, "dur": 20, "ph": "X", "name": "ReadAsync 460", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735839701975, "dur": 57, "ph": "X", "name": "ReadAsync 240", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735839702034, "dur": 22, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735839702058, "dur": 24, "ph": "X", "name": "ReadAsync 358", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735839702083, "dur": 36, "ph": "X", "name": "ReadAsync 504", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735839702120, "dur": 21, "ph": "X", "name": "ReadAsync 318", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735839702143, "dur": 48, "ph": "X", "name": "ReadAsync 57", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735839702192, "dur": 25, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735839702218, "dur": 21, "ph": "X", "name": "ReadAsync 374", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735839702240, "dur": 22, "ph": "X", "name": "ReadAsync 174", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735839702264, "dur": 21, "ph": "X", "name": "ReadAsync 401", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735839702287, "dur": 53, "ph": "X", "name": "ReadAsync 145", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735839702341, "dur": 22, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735839702365, "dur": 22, "ph": "X", "name": "ReadAsync 265", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735839702389, "dur": 22, "ph": "X", "name": "ReadAsync 342", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735839702413, "dur": 20, "ph": "X", "name": "ReadAsync 404", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735839702434, "dur": 46, "ph": "X", "name": "ReadAsync 61", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735839702482, "dur": 22, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735839702506, "dur": 21, "ph": "X", "name": "ReadAsync 283", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735839702529, "dur": 22, "ph": "X", "name": "ReadAsync 298", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735839702552, "dur": 18, "ph": "X", "name": "ReadAsync 383", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735839702571, "dur": 55, "ph": "X", "name": "ReadAsync 66", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735839702627, "dur": 22, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735839702650, "dur": 22, "ph": "X", "name": "ReadAsync 296", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735839702674, "dur": 21, "ph": "X", "name": "ReadAsync 243", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735839702697, "dur": 20, "ph": "X", "name": "ReadAsync 377", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735839702719, "dur": 50, "ph": "X", "name": "ReadAsync 66", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735839702770, "dur": 22, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735839702794, "dur": 21, "ph": "X", "name": "ReadAsync 244", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735839702818, "dur": 22, "ph": "X", "name": "ReadAsync 201", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735839702841, "dur": 22, "ph": "X", "name": "ReadAsync 238", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735839702864, "dur": 21, "ph": "X", "name": "ReadAsync 389", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735839702887, "dur": 227, "ph": "X", "name": "ReadAsync 359", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735839703116, "dur": 1, "ph": "X", "name": "ProcessMessages 286", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735839703118, "dur": 41, "ph": "X", "name": "ReadAsync 286", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735839703160, "dur": 1, "ph": "X", "name": "ProcessMessages 2532", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735839703162, "dur": 42, "ph": "X", "name": "ReadAsync 2532", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735839703207, "dur": 35, "ph": "X", "name": "ReadAsync 266", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735839703245, "dur": 26, "ph": "X", "name": "ReadAsync 476", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735839703272, "dur": 23, "ph": "X", "name": "ReadAsync 297", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735839703297, "dur": 22, "ph": "X", "name": "ReadAsync 73", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735839703321, "dur": 63, "ph": "X", "name": "ReadAsync 240", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735839703388, "dur": 44, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735839703435, "dur": 1, "ph": "X", "name": "ProcessMessages 540", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735839703436, "dur": 44, "ph": "X", "name": "ReadAsync 540", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735839703484, "dur": 1, "ph": "X", "name": "ProcessMessages 584", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735839703487, "dur": 46, "ph": "X", "name": "ReadAsync 584", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735839703535, "dur": 39, "ph": "X", "name": "ReadAsync 352", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735839703578, "dur": 33, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735839703613, "dur": 25, "ph": "X", "name": "ReadAsync 581", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735839703640, "dur": 25, "ph": "X", "name": "ReadAsync 496", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735839703667, "dur": 1, "ph": "X", "name": "ProcessMessages 66", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735839703669, "dur": 32, "ph": "X", "name": "ReadAsync 66", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735839703704, "dur": 23, "ph": "X", "name": "ReadAsync 505", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735839703728, "dur": 35, "ph": "X", "name": "ReadAsync 67", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735839703766, "dur": 28, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735839703796, "dur": 22, "ph": "X", "name": "ReadAsync 366", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735839703819, "dur": 22, "ph": "X", "name": "ReadAsync 312", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735839703843, "dur": 77, "ph": "X", "name": "ReadAsync 175", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735839703922, "dur": 21, "ph": "X", "name": "ReadAsync 99", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735839703944, "dur": 64, "ph": "X", "name": "ReadAsync 138", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735839704010, "dur": 22, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735839704033, "dur": 22, "ph": "X", "name": "ReadAsync 230", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735839704057, "dur": 27, "ph": "X", "name": "ReadAsync 233", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735839704086, "dur": 22, "ph": "X", "name": "ReadAsync 419", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735839704109, "dur": 59, "ph": "X", "name": "ReadAsync 139", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735839704170, "dur": 21, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735839704193, "dur": 24, "ph": "X", "name": "ReadAsync 209", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735839704218, "dur": 25, "ph": "X", "name": "ReadAsync 279", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735839704244, "dur": 21, "ph": "X", "name": "ReadAsync 410", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735839704267, "dur": 59, "ph": "X", "name": "ReadAsync 138", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735839704328, "dur": 48, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735839704379, "dur": 43, "ph": "X", "name": "ReadAsync 167", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735839704425, "dur": 1, "ph": "X", "name": "ProcessMessages 691", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735839704427, "dur": 46, "ph": "X", "name": "ReadAsync 691", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735839704476, "dur": 1, "ph": "X", "name": "ProcessMessages 359", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735839704478, "dur": 28, "ph": "X", "name": "ReadAsync 359", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735839704508, "dur": 29, "ph": "X", "name": "ReadAsync 323", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735839704539, "dur": 23, "ph": "X", "name": "ReadAsync 282", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735839704563, "dur": 23, "ph": "X", "name": "ReadAsync 319", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735839704587, "dur": 20, "ph": "X", "name": "ReadAsync 361", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735839704609, "dur": 18, "ph": "X", "name": "ReadAsync 269", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735839704629, "dur": 20, "ph": "X", "name": "ReadAsync 100", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735839704650, "dur": 77, "ph": "X", "name": "ReadAsync 140", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735839704728, "dur": 27, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735839704758, "dur": 31, "ph": "X", "name": "ReadAsync 348", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735839704791, "dur": 1, "ph": "X", "name": "ProcessMessages 382", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735839704793, "dur": 28, "ph": "X", "name": "ReadAsync 382", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735839704822, "dur": 55, "ph": "X", "name": "ReadAsync 358", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735839704879, "dur": 24, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735839704905, "dur": 1, "ph": "X", "name": "ProcessMessages 274", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735839704907, "dur": 35, "ph": "X", "name": "ReadAsync 274", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735839704944, "dur": 23, "ph": "X", "name": "ReadAsync 592", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735839704968, "dur": 21, "ph": "X", "name": "ReadAsync 119", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735839704991, "dur": 82, "ph": "X", "name": "ReadAsync 138", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735839705074, "dur": 28, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735839705103, "dur": 28, "ph": "X", "name": "ReadAsync 260", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735839705133, "dur": 24, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735839705159, "dur": 23, "ph": "X", "name": "ReadAsync 230", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735839705184, "dur": 21, "ph": "X", "name": "ReadAsync 399", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735839705207, "dur": 71, "ph": "X", "name": "ReadAsync 196", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735839705279, "dur": 24, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735839705304, "dur": 21, "ph": "X", "name": "ReadAsync 330", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735839705327, "dur": 21, "ph": "X", "name": "ReadAsync 177", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735839705349, "dur": 21, "ph": "X", "name": "ReadAsync 301", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735839705372, "dur": 60, "ph": "X", "name": "ReadAsync 237", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735839705433, "dur": 23, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735839705458, "dur": 22, "ph": "X", "name": "ReadAsync 233", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735839705482, "dur": 21, "ph": "X", "name": "ReadAsync 276", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735839705504, "dur": 31, "ph": "X", "name": "ReadAsync 361", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735839705536, "dur": 1, "ph": "X", "name": "ProcessMessages 200", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735839705537, "dur": 45, "ph": "X", "name": "ReadAsync 200", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735839705584, "dur": 28, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735839705614, "dur": 23, "ph": "X", "name": "ReadAsync 248", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735839705638, "dur": 23, "ph": "X", "name": "ReadAsync 366", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735839705662, "dur": 21, "ph": "X", "name": "ReadAsync 427", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735839705685, "dur": 22, "ph": "X", "name": "ReadAsync 366", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735839705710, "dur": 22, "ph": "X", "name": "ReadAsync 275", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735839705733, "dur": 21, "ph": "X", "name": "ReadAsync 282", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735839705756, "dur": 21, "ph": "X", "name": "ReadAsync 370", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735839705779, "dur": 23, "ph": "X", "name": "ReadAsync 295", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735839705803, "dur": 29, "ph": "X", "name": "ReadAsync 185", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735839705834, "dur": 42, "ph": "X", "name": "ReadAsync 52", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735839705878, "dur": 25, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735839705904, "dur": 22, "ph": "X", "name": "ReadAsync 277", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735839705928, "dur": 22, "ph": "X", "name": "ReadAsync 287", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735839705951, "dur": 21, "ph": "X", "name": "ReadAsync 478", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735839705974, "dur": 21, "ph": "X", "name": "ReadAsync 408", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735839705997, "dur": 22, "ph": "X", "name": "ReadAsync 440", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735839706020, "dur": 22, "ph": "X", "name": "ReadAsync 314", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735839706044, "dur": 21, "ph": "X", "name": "ReadAsync 415", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735839706067, "dur": 21, "ph": "X", "name": "ReadAsync 358", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735839706089, "dur": 18, "ph": "X", "name": "ReadAsync 95", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735839706108, "dur": 58, "ph": "X", "name": "ReadAsync 145", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735839706167, "dur": 22, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735839706192, "dur": 23, "ph": "X", "name": "ReadAsync 319", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735839706217, "dur": 21, "ph": "X", "name": "ReadAsync 469", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735839706239, "dur": 30, "ph": "X", "name": "ReadAsync 343", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735839706273, "dur": 1, "ph": "X", "name": "ProcessMessages 395", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735839706275, "dur": 49, "ph": "X", "name": "ReadAsync 395", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735839706327, "dur": 1, "ph": "X", "name": "ProcessMessages 594", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735839706329, "dur": 37, "ph": "X", "name": "ReadAsync 594", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735839706368, "dur": 1, "ph": "X", "name": "ProcessMessages 87", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735839706370, "dur": 41, "ph": "X", "name": "ReadAsync 87", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735839706413, "dur": 28, "ph": "X", "name": "ReadAsync 445", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735839706443, "dur": 32, "ph": "X", "name": "ReadAsync 191", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735839706477, "dur": 57, "ph": "X", "name": "ReadAsync 237", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735839706538, "dur": 40, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735839706580, "dur": 25, "ph": "X", "name": "ReadAsync 474", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735839706607, "dur": 1, "ph": "X", "name": "ProcessMessages 413", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735839706608, "dur": 29, "ph": "X", "name": "ReadAsync 413", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735839706641, "dur": 22, "ph": "X", "name": "ReadAsync 266", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735839706664, "dur": 61, "ph": "X", "name": "ReadAsync 58", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735839706727, "dur": 25, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735839706753, "dur": 23, "ph": "X", "name": "ReadAsync 290", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735839706777, "dur": 22, "ph": "X", "name": "ReadAsync 400", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735839706801, "dur": 21, "ph": "X", "name": "ReadAsync 349", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735839706824, "dur": 58, "ph": "X", "name": "ReadAsync 138", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735839706884, "dur": 33, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735839706921, "dur": 37, "ph": "X", "name": "ReadAsync 381", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735839706960, "dur": 35, "ph": "X", "name": "ReadAsync 705", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735839706998, "dur": 1, "ph": "X", "name": "ProcessMessages 140", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735839706999, "dur": 59, "ph": "X", "name": "ReadAsync 140", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735839707060, "dur": 25, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735839707087, "dur": 40, "ph": "X", "name": "ReadAsync 490", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735839707131, "dur": 1, "ph": "X", "name": "ProcessMessages 456", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735839707132, "dur": 25, "ph": "X", "name": "ReadAsync 456", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735839707159, "dur": 36, "ph": "X", "name": "ReadAsync 237", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735839707198, "dur": 30, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735839707231, "dur": 20, "ph": "X", "name": "ReadAsync 485", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735839707252, "dur": 24, "ph": "X", "name": "ReadAsync 60", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735839707280, "dur": 30, "ph": "X", "name": "ReadAsync 414", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735839707311, "dur": 44, "ph": "X", "name": "ReadAsync 143", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735839707359, "dur": 26, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735839707388, "dur": 1, "ph": "X", "name": "ProcessMessages 338", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735839707389, "dur": 36, "ph": "X", "name": "ReadAsync 338", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735839707428, "dur": 1, "ph": "X", "name": "ProcessMessages 506", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735839707430, "dur": 40, "ph": "X", "name": "ReadAsync 506", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735839707472, "dur": 31, "ph": "X", "name": "ReadAsync 613", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735839707506, "dur": 39, "ph": "X", "name": "ReadAsync 474", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735839707548, "dur": 24, "ph": "X", "name": "ReadAsync 591", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735839707573, "dur": 28, "ph": "X", "name": "ReadAsync 176", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735839707602, "dur": 47, "ph": "X", "name": "ReadAsync 143", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735839707653, "dur": 31, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735839707685, "dur": 24, "ph": "X", "name": "ReadAsync 496", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735839707712, "dur": 22, "ph": "X", "name": "ReadAsync 413", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735839707735, "dur": 20, "ph": "X", "name": "ReadAsync 228", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735839707757, "dur": 41, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735839707800, "dur": 22, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735839707823, "dur": 23, "ph": "X", "name": "ReadAsync 319", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735839707848, "dur": 21, "ph": "X", "name": "ReadAsync 326", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735839707870, "dur": 18, "ph": "X", "name": "ReadAsync 329", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735839707890, "dur": 53, "ph": "X", "name": "ReadAsync 134", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735839707945, "dur": 23, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735839707969, "dur": 23, "ph": "X", "name": "ReadAsync 270", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735839707993, "dur": 22, "ph": "X", "name": "ReadAsync 365", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735839708016, "dur": 21, "ph": "X", "name": "ReadAsync 246", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735839708039, "dur": 59, "ph": "X", "name": "ReadAsync 240", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735839708099, "dur": 22, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735839708122, "dur": 25, "ph": "X", "name": "ReadAsync 301", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735839708149, "dur": 23, "ph": "X", "name": "ReadAsync 444", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735839708173, "dur": 20, "ph": "X", "name": "ReadAsync 306", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735839708195, "dur": 47, "ph": "X", "name": "ReadAsync 57", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735839708244, "dur": 22, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735839708315, "dur": 26, "ph": "X", "name": "ReadAsync 283", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735839708342, "dur": 27, "ph": "X", "name": "ReadAsync 859", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735839708371, "dur": 22, "ph": "X", "name": "ReadAsync 264", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735839708394, "dur": 22, "ph": "X", "name": "ReadAsync 294", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735839708418, "dur": 22, "ph": "X", "name": "ReadAsync 262", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735839708442, "dur": 17, "ph": "X", "name": "ReadAsync 300", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735839708460, "dur": 21, "ph": "X", "name": "ReadAsync 60", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735839708482, "dur": 65, "ph": "X", "name": "ReadAsync 197", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735839708550, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735839708553, "dur": 37, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735839708591, "dur": 1, "ph": "X", "name": "ProcessMessages 442", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735839708592, "dur": 34, "ph": "X", "name": "ReadAsync 442", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735839708628, "dur": 30, "ph": "X", "name": "ReadAsync 277", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735839708659, "dur": 19, "ph": "X", "name": "ReadAsync 362", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735839708680, "dur": 21, "ph": "X", "name": "ReadAsync 339", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735839708702, "dur": 1, "ph": "X", "name": "ProcessMessages 271", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735839708703, "dur": 22, "ph": "X", "name": "ReadAsync 271", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735839708727, "dur": 21, "ph": "X", "name": "ReadAsync 362", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735839708749, "dur": 21, "ph": "X", "name": "ReadAsync 193", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735839708772, "dur": 22, "ph": "X", "name": "ReadAsync 289", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735839708796, "dur": 15, "ph": "X", "name": "ReadAsync 173", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735839708812, "dur": 55, "ph": "X", "name": "ReadAsync 67", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735839708869, "dur": 125, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735839708997, "dur": 1, "ph": "X", "name": "ProcessMessages 314", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735839708999, "dur": 53, "ph": "X", "name": "ReadAsync 314", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735839709054, "dur": 1, "ph": "X", "name": "ProcessMessages 1377", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735839709056, "dur": 41, "ph": "X", "name": "ReadAsync 1377", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735839709099, "dur": 26, "ph": "X", "name": "ReadAsync 474", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735839709129, "dur": 35, "ph": "X", "name": "ReadAsync 56", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735839709166, "dur": 51, "ph": "X", "name": "ReadAsync 240", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735839709218, "dur": 22, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735839709242, "dur": 30, "ph": "X", "name": "ReadAsync 197", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735839709276, "dur": 1, "ph": "X", "name": "ProcessMessages 272", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735839709278, "dur": 46, "ph": "X", "name": "ReadAsync 272", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735839709325, "dur": 67, "ph": "X", "name": "ReadAsync 637", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735839709398, "dur": 40, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735839709441, "dur": 1, "ph": "X", "name": "ProcessMessages 569", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735839709443, "dur": 36, "ph": "X", "name": "ReadAsync 569", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735839709482, "dur": 34, "ph": "X", "name": "ReadAsync 469", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735839709520, "dur": 47, "ph": "X", "name": "ReadAsync 193", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735839709569, "dur": 25, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735839709596, "dur": 24, "ph": "X", "name": "ReadAsync 410", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735839709622, "dur": 24, "ph": "X", "name": "ReadAsync 466", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735839709648, "dur": 59, "ph": "X", "name": "ReadAsync 310", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735839709709, "dur": 28, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735839709739, "dur": 32, "ph": "X", "name": "ReadAsync 304", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735839709773, "dur": 22, "ph": "X", "name": "ReadAsync 548", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735839709797, "dur": 22, "ph": "X", "name": "ReadAsync 447", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735839709820, "dur": 22, "ph": "X", "name": "ReadAsync 478", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735839709843, "dur": 22, "ph": "X", "name": "ReadAsync 308", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735839709867, "dur": 22, "ph": "X", "name": "ReadAsync 296", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735839709891, "dur": 20, "ph": "X", "name": "ReadAsync 354", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735839709914, "dur": 22, "ph": "X", "name": "ReadAsync 193", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735839709937, "dur": 55, "ph": "X", "name": "ReadAsync 237", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735839709993, "dur": 18, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735839710013, "dur": 22, "ph": "X", "name": "ReadAsync 266", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735839710037, "dur": 21, "ph": "X", "name": "ReadAsync 251", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735839710059, "dur": 21, "ph": "X", "name": "ReadAsync 386", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735839710082, "dur": 22, "ph": "X", "name": "ReadAsync 383", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735839710105, "dur": 21, "ph": "X", "name": "ReadAsync 469", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735839710127, "dur": 27, "ph": "X", "name": "ReadAsync 299", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735839710155, "dur": 21, "ph": "X", "name": "ReadAsync 420", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735839710178, "dur": 20, "ph": "X", "name": "ReadAsync 390", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735839710200, "dur": 22, "ph": "X", "name": "ReadAsync 33", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735839710224, "dur": 56, "ph": "X", "name": "ReadAsync 228", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735839710282, "dur": 37, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735839710321, "dur": 21, "ph": "X", "name": "ReadAsync 370", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735839710344, "dur": 28, "ph": "X", "name": "ReadAsync 604", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735839710375, "dur": 1, "ph": "X", "name": "ProcessMessages 375", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735839710376, "dur": 40, "ph": "X", "name": "ReadAsync 375", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735839710419, "dur": 1, "ph": "X", "name": "ProcessMessages 666", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735839710421, "dur": 44, "ph": "X", "name": "ReadAsync 666", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735839710467, "dur": 1, "ph": "X", "name": "ProcessMessages 742", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735839710469, "dur": 25, "ph": "X", "name": "ReadAsync 742", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735839710495, "dur": 23, "ph": "X", "name": "ReadAsync 196", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735839710519, "dur": 24, "ph": "X", "name": "ReadAsync 229", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735839710547, "dur": 51, "ph": "X", "name": "ReadAsync 8", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735839710600, "dur": 25, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735839710627, "dur": 28, "ph": "X", "name": "ReadAsync 359", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735839710657, "dur": 22, "ph": "X", "name": "ReadAsync 353", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735839710683, "dur": 35, "ph": "X", "name": "ReadAsync 111", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735839710720, "dur": 21, "ph": "X", "name": "ReadAsync 425", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735839710743, "dur": 22, "ph": "X", "name": "ReadAsync 211", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735839710766, "dur": 22, "ph": "X", "name": "ReadAsync 201", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735839710789, "dur": 22, "ph": "X", "name": "ReadAsync 256", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735839710813, "dur": 20, "ph": "X", "name": "ReadAsync 385", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735839710835, "dur": 22, "ph": "X", "name": "ReadAsync 161", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735839710859, "dur": 21, "ph": "X", "name": "ReadAsync 217", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735839710881, "dur": 48, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735839710933, "dur": 32, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735839710966, "dur": 22, "ph": "X", "name": "ReadAsync 406", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735839710990, "dur": 22, "ph": "X", "name": "ReadAsync 221", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735839711014, "dur": 20, "ph": "X", "name": "ReadAsync 420", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735839711036, "dur": 55, "ph": "X", "name": "ReadAsync 61", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735839711093, "dur": 22, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735839711117, "dur": 23, "ph": "X", "name": "ReadAsync 303", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735839711141, "dur": 22, "ph": "X", "name": "ReadAsync 366", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735839711165, "dur": 21, "ph": "X", "name": "ReadAsync 366", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735839711188, "dur": 21, "ph": "X", "name": "ReadAsync 485", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735839711210, "dur": 25, "ph": "X", "name": "ReadAsync 101", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735839711237, "dur": 21, "ph": "X", "name": "ReadAsync 208", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735839711260, "dur": 17, "ph": "X", "name": "ReadAsync 365", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735839711279, "dur": 22, "ph": "X", "name": "ReadAsync 140", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735839711302, "dur": 21, "ph": "X", "name": "ReadAsync 330", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735839711324, "dur": 22, "ph": "X", "name": "ReadAsync 192", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735839711348, "dur": 20, "ph": "X", "name": "ReadAsync 171", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735839711369, "dur": 61, "ph": "X", "name": "ReadAsync 66", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735839711431, "dur": 22, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735839711454, "dur": 18, "ph": "X", "name": "ReadAsync 269", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735839711474, "dur": 54, "ph": "X", "name": "ReadAsync 254", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735839711529, "dur": 21, "ph": "X", "name": "ReadAsync 354", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735839711552, "dur": 23, "ph": "X", "name": "ReadAsync 339", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735839711576, "dur": 21, "ph": "X", "name": "ReadAsync 374", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735839711598, "dur": 21, "ph": "X", "name": "ReadAsync 278", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735839711621, "dur": 22, "ph": "X", "name": "ReadAsync 351", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735839711644, "dur": 20, "ph": "X", "name": "ReadAsync 328", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735839711666, "dur": 23, "ph": "X", "name": "ReadAsync 178", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735839711690, "dur": 21, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735839711712, "dur": 65, "ph": "X", "name": "ReadAsync 225", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735839711778, "dur": 21, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735839711801, "dur": 18, "ph": "X", "name": "ReadAsync 242", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735839711820, "dur": 21, "ph": "X", "name": "ReadAsync 215", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735839711842, "dur": 22, "ph": "X", "name": "ReadAsync 320", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735839711866, "dur": 21, "ph": "X", "name": "ReadAsync 287", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735839711889, "dur": 20, "ph": "X", "name": "ReadAsync 303", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735839711911, "dur": 21, "ph": "X", "name": "ReadAsync 275", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735839711933, "dur": 18, "ph": "X", "name": "ReadAsync 238", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735839711952, "dur": 1, "ph": "X", "name": "ProcessMessages 233", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735839711953, "dur": 21, "ph": "X", "name": "ReadAsync 233", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735839711975, "dur": 20, "ph": "X", "name": "ReadAsync 253", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735839711997, "dur": 21, "ph": "X", "name": "ReadAsync 60", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735839712020, "dur": 61, "ph": "X", "name": "ReadAsync 200", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735839712082, "dur": 22, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735839712106, "dur": 22, "ph": "X", "name": "ReadAsync 247", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735839712130, "dur": 20, "ph": "X", "name": "ReadAsync 312", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735839712152, "dur": 21, "ph": "X", "name": "ReadAsync 333", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735839712175, "dur": 21, "ph": "X", "name": "ReadAsync 336", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735839712197, "dur": 21, "ph": "X", "name": "ReadAsync 190", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735839712219, "dur": 21, "ph": "X", "name": "ReadAsync 268", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735839712242, "dur": 20, "ph": "X", "name": "ReadAsync 287", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735839712264, "dur": 21, "ph": "X", "name": "ReadAsync 302", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735839712287, "dur": 21, "ph": "X", "name": "ReadAsync 246", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735839712309, "dur": 21, "ph": "X", "name": "ReadAsync 175", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735839712332, "dur": 21, "ph": "X", "name": "ReadAsync 54", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735839712356, "dur": 1, "ph": "X", "name": "ProcessMessages 8", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735839712358, "dur": 52, "ph": "X", "name": "ReadAsync 8", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735839712412, "dur": 42, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735839712459, "dur": 81, "ph": "X", "name": "ReadAsync 224", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735839712544, "dur": 30, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735839712576, "dur": 347, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735839712926, "dur": 45, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735839712974, "dur": 4, "ph": "X", "name": "ProcessMessages 864", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735839712979, "dur": 319, "ph": "X", "name": "ReadAsync 864", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735839713302, "dur": 33, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735839713336, "dur": 180, "ph": "X", "name": "ProcessMessages 13", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735839713518, "dur": 5256, "ph": "X", "name": "ReadAsync 13", "args": {}}, {"pid": 3100, "tid": 24786, "ts": 1754735841957108, "dur": 729, "ph": "X", "name": "ReadEntireBinlogFromIpcAsync", "args": {}}, {"pid": 3100, "tid": 8589934592, "ph": "M", "name": "thread_name", "args": {"name": "WaitForBuildProgramInputDataBeingWrittenAndSendDagReadyMessageAsync"}}, {"pid": 3100, "tid": 8589934592, "ts": 1754735839537794, "dur": 62340, "ph": "X", "name": "await writeBuildProgramInputDataTask", "args": {}}, {"pid": 3100, "tid": 8589934592, "ts": 1754735839600137, "dur": 88705, "ph": "X", "name": "WritePipe.WaitConnectionAsync", "args": {}}, {"pid": 3100, "tid": 8589934592, "ts": 1754735839688845, "dur": 1531, "ph": "X", "name": "WriteDagReadyMessage", "args": {}}, {"pid": 3100, "tid": 24786, "ts": 1754735841957840, "dur": 4, "ph": "X", "name": "WaitForBuildProgramInputDataBeingWrittenAndSendDagReadyMessageAsync", "args": {}}, {"pid": 3100, "tid": 4294967296, "ph": "M", "name": "thread_name", "args": {"name": "BuildAsync"}}, {"pid": 3100, "tid": 4294967296, "ts": 1754735839414808, "dur": 304645, "ph": "X", "name": "RunBackend", "args": {}}, {"pid": 3100, "tid": 4294967296, "ts": 1754735839417000, "dur": 117246, "ph": "X", "name": "BackendProgram.Start", "args": {}}, {"pid": 3100, "tid": 4294967296, "ts": 1754735839719535, "dur": 687480, "ph": "X", "name": "await ExecuteBuildProgram", "args": {}}, {"pid": 3100, "tid": 4294967296, "ts": 1754735840407244, "dur": 1538735, "ph": "X", "name": "RunBackend", "args": {}}, {"pid": 3100, "tid": 4294967296, "ts": 1754735840407362, "dur": 130107, "ph": "X", "name": "BackendProgram.Start", "args": {}}, {"pid": 3100, "tid": 4294967296, "ts": 1754735841945994, "dur": 3792, "ph": "X", "name": "await WaitForAndApplyScriptUpdaters", "args": {}}, {"pid": 3100, "tid": 4294967296, "ts": 1754735841948727, "dur": 34, "ph": "X", "name": "await <PERSON><PERSON>tUp<PERSON><PERSON>", "args": {}}, {"pid": 3100, "tid": 4294967296, "ts": 1754735841949791, "dur": 15, "ph": "X", "name": "await taskToReadBuildProgramOutput", "args": {}}, {"pid": 3100, "tid": 24786, "ts": 1754735841957845, "dur": 9, "ph": "X", "name": "BuildAsync", "args": {}}, {"cat": "", "pid": 12345, "tid": 0, "ts": 0, "ph": "M", "name": "process_name", "args": {"name": "bee_backend"}}, {"pid": 12345, "tid": 0, "ts": 1754735840696146, "dur": 105200, "ph": "X", "name": "DriverInitData", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1754735840801355, "dur": 279, "ph": "X", "name": "RemoveStaleOutputs", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1754735840801732, "dur": 53, "ph": "X", "name": "<PERSON><PERSON>", "args": {"detail": "PrepareNodes"}}, {"pid": 12345, "tid": 0, "ts": 1754735840801785, "dur": 427, "ph": "X", "name": "BuildQueueInit", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1754735840802477, "dur": 72, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.PresetsUIModule.dll_A8B81DEC69E8E1CF.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1754735840804930, "dur": 195, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/UnityEngine.TestRunner.dll.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1754735840808440, "dur": 56, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Searcher.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 0, "ts": 1754735840810030, "dur": 52, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Flow.Editor.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 0, "ts": 1754735840813479, "dur": 50, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/CrashKonijn.Agent.Core.dll (+pdb)"}}, {"pid": 12345, "tid": 0, "ts": 1754735840817586, "dur": 77, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.Timeline.Editor.dll"}}, {"pid": 12345, "tid": 0, "ts": 1754735840821531, "dur": 50, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/com.crashkonijn.goap.demos.complex.dll (+2 others)"}}, {"pid": 12345, "tid": 0, "ts": 1754735840822014, "dur": 100, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/com.crashkonijn.goap.demos.turn-based.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 0, "ts": 1754735840822255, "dur": 76, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/PPv2URPConverters.dll (+pdb)"}}, {"pid": 12345, "tid": 0, "ts": 1754735840822364, "dur": 70, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/PPv2URPConverters.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 0, "ts": 1754735840802227, "dur": 21815, "ph": "X", "name": "EnqueueRequestedNodes", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1754735840824055, "dur": 1112715, "ph": "X", "name": "WaitForBuildFinished", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1754735841936771, "dur": 179, "ph": "X", "name": "JoinBuildThread", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1754735841937178, "dur": 65, "ph": "X", "name": "BuildQueueDestroyTail", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1754735841937272, "dur": 1372, "ph": "X", "name": "<PERSON><PERSON>", "args": {"detail": "Write AllBuiltNodes"}}, {"pid": 12345, "tid": 1, "ts": 1754735840802737, "dur": 21330, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1754735840824075, "dur": 2899, "ph": "X", "name": "CheckDagSignatures", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1754735840826974, "dur": 244, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1754735840827218, "dur": 348, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1754735840827566, "dur": 746, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1754735840828313, "dur": 739, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1754735840829052, "dur": 1261, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1754735840830313, "dur": 426, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1754735840830740, "dur": 557, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1754735840831297, "dur": 178, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1754735840831475, "dur": 224, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1754735840831699, "dur": 268, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1754735840831967, "dur": 208, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1754735840832313, "dur": 248, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1754735840832600, "dur": 369, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1754735840832970, "dur": 83, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.VSCode.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1754735840833066, "dur": 121, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/CrashKonijn.Goap.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1754735840833212, "dur": 59, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/com.crashkonijn.docs.getting_started.dll.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1754735840833272, "dur": 122, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1754735840833400, "dur": 144, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/GlassSystem.dll.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1754735840833674, "dur": 289, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\GameSoft\\Unity\\Editor\\2022.3.38f1c1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.UnityWebRequestTextureModule.dll"}}, {"pid": 12345, "tid": 1, "ts": 1754735840834048, "dur": 69, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\GameSoft\\Unity\\Editor\\2022.3.38f1c1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.IO.Pipes.dll"}}, {"pid": 12345, "tid": 1, "ts": 1754735840834146, "dur": 178, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\GameSoft\\Unity\\Editor\\2022.3.38f1c1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Net.NetworkInformation.dll"}}, {"pid": 12345, "tid": 1, "ts": 1754735840834391, "dur": 52, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\GameSoft\\Unity\\Editor\\2022.3.38f1c1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Runtime.dll"}}, {"pid": 12345, "tid": 1, "ts": 1754735840833588, "dur": 975, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/GlassSystem.dll (+2 others)"}}, {"pid": 12345, "tid": 1, "ts": 1754735840834564, "dur": 62, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1754735840834632, "dur": 116, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.InputSystem.TestFramework.dll.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1754735840834769, "dur": 309, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.InputSystem.TestFramework.dll (+2 others)"}}, {"pid": 12345, "tid": 1, "ts": 1754735840835123, "dur": 68, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1754735840835191, "dur": 209, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1754735840835400, "dur": 548, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1754735840835948, "dur": 1923, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1754735840837872, "dur": 59, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Assembly-CSharp-Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1754735840837954, "dur": 342711, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1754735841181940, "dur": 134, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\GameSoft\\Unity\\Editor\\2022.3.38f1c1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Text.Encoding.Extensions.dll"}}, {"pid": 12345, "tid": 1, "ts": 1754735841180666, "dur": 1641, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.RenderPipelines.ShaderGraph.ShaderGraphLibrary.dll (+pdb)"}}, {"pid": 12345, "tid": 1, "ts": 1754735841182308, "dur": 81, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1754735841183010, "dur": 302, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\GameSoft\\Unity\\Editor\\2022.3.38f1c1\\Editor\\Data\\Tools\\ilpp\\Unity.ILPP.Runner\\Microsoft.AspNetCore.Server.Kestrel.Core.dll"}}, {"pid": 12345, "tid": 1, "ts": 1754735841183870, "dur": 122, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\GameSoft\\Unity\\Editor\\2022.3.38f1c1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Reflection.Extensions.dll"}}, {"pid": 12345, "tid": 1, "ts": 1754735841182399, "dur": 1829, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.InputSystem.TestFramework.dll (+pdb)"}}, {"pid": 12345, "tid": 1, "ts": 1754735841184858, "dur": 507, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\GameSoft\\Unity\\Editor\\2022.3.38f1c1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Runtime.Extensions.dll"}}, {"pid": 12345, "tid": 1, "ts": 1754735841185689, "dur": 53, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\GameSoft\\Unity\\Editor\\2022.3.38f1c1\\Editor\\Data\\Tools\\ilpp\\Unity.ILPP.Runner\\Grpc.Net.Common.dll"}}, {"pid": 12345, "tid": 1, "ts": 1754735841186003, "dur": 390, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\GameSoft\\Unity\\Editor\\2022.3.38f1c1\\Editor\\Data\\Tools\\ilpp\\Unity.ILPP.Runner\\System.Drawing.Primitives.dll"}}, {"pid": 12345, "tid": 1, "ts": 1754735841187889, "dur": 52, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\GameSoft\\Unity\\Editor\\2022.3.38f1c1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Diagnostics.Process.dll"}}, {"pid": 12345, "tid": 1, "ts": 1754735841184265, "dur": 4013, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.RenderPipelines.Core.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 1, "ts": 1754735841188329, "dur": 80, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1754735841188480, "dur": 111, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1754735841188595, "dur": 191, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\Bee\\artifacts\\1900b0aE.dag\\post-processed\\Unity.VisualScripting.Core.pdb"}}, {"pid": 12345, "tid": 1, "ts": 1754735841188595, "dur": 191, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.VisualScripting.Core.pdb"}}, {"pid": 12345, "tid": 1, "ts": 1754735841188798, "dur": 277, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1754735841189104, "dur": 747673, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1754735840802878, "dur": 21246, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1754735840824131, "dur": 57, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityWebRequestWWWModule.dll_BA3DE9AF0772CF8F.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1754735840824523, "dur": 93, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1754735840824878, "dur": 153, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Core.Editor.rsp2"}}, {"pid": 12345, "tid": 2, "ts": 1754735840825078, "dur": 166, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Burst.Editor.rsp2"}}, {"pid": 12345, "tid": 2, "ts": 1754735840825450, "dur": 62, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/URPWizard.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 2, "ts": 1754735840825600, "dur": 108, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.InputSystem.TestFramework.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 2, "ts": 1754735840825723, "dur": 115, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Collections.DocCodeSamples.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 2, "ts": 1754735840826015, "dur": 228, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1754735840826248, "dur": 288, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1754735840826537, "dur": 269, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1754735840826806, "dur": 244, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1754735840827050, "dur": 257, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1754735840827307, "dur": 171, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1754735840827478, "dur": 174, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1754735840827652, "dur": 196, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1754735840827848, "dur": 243, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1754735840828207, "dur": 544, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.render-pipelines.universal@14.0.11\\Editor\\2D\\ShapeEditor\\EditorTool\\PathEditorToolExtensions.cs"}}, {"pid": 12345, "tid": 2, "ts": 1754735840828092, "dur": 1083, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1754735840829176, "dur": 412, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1754735840829588, "dur": 203, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1754735840829843, "dur": 389, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1754735840830232, "dur": 435, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1754735840830667, "dur": 196, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1754735840830863, "dur": 227, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1754735840831090, "dur": 488, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1754735840831578, "dur": 225, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1754735840831803, "dur": 181, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1754735840831984, "dur": 204, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1754735840832313, "dur": 270, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1754735840832583, "dur": 367, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1754735840832954, "dur": 69, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.Timeline.dll.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1754735840833119, "dur": 84, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\GameSoft\\Unity\\Editor\\2022.3.38f1c1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.PerformanceReportingModule.dll"}}, {"pid": 12345, "tid": 2, "ts": 1754735840833481, "dur": 211, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\GameSoft\\Unity\\Editor\\2022.3.38f1c1\\Editor\\Data\\PlaybackEngines\\WindowsStandaloneSupport\\UnityEditor.WindowsStandalone.Extensions.dll"}}, {"pid": 12345, "tid": 2, "ts": 1754735840833038, "dur": 793, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Timeline.dll (+2 others)"}}, {"pid": 12345, "tid": 2, "ts": 1754735840833899, "dur": 112, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.Timeline.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1754735840834361, "dur": 83, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\GameSoft\\Unity\\Editor\\2022.3.38f1c1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Reflection.Emit.dll"}}, {"pid": 12345, "tid": 2, "ts": 1754735840834721, "dur": 60, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.timeline@1.7.6\\Editor\\Animation\\AnimationTrackActions.cs"}}, {"pid": 12345, "tid": 2, "ts": 1754735840834032, "dur": 1090, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Timeline.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 2, "ts": 1754735840835202, "dur": 247, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1754735840835455, "dur": 497, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1754735840835952, "dur": 344699, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1754735841180930, "dur": 227, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\GameSoft\\Unity\\Editor\\2022.3.38f1c1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\Microsoft.Win32.Primitives.dll"}}, {"pid": 12345, "tid": 2, "ts": 1754735841181674, "dur": 119, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\GameSoft\\Unity\\Editor\\2022.3.38f1c1\\Editor\\Data\\Tools\\ilpp\\Unity.ILPP.Runner\\Microsoft.AspNetCore.Razor.Runtime.dll"}}, {"pid": 12345, "tid": 2, "ts": 1754735841180651, "dur": 1896, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/com.crashkonijn.goap.demos.simple.dll (+pdb)"}}, {"pid": 12345, "tid": 2, "ts": 1754735841182903, "dur": 79, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\GameSoft\\Unity\\Editor\\2022.3.38f1c1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Runtime.Serialization.Json.dll"}}, {"pid": 12345, "tid": 2, "ts": 1754735841183821, "dur": 71, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\GameSoft\\Unity\\Editor\\2022.3.38f1c1\\Editor\\Data\\Tools\\ilpp\\Unity.ILPP.Runner\\System.Security.Principal.Windows.dll"}}, {"pid": 12345, "tid": 2, "ts": 1754735841182600, "dur": 1886, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.VisualScripting.Core.dll (+pdb)"}}, {"pid": 12345, "tid": 2, "ts": 1754735841184527, "dur": 60, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.VisualScripting.Core.dll (+pdb)"}}, {"pid": 12345, "tid": 2, "ts": 1754735841185095, "dur": 80, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\GameSoft\\Unity\\Editor\\2022.3.38f1c1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.ServiceModel.NetTcp.dll"}}, {"pid": 12345, "tid": 2, "ts": 1754735841184590, "dur": 2180, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.PlasticSCM.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 2, "ts": 1754735841187375, "dur": 73, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\GameSoft\\Unity\\Editor\\2022.3.38f1c1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Collections.NonGeneric.dll"}}, {"pid": 12345, "tid": 2, "ts": 1754735841187595, "dur": 50, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\GameSoft\\Unity\\Editor\\2022.3.38f1c1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\System.ComponentModel.Composition.dll"}}, {"pid": 12345, "tid": 2, "ts": 1754735841187743, "dur": 126, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\GameSoft\\Unity\\Editor\\2022.3.38f1c1\\Editor\\Data\\Tools\\ilpp\\Unity.ILPP.Runner\\api-ms-win-core-rtlsupport-l1-1-0.dll"}}, {"pid": 12345, "tid": 2, "ts": 1754735841186826, "dur": 2155, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/UnityEditor.UI.dll (+pdb)"}}, {"pid": 12345, "tid": 2, "ts": 1754735841189025, "dur": 747875, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1754735840802844, "dur": 21267, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1754735840824118, "dur": 83, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.UIElementsSamplesModule.dll_623E7535F4BD3DF5.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1754735840824223, "dur": 95, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\GameSoft\\Unity\\Editor\\2022.3.38f1c1\\Editor\\Data\\Managed\\UnityEngine\\UnityEditor.EditorToolbarModule.dll"}}, {"pid": 12345, "tid": 3, "ts": 1754735840824222, "dur": 98, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.EditorToolbarModule.dll_F4EF6CA575FE5075.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1754735840824439, "dur": 56, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.PerformanceReportingModule.dll_A00BE41311F50426.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1754735840824553, "dur": 81, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1754735840824673, "dur": 77, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.VisualScripting.YamlDotNet.dll_4A5905A13DE9A491.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1754735840824766, "dur": 102, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Core.Runtime.rsp2"}}, {"pid": 12345, "tid": 3, "ts": 1754735840824879, "dur": 228, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Core.Editor.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 3, "ts": 1754735840825287, "dur": 132, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Rider.Editor.rsp2"}}, {"pid": 12345, "tid": 3, "ts": 1754735840825476, "dur": 218, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/GlassSystem.rsp2"}}, {"pid": 12345, "tid": 3, "ts": 1754735840825836, "dur": 127, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/1851555959726063663.rsp"}}, {"pid": 12345, "tid": 3, "ts": 1754735840825992, "dur": 139, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1754735840826137, "dur": 255, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1754735840826392, "dur": 383, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1754735840826775, "dur": 237, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1754735840827012, "dur": 315, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1754735840827327, "dur": 163, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1754735840827491, "dur": 534, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1754735840828026, "dur": 731, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1754735840828757, "dur": 280, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1754735840829037, "dur": 239, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1754735840829277, "dur": 662, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1754735840829939, "dur": 176, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1754735840830115, "dur": 306, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1754735840830422, "dur": 178, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1754735840830600, "dur": 172, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1754735840830773, "dur": 225, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1754735840830998, "dur": 163, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1754735840831162, "dur": 633, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1754735840831795, "dur": 170, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1754735840831965, "dur": 243, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1754735840832314, "dur": 250, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1754735840832564, "dur": 376, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1754735840832944, "dur": 69, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.Mathematics.dll.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1754735840833482, "dur": 282, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.mathematics@1.2.6\\Unity.Mathematics\\int4.gen.cs"}}, {"pid": 12345, "tid": 3, "ts": 1754735840833031, "dur": 779, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Mathematics.dll (+2 others)"}}, {"pid": 12345, "tid": 3, "ts": 1754735840833811, "dur": 331, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1754735840834151, "dur": 80, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\Bee\\artifacts\\1900b0aE.dag\\Unity.Mathematics.ref.dll"}}, {"pid": 12345, "tid": 3, "ts": 1754735840834150, "dur": 82, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.Mathematics.ref.dll_E8EFDB1B8D5C174E.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1754735840834261, "dur": 241, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.Collections.dll.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1754735840834517, "dur": 497, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Collections.dll (+2 others)"}}, {"pid": 12345, "tid": 3, "ts": 1754735840835015, "dur": 63, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1754735840835196, "dur": 63, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/CrashKonijn.Goap.Resolver.dll.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1754735840835285, "dur": 244, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/CrashKonijn.Goap.Resolver.dll (+2 others)"}}, {"pid": 12345, "tid": 3, "ts": 1754735840835575, "dur": 306, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/CrashKonijn.Goap.Runtime.dll (+2 others)"}}, {"pid": 12345, "tid": 3, "ts": 1754735840835935, "dur": 324, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/CrashKonijn.Goap.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 3, "ts": 1754735840836288, "dur": 196, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/com.crashkonijn.goap.demos.simple.dll (+2 others)"}}, {"pid": 12345, "tid": 3, "ts": 1754735840836509, "dur": 344124, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1754735841181683, "dur": 181, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\GameSoft\\Unity\\Editor\\2022.3.38f1c1\\Editor\\Data\\Tools\\ilpp\\Unity.ILPP.Runner\\System.Xml.XPath.XDocument.dll"}}, {"pid": 12345, "tid": 3, "ts": 1754735841180635, "dur": 1708, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.RenderPipeline.Universal.ShaderLibrary.dll (+pdb)"}}, {"pid": 12345, "tid": 3, "ts": 1754735841182344, "dur": 111, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1754735841183993, "dur": 63, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\GameSoft\\Unity\\Editor\\2022.3.38f1c1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Reflection.Primitives.dll"}}, {"pid": 12345, "tid": 3, "ts": 1754735841182464, "dur": 1932, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.InputSystem.dll (+pdb)"}}, {"pid": 12345, "tid": 3, "ts": 1754735841184397, "dur": 129, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1754735841184670, "dur": 211, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\GameSoft\\Unity\\Editor\\2022.3.38f1c1\\Editor\\Data\\Managed\\UnityEngine\\UnityEditor.UIElementsSamplesModule.dll"}}, {"pid": 12345, "tid": 3, "ts": 1754735841184541, "dur": 2068, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.RenderPipelines.Universal.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 3, "ts": 1754735841186643, "dur": 83, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.RenderPipelines.Universal.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 3, "ts": 1754735841187373, "dur": 58, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\GameSoft\\Unity\\Editor\\2022.3.38f1c1\\Editor\\Data\\Tools\\ilpp\\Unity.ILPP.Runner\\api-ms-win-core-timezone-l1-1-0.dll"}}, {"pid": 12345, "tid": 3, "ts": 1754735841187691, "dur": 103, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\GameSoft\\Unity\\Editor\\2022.3.38f1c1\\Editor\\Data\\Tools\\ilpp\\Unity.ILPP.Runner\\System.Globalization.dll"}}, {"pid": 12345, "tid": 3, "ts": 1754735841187805, "dur": 68, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\GameSoft\\Unity\\Editor\\2022.3.38f1c1\\Editor\\Data\\Tools\\ilpp\\Unity.ILPP.Runner\\System.IO.FileSystem.AccessControl.dll"}}, {"pid": 12345, "tid": 3, "ts": 1754735841186733, "dur": 2068, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/com.unity.cinemachine.editor.dll (+pdb)"}}, {"pid": 12345, "tid": 3, "ts": 1754735841188851, "dur": 475299, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1754735841664152, "dur": 129, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\Bee\\artifacts\\1900b0aE.dag\\post-processed\\Assembly-CSharp.pdb"}}, {"pid": 12345, "tid": 3, "ts": 1754735841664151, "dur": 131, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Assembly-CSharp.pdb"}}, {"pid": 12345, "tid": 3, "ts": 1754735841664312, "dur": 1798, "ph": "X", "name": "CopyFiles", "args": {"detail": "Library/ScriptAssemblies/Assembly-CSharp.pdb"}}, {"pid": 12345, "tid": 3, "ts": 1754735841666115, "dur": 270624, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1754735840802778, "dur": 21319, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1754735840824154, "dur": 52, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.VideoModule.dll_1DBF74AC9293BF07.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1754735840824207, "dur": 58, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1754735840824292, "dur": 50, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\GameSoft\\Unity\\Editor\\2022.3.38f1c1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.ARModule.dll"}}, {"pid": 12345, "tid": 4, "ts": 1754735840824291, "dur": 52, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ARModule.dll_969BEA2E4A09BAD9.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1754735840824353, "dur": 61, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ARModule.dll_969BEA2E4A09BAD9.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1754735840824460, "dur": 104, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ParticleSystemModule.dll_33F6F62BD0C52DED.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1754735840824573, "dur": 102, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1754735840824755, "dur": 101, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.InputSystem.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 4, "ts": 1754735840825079, "dur": 239, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Mathematics.Editor.rsp2"}}, {"pid": 12345, "tid": 4, "ts": 1754735840825328, "dur": 62, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Shared.Editor.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 4, "ts": 1754735840825613, "dur": 180, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Collections.BurstCompatibilityGen.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 4, "ts": 1754735840825851, "dur": 69, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/3733166401979772732.rsp"}}, {"pid": 12345, "tid": 4, "ts": 1754735840825979, "dur": 59, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/6947401630772442630.rsp"}}, {"pid": 12345, "tid": 4, "ts": 1754735840826038, "dur": 536, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1754735840826574, "dur": 783, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1754735840827357, "dur": 481, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1754735840827839, "dur": 414, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1754735840828253, "dur": 747, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1754735840829000, "dur": 370, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1754735840829374, "dur": 237, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1754735840829611, "dur": 208, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1754735840829820, "dur": 286, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1754735840830107, "dur": 379, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1754735840830486, "dur": 175, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1754735840830661, "dur": 271, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1754735840830932, "dur": 213, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1754735840831145, "dur": 157, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1754735840831302, "dur": 163, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1754735840831465, "dur": 380, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1754735840831846, "dur": 157, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1754735840832003, "dur": 391, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1754735840832395, "dur": 194, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1754735840832589, "dur": 543, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1754735840833674, "dur": 267, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\GameSoft\\Unity\\Editor\\2022.3.38f1c1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.ValueTuple.dll"}}, {"pid": 12345, "tid": 4, "ts": 1754735840833133, "dur": 888, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.VSCode.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 4, "ts": 1754735840834021, "dur": 178, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1754735840834279, "dur": 216, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/CrashKonijn.Goap.Runtime.dll.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1754735840834530, "dur": 153, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/com.crashkonijn.goap.demos.complex.dll.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1754735840834748, "dur": 438, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1754735840835186, "dur": 208, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1754735840835394, "dur": 280, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1754735840835675, "dur": 70, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Core.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1754735840835771, "dur": 817, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Core.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 4, "ts": 1754735840836680, "dur": 321, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Flow.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 4, "ts": 1754735840837096, "dur": 374, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.State.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 4, "ts": 1754735840837516, "dur": 61, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Shared.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1754735840837588, "dur": 217, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Shared.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 4, "ts": 1754735840837832, "dur": 61, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/PPv2URPConverters.dll.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1754735840837906, "dur": 234, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/PPv2URPConverters.dll (+2 others)"}}, {"pid": 12345, "tid": 4, "ts": 1754735840838167, "dur": 342461, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1754735841182119, "dur": 236, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\GameSoft\\Unity\\Editor\\2022.3.38f1c1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Net.WebHeaderCollection.dll"}}, {"pid": 12345, "tid": 4, "ts": 1754735841182442, "dur": 563, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\GameSoft\\Unity\\Editor\\2022.3.38f1c1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\System.Transactions.dll"}}, {"pid": 12345, "tid": 4, "ts": 1754735841183010, "dur": 93, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.collab-proxy@2.8.2\\Lib\\Editor\\log4netPlastic.dll"}}, {"pid": 12345, "tid": 4, "ts": 1754735841180629, "dur": 2493, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.RenderPipelines.Universal.Config.Runtime.dll (+pdb)"}}, {"pid": 12345, "tid": 4, "ts": 1754735841183123, "dur": 76, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1754735841183219, "dur": 91, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\GameSoft\\Unity\\Editor\\2022.3.38f1c1\\Editor\\Data\\Managed\\UnityEngine\\UnityEditor.EditorToolbarModule.dll"}}, {"pid": 12345, "tid": 4, "ts": 1754735841184031, "dur": 53, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\GameSoft\\Unity\\Editor\\2022.3.38f1c1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Memory.dll"}}, {"pid": 12345, "tid": 4, "ts": 1754735841184556, "dur": 62, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\GameSoft\\Unity\\Editor\\2022.3.38f1c1\\Editor\\Data\\Tools\\ilpp\\Unity.ILPP.Runner\\System.Diagnostics.Tools.dll"}}, {"pid": 12345, "tid": 4, "ts": 1754735841185382, "dur": 142, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\GameSoft\\Unity\\Editor\\2022.3.38f1c1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\System.Drawing.dll"}}, {"pid": 12345, "tid": 4, "ts": 1754735841183208, "dur": 2354, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Burst.dll (+pdb)"}}, {"pid": 12345, "tid": 4, "ts": 1754735841185611, "dur": 1522, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.VisualScripting.State.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 4, "ts": 1754735841187372, "dur": 337, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\GameSoft\\Unity\\Editor\\2022.3.38f1c1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Runtime.InteropServices.dll"}}, {"pid": 12345, "tid": 4, "ts": 1754735841187801, "dur": 163, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\GameSoft\\Unity\\Editor\\2022.3.38f1c1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Diagnostics.Tools.dll"}}, {"pid": 12345, "tid": 4, "ts": 1754735841187166, "dur": 2001, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.ShaderGraph.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 4, "ts": 1754735841189201, "dur": 747512, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1754735840802770, "dur": 21311, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1754735840824521, "dur": 96, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1754735840824687, "dur": 266, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/UnityEngine.UI.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 5, "ts": 1754735840824956, "dur": 52, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/UnityEngine.UI.dll.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1754735840825365, "dur": 73, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\GameSoft\\Unity\\Editor\\2022.3.38f1c1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.ComponentModel.TypeConverter.dll"}}, {"pid": 12345, "tid": 5, "ts": 1754735840825806, "dur": 61, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\GameSoft\\Unity\\Editor\\2022.3.38f1c1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Net.WebSockets.Client.dll"}}, {"pid": 12345, "tid": 5, "ts": 1754735840826001, "dur": 144, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\GameSoft\\Unity\\Editor\\2022.3.38f1c1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Runtime.Handles.dll"}}, {"pid": 12345, "tid": 5, "ts": 1754735840826418, "dur": 142, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\GameSoft\\Unity\\Editor\\2022.3.38f1c1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Text.RegularExpressions.dll"}}, {"pid": 12345, "tid": 5, "ts": 1754735840826561, "dur": 54, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\GameSoft\\Unity\\Editor\\2022.3.38f1c1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Threading.dll"}}, {"pid": 12345, "tid": 5, "ts": 1754735840826866, "dur": 390, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.shadergraph@14.0.11\\Editor\\Generation\\Targets\\BuiltIn\\Editor\\ShaderGUI\\MaterialAssemblyReference\\RawRenderQueue.cs"}}, {"pid": 12345, "tid": 5, "ts": 1754735840827381, "dur": 113, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.ugui@1.0.0\\Runtime\\EventSystem\\InputModules\\StandaloneInputModule.cs"}}, {"pid": 12345, "tid": 5, "ts": 1754735840827743, "dur": 127, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.ugui@1.0.0\\Runtime\\UI\\Core\\FontUpdateTracker.cs"}}, {"pid": 12345, "tid": 5, "ts": 1754735840827918, "dur": 60, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.ugui@1.0.0\\Runtime\\UI\\Core\\GraphicRegistry.cs"}}, {"pid": 12345, "tid": 5, "ts": 1754735840828141, "dur": 546, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.ugui@1.0.0\\Runtime\\UI\\Core\\Layout\\ILayoutElement.cs"}}, {"pid": 12345, "tid": 5, "ts": 1754735840828965, "dur": 77, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.ugui@1.0.0\\Runtime\\UI\\Core\\Selectable.cs"}}, {"pid": 12345, "tid": 5, "ts": 1754735840829059, "dur": 56, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.ugui@1.0.0\\Runtime\\UI\\Core\\Slider.cs"}}, {"pid": 12345, "tid": 5, "ts": 1754735840825023, "dur": 4256, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/UnityEngine.UI.dll (+2 others)"}}, {"pid": 12345, "tid": 5, "ts": 1754735840829280, "dur": 60, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1754735840829373, "dur": 172, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1754735840829545, "dur": 187, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1754735840829732, "dur": 209, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1754735840829942, "dur": 203, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1754735840830146, "dur": 310, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1754735840830456, "dur": 164, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1754735840830620, "dur": 165, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1754735840830785, "dur": 238, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1754735840831024, "dur": 176, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1754735840831200, "dur": 204, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1754735840831404, "dur": 383, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1754735840831787, "dur": 183, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1754735840831970, "dur": 189, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1754735840832232, "dur": 51, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1754735840832310, "dur": 255, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1754735840832565, "dur": 381, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1754735840832947, "dur": 68, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.ShaderGraph.Utilities.dll.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1754735840833322, "dur": 361, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\GameSoft\\Unity\\Editor\\2022.3.38f1c1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Net.NameResolution.dll"}}, {"pid": 12345, "tid": 5, "ts": 1754735840833684, "dur": 276, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\GameSoft\\Unity\\Editor\\2022.3.38f1c1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Net.NetworkInformation.dll"}}, {"pid": 12345, "tid": 5, "ts": 1754735840833970, "dur": 187, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\GameSoft\\Unity\\Editor\\2022.3.38f1c1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.ObjectModel.dll"}}, {"pid": 12345, "tid": 5, "ts": 1754735840834274, "dur": 50, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\GameSoft\\Unity\\Editor\\2022.3.38f1c1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Xml.XPath.XDocument.dll"}}, {"pid": 12345, "tid": 5, "ts": 1754735840833052, "dur": 1375, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.ShaderGraph.Utilities.dll (+2 others)"}}, {"pid": 12345, "tid": 5, "ts": 1754735840834428, "dur": 122, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1754735840834558, "dur": 62, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1754735840834625, "dur": 131, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.InputSystem.ForUI.dll.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1754735840834782, "dur": 317, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.InputSystem.ForUI.dll (+2 others)"}}, {"pid": 12345, "tid": 5, "ts": 1754735840835200, "dur": 205, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1754735840835405, "dur": 533, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1754735840835939, "dur": 329, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/com.crashkonijn.goap.demos.complex.dll (+2 others)"}}, {"pid": 12345, "tid": 5, "ts": 1754735840836322, "dur": 344883, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1754735841182343, "dur": 110, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\GameSoft\\Unity\\Editor\\2022.3.38f1c1\\Editor\\Data\\Tools\\ilpp\\Unity.ILPP.Runner\\System.IO.Compression.Brotli.dll"}}, {"pid": 12345, "tid": 5, "ts": 1754735841182458, "dur": 138, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\GameSoft\\Unity\\Editor\\2022.3.38f1c1\\Editor\\Data\\Tools\\ilpp\\Unity.ILPP.Runner\\System.IO.dll"}}, {"pid": 12345, "tid": 5, "ts": 1754735841182768, "dur": 61, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\GameSoft\\Unity\\Editor\\2022.3.38f1c1\\Editor\\Data\\Tools\\ilpp\\Unity.ILPP.Runner\\System.Resources.Writer.dll"}}, {"pid": 12345, "tid": 5, "ts": 1754735841183402, "dur": 98, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\GameSoft\\Unity\\Editor\\2022.3.38f1c1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Numerics.Vectors.dll"}}, {"pid": 12345, "tid": 5, "ts": 1754735841183917, "dur": 285, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\GameSoft\\Unity\\Editor\\2022.3.38f1c1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.AppContext.dll"}}, {"pid": 12345, "tid": 5, "ts": 1754735841184295, "dur": 241, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\GameSoft\\Unity\\Editor\\2022.3.38f1c1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Reflection.Primitives.dll"}}, {"pid": 12345, "tid": 5, "ts": 1754735841184557, "dur": 94, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\GameSoft\\Unity\\Editor\\2022.3.38f1c1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Runtime.Serialization.Xml.dll"}}, {"pid": 12345, "tid": 5, "ts": 1754735841181206, "dur": 3561, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.RenderPipelines.Universal.Shaders.dll (+pdb)"}}, {"pid": 12345, "tid": 5, "ts": 1754735841184995, "dur": 79, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\GameSoft\\Unity\\Editor\\2022.3.38f1c1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Globalization.Calendars.dll"}}, {"pid": 12345, "tid": 5, "ts": 1754735841184815, "dur": 1658, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.VisualScripting.Core.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 5, "ts": 1754735841186474, "dur": 189, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1754735841186663, "dur": 724, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.VisualScripting.Core.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 5, "ts": 1754735841187457, "dur": 710, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1754735841188330, "dur": 73, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1754735841188474, "dur": 54, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1754735841188602, "dur": 56, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1754735841188774, "dur": 71, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1754735841188869, "dur": 731549, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1754735841920420, "dur": 15355, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\Bee\\artifacts\\1900b0aE.dag\\post-processed\\Assembly-CSharp-Editor.dll"}}, {"pid": 12345, "tid": 5, "ts": 1754735841920419, "dur": 15359, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Assembly-CSharp-Editor.dll"}}, {"pid": 12345, "tid": 5, "ts": 1754735841935806, "dur": 856, "ph": "X", "name": "CopyFiles", "args": {"detail": "Library/ScriptAssemblies/Assembly-CSharp-Editor.dll"}}, {"pid": 12345, "tid": 6, "ts": 1754735840802893, "dur": 21243, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1754735840824290, "dur": 88, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\GameSoft\\Unity\\Editor\\2022.3.38f1c1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.AndroidJNIModule.dll"}}, {"pid": 12345, "tid": 6, "ts": 1754735840824289, "dur": 103, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.AndroidJNIModule.dll_DF83CFB61A6929CE.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1754735840824477, "dur": 66, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.StreamingModule.dll_706C4E83917515AE.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1754735840824545, "dur": 91, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\GameSoft\\Unity\\Editor\\2022.3.38f1c1\\Editor\\Data\\Managed\\UnityEditor.Graphs.dll"}}, {"pid": 12345, "tid": 6, "ts": 1754735840824544, "dur": 93, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.Graphs.dll_34C604C212D62B6E.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1754735840824686, "dur": 77, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/UnityEditor.TestRunner.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 6, "ts": 1754735840824817, "dur": 242, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Core.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 6, "ts": 1754735840825158, "dur": 77, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.ShaderGraph.Editor.rsp"}}, {"pid": 12345, "tid": 6, "ts": 1754735840825287, "dur": 79, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Cinemachine.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 6, "ts": 1754735840825497, "dur": 77, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/PPv2URPConverters.rsp2"}}, {"pid": 12345, "tid": 6, "ts": 1754735840826455, "dur": 726, "ph": "X", "name": "WriteText", "args": {"detail": "Library/Bee/artifacts/1900b0aE.dag/Assembly-CSharp.rsp"}}, {"pid": 12345, "tid": 6, "ts": 1754735840827182, "dur": 260, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1754735840827442, "dur": 191, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1754735840827633, "dur": 675, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1754735840828309, "dur": 731, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1754735840829040, "dur": 168, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1754735840829208, "dur": 203, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1754735840829411, "dur": 410, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1754735840829822, "dur": 328, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1754735840830150, "dur": 387, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1754735840830537, "dur": 259, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1754735840830796, "dur": 272, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1754735840831068, "dur": 190, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1754735840831258, "dur": 177, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1754735840831435, "dur": 190, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1754735840831625, "dur": 194, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1754735840831820, "dur": 166, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1754735840831986, "dur": 468, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1754735840832455, "dur": 117, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1754735840832573, "dur": 368, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1754735840832948, "dur": 66, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/CrashKonijn.Agent.Core.dll.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1754735840833015, "dur": 126, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1754735840833481, "dur": 63, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.crashkonijn.goap@601802d6e1\\Runtime\\CrashKonijn.Agent.Core\\Interfaces\\ITarget.cs"}}, {"pid": 12345, "tid": 6, "ts": 1754735840833165, "dur": 384, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/CrashKonijn.Agent.Core.dll (+2 others)"}}, {"pid": 12345, "tid": 6, "ts": 1754735840833550, "dur": 175, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1754735840833733, "dur": 389, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/CrashKonijn.Goap.Core.dll (+2 others)"}}, {"pid": 12345, "tid": 6, "ts": 1754735840834123, "dur": 69, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1754735840834202, "dur": 165, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1754735840834373, "dur": 163, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1754735840834537, "dur": 119, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/com.crashkonijn.goap.demos.turn-based.dll.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1754735840834657, "dur": 76, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1754735840834766, "dur": 419, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1754735840835185, "dur": 210, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1754735840835395, "dur": 546, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1754735840835941, "dur": 1106, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1754735840837114, "dur": 246, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.SettingsProvider.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 6, "ts": 1754735840837425, "dur": 343243, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1754735841180669, "dur": 1398, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/CrashKonijn.Goap.Runtime.dll (+pdb)"}}, {"pid": 12345, "tid": 6, "ts": 1754735841182068, "dur": 98, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1754735841182717, "dur": 61, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\GameSoft\\Unity\\Editor\\2022.3.38f1c1\\Editor\\Data\\Tools\\ilpp\\Unity.ILPP.Runner\\Grpc.Net.Common.dll"}}, {"pid": 12345, "tid": 6, "ts": 1754735841183824, "dur": 58, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.visualscripting@1.9.4\\Editor\\VisualScripting.Core\\Dependencies\\YamlDotNet\\Unity.VisualScripting.YamlDotNet.dll"}}, {"pid": 12345, "tid": 6, "ts": 1754735841182178, "dur": 1715, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.RenderPipelines.Core.Runtime.dll (+pdb)"}}, {"pid": 12345, "tid": 6, "ts": 1754735841183968, "dur": 472, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\GameSoft\\Unity\\Editor\\2022.3.38f1c1\\Editor\\Data\\Managed\\UnityEngine\\UnityEditor.UnityConnectModule.dll"}}, {"pid": 12345, "tid": 6, "ts": 1754735841184524, "dur": 63, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\GameSoft\\Unity\\Editor\\2022.3.38f1c1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.GameCenterModule.dll"}}, {"pid": 12345, "tid": 6, "ts": 1754735841185921, "dur": 128, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\GameSoft\\Unity\\Editor\\2022.3.38f1c1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Net.Sockets.dll"}}, {"pid": 12345, "tid": 6, "ts": 1754735841183943, "dur": 2226, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Timeline.dll (+pdb)"}}, {"pid": 12345, "tid": 6, "ts": 1754735841186169, "dur": 229, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1754735841187372, "dur": 61, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\GameSoft\\Unity\\Editor\\2022.3.38f1c1\\Editor\\Data\\Managed\\UnityEngine\\UnityEditor.SceneTemplateModule.dll"}}, {"pid": 12345, "tid": 6, "ts": 1754735841186410, "dur": 1605, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Collections.DocCodeSamples.dll (+pdb)"}}, {"pid": 12345, "tid": 6, "ts": 1754735841188015, "dur": 103, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1754735841188484, "dur": 314, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1754735841188801, "dur": 394, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1754735841189208, "dur": 747502, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1754735840802937, "dur": 21235, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1754735840824517, "dur": 109, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\GameSoft\\Unity\\Editor\\2022.3.38f1c1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.UnityWebRequestAssetBundleModule.dll"}}, {"pid": 12345, "tid": 7, "ts": 1754735840824516, "dur": 111, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityWebRequestAssetBundleModule.dll_20062661B6C0E97A.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1754735840824690, "dur": 101, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.VisualScripting.IonicZip.dll_840FAE5C8A141D5B.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1754735840824831, "dur": 88, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Flow.rsp2"}}, {"pid": 12345, "tid": 7, "ts": 1754735840825014, "dur": 52, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/CrashKonijn.Goap.Core.rsp"}}, {"pid": 12345, "tid": 7, "ts": 1754735840825294, "dur": 239, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Core.ShaderLibrary.rsp2"}}, {"pid": 12345, "tid": 7, "ts": 1754735840825564, "dur": 198, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/URPWizard.rsp"}}, {"pid": 12345, "tid": 7, "ts": 1754735840825826, "dur": 57, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/4691676790321305727.rsp"}}, {"pid": 12345, "tid": 7, "ts": 1754735840825941, "dur": 78, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/15183149355271759364.rsp"}}, {"pid": 12345, "tid": 7, "ts": 1754735840826020, "dur": 353, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1754735840826373, "dur": 177, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1754735840826550, "dur": 192, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1754735840826742, "dur": 202, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1754735840826945, "dur": 184, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1754735840827130, "dur": 436, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1754735840827566, "dur": 413, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1754735840827980, "dur": 325, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1754735840828305, "dur": 530, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1754735840828835, "dur": 229, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1754735840829064, "dur": 172, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1754735840829236, "dur": 189, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1754735840829426, "dur": 171, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1754735840829597, "dur": 188, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1754735840829803, "dur": 233, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1754735840830036, "dur": 190, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1754735840830226, "dur": 248, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1754735840830474, "dur": 181, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1754735840830655, "dur": 225, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1754735840830881, "dur": 199, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1754735840831080, "dur": 194, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1754735840831274, "dur": 196, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1754735840831470, "dur": 440, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1754735840831911, "dur": 227, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1754735840832139, "dur": 193, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1754735840832333, "dur": 248, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1754735840832581, "dur": 382, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1754735840832965, "dur": 80, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.PlasticSCM.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1754735840833046, "dur": 95, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1754735840833197, "dur": 90, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\GameSoft\\Unity\\Editor\\2022.3.38f1c1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.LocalizationModule.dll"}}, {"pid": 12345, "tid": 7, "ts": 1754735840833294, "dur": 194, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\GameSoft\\Unity\\Editor\\2022.3.38f1c1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.RuntimeInitializeOnLoadManagerInitializerModule.dll"}}, {"pid": 12345, "tid": 7, "ts": 1754735840833674, "dur": 287, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.collab-proxy@2.8.2\\Editor\\AssetMenu\\AssetCopyPathOperation.cs"}}, {"pid": 12345, "tid": 7, "ts": 1754735840833970, "dur": 91, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.collab-proxy@2.8.2\\Editor\\AssetOverlays\\AssetStatus.cs"}}, {"pid": 12345, "tid": 7, "ts": 1754735840834261, "dur": 69, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.collab-proxy@2.8.2\\Editor\\UI\\BoolSetting.cs"}}, {"pid": 12345, "tid": 7, "ts": 1754735840834392, "dur": 54, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.collab-proxy@2.8.2\\Editor\\UI\\MeasureMaxWidth.cs"}}, {"pid": 12345, "tid": 7, "ts": 1754735840834484, "dur": 124, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.collab-proxy@2.8.2\\Editor\\UI\\Tree\\TreeViewSessionState.cs"}}, {"pid": 12345, "tid": 7, "ts": 1754735840833147, "dur": 1801, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.PlasticSCM.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 7, "ts": 1754735840835056, "dur": 61, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.PlasticSCM.Editor.ref.dll_257AEB342BE77856.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1754735840835118, "dur": 73, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.CollabProxy.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1754735840835220, "dur": 204, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.CollabProxy.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 7, "ts": 1754735840835461, "dur": 474, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1754735840835936, "dur": 271, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/com.crashkonijn.docs.getting_started.dll (+2 others)"}}, {"pid": 12345, "tid": 7, "ts": 1754735840836252, "dur": 345440, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1754735841181693, "dur": 1819, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.RenderPipelines.Universal.Runtime.dll (+pdb)"}}, {"pid": 12345, "tid": 7, "ts": 1754735841183513, "dur": 259, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1754735841183870, "dur": 106, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\GameSoft\\Unity\\Editor\\2022.3.38f1c1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.PhysicsModule.dll"}}, {"pid": 12345, "tid": 7, "ts": 1754735841184623, "dur": 58, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\GameSoft\\Unity\\Editor\\2022.3.38f1c1\\Editor\\Data\\Tools\\ilpp\\Unity.ILPP.Runner\\Microsoft.AspNetCore.Razor.dll"}}, {"pid": 12345, "tid": 7, "ts": 1754735841183783, "dur": 1768, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.TextMeshPro.dll (+pdb)"}}, {"pid": 12345, "tid": 7, "ts": 1754735841185551, "dur": 63, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1754735841186014, "dur": 396, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\GameSoft\\Unity\\Editor\\2022.3.38f1c1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Reflection.Emit.Lightweight.dll"}}, {"pid": 12345, "tid": 7, "ts": 1754735841185621, "dur": 1751, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.ShaderGraph.Utilities.dll (+pdb)"}}, {"pid": 12345, "tid": 7, "ts": 1754735841187375, "dur": 373, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1754735841187791, "dur": 274, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1754735841188252, "dur": 273, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1754735841188598, "dur": 59, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\Bee\\artifacts\\1900b0aE.dag\\post-processed\\Unity.InputSystem.pdb"}}, {"pid": 12345, "tid": 7, "ts": 1754735841188598, "dur": 59, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.InputSystem.pdb"}}, {"pid": 12345, "tid": 7, "ts": 1754735841188667, "dur": 108, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1754735841188793, "dur": 255, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1754735841189060, "dur": 747728, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1754735840802928, "dur": 21227, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1754735840824168, "dur": 55, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.VirtualTexturingModule.dll_38C1DD3B59418845.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1754735840824515, "dur": 53, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.TextCoreTextEngineModule.dll_EF7BA45317B31323.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1754735840824570, "dur": 74, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.collab-proxy@2.8.2\\Lib\\Editor\\Unity.Plastic.Newtonsoft.Json.dll"}}, {"pid": 12345, "tid": 8, "ts": 1754735840824569, "dur": 77, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.Plastic.Newtonsoft.Json.dll_CF17D4CA2A31CD8C.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1754735840824692, "dur": 113, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/UnityEditor.TestRunner.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 8, "ts": 1754735840824899, "dur": 52, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Core.Editor.rsp"}}, {"pid": 12345, "tid": 8, "ts": 1754735840824978, "dur": 141, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Core.Editor.rsp"}}, {"pid": 12345, "tid": 8, "ts": 1754735840825447, "dur": 149, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VSCode.Editor.rsp"}}, {"pid": 12345, "tid": 8, "ts": 1754735840825612, "dur": 165, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Tests.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 8, "ts": 1754735840826010, "dur": 204, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1754735840826218, "dur": 233, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1754735840826451, "dur": 208, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1754735840826659, "dur": 180, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1754735840826839, "dur": 194, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1754735840827033, "dur": 155, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1754735840827188, "dur": 640, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1754735840827829, "dur": 208, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1754735840828038, "dur": 675, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1754735840828713, "dur": 249, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1754735840828963, "dur": 308, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1754735840829271, "dur": 208, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1754735840829479, "dur": 180, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1754735840829659, "dur": 340, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1754735840829999, "dur": 431, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1754735840830430, "dur": 188, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1754735840830618, "dur": 191, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1754735840830810, "dur": 185, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1754735840830995, "dur": 175, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1754735840831170, "dur": 184, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1754735840831354, "dur": 234, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1754735840831588, "dur": 255, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1754735840831843, "dur": 170, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1754735840832032, "dur": 56, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1754735840832088, "dur": 256, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1754735840832344, "dur": 223, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1754735840832568, "dur": 557, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1754735840833129, "dur": 159, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Universal.Shaders.dll.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1754735840833289, "dur": 201, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1754735840833686, "dur": 277, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\GameSoft\\Unity\\Editor\\2022.3.38f1c1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.ARModule.dll"}}, {"pid": 12345, "tid": 8, "ts": 1754735840834049, "dur": 173, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\GameSoft\\Unity\\Editor\\2022.3.38f1c1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.ParticleSystemModule.dll"}}, {"pid": 12345, "tid": 8, "ts": 1754735840834361, "dur": 90, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\GameSoft\\Unity\\Editor\\2022.3.38f1c1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Diagnostics.Process.dll"}}, {"pid": 12345, "tid": 8, "ts": 1754735840834556, "dur": 87, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\GameSoft\\Unity\\Editor\\2022.3.38f1c1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Security.Cryptography.Primitives.dll"}}, {"pid": 12345, "tid": 8, "ts": 1754735840833519, "dur": 1222, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Universal.Shaders.dll (+2 others)"}}, {"pid": 12345, "tid": 8, "ts": 1754735840834773, "dur": 81, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\Bee\\artifacts\\1900b0aE.dag\\Unity.RenderPipelines.Universal.Shaders.ref.dll"}}, {"pid": 12345, "tid": 8, "ts": 1754735840834772, "dur": 84, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.RenderPipelines.Universal.Shaders.ref.dll_9468B054363B0720.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1754735840834857, "dur": 163, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1754735840835021, "dur": 82, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.RenderPipelines.Universal.Shaders.ref.dll_9468B054363B0720.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1754735840835105, "dur": 84, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1754735840835189, "dur": 210, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1754735840835399, "dur": 548, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1754735840835947, "dur": 1887, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1754735840837865, "dur": 56, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Assembly-CSharp.dll.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1754735840837932, "dur": 342690, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1754735841182119, "dur": 56, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\GameSoft\\Unity\\Editor\\2022.3.38f1c1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.IO.FileSystem.dll"}}, {"pid": 12345, "tid": 8, "ts": 1754735841182343, "dur": 109, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\GameSoft\\Unity\\Editor\\2022.3.38f1c1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Reflection.Emit.Lightweight.dll"}}, {"pid": 12345, "tid": 8, "ts": 1754735841180625, "dur": 1918, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/CrashKonijn.Goap.Resolver.dll (+pdb)"}}, {"pid": 12345, "tid": 8, "ts": 1754735841182595, "dur": 1525, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Collections.dll (+pdb)"}}, {"pid": 12345, "tid": 8, "ts": 1754735841184155, "dur": 1649, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.VisualScripting.SettingsProvider.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 8, "ts": 1754735841185805, "dur": 438, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1754735841186386, "dur": 997, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\GameSoft\\Unity\\Editor\\2022.3.38f1c1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Collections.Specialized.dll"}}, {"pid": 12345, "tid": 8, "ts": 1754735841187691, "dur": 73, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\GameSoft\\Unity\\Editor\\2022.3.38f1c1\\Editor\\Data\\Tools\\ilpp\\Unity.ILPP.Runner\\api-ms-win-core-console-l1-1-0.dll"}}, {"pid": 12345, "tid": 8, "ts": 1754735841188192, "dur": 152, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\GameSoft\\Unity\\Editor\\2022.3.38f1c1\\Editor\\Data\\Tools\\ilpp\\Unity.ILPP.Runner\\System.Globalization.Calendars.dll"}}, {"pid": 12345, "tid": 8, "ts": 1754735841188381, "dur": 60, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\GameSoft\\Unity\\Editor\\2022.3.38f1c1\\Editor\\Data\\Tools\\ilpp\\Unity.ILPP.Runner\\System.Linq.Parallel.dll"}}, {"pid": 12345, "tid": 8, "ts": 1754735841186249, "dur": 2768, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/URPWizard.dll (+pdb)"}}, {"pid": 12345, "tid": 8, "ts": 1754735841189051, "dur": 747660, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1754735840802978, "dur": 21207, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1754735840824253, "dur": 69, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1754735840824517, "dur": 98, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1754735840824881, "dur": 118, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/CrashKonijn.Agent.Core.rsp"}}, {"pid": 12345, "tid": 9, "ts": 1754735840825010, "dur": 72, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Flow.Editor.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 9, "ts": 1754735840825153, "dur": 435, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Mathematics.Editor.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 9, "ts": 1754735840825619, "dur": 200, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Collections.DocCodeSamples.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 9, "ts": 1754735840825851, "dur": 75, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/9562792982806824100.rsp"}}, {"pid": 12345, "tid": 9, "ts": 1754735840826015, "dur": 685, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1754735840826704, "dur": 301, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1754735840827006, "dur": 271, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1754735840827277, "dur": 687, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1754735840827965, "dur": 202, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1754735840828168, "dur": 582, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1754735840828750, "dur": 264, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1754735840829014, "dur": 469, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1754735840829483, "dur": 178, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1754735840829662, "dur": 281, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1754735840829943, "dur": 228, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1754735840830171, "dur": 226, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1754735840830397, "dur": 867, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1754735840831265, "dur": 161, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1754735840831426, "dur": 185, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1754735840831611, "dur": 236, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1754735840831847, "dur": 189, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1754735840832088, "dur": 51, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1754735840832139, "dur": 227, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1754735840832366, "dur": 228, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1754735840832594, "dur": 367, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1754735840832970, "dur": 72, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.TextMeshPro.dll.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1754735840833042, "dur": 451, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1754735840833494, "dur": 213, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.TextMeshPro.dll.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1754735840833971, "dur": 84, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\GameSoft\\Unity\\Editor\\2022.3.38f1c1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Security.Claims.dll"}}, {"pid": 12345, "tid": 9, "ts": 1754735840833708, "dur": 494, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.TextMeshPro.dll (+2 others)"}}, {"pid": 12345, "tid": 9, "ts": 1754735840834203, "dur": 101, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1754735840834305, "dur": 72, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.TextMeshPro.dll (+2 others)"}}, {"pid": 12345, "tid": 9, "ts": 1754735840834383, "dur": 108, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1754735840834499, "dur": 130, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.TextMeshPro.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1754735840834644, "dur": 457, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.TextMeshPro.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 9, "ts": 1754735840835197, "dur": 194, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1754735840835392, "dur": 61, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Universal.Config.Runtime.dll.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1754735840835472, "dur": 194, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Universal.Config.Runtime.dll (+2 others)"}}, {"pid": 12345, "tid": 9, "ts": 1754735840835722, "dur": 51, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Universal.Runtime.dll.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1754735840835803, "dur": 418, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Universal.Runtime.dll (+2 others)"}}, {"pid": 12345, "tid": 9, "ts": 1754735840836278, "dur": 57, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Cinemachine.dll.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1754735840836345, "dur": 230, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Cinemachine.dll (+2 others)"}}, {"pid": 12345, "tid": 9, "ts": 1754735840836604, "dur": 344020, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1754735841180626, "dur": 1464, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.RenderPipelines.Core.ShaderLibrary.dll (+pdb)"}}, {"pid": 12345, "tid": 9, "ts": 1754735841182091, "dur": 74, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1754735841182173, "dur": 1562, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.VisualScripting.Flow.dll (+pdb)"}}, {"pid": 12345, "tid": 9, "ts": 1754735841183735, "dur": 260, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1754735841184268, "dur": 60, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\GameSoft\\Unity\\Editor\\2022.3.38f1c1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Runtime.Handles.dll"}}, {"pid": 12345, "tid": 9, "ts": 1754735841184625, "dur": 78, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\GameSoft\\Unity\\Editor\\2022.3.38f1c1\\Editor\\Data\\Tools\\ilpp\\Unity.ILPP.Runner\\api-ms-win-core-processenvironment-l1-1-0.dll"}}, {"pid": 12345, "tid": 9, "ts": 1754735841184817, "dur": 95, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\GameSoft\\Unity\\Editor\\2022.3.38f1c1\\Editor\\Data\\Tools\\ilpp\\Unity.ILPP.Runner\\Microsoft.AspNetCore.Mvc.ApiExplorer.dll"}}, {"pid": 12345, "tid": 9, "ts": 1754735841184949, "dur": 154, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\GameSoft\\Unity\\Editor\\2022.3.38f1c1\\Editor\\Data\\Tools\\ilpp\\Unity.ILPP.Runner\\Microsoft.AspNetCore.Server.Kestrel.Transport.Sockets.dll"}}, {"pid": 12345, "tid": 9, "ts": 1754735841184015, "dur": 1946, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/CrashKonijn.Agent.Runtime.dll (+pdb)"}}, {"pid": 12345, "tid": 9, "ts": 1754735841186386, "dur": 1010, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\GameSoft\\Unity\\Editor\\2022.3.38f1c1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Reflection.dll"}}, {"pid": 12345, "tid": 9, "ts": 1754735841185999, "dur": 2694, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/PPv2URPConverters.dll (+pdb)"}}, {"pid": 12345, "tid": 9, "ts": 1754735841188787, "dur": 229, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1754735841189029, "dur": 747742, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1754735840803006, "dur": 21187, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1754735840824279, "dur": 89, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\GameSoft\\Unity\\Editor\\2022.3.38f1c1\\Editor\\Data\\Managed\\UnityEngine\\UnityEditor.UIElementsModule.dll"}}, {"pid": 12345, "tid": 10, "ts": 1754735840824278, "dur": 91, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.UIElementsModule.dll_613AE6E2D149BAF8.mvfrm"}}, {"pid": 12345, "tid": 10, "ts": 1754735840824406, "dur": 81, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.UIElementsModule.dll_613AE6E2D149BAF8.mvfrm"}}, {"pid": 12345, "tid": 10, "ts": 1754735840824531, "dur": 98, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\GameSoft\\Unity\\Editor\\2022.3.38f1c1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.UnityWebRequestAudioModule.dll"}}, {"pid": 12345, "tid": 10, "ts": 1754735840824529, "dur": 102, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityWebRequestAudioModule.dll_180837F1985FCEBE.mvfrm"}}, {"pid": 12345, "tid": 10, "ts": 1754735840825082, "dur": 131, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Burst.Editor.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 10, "ts": 1754735840825462, "dur": 61, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Universal.Shaders.rsp"}}, {"pid": 12345, "tid": 10, "ts": 1754735840825599, "dur": 59, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/com.crashkonijn.goap.demos.turn-based.rsp"}}, {"pid": 12345, "tid": 10, "ts": 1754735840826015, "dur": 173, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1754735840826193, "dur": 185, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1754735840826378, "dur": 175, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1754735840826553, "dur": 398, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1754735840826951, "dur": 189, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1754735840827140, "dur": 161, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1754735840827301, "dur": 122, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1754735840827424, "dur": 170, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1754735840827670, "dur": 510, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.timeline@1.7.6\\Editor\\Manipulators\\Utils\\EditModeMixUtils.cs"}}, {"pid": 12345, "tid": 10, "ts": 1754735840827594, "dur": 1204, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1754735840828799, "dur": 219, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1754735840829018, "dur": 175, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1754735840829193, "dur": 213, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1754735840829406, "dur": 181, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1754735840829588, "dur": 217, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1754735840829805, "dur": 857, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1754735840830723, "dur": 504, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.visualscripting@1.9.4\\Runtime\\VisualScripting.Core\\Reflection\\Optimization\\StaticPropertyAccessor.cs"}}, {"pid": 12345, "tid": 10, "ts": 1754735840830663, "dur": 797, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1754735840831460, "dur": 217, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1754735840831678, "dur": 466, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1754735840832145, "dur": 136, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1754735840832319, "dur": 258, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1754735840832577, "dur": 366, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1754735840832945, "dur": 70, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/CrashKonijn.Goap.Core.dll.mvfrm"}}, {"pid": 12345, "tid": 10, "ts": 1754735840833034, "dur": 182, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.VisualStudio.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 10, "ts": 1754735840833481, "dur": 214, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\GameSoft\\Unity\\Editor\\2022.3.38f1c1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Security.SecureString.dll"}}, {"pid": 12345, "tid": 10, "ts": 1754735840833235, "dur": 587, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.VisualStudio.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 10, "ts": 1754735840833822, "dur": 85, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1754735840833936, "dur": 269, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Tests.dll.mvfrm"}}, {"pid": 12345, "tid": 10, "ts": 1754735840834273, "dur": 70, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\GameSoft\\Unity\\Editor\\2022.3.38f1c1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.IMGUIModule.dll"}}, {"pid": 12345, "tid": 10, "ts": 1754735840834222, "dur": 535, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Tests.dll (+2 others)"}}, {"pid": 12345, "tid": 10, "ts": 1754735840834758, "dur": 85, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1754735840834862, "dur": 321, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1754735840835184, "dur": 71, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.Collections.BurstCompatibilityGen.dll.mvfrm"}}, {"pid": 12345, "tid": 10, "ts": 1754735840835279, "dur": 279, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Collections.BurstCompatibilityGen.dll (+2 others)"}}, {"pid": 12345, "tid": 10, "ts": 1754735840835597, "dur": 342, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1754735840835940, "dur": 343, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1754735840836284, "dur": 344342, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1754735841180627, "dur": 1476, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Tests.dll (+pdb)"}}, {"pid": 12345, "tid": 10, "ts": 1754735841182104, "dur": 282, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1754735841182717, "dur": 76, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\GameSoft\\Unity\\Editor\\2022.3.38f1c1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Collections.NonGeneric.dll"}}, {"pid": 12345, "tid": 10, "ts": 1754735841183968, "dur": 95, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\GameSoft\\Unity\\Editor\\2022.3.38f1c1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Collections.dll"}}, {"pid": 12345, "tid": 10, "ts": 1754735841182396, "dur": 1861, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/com.crashkonijn.docs.getting_started.dll (+pdb)"}}, {"pid": 12345, "tid": 10, "ts": 1754735841184257, "dur": 1226, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1754735841185490, "dur": 1691, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Collections.BurstCompatibilityGen.dll (+pdb)"}}, {"pid": 12345, "tid": 10, "ts": 1754735841188494, "dur": 76, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\GameSoft\\Unity\\Editor\\2022.3.38f1c1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.ImageConversionModule.dll"}}, {"pid": 12345, "tid": 10, "ts": 1754735841187238, "dur": 1807, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Mathematics.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 10, "ts": 1754735841189096, "dur": 747665, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1754735840803039, "dur": 21169, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1754735840824225, "dur": 50, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\GameSoft\\Unity\\Editor\\2022.3.38f1c1\\Editor\\Data\\Managed\\UnityEngine\\UnityEditor.DiagnosticsModule.dll"}}, {"pid": 12345, "tid": 11, "ts": 1754735840824215, "dur": 62, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.DiagnosticsModule.dll_3335CD69E05AF253.mvfrm"}}, {"pid": 12345, "tid": 11, "ts": 1754735840824418, "dur": 93, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.IMGUIModule.dll_31158FA896333743.mvfrm"}}, {"pid": 12345, "tid": 11, "ts": 1754735840824525, "dur": 90, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\GameSoft\\Unity\\Editor\\2022.3.38f1c1\\Editor\\Data\\Tools\\ScriptUpdater\\ApiUpdater.MovedFromExtractor.exe"}}, {"pid": 12345, "tid": 11, "ts": 1754735840824513, "dur": 103, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityTestProtocolModule.dll_1E012B284EB8934B.mvfrm"}}, {"pid": 12345, "tid": 11, "ts": 1754735840824638, "dur": 51, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityTestProtocolModule.dll_1E012B284EB8934B.mvfrm"}}, {"pid": 12345, "tid": 11, "ts": 1754735840824738, "dur": 106, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/UnityEditor.UI.rsp"}}, {"pid": 12345, "tid": 11, "ts": 1754735840824872, "dur": 111, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipeline.Universal.ShaderLibrary.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 11, "ts": 1754735840825266, "dur": 115, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/CrashKonijn.Goap.Runtime.rsp"}}, {"pid": 12345, "tid": 11, "ts": 1754735840825574, "dur": 147, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/com.crashkonijn.goap.demos.complex.rsp"}}, {"pid": 12345, "tid": 11, "ts": 1754735840826009, "dur": 164, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1754735840826178, "dur": 336, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1754735840826514, "dur": 362, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1754735840826877, "dur": 331, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1754735840827208, "dur": 198, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1754735840827407, "dur": 169, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1754735840827673, "dur": 565, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.timeline@1.7.6\\Editor\\Signals\\TreeView\\SignalReceiverItem.cs"}}, {"pid": 12345, "tid": 11, "ts": 1754735840827576, "dur": 1251, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1754735840828828, "dur": 557, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1754735840829385, "dur": 287, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1754735840829672, "dur": 232, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1754735840829904, "dur": 187, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1754735840830091, "dur": 328, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1754735840830419, "dur": 330, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1754735840830749, "dur": 366, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1754735840831115, "dur": 309, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1754735840831424, "dur": 310, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1754735840831734, "dur": 212, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1754735840831946, "dur": 389, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1754735840832335, "dur": 225, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1754735840832592, "dur": 534, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1754735840833127, "dur": 204, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.ShaderGraph.ShaderGraphLibrary.dll.mvfrm"}}, {"pid": 12345, "tid": 11, "ts": 1754735840833481, "dur": 214, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\GameSoft\\Unity\\Editor\\2022.3.38f1c1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Globalization.Calendars.dll"}}, {"pid": 12345, "tid": 11, "ts": 1754735840833697, "dur": 53, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\GameSoft\\Unity\\Editor\\2022.3.38f1c1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Globalization.Extensions.dll"}}, {"pid": 12345, "tid": 11, "ts": 1754735840833347, "dur": 526, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.ShaderGraph.ShaderGraphLibrary.dll (+2 others)"}}, {"pid": 12345, "tid": 11, "ts": 1754735840833943, "dur": 247, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1754735840834270, "dur": 484, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.Mathematics.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 11, "ts": 1754735840834775, "dur": 333, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Mathematics.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 11, "ts": 1754735840835199, "dur": 201, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1754735840835400, "dur": 556, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1754735840835956, "dur": 344688, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1754735841181165, "dur": 87, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\GameSoft\\Unity\\Editor\\2022.3.38f1c1\\Editor\\Data\\Tools\\ilpp\\Unity.ILPP.Runner\\api-ms-win-core-rtlsupport-l1-1-0.dll"}}, {"pid": 12345, "tid": 11, "ts": 1754735841181255, "dur": 59, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\GameSoft\\Unity\\Editor\\2022.3.38f1c1\\Editor\\Data\\Tools\\ilpp\\Unity.ILPP.Runner\\api-ms-win-core-synch-l1-1-0.dll"}}, {"pid": 12345, "tid": 11, "ts": 1754735841181417, "dur": 53, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\GameSoft\\Unity\\Editor\\2022.3.38f1c1\\Editor\\Data\\Tools\\ilpp\\Unity.ILPP.Runner\\api-ms-win-crt-environment-l1-1-0.dll"}}, {"pid": 12345, "tid": 11, "ts": 1754735841182173, "dur": 189, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\GameSoft\\Unity\\Editor\\2022.3.38f1c1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.IMGUIModule.dll"}}, {"pid": 12345, "tid": 11, "ts": 1754735841182442, "dur": 208, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\GameSoft\\Unity\\Editor\\2022.3.38f1c1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.ComponentModel.Primitives.dll"}}, {"pid": 12345, "tid": 11, "ts": 1754735841180646, "dur": 2315, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.VisualScripting.State.dll (+pdb)"}}, {"pid": 12345, "tid": 11, "ts": 1754735841182962, "dur": 506, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1754735841183480, "dur": 1834, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Mathematics.dll (+pdb)"}}, {"pid": 12345, "tid": 11, "ts": 1754735841185315, "dur": 156, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1754735841185921, "dur": 129, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\GameSoft\\Unity\\Editor\\2022.3.38f1c1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Xml.XmlSerializer.dll"}}, {"pid": 12345, "tid": 11, "ts": 1754735841185481, "dur": 1532, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.TextMeshPro.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 11, "ts": 1754735841187580, "dur": 67, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\GameSoft\\Unity\\Editor\\2022.3.38f1c1\\Editor\\Data\\Tools\\ilpp\\Unity.ILPP.Runner\\Grpc.Net.ClientFactory.dll"}}, {"pid": 12345, "tid": 11, "ts": 1754735841188193, "dur": 153, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\GameSoft\\Unity\\Editor\\2022.3.38f1c1\\Editor\\Data\\Managed\\UnityEngine\\UnityEditor.PresetsUIModule.dll"}}, {"pid": 12345, "tid": 11, "ts": 1754735841187064, "dur": 1834, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.VisualScripting.Flow.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 11, "ts": 1754735841188936, "dur": 747977, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1754735840803070, "dur": 21148, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1754735840824534, "dur": 104, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\GameSoft\\Unity\\Editor\\2022.3.38f1c1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.UnityWebRequestModule.dll"}}, {"pid": 12345, "tid": 12, "ts": 1754735840824532, "dur": 107, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityWebRequestModule.dll_55F435D8463A793C.mvfrm"}}, {"pid": 12345, "tid": 12, "ts": 1754735840824659, "dur": 246, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityWebRequestModule.dll_55F435D8463A793C.mvfrm"}}, {"pid": 12345, "tid": 12, "ts": 1754735840824911, "dur": 56, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/UnityEngine.TestRunner.dll.mvfrm"}}, {"pid": 12345, "tid": 12, "ts": 1754735840826002, "dur": 152, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\GameSoft\\Unity\\Editor\\2022.3.38f1c1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Runtime.Handles.dll"}}, {"pid": 12345, "tid": 12, "ts": 1754735840826186, "dur": 102, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\GameSoft\\Unity\\Editor\\2022.3.38f1c1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Runtime.Serialization.Formatters.dll"}}, {"pid": 12345, "tid": 12, "ts": 1754735840826359, "dur": 77, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\GameSoft\\Unity\\Editor\\2022.3.38f1c1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Security.SecureString.dll"}}, {"pid": 12345, "tid": 12, "ts": 1754735840826483, "dur": 186, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\GameSoft\\Unity\\Editor\\2022.3.38f1c1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Threading.dll"}}, {"pid": 12345, "tid": 12, "ts": 1754735840826747, "dur": 138, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\GameSoft\\Unity\\Editor\\2022.3.38f1c1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Xml.XDocument.dll"}}, {"pid": 12345, "tid": 12, "ts": 1754735840826962, "dur": 65, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.test-framework@1.1.33\\UnityEngine.TestRunner\\Assertions\\ConstraintsExtensions.cs"}}, {"pid": 12345, "tid": 12, "ts": 1754735840827073, "dur": 52, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.test-framework@1.1.33\\UnityEngine.TestRunner\\Assertions\\LogScope\\LogEvent.cs"}}, {"pid": 12345, "tid": 12, "ts": 1754735840827719, "dur": 124, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.test-framework@1.1.33\\UnityEngine.TestRunner\\NUnitExtensions\\Runner\\TestCommandBuilder.cs"}}, {"pid": 12345, "tid": 12, "ts": 1754735840827844, "dur": 272, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.test-framework@1.1.33\\UnityEngine.TestRunner\\NUnitExtensions\\Runner\\UnityLogCheckDelegatingCommand.cs"}}, {"pid": 12345, "tid": 12, "ts": 1754735840828146, "dur": 556, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.test-framework@1.1.33\\UnityEngine.TestRunner\\NUnitExtensions\\Runner\\UnityWorkItem.cs"}}, {"pid": 12345, "tid": 12, "ts": 1754735840828834, "dur": 414, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.test-framework@1.1.33\\UnityEngine.TestRunner\\TestRunner\\PlaymodeTestsController.cs"}}, {"pid": 12345, "tid": 12, "ts": 1754735840824990, "dur": 4742, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/UnityEngine.TestRunner.dll (+2 others)"}}, {"pid": 12345, "tid": 12, "ts": 1754735840829791, "dur": 107, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/UnityEditor.TestRunner.dll.mvfrm"}}, {"pid": 12345, "tid": 12, "ts": 1754735840830174, "dur": 52, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\GameSoft\\Unity\\Editor\\2022.3.38f1c1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.ComponentModel.Primitives.dll"}}, {"pid": 12345, "tid": 12, "ts": 1754735840830633, "dur": 87, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\GameSoft\\Unity\\Editor\\2022.3.38f1c1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Memory.dll"}}, {"pid": 12345, "tid": 12, "ts": 1754735840830792, "dur": 104, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\GameSoft\\Unity\\Editor\\2022.3.38f1c1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Net.Ping.dll"}}, {"pid": 12345, "tid": 12, "ts": 1754735840831760, "dur": 560, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.test-framework@1.1.33\\UnityEditor.TestRunner\\Api\\Analytics\\TestTreeData.cs"}}, {"pid": 12345, "tid": 12, "ts": 1754735840829911, "dur": 2600, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/UnityEditor.TestRunner.dll (+2 others)"}}, {"pid": 12345, "tid": 12, "ts": 1754735840832634, "dur": 219, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/UnityEditor.UI.dll (+2 others)"}}, {"pid": 12345, "tid": 12, "ts": 1754735840832938, "dur": 58, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.Burst.dll.mvfrm"}}, {"pid": 12345, "tid": 12, "ts": 1754735840833119, "dur": 85, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\GameSoft\\Unity\\Editor\\2022.3.38f1c1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netfx\\System.Xml.Serialization.dll"}}, {"pid": 12345, "tid": 12, "ts": 1754735840833012, "dur": 435, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Burst.dll (+2 others)"}}, {"pid": 12345, "tid": 12, "ts": 1754735840833493, "dur": 201, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Burst.dll (+2 others)"}}, {"pid": 12345, "tid": 12, "ts": 1754735840833726, "dur": 143, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.Burst.CodeGen.dll.mvfrm"}}, {"pid": 12345, "tid": 12, "ts": 1754735840834361, "dur": 103, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.burst@1.8.16\\Unity.Burst.CodeGen\\BurstILPostProcessor.cs"}}, {"pid": 12345, "tid": 12, "ts": 1754735840833911, "dur": 566, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Burst.CodeGen.dll (+2 others)"}}, {"pid": 12345, "tid": 12, "ts": 1754735840834478, "dur": 111, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1754735840834720, "dur": 69, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\GameSoft\\Unity\\Editor\\2022.3.38f1c1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.GameCenterModule.dll"}}, {"pid": 12345, "tid": 12, "ts": 1754735840834599, "dur": 617, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPP-Configuration Library/ilpp-configuration.nevergeneratedoutput"}}, {"pid": 12345, "tid": 12, "ts": 1754735840835246, "dur": 99, "ph": "X", "name": "StoreTimestampsOfNonGeneratedInputFiles", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1754735840835356, "dur": 342950, "ph": "X", "name": "ILPP-Configuration", "args": {"detail": "Library/ilpp-configuration.nevergeneratedoutput"}}, {"pid": 12345, "tid": 12, "ts": 1754735841180624, "dur": 1449, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/com.crashkonijn.goap.demos.complex.dll (+pdb)"}}, {"pid": 12345, "tid": 12, "ts": 1754735841182074, "dur": 80, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1754735841183219, "dur": 95, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\GameSoft\\Unity\\Editor\\2022.3.38f1c1\\Editor\\Data\\Tools\\ilpp\\Unity.ILPP.Runner\\System.Security.Cryptography.Algorithms.dll"}}, {"pid": 12345, "tid": 12, "ts": 1754735841182169, "dur": 1695, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/com.crashkonijn.goap.demos.turn-based.dll (+pdb)"}}, {"pid": 12345, "tid": 12, "ts": 1754735841183865, "dur": 51, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1754735841183927, "dur": 1537, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/GlassSystem.dll (+pdb)"}}, {"pid": 12345, "tid": 12, "ts": 1754735841185498, "dur": 1402, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Searcher.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 12, "ts": 1754735841187692, "dur": 58, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\GameSoft\\Unity\\Editor\\2022.3.38f1c1\\Editor\\Data\\Tools\\ilpp\\Unity.ILPP.Runner\\System.IO.Compression.FileSystem.dll"}}, {"pid": 12345, "tid": 12, "ts": 1754735841186953, "dur": 1587, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.VisualScripting.Shared.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 12, "ts": 1754735841188541, "dur": 61, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1754735841188777, "dur": 138, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1754735841188931, "dur": 747810, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1754735840803116, "dur": 21113, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1754735840824237, "dur": 85, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.PresetsUIModule.dll_A8B81DEC69E8E1CF.mvfrm"}}, {"pid": 12345, "tid": 13, "ts": 1754735840824323, "dur": 72, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1754735840824467, "dur": 98, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.SpriteMaskModule.dll_F2FD8C610219B039.mvfrm"}}, {"pid": 12345, "tid": 13, "ts": 1754735840824594, "dur": 71, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1754735840824837, "dur": 95, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Collections.rsp2"}}, {"pid": 12345, "tid": 13, "ts": 1754735840824975, "dur": 161, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.ShaderGraph.Utilities.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 13, "ts": 1754735840825286, "dur": 75, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Rider.Editor.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 13, "ts": 1754735840825449, "dur": 51, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.TextMeshPro.Editor.rsp"}}, {"pid": 12345, "tid": 13, "ts": 1754735840825602, "dur": 134, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Tests.rsp2"}}, {"pid": 12345, "tid": 13, "ts": 1754735840825767, "dur": 96, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.CollabProxy.Editor.rsp"}}, {"pid": 12345, "tid": 13, "ts": 1754735840826021, "dur": 143, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1754735840826167, "dur": 193, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1754735840826360, "dur": 676, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1754735840827036, "dur": 262, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1754735840827298, "dur": 561, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1754735840827859, "dur": 445, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1754735840828304, "dur": 560, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1754735840828864, "dur": 571, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1754735840829435, "dur": 243, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1754735840829678, "dur": 206, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1754735840829884, "dur": 189, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1754735840830073, "dur": 363, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1754735840830436, "dur": 222, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1754735840830658, "dur": 173, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1754735840830832, "dur": 203, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1754735840831036, "dur": 174, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1754735840831210, "dur": 468, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1754735840831679, "dur": 217, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1754735840831896, "dur": 204, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1754735840832100, "dur": 258, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1754735840832358, "dur": 205, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1754735840832563, "dur": 382, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1754735840832946, "dur": 68, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.Searcher.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 13, "ts": 1754735840833043, "dur": 678, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Searcher.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 13, "ts": 1754735840833779, "dur": 64, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.Burst.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 13, "ts": 1754735840833970, "dur": 106, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\GameSoft\\Unity\\Editor\\2022.3.38f1c1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.UnityCurlModule.dll"}}, {"pid": 12345, "tid": 13, "ts": 1754735840834210, "dur": 87, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\GameSoft\\Unity\\Editor\\2022.3.38f1c1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Reflection.Emit.dll"}}, {"pid": 12345, "tid": 13, "ts": 1754735840834532, "dur": 52, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\GameSoft\\Unity\\Editor\\2022.3.38f1c1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\System.Data.DataSetExtensions.dll"}}, {"pid": 12345, "tid": 13, "ts": 1754735840833869, "dur": 808, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Burst.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 13, "ts": 1754735840834677, "dur": 119, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1754735840834876, "dur": 308, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1754735840835185, "dur": 71, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.Collections.DocCodeSamples.dll.mvfrm"}}, {"pid": 12345, "tid": 13, "ts": 1754735840835280, "dur": 266, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Collections.DocCodeSamples.dll (+2 others)"}}, {"pid": 12345, "tid": 13, "ts": 1754735840835593, "dur": 343, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1754735840835937, "dur": 287, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/com.crashkonijn.goap.demos.turn-based.dll (+2 others)"}}, {"pid": 12345, "tid": 13, "ts": 1754735840836225, "dur": 50, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1754735840836281, "dur": 76, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/URPWizard.dll.mvfrm"}}, {"pid": 12345, "tid": 13, "ts": 1754735840836374, "dur": 243, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/URPWizard.dll (+2 others)"}}, {"pid": 12345, "tid": 13, "ts": 1754735840836643, "dur": 344157, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1754735841182119, "dur": 569, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\Bee\\artifacts\\1900b0aE.dag\\Unity.Burst.CodeGen.dll"}}, {"pid": 12345, "tid": 13, "ts": 1754735841182712, "dur": 313, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\GameSoft\\Unity\\Editor\\2022.3.38f1c1\\Editor\\Data\\Managed\\UnityEngine\\UnityEditor.QuickSearchModule.dll"}}, {"pid": 12345, "tid": 13, "ts": 1754735841183270, "dur": 609, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\GameSoft\\Unity\\Editor\\2022.3.38f1c1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Security.Claims.dll"}}, {"pid": 12345, "tid": 13, "ts": 1754735841184201, "dur": 84, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\GameSoft\\Unity\\Editor\\2022.3.38f1c1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Threading.dll"}}, {"pid": 12345, "tid": 13, "ts": 1754735841184286, "dur": 166, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\GameSoft\\Unity\\Editor\\2022.3.38f1c1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Threading.Overlapped.dll"}}, {"pid": 12345, "tid": 13, "ts": 1754735841180801, "dur": 3710, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/CrashKonijn.Agent.Core.dll (+pdb)"}}, {"pid": 12345, "tid": 13, "ts": 1754735841184513, "dur": 119, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1754735841184641, "dur": 1591, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.CollabProxy.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 13, "ts": 1754735841186233, "dur": 223, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1754735841187372, "dur": 455, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\GameSoft\\Unity\\Editor\\2022.3.38f1c1\\Editor\\Data\\Tools\\ilpp\\Unity.ILPP.Runner\\System.Memory.dll"}}, {"pid": 12345, "tid": 13, "ts": 1754735841186462, "dur": 2120, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Timeline.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 13, "ts": 1754735841188781, "dur": 146, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1754735841188927, "dur": 747813, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1754735840803141, "dur": 21103, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1754735840824263, "dur": 80, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\GameSoft\\Unity\\Editor\\2022.3.38f1c1\\Editor\\Data\\Managed\\UnityEngine\\UnityEditor.SceneViewModule.dll"}}, {"pid": 12345, "tid": 14, "ts": 1754735840824257, "dur": 87, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.SceneViewModule.dll_2C01C8125CB356D8.mvfrm"}}, {"pid": 12345, "tid": 14, "ts": 1754735840824345, "dur": 50, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1754735840824537, "dur": 116, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\GameSoft\\Unity\\Editor\\2022.3.38f1c1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.UnityWebRequestTextureModule.dll"}}, {"pid": 12345, "tid": 14, "ts": 1754735840824536, "dur": 119, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityWebRequestTextureModule.dll_3AB3AEE512D0C853.mvfrm"}}, {"pid": 12345, "tid": 14, "ts": 1754735840824857, "dur": 273, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Core.rsp"}}, {"pid": 12345, "tid": 14, "ts": 1754735840825577, "dur": 100, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/com.crashkonijn.goap.demos.simple.rsp"}}, {"pid": 12345, "tid": 14, "ts": 1754735840825983, "dur": 169, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1754735840826156, "dur": 273, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1754735840826430, "dur": 207, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1754735840826637, "dur": 216, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1754735840826854, "dur": 395, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1754735840827249, "dur": 158, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1754735840827407, "dur": 382, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1754735840827790, "dur": 207, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1754735840827997, "dur": 295, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1754735840828293, "dur": 605, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1754735840828898, "dur": 351, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1754735840829276, "dur": 289, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1754735840829565, "dur": 246, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1754735840829811, "dur": 176, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1754735840829987, "dur": 189, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1754735840830176, "dur": 329, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1754735840830505, "dur": 189, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1754735840830694, "dur": 188, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1754735840830882, "dur": 228, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1754735840831110, "dur": 271, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1754735840831381, "dur": 270, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1754735840831651, "dur": 466, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1754735840832117, "dur": 296, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1754735840832413, "dur": 153, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1754735840832566, "dur": 373, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1754735840832945, "dur": 70, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.InputSystem.dll.mvfrm"}}, {"pid": 12345, "tid": 14, "ts": 1754735840833015, "dur": 62, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1754735840833776, "dur": 55, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.inputsystem@1.7.0\\InputSystem\\Editor\\AssetEditor\\InputActionAssetManager.cs"}}, {"pid": 12345, "tid": 14, "ts": 1754735840833081, "dur": 1259, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.InputSystem.dll (+2 others)"}}, {"pid": 12345, "tid": 14, "ts": 1754735840834341, "dur": 121, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1754735840834475, "dur": 77, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1754735840834560, "dur": 179, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Core.Runtime.dll.mvfrm"}}, {"pid": 12345, "tid": 14, "ts": 1754735840834787, "dur": 541, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Core.Runtime.dll (+2 others)"}}, {"pid": 12345, "tid": 14, "ts": 1754735840835390, "dur": 69, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipeline.Universal.ShaderLibrary.dll.mvfrm"}}, {"pid": 12345, "tid": 14, "ts": 1754735840835469, "dur": 207, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipeline.Universal.ShaderLibrary.dll (+2 others)"}}, {"pid": 12345, "tid": 14, "ts": 1754735840835717, "dur": 238, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1754735840835955, "dur": 346140, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1754735841182118, "dur": 50, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\GameSoft\\Unity\\Editor\\2022.3.38f1c1\\Editor\\Data\\Managed\\UnityEngine\\UnityEditor.TextCoreTextEngineModule.dll"}}, {"pid": 12345, "tid": 14, "ts": 1754735841183828, "dur": 112, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.burst@1.8.16\\Unity.Burst.CodeGen\\Unity.Burst.Cecil.Mdb.dll"}}, {"pid": 12345, "tid": 14, "ts": 1754735841182096, "dur": 1848, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/CrashKonijn.Goap.Core.dll (+pdb)"}}, {"pid": 12345, "tid": 14, "ts": 1754735841184286, "dur": 86, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\GameSoft\\Unity\\Editor\\2022.3.38f1c1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Numerics.Vectors.dll"}}, {"pid": 12345, "tid": 14, "ts": 1754735841184669, "dur": 76, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\GameSoft\\Unity\\Editor\\2022.3.38f1c1\\Editor\\Data\\Tools\\ilpp\\Unity.ILPP.Runner\\Microsoft.Extensions.Configuration.FileExtensions.dll"}}, {"pid": 12345, "tid": 14, "ts": 1754735841185378, "dur": 581, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\GameSoft\\Unity\\Editor\\2022.3.38f1c1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.IO.FileSystem.Watcher.dll"}}, {"pid": 12345, "tid": 14, "ts": 1754735841184008, "dur": 2139, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/UnityEngine.UI.dll (+pdb)"}}, {"pid": 12345, "tid": 14, "ts": 1754735841186865, "dur": 55, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\GameSoft\\Unity\\Editor\\2022.3.38f1c1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Xml.XmlSerializer.dll"}}, {"pid": 12345, "tid": 14, "ts": 1754735841187373, "dur": 245, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\GameSoft\\Unity\\Editor\\2022.3.38f1c1\\Editor\\Data\\Tools\\ilpp\\Unity.ILPP.Runner\\Microsoft.Extensions.Options.dll"}}, {"pid": 12345, "tid": 14, "ts": 1754735841187979, "dur": 222, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\GameSoft\\Unity\\Editor\\2022.3.38f1c1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.Physics2DModule.dll"}}, {"pid": 12345, "tid": 14, "ts": 1754735841188336, "dur": 87, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\GameSoft\\Unity\\Editor\\2022.3.38f1c1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Net.NameResolution.dll"}}, {"pid": 12345, "tid": 14, "ts": 1754735841188490, "dur": 89, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\GameSoft\\Unity\\Editor\\2022.3.38f1c1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Runtime.CompilerServices.VisualC.dll"}}, {"pid": 12345, "tid": 14, "ts": 1754735841186196, "dur": 2654, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/CrashKonijn.Goap.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 14, "ts": 1754735841188939, "dur": 747776, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1754735840803173, "dur": 21104, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1754735840824289, "dur": 74, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\GameSoft\\Unity\\Editor\\2022.3.38f1c1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.InputLegacyModule.dll"}}, {"pid": 12345, "tid": 15, "ts": 1754735840824283, "dur": 83, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.InputLegacyModule.dll_93739C87338A598F.mvfrm"}}, {"pid": 12345, "tid": 15, "ts": 1754735840824540, "dur": 120, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\GameSoft\\Unity\\Editor\\2022.3.38f1c1\\Editor\\Data\\PlaybackEngines\\WindowsStandaloneSupport\\UnityEditor.WindowsStandalone.Extensions.dll"}}, {"pid": 12345, "tid": 15, "ts": 1754735840824538, "dur": 124, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.WindowsStandalone.Extensions.dll_35385077F0CEC424.mvfrm"}}, {"pid": 12345, "tid": 15, "ts": 1754735840824690, "dur": 90, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.WindowsStandalone.Extensions.dll_35385077F0CEC424.mvfrm"}}, {"pid": 12345, "tid": 15, "ts": 1754735840824839, "dur": 102, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Collections.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 15, "ts": 1754735840825071, "dur": 262, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.ShaderGraph.Editor.rsp2"}}, {"pid": 12345, "tid": 15, "ts": 1754735840825360, "dur": 61, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Core.ShaderLibrary.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 15, "ts": 1754735840825474, "dur": 85, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Shared.Editor.rsp"}}, {"pid": 12345, "tid": 15, "ts": 1754735840825589, "dur": 160, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Assembly-CSharp.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 15, "ts": 1754735840826004, "dur": 390, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1754735840826395, "dur": 185, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1754735840826580, "dur": 372, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1754735840826953, "dur": 282, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1754735840827235, "dur": 225, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1754735840827460, "dur": 239, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1754735840827699, "dur": 169, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1754735840827869, "dur": 168, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1754735840828037, "dur": 266, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1754735840828338, "dur": 546, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.visualscripting@1.9.4\\Editor\\VisualScripting.Flow\\Plugin\\Migrations\\Migration_1_6_to_1_7.cs"}}, {"pid": 12345, "tid": 15, "ts": 1754735840828304, "dur": 723, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1754735840829027, "dur": 175, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1754735840829202, "dur": 194, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1754735840829396, "dur": 165, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1754735840829562, "dur": 171, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1754735840829733, "dur": 211, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1754735840829944, "dur": 181, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1754735840830125, "dur": 190, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1754735840830315, "dur": 195, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1754735840830511, "dur": 189, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1754735840830700, "dur": 203, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1754735840830903, "dur": 184, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1754735840831087, "dur": 190, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1754735840831277, "dur": 200, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1754735840831477, "dur": 239, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1754735840831717, "dur": 200, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1754735840831917, "dur": 206, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1754735840832123, "dur": 287, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1754735840832411, "dur": 174, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1754735840832585, "dur": 363, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1754735840832954, "dur": 85, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/CrashKonijn.Agent.Runtime.dll.mvfrm"}}, {"pid": 12345, "tid": 15, "ts": 1754735840833040, "dur": 72, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1754735840833120, "dur": 102, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.Rider.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 15, "ts": 1754735840833245, "dur": 381, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Rider.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 15, "ts": 1754735840833667, "dur": 93, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1754735840833768, "dur": 358, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/CrashKonijn.Agent.Runtime.dll (+2 others)"}}, {"pid": 12345, "tid": 15, "ts": 1754735840834126, "dur": 139, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1754735840834265, "dur": 73, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/CrashKonijn.Agent.Runtime.dll (+2 others)"}}, {"pid": 12345, "tid": 15, "ts": 1754735840834339, "dur": 199, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1754735840834583, "dur": 100, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Core.dll.mvfrm"}}, {"pid": 12345, "tid": 15, "ts": 1754735840834712, "dur": 910, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Core.dll (+2 others)"}}, {"pid": 12345, "tid": 15, "ts": 1754735840835732, "dur": 753, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Flow.dll (+2 others)"}}, {"pid": 12345, "tid": 15, "ts": 1754735840836541, "dur": 62, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.State.dll.mvfrm"}}, {"pid": 12345, "tid": 15, "ts": 1754735840836617, "dur": 183, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.State.dll (+2 others)"}}, {"pid": 12345, "tid": 15, "ts": 1754735840836850, "dur": 344020, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1754735841180871, "dur": 1828, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.InputSystem.ForUI.dll (+pdb)"}}, {"pid": 12345, "tid": 15, "ts": 1754735841182700, "dur": 60, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1754735841183219, "dur": 96, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\GameSoft\\Unity\\Editor\\2022.3.38f1c1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Xml.ReaderWriter.dll"}}, {"pid": 12345, "tid": 15, "ts": 1754735841183821, "dur": 107, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\GameSoft\\Unity\\Editor\\2022.3.38f1c1\\Editor\\Data\\Tools\\ilpp\\Unity.ILPP.Runner\\System.Text.Encodings.Web.dll"}}, {"pid": 12345, "tid": 15, "ts": 1754735841182781, "dur": 1710, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Cinemachine.dll (+pdb)"}}, {"pid": 12345, "tid": 15, "ts": 1754735841184492, "dur": 144, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1754735841185095, "dur": 295, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\GameSoft\\Unity\\Editor\\2022.3.38f1c1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.ServiceModel.Primitives.dll"}}, {"pid": 12345, "tid": 15, "ts": 1754735841186422, "dur": 66, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\GameSoft\\Unity\\Editor\\2022.3.38f1c1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Threading.Overlapped.dll"}}, {"pid": 12345, "tid": 15, "ts": 1754735841186498, "dur": 655, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\GameSoft\\Unity\\Editor\\2022.3.38f1c1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Threading.ThreadPool.dll"}}, {"pid": 12345, "tid": 15, "ts": 1754735841184646, "dur": 2568, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Burst.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 15, "ts": 1754735841187215, "dur": 215, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1754735841187442, "dur": 106, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1754735841187683, "dur": 161, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1754735841187912, "dur": 147, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1754735841188136, "dur": 118, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1754735841188261, "dur": 141, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1754735841188408, "dur": 208, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1754735841188636, "dur": 174, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1754735841188815, "dur": 82672, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1754735841271490, "dur": 48777, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\Bee\\artifacts\\1900b0aE.dag\\Assembly-CSharp.dll"}}, {"pid": 12345, "tid": 15, "ts": 1754735841271488, "dur": 50151, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Assembly-CSharp.dll (+pdb)"}}, {"pid": 12345, "tid": 15, "ts": 1754735841322867, "dur": 173, "ph": "X", "name": "StoreTimestampsOfNonGeneratedInputFiles", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1754735841323097, "dur": 334743, "ph": "X", "name": "ILPostProcess", "args": {"detail": "Library/Bee/artifacts/1900b0aE.dag/post-processed/Assembly-CSharp.dll (+pdb)"}}, {"pid": 12345, "tid": 15, "ts": 1754735841664145, "dur": 73440, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\Bee\\artifacts\\1900b0aE.dag\\post-processed\\Assembly-CSharp.dll"}}, {"pid": 12345, "tid": 15, "ts": 1754735841664143, "dur": 73445, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Assembly-CSharp.dll"}}, {"pid": 12345, "tid": 15, "ts": 1754735841737613, "dur": 793, "ph": "X", "name": "CopyFiles", "args": {"detail": "Library/ScriptAssemblies/Assembly-CSharp.dll"}}, {"pid": 12345, "tid": 15, "ts": 1754735841738412, "dur": 198325, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1754735840803202, "dur": 21088, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1754735840824298, "dur": 62, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\GameSoft\\Unity\\Editor\\2022.3.38f1c1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.AnimationModule.dll"}}, {"pid": 12345, "tid": 16, "ts": 1754735840824291, "dur": 71, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.AnimationModule.dll_BBD7613341ED5E70.mvfrm"}}, {"pid": 12345, "tid": 16, "ts": 1754735840824397, "dur": 160, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.GridModule.dll_B76D6751422DDE5E.mvfrm"}}, {"pid": 12345, "tid": 16, "ts": 1754735840824564, "dur": 96, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1754735840824660, "dur": 219, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/UnityEngine.UI.rsp2"}}, {"pid": 12345, "tid": 16, "ts": 1754735840824987, "dur": 284, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/CrashKonijn.Agent.Runtime.rsp2"}}, {"pid": 12345, "tid": 16, "ts": 1754735840825283, "dur": 73, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Cinemachine.rsp2"}}, {"pid": 12345, "tid": 16, "ts": 1754735840825622, "dur": 201, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.CollabProxy.Editor.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 16, "ts": 1754735840825952, "dur": 80, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/7759033573704817190.rsp"}}, {"pid": 12345, "tid": 16, "ts": 1754735840826034, "dur": 847, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.cinemachine@2.10.1\\Editor\\Utility\\SerializedPropertyHelper.cs"}}, {"pid": 12345, "tid": 16, "ts": 1754735840826034, "dur": 1062, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1754735840827096, "dur": 261, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1754735840827358, "dur": 171, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1754735840827529, "dur": 192, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1754735840827721, "dur": 243, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1754735840827966, "dur": 177, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1754735840828143, "dur": 663, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1754735840828807, "dur": 218, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1754735840829025, "dur": 333, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1754735840829358, "dur": 217, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1754735840829576, "dur": 243, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1754735840829819, "dur": 235, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1754735840830055, "dur": 193, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1754735840830248, "dur": 231, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1754735840830479, "dur": 183, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1754735840830662, "dur": 261, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1754735840830923, "dur": 202, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1754735840831125, "dur": 176, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1754735840831301, "dur": 162, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1754735840831463, "dur": 241, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1754735840831704, "dur": 232, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1754735840831936, "dur": 194, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1754735840832131, "dur": 212, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1754735840832343, "dur": 245, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1754735840832588, "dur": 536, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1754735840833125, "dur": 394, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Core.ShaderLibrary.dll.mvfrm"}}, {"pid": 12345, "tid": 16, "ts": 1754735840833681, "dur": 303, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\GameSoft\\Unity\\Editor\\2022.3.38f1c1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netfx\\System.ServiceModel.Web.dll"}}, {"pid": 12345, "tid": 16, "ts": 1754735840834049, "dur": 172, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\GameSoft\\Unity\\Editor\\2022.3.38f1c1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Net.WebSockets.Client.dll"}}, {"pid": 12345, "tid": 16, "ts": 1754735840833534, "dur": 815, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Core.ShaderLibrary.dll (+2 others)"}}, {"pid": 12345, "tid": 16, "ts": 1754735840834350, "dur": 108, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1754735840834504, "dur": 300, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/com.crashkonijn.goap.demos.simple.dll.mvfrm"}}, {"pid": 12345, "tid": 16, "ts": 1754735840834842, "dur": 345, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1754735840835188, "dur": 204, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1754735840835396, "dur": 72, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Core.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 16, "ts": 1754735840835498, "dur": 426, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Core.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 16, "ts": 1754735840835924, "dur": 168, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1754735840836119, "dur": 64, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.ShaderGraph.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 16, "ts": 1754735840836184, "dur": 67, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1754735840836256, "dur": 892, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.ShaderGraph.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 16, "ts": 1754735840837215, "dur": 60, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Universal.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 16, "ts": 1754735840837303, "dur": 483, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Universal.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 16, "ts": 1754735840837831, "dur": 63, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/com.unity.cinemachine.editor.dll.mvfrm"}}, {"pid": 12345, "tid": 16, "ts": 1754735840837909, "dur": 282, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/com.unity.cinemachine.editor.dll (+2 others)"}}, {"pid": 12345, "tid": 16, "ts": 1754735840838363, "dur": 102, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\Bee\\artifacts\\1900b0aE.dag\\Assembly-CSharp.rsp"}}, {"pid": 12345, "tid": 16, "ts": 1754735840838212, "dur": 253, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Assembly-CSharp.dll (+2 others)"}}, {"pid": 12345, "tid": 16, "ts": 1754735840838757, "dur": 431071, "ph": "X", "name": "Csc", "args": {"detail": "Library/Bee/artifacts/1900b0aE.dag/Assembly-CSharp.dll (+2 others)"}}, {"pid": 12345, "tid": 16, "ts": 1754735841271734, "dur": 95, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\Bee\\artifacts\\1900b0aE.dag\\Assembly-CSharp.ref.dll"}}, {"pid": 12345, "tid": 16, "ts": 1754735841271477, "dur": 407, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Assembly-CSharp-Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 16, "ts": 1754735841272101, "dur": 355139, "ph": "X", "name": "Csc", "args": {"detail": "Library/Bee/artifacts/1900b0aE.dag/Assembly-CSharp-Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 16, "ts": 1754735841628590, "dur": 17095, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\Bee\\artifacts\\1900b0aE.dag\\Assembly-CSharp-Editor.dll"}}, {"pid": 12345, "tid": 16, "ts": 1754735841628588, "dur": 18341, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Assembly-CSharp-Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 16, "ts": 1754735841647800, "dur": 199, "ph": "X", "name": "StoreTimestampsOfNonGeneratedInputFiles", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1754735841648026, "dur": 265953, "ph": "X", "name": "ILPostProcess", "args": {"detail": "Library/Bee/artifacts/1900b0aE.dag/post-processed/Assembly-CSharp-Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 16, "ts": 1754735841920411, "dur": 111, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\Bee\\artifacts\\1900b0aE.dag\\post-processed\\Assembly-CSharp-Editor.pdb"}}, {"pid": 12345, "tid": 16, "ts": 1754735841920410, "dur": 113, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Assembly-CSharp-Editor.pdb"}}, {"pid": 12345, "tid": 16, "ts": 1754735841920552, "dur": 794, "ph": "X", "name": "CopyFiles", "args": {"detail": "Library/ScriptAssemblies/Assembly-CSharp-Editor.pdb"}}, {"pid": 12345, "tid": 16, "ts": 1754735841921351, "dur": 15414, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1754735841943616, "dur": 2613, "ph": "X", "name": "ProfilerWriteOutput"}, {"pid": 35942, "tid": -1, "ph": "M", "name": "process_name", "args": {"name": "netcorerun.dll"}}, {"pid": 35942, "tid": -1, "ph": "M", "name": "process_sort_index", "args": {"sort_index": "-1"}}, {"pid": 35942, "tid": 1, "ph": "M", "name": "thread_name", "args": {"name": ""}}, {"pid": 35942, "tid": 1, "ts": 1754735840073138, "dur": 310898, "ph": "X", "name": "BuildProgram", "args": {}}, {"pid": 35942, "tid": 1, "ts": 1754735840073977, "dur": 49236, "ph": "X", "name": "BuildProgramContextConstructor", "args": {}}, {"pid": 35942, "tid": 1, "ts": 1754735840325533, "dur": 3284, "ph": "X", "name": "OutputData.Write", "args": {}}, {"pid": 35942, "tid": 1, "ts": 1754735840328821, "dur": 55197, "ph": "X", "name": "Backend.Write", "args": {}}, {"pid": 35942, "tid": 1, "ts": 1754735840330442, "dur": 32026, "ph": "X", "name": "JsonToString", "args": {}}, {"pid": 35942, "tid": 1, "ts": 1754735840389937, "dur": 1040, "ph": "X", "name": "", "args": {}}, {"pid": 35942, "tid": 1, "ts": 1754735840389531, "dur": 1684, "ph": "X", "name": "Write chrome-trace events", "args": {}}, {"cat": "", "pid": 12345, "tid": 0, "ts": 0, "ph": "M", "name": "process_name", "args": {"name": "bee_backend"}}, {"pid": 12345, "tid": 0, "ts": 1754735839687521, "dur": 1365, "ph": "X", "name": "DriverInitData", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1754735839688900, "dur": 798, "ph": "X", "name": "RemoveStaleOutputs", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1754735839689807, "dur": 57, "ph": "X", "name": "<PERSON><PERSON>", "args": {"detail": "PrepareNodes"}}, {"pid": 12345, "tid": 0, "ts": 1754735839689864, "dur": 351, "ph": "X", "name": "BuildQueueInit", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1754735839690809, "dur": 75, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.AudioModule.dll_F8B5F041D001A362.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1754735839692252, "dur": 311, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.WindModule.dll_504B268BBB0FC6D0.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1754735839698849, "dur": 94, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.Rider.Editor.ref.dll_9B5591808ABA37AF.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1754735839704074, "dur": 73, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/3938377011463375229.rsp"}}, {"pid": 12345, "tid": 0, "ts": 1754735839706511, "dur": 59, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Collections.DocCodeSamples.rsp2"}}, {"pid": 12345, "tid": 0, "ts": 1754735839711693, "dur": 54, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/com.unity.cinemachine.editor.dll (+pdb)"}}, {"pid": 12345, "tid": 0, "ts": 1754735839690233, "dur": 22407, "ph": "X", "name": "EnqueueRequestedNodes", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1754735839712653, "dur": 393, "ph": "X", "name": "WaitForBuildFinished", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1754735839713047, "dur": 233, "ph": "X", "name": "JoinBuildThread", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1754735839713281, "dur": 58, "ph": "X", "name": "ThreadStateDestroy", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1754735839713514, "dur": 1128, "ph": "X", "name": "<PERSON><PERSON>", "args": {"detail": "Write AllBuiltNodes"}}, {"pid": 12345, "tid": 1, "ts": 1754735839690594, "dur": 22148, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1754735839712757, "dur": 63, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.VehiclesModule.dll_B4028357F84AC7DF.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1754735839712884, "dur": 58, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\GameSoft\\Unity\\Editor\\2022.3.38f1c1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.AccessibilityModule.dll"}}, {"pid": 12345, "tid": 1, "ts": 1754735839712882, "dur": 65, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.AccessibilityModule.dll_4B3448FA7561224A.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1754735839713030, "dur": 50, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.HotReloadModule.dll_3D201F391D2079F2.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1754735839690568, "dur": 22139, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1754735839712749, "dur": 54, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\GameSoft\\Unity\\Editor\\2022.3.38f1c1\\Editor\\Data\\Managed\\UnityEngine\\UnityEditor.UIElementsSamplesModule.dll"}}, {"pid": 12345, "tid": 2, "ts": 1754735839712725, "dur": 81, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.UIElementsSamplesModule.dll_623E7535F4BD3DF5.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1754735839712857, "dur": 55, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1754735839712973, "dur": 54, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.CrashReportingModule.dll_E0E4ACDF62B3A25F.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1754735839690550, "dur": 22138, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1754735839712881, "dur": 50, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\GameSoft\\Unity\\Editor\\2022.3.38f1c1\\Editor\\Data\\Managed\\UnityEngine\\UnityEditor.UnityConnectModule.dll"}}, {"pid": 12345, "tid": 3, "ts": 1754735839712880, "dur": 54, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.UnityConnectModule.dll_591A98B8662B29F2.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1754735839690517, "dur": 22159, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1754735839712977, "dur": 93, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.dll_D66E5D73A81B547E.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1754735839690486, "dur": 22178, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1754735839712672, "dur": 363, "ph": "X", "name": "CheckDagSignatures", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1754735839690593, "dur": 22133, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1754735839712734, "dur": 74, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityWebRequestWWWModule.dll_BA3DE9AF0772CF8F.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1754735839712894, "dur": 63, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\GameSoft\\Unity\\Editor\\2022.3.38f1c1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.AnimationModule.dll"}}, {"pid": 12345, "tid": 6, "ts": 1754735839712893, "dur": 68, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.AnimationModule.dll_BBD7613341ED5E70.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1754735839690631, "dur": 22135, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1754735839712778, "dur": 54, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.VirtualTexturingModule.dll_38C1DD3B59418845.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1754735839690675, "dur": 22107, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1754735839712961, "dur": 62, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.CoreModule.dll_D9FC4B6329D94867.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1754735839690717, "dur": 22077, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1754735839712884, "dur": 51, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\GameSoft\\Unity\\Editor\\2022.3.38f1c1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.AIModule.dll"}}, {"pid": 12345, "tid": 9, "ts": 1754735839712883, "dur": 55, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.AIModule.dll_DD29252AECEC5C10.mvfrm"}}, {"pid": 12345, "tid": 10, "ts": 1754735839690754, "dur": 22053, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1754735839712976, "dur": 79, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.DirectorModule.dll_3C3F89D87E20F844.mvfrm"}}, {"pid": 12345, "tid": 11, "ts": 1754735839690789, "dur": 22039, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1754735839712880, "dur": 90, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1754735839690825, "dur": 22017, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1754735839712858, "dur": 52, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\GameSoft\\Unity\\Editor\\2022.3.38f1c1\\Editor\\Data\\Managed\\UnityEngine\\UnityEditor.PresetsUIModule.dll"}}, {"pid": 12345, "tid": 12, "ts": 1754735839712849, "dur": 63, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.PresetsUIModule.dll_A8B81DEC69E8E1CF.mvfrm"}}, {"pid": 12345, "tid": 13, "ts": 1754735839690863, "dur": 22005, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1754735839712889, "dur": 66, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.UIElementsModule.dll_613AE6E2D149BAF8.mvfrm"}}, {"pid": 12345, "tid": 13, "ts": 1754735839712983, "dur": 72, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.UIElementsModule.dll_613AE6E2D149BAF8.mvfrm"}}, {"pid": 12345, "tid": 14, "ts": 1754735839690907, "dur": 21982, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1754735839712963, "dur": 85, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.AndroidJNIModule.dll_DF83CFB61A6929CE.mvfrm"}}, {"pid": 12345, "tid": 15, "ts": 1754735839690940, "dur": 21961, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1754735839690975, "dur": 21946, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1754735839718267, "dur": 299, "ph": "X", "name": "ProfilerWriteOutput"}, {"pid": 3100, "tid": 24786, "ts": 1754735841958291, "dur": 1740, "ph": "X", "name": "Wait for external events", "args": {"First to finish": "backend2.traceevents"}}, {"pid": 3100, "tid": 24786, "ts": 1754735841961474, "dur": 35, "ph": "X", "name": "Wait for external events", "args": {"First to finish": "buildprogram0.traceevents"}}, {"pid": 3100, "tid": 24786, "ts": 1754735841961671, "dur": 14, "ph": "X", "name": "Wait for external events", "args": {"First to finish": "backend1.traceevents"}}, {"pid": 3100, "tid": 24786, "ts": 1754735841960196, "dur": 1275, "ph": "X", "name": "backend2.traceevents", "args": {}}, {"pid": 3100, "tid": 24786, "ts": 1754735841961571, "dur": 99, "ph": "X", "name": "buildprogram0.traceevents", "args": {}}, {"pid": 3100, "tid": 24786, "ts": 1754735841961714, "dur": 128, "ph": "X", "name": "backend1.traceevents", "args": {}}, {"pid": 3100, "tid": 24786, "ts": 1754735841954045, "dur": 8547, "ph": "X", "name": "Write chrome-trace events", "args": {}}, {}]}