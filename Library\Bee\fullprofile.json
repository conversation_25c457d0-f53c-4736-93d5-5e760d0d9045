{"Instructions Readme": "1) Open Chrome or Edge, 2) go to chrome://tracing, 3) click Load, 4) navigate to this file.", "processInfo": {}, "traceEvents": [{"pid": 3100, "tid": -1, "ph": "M", "name": "process_name", "args": {"name": "BeeDriver"}}, {"pid": 3100, "tid": -1, "ph": "M", "name": "process_sort_index", "args": {"sort_index": "-2"}}, {"pid": 3100, "tid": 24780, "ph": "M", "name": "thread_name", "args": {"name": "Thread Pool Worker"}}, {"pid": 3100, "tid": 24780, "ts": 1754736190639878, "dur": 416, "ph": "X", "name": "ChromeTraceHeader", "args": {}}, {"pid": 3100, "tid": 24780, "ts": 1754736190643030, "dur": 546, "ph": "X", "name": "Thread Pool Worker", "args": {}}, {"pid": 3100, "tid": 21474836480, "ph": "M", "name": "thread_name", "args": {"name": "ReadEntireBinlogFromIpcAsync"}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736189436819, "dur": 135776, "ph": "X", "name": "WaitForConnectionAsync", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736189572596, "dur": 1062174, "ph": "X", "name": "UpdateFromStreamAsync", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736189572609, "dur": 27, "ph": "X", "name": "ReadAsync 0", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736189572642, "dur": 99379, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736189672035, "dur": 3, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736189672041, "dur": 103, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736189672150, "dur": 12, "ph": "X", "name": "ProcessMessages 41", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736189672166, "dur": 1688, "ph": "X", "name": "ReadAsync 41", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736189673860, "dur": 56, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736189673917, "dur": 1, "ph": "X", "name": "ProcessMessages 639", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736189673919, "dur": 23, "ph": "X", "name": "ReadAsync 639", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736189673944, "dur": 38, "ph": "X", "name": "ReadAsync 349", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736189673986, "dur": 1, "ph": "X", "name": "ProcessMessages 148", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736189673988, "dur": 39, "ph": "X", "name": "ReadAsync 148", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736189674030, "dur": 28, "ph": "X", "name": "ReadAsync 614", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736189674060, "dur": 25, "ph": "X", "name": "ReadAsync 469", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736189674088, "dur": 23, "ph": "X", "name": "ReadAsync 348", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736189674113, "dur": 35, "ph": "X", "name": "ReadAsync 457", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736189674151, "dur": 1, "ph": "X", "name": "ProcessMessages 358", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736189674153, "dur": 38, "ph": "X", "name": "ReadAsync 358", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736189674193, "dur": 1, "ph": "X", "name": "ProcessMessages 701", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736189674195, "dur": 39, "ph": "X", "name": "ReadAsync 701", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736189674237, "dur": 1, "ph": "X", "name": "ProcessMessages 455", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736189674239, "dur": 36, "ph": "X", "name": "ReadAsync 455", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736189674278, "dur": 1, "ph": "X", "name": "ProcessMessages 604", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736189674280, "dur": 26, "ph": "X", "name": "ReadAsync 604", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736189674308, "dur": 38, "ph": "X", "name": "ReadAsync 103", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736189674348, "dur": 25, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736189674376, "dur": 19, "ph": "X", "name": "ReadAsync 604", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736189674397, "dur": 23, "ph": "X", "name": "ReadAsync 113", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736189674422, "dur": 23, "ph": "X", "name": "ReadAsync 354", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736189674446, "dur": 23, "ph": "X", "name": "ReadAsync 353", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736189674471, "dur": 16, "ph": "X", "name": "ReadAsync 428", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736189674489, "dur": 19, "ph": "X", "name": "ReadAsync 341", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736189674509, "dur": 22, "ph": "X", "name": "ReadAsync 324", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736189674532, "dur": 19, "ph": "X", "name": "ReadAsync 348", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736189674553, "dur": 21, "ph": "X", "name": "ReadAsync 334", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736189674575, "dur": 21, "ph": "X", "name": "ReadAsync 442", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736189674597, "dur": 27, "ph": "X", "name": "ReadAsync 362", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736189674626, "dur": 23, "ph": "X", "name": "ReadAsync 431", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736189674651, "dur": 1, "ph": "X", "name": "ProcessMessages 481", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736189674653, "dur": 31, "ph": "X", "name": "ReadAsync 481", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736189674686, "dur": 29, "ph": "X", "name": "ReadAsync 543", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736189674717, "dur": 22, "ph": "X", "name": "ReadAsync 242", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736189674741, "dur": 22, "ph": "X", "name": "ReadAsync 84", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736189674764, "dur": 21, "ph": "X", "name": "ReadAsync 367", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736189674787, "dur": 22, "ph": "X", "name": "ReadAsync 349", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736189674810, "dur": 21, "ph": "X", "name": "ReadAsync 317", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736189674833, "dur": 18, "ph": "X", "name": "ReadAsync 331", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736189674852, "dur": 24, "ph": "X", "name": "ReadAsync 338", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736189674877, "dur": 20, "ph": "X", "name": "ReadAsync 320", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736189674899, "dur": 22, "ph": "X", "name": "ReadAsync 459", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736189674922, "dur": 22, "ph": "X", "name": "ReadAsync 343", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736189674945, "dur": 22, "ph": "X", "name": "ReadAsync 338", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736189674969, "dur": 24, "ph": "X", "name": "ReadAsync 358", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736189674994, "dur": 22, "ph": "X", "name": "ReadAsync 465", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736189675018, "dur": 23, "ph": "X", "name": "ReadAsync 373", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736189675042, "dur": 21, "ph": "X", "name": "ReadAsync 359", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736189675065, "dur": 21, "ph": "X", "name": "ReadAsync 342", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736189675088, "dur": 20, "ph": "X", "name": "ReadAsync 404", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736189675109, "dur": 21, "ph": "X", "name": "ReadAsync 277", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736189675131, "dur": 1, "ph": "X", "name": "ProcessMessages 392", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736189675133, "dur": 21, "ph": "X", "name": "ReadAsync 392", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736189675156, "dur": 22, "ph": "X", "name": "ReadAsync 383", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736189675179, "dur": 21, "ph": "X", "name": "ReadAsync 362", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736189675202, "dur": 20, "ph": "X", "name": "ReadAsync 351", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736189675224, "dur": 22, "ph": "X", "name": "ReadAsync 352", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736189675247, "dur": 21, "ph": "X", "name": "ReadAsync 348", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736189675270, "dur": 21, "ph": "X", "name": "ReadAsync 410", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736189675292, "dur": 21, "ph": "X", "name": "ReadAsync 357", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736189675314, "dur": 21, "ph": "X", "name": "ReadAsync 344", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736189675337, "dur": 21, "ph": "X", "name": "ReadAsync 357", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736189675360, "dur": 20, "ph": "X", "name": "ReadAsync 413", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736189675381, "dur": 21, "ph": "X", "name": "ReadAsync 365", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736189675404, "dur": 21, "ph": "X", "name": "ReadAsync 326", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736189675426, "dur": 21, "ph": "X", "name": "ReadAsync 368", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736189675448, "dur": 20, "ph": "X", "name": "ReadAsync 320", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736189675469, "dur": 22, "ph": "X", "name": "ReadAsync 305", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736189675492, "dur": 21, "ph": "X", "name": "ReadAsync 231", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736189675515, "dur": 28, "ph": "X", "name": "ReadAsync 331", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736189675545, "dur": 21, "ph": "X", "name": "ReadAsync 356", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736189675567, "dur": 21, "ph": "X", "name": "ReadAsync 368", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736189675589, "dur": 21, "ph": "X", "name": "ReadAsync 357", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736189675612, "dur": 22, "ph": "X", "name": "ReadAsync 413", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736189675635, "dur": 21, "ph": "X", "name": "ReadAsync 378", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736189675657, "dur": 23, "ph": "X", "name": "ReadAsync 441", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736189675682, "dur": 21, "ph": "X", "name": "ReadAsync 379", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736189675704, "dur": 22, "ph": "X", "name": "ReadAsync 457", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736189675727, "dur": 21, "ph": "X", "name": "ReadAsync 497", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736189675750, "dur": 21, "ph": "X", "name": "ReadAsync 355", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736189675772, "dur": 21, "ph": "X", "name": "ReadAsync 334", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736189675794, "dur": 21, "ph": "X", "name": "ReadAsync 408", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736189675816, "dur": 18, "ph": "X", "name": "ReadAsync 371", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736189675837, "dur": 31, "ph": "X", "name": "ReadAsync 321", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736189675869, "dur": 21, "ph": "X", "name": "ReadAsync 307", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736189675892, "dur": 21, "ph": "X", "name": "ReadAsync 341", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736189675915, "dur": 18, "ph": "X", "name": "ReadAsync 509", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736189675934, "dur": 20, "ph": "X", "name": "ReadAsync 347", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736189675956, "dur": 22, "ph": "X", "name": "ReadAsync 354", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736189675980, "dur": 21, "ph": "X", "name": "ReadAsync 369", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736189676002, "dur": 23, "ph": "X", "name": "ReadAsync 464", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736189676027, "dur": 24, "ph": "X", "name": "ReadAsync 368", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736189676052, "dur": 18, "ph": "X", "name": "ReadAsync 475", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736189676071, "dur": 21, "ph": "X", "name": "ReadAsync 339", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736189676093, "dur": 21, "ph": "X", "name": "ReadAsync 315", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736189676116, "dur": 21, "ph": "X", "name": "ReadAsync 328", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736189676138, "dur": 22, "ph": "X", "name": "ReadAsync 288", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736189676162, "dur": 21, "ph": "X", "name": "ReadAsync 331", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736189676184, "dur": 20, "ph": "X", "name": "ReadAsync 226", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736189676205, "dur": 21, "ph": "X", "name": "ReadAsync 246", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736189676228, "dur": 21, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736189676250, "dur": 21, "ph": "X", "name": "ReadAsync 365", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736189676273, "dur": 21, "ph": "X", "name": "ReadAsync 405", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736189676295, "dur": 21, "ph": "X", "name": "ReadAsync 340", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736189676318, "dur": 20, "ph": "X", "name": "ReadAsync 373", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736189676340, "dur": 18, "ph": "X", "name": "ReadAsync 267", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736189676359, "dur": 18, "ph": "X", "name": "ReadAsync 338", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736189676378, "dur": 38, "ph": "X", "name": "ReadAsync 285", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736189676418, "dur": 22, "ph": "X", "name": "ReadAsync 316", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736189676441, "dur": 21, "ph": "X", "name": "ReadAsync 206", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736189676463, "dur": 21, "ph": "X", "name": "ReadAsync 318", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736189676486, "dur": 21, "ph": "X", "name": "ReadAsync 283", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736189676508, "dur": 18, "ph": "X", "name": "ReadAsync 255", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736189676528, "dur": 20, "ph": "X", "name": "ReadAsync 278", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736189676549, "dur": 43, "ph": "X", "name": "ReadAsync 262", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736189676596, "dur": 29, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736189676628, "dur": 22, "ph": "X", "name": "ReadAsync 489", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736189676651, "dur": 24, "ph": "X", "name": "ReadAsync 141", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736189676676, "dur": 22, "ph": "X", "name": "ReadAsync 345", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736189676700, "dur": 22, "ph": "X", "name": "ReadAsync 238", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736189676723, "dur": 22, "ph": "X", "name": "ReadAsync 240", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736189676747, "dur": 17, "ph": "X", "name": "ReadAsync 282", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736189676765, "dur": 21, "ph": "X", "name": "ReadAsync 150", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736189676787, "dur": 18, "ph": "X", "name": "ReadAsync 232", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736189676806, "dur": 22, "ph": "X", "name": "ReadAsync 308", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736189676829, "dur": 21, "ph": "X", "name": "ReadAsync 176", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736189676852, "dur": 34, "ph": "X", "name": "ReadAsync 220", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736189676888, "dur": 22, "ph": "X", "name": "ReadAsync 441", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736189676912, "dur": 21, "ph": "X", "name": "ReadAsync 247", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736189676934, "dur": 34, "ph": "X", "name": "ReadAsync 269", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736189676969, "dur": 21, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736189676992, "dur": 20, "ph": "X", "name": "ReadAsync 241", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736189677013, "dur": 21, "ph": "X", "name": "ReadAsync 260", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736189677035, "dur": 1, "ph": "X", "name": "ProcessMessages 331", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736189677036, "dur": 17, "ph": "X", "name": "ReadAsync 331", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736189677055, "dur": 18, "ph": "X", "name": "ReadAsync 305", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736189677074, "dur": 22, "ph": "X", "name": "ReadAsync 228", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736189677097, "dur": 21, "ph": "X", "name": "ReadAsync 321", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736189677120, "dur": 21, "ph": "X", "name": "ReadAsync 335", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736189677142, "dur": 21, "ph": "X", "name": "ReadAsync 316", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736189677165, "dur": 18, "ph": "X", "name": "ReadAsync 339", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736189677185, "dur": 20, "ph": "X", "name": "ReadAsync 192", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736189677206, "dur": 18, "ph": "X", "name": "ReadAsync 271", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736189677226, "dur": 19, "ph": "X", "name": "ReadAsync 294", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736189677246, "dur": 20, "ph": "X", "name": "ReadAsync 273", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736189677268, "dur": 21, "ph": "X", "name": "ReadAsync 277", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736189677290, "dur": 21, "ph": "X", "name": "ReadAsync 233", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736189677313, "dur": 21, "ph": "X", "name": "ReadAsync 344", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736189677335, "dur": 22, "ph": "X", "name": "ReadAsync 263", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736189677358, "dur": 22, "ph": "X", "name": "ReadAsync 271", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736189677382, "dur": 21, "ph": "X", "name": "ReadAsync 294", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736189677405, "dur": 20, "ph": "X", "name": "ReadAsync 287", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736189677427, "dur": 18, "ph": "X", "name": "ReadAsync 271", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736189677446, "dur": 21, "ph": "X", "name": "ReadAsync 324", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736189677469, "dur": 22, "ph": "X", "name": "ReadAsync 295", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736189677492, "dur": 21, "ph": "X", "name": "ReadAsync 401", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736189677515, "dur": 21, "ph": "X", "name": "ReadAsync 252", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736189677537, "dur": 21, "ph": "X", "name": "ReadAsync 342", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736189677559, "dur": 18, "ph": "X", "name": "ReadAsync 285", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736189677579, "dur": 20, "ph": "X", "name": "ReadAsync 191", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736189677600, "dur": 18, "ph": "X", "name": "ReadAsync 303", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736189677620, "dur": 22, "ph": "X", "name": "ReadAsync 233", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736189677643, "dur": 22, "ph": "X", "name": "ReadAsync 183", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736189677667, "dur": 21, "ph": "X", "name": "ReadAsync 307", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736189677689, "dur": 22, "ph": "X", "name": "ReadAsync 292", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736189677713, "dur": 17, "ph": "X", "name": "ReadAsync 362", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736189677731, "dur": 18, "ph": "X", "name": "ReadAsync 168", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736189677750, "dur": 21, "ph": "X", "name": "ReadAsync 303", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736189677773, "dur": 22, "ph": "X", "name": "ReadAsync 283", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736189677796, "dur": 22, "ph": "X", "name": "ReadAsync 389", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736189677819, "dur": 22, "ph": "X", "name": "ReadAsync 307", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736189677843, "dur": 22, "ph": "X", "name": "ReadAsync 271", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736189677867, "dur": 19, "ph": "X", "name": "ReadAsync 311", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736189677887, "dur": 44, "ph": "X", "name": "ReadAsync 248", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736189677934, "dur": 1, "ph": "X", "name": "ProcessMessages 284", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736189677937, "dur": 59, "ph": "X", "name": "ReadAsync 284", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736189677997, "dur": 3, "ph": "X", "name": "ProcessMessages 833", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736189678002, "dur": 36, "ph": "X", "name": "ReadAsync 833", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736189678040, "dur": 1, "ph": "X", "name": "ProcessMessages 559", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736189678041, "dur": 40, "ph": "X", "name": "ReadAsync 559", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736189678084, "dur": 1, "ph": "X", "name": "ProcessMessages 414", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736189678085, "dur": 40, "ph": "X", "name": "ReadAsync 414", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736189678128, "dur": 1, "ph": "X", "name": "ProcessMessages 522", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736189678129, "dur": 46, "ph": "X", "name": "ReadAsync 522", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736189678178, "dur": 1, "ph": "X", "name": "ProcessMessages 518", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736189678179, "dur": 38, "ph": "X", "name": "ReadAsync 518", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736189678218, "dur": 28, "ph": "X", "name": "ReadAsync 613", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736189678248, "dur": 28, "ph": "X", "name": "ReadAsync 330", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736189678278, "dur": 25, "ph": "X", "name": "ReadAsync 466", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736189678305, "dur": 21, "ph": "X", "name": "ReadAsync 524", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736189678328, "dur": 21, "ph": "X", "name": "ReadAsync 224", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736189678350, "dur": 69, "ph": "X", "name": "ReadAsync 315", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736189678423, "dur": 1, "ph": "X", "name": "ProcessMessages 334", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736189678425, "dur": 139, "ph": "X", "name": "ReadAsync 334", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736189678567, "dur": 1, "ph": "X", "name": "ProcessMessages 1106", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736189678569, "dur": 40, "ph": "X", "name": "ReadAsync 1106", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736189678611, "dur": 20, "ph": "X", "name": "ReadAsync 683", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736189678634, "dur": 23, "ph": "X", "name": "ReadAsync 264", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736189678658, "dur": 23, "ph": "X", "name": "ReadAsync 430", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736189678683, "dur": 21, "ph": "X", "name": "ReadAsync 372", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736189678705, "dur": 21, "ph": "X", "name": "ReadAsync 411", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736189678728, "dur": 20, "ph": "X", "name": "ReadAsync 411", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736189678749, "dur": 22, "ph": "X", "name": "ReadAsync 285", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736189678773, "dur": 20, "ph": "X", "name": "ReadAsync 411", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736189678795, "dur": 20, "ph": "X", "name": "ReadAsync 351", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736189678816, "dur": 21, "ph": "X", "name": "ReadAsync 414", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736189678839, "dur": 21, "ph": "X", "name": "ReadAsync 318", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736189678861, "dur": 21, "ph": "X", "name": "ReadAsync 372", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736189678884, "dur": 22, "ph": "X", "name": "ReadAsync 371", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736189678907, "dur": 20, "ph": "X", "name": "ReadAsync 453", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736189678929, "dur": 22, "ph": "X", "name": "ReadAsync 296", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736189678952, "dur": 21, "ph": "X", "name": "ReadAsync 405", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736189678974, "dur": 20, "ph": "X", "name": "ReadAsync 332", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736189678996, "dur": 21, "ph": "X", "name": "ReadAsync 86", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736189679018, "dur": 21, "ph": "X", "name": "ReadAsync 336", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736189679041, "dur": 20, "ph": "X", "name": "ReadAsync 311", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736189679062, "dur": 21, "ph": "X", "name": "ReadAsync 357", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736189679085, "dur": 21, "ph": "X", "name": "ReadAsync 315", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736189679107, "dur": 23, "ph": "X", "name": "ReadAsync 362", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736189679132, "dur": 22, "ph": "X", "name": "ReadAsync 301", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736189679155, "dur": 21, "ph": "X", "name": "ReadAsync 321", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736189679177, "dur": 23, "ph": "X", "name": "ReadAsync 314", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736189679201, "dur": 1, "ph": "X", "name": "ProcessMessages 433", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736189679203, "dur": 21, "ph": "X", "name": "ReadAsync 433", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736189679225, "dur": 20, "ph": "X", "name": "ReadAsync 304", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736189679247, "dur": 24, "ph": "X", "name": "ReadAsync 378", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736189679272, "dur": 20, "ph": "X", "name": "ReadAsync 417", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736189679294, "dur": 17, "ph": "X", "name": "ReadAsync 129", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736189679312, "dur": 20, "ph": "X", "name": "ReadAsync 307", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736189679334, "dur": 22, "ph": "X", "name": "ReadAsync 236", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736189679357, "dur": 19, "ph": "X", "name": "ReadAsync 398", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736189679378, "dur": 22, "ph": "X", "name": "ReadAsync 212", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736189679401, "dur": 22, "ph": "X", "name": "ReadAsync 299", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736189679425, "dur": 21, "ph": "X", "name": "ReadAsync 406", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736189679448, "dur": 18, "ph": "X", "name": "ReadAsync 333", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736189679467, "dur": 104, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736189679574, "dur": 40, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736189679617, "dur": 29, "ph": "X", "name": "ReadAsync 662", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736189679648, "dur": 1, "ph": "X", "name": "ProcessMessages 376", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736189679649, "dur": 31, "ph": "X", "name": "ReadAsync 376", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736189679682, "dur": 22, "ph": "X", "name": "ReadAsync 614", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736189679706, "dur": 23, "ph": "X", "name": "ReadAsync 324", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736189679731, "dur": 37, "ph": "X", "name": "ReadAsync 173", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736189679769, "dur": 24, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736189679795, "dur": 37, "ph": "X", "name": "ReadAsync 500", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736189679834, "dur": 22, "ph": "X", "name": "ReadAsync 620", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736189679858, "dur": 21, "ph": "X", "name": "ReadAsync 295", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736189679880, "dur": 22, "ph": "X", "name": "ReadAsync 330", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736189679904, "dur": 18, "ph": "X", "name": "ReadAsync 485", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736189679923, "dur": 21, "ph": "X", "name": "ReadAsync 205", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736189679946, "dur": 21, "ph": "X", "name": "ReadAsync 319", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736189679969, "dur": 22, "ph": "X", "name": "ReadAsync 307", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736189679992, "dur": 21, "ph": "X", "name": "ReadAsync 405", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736189680015, "dur": 22, "ph": "X", "name": "ReadAsync 323", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736189680039, "dur": 22, "ph": "X", "name": "ReadAsync 279", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736189680062, "dur": 18, "ph": "X", "name": "ReadAsync 331", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736189680081, "dur": 18, "ph": "X", "name": "ReadAsync 65", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736189680100, "dur": 21, "ph": "X", "name": "ReadAsync 301", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736189680122, "dur": 23, "ph": "X", "name": "ReadAsync 323", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736189680146, "dur": 22, "ph": "X", "name": "ReadAsync 430", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736189680170, "dur": 21, "ph": "X", "name": "ReadAsync 346", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736189680192, "dur": 22, "ph": "X", "name": "ReadAsync 302", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736189680217, "dur": 20, "ph": "X", "name": "ReadAsync 363", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736189680238, "dur": 29, "ph": "X", "name": "ReadAsync 250", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736189680269, "dur": 21, "ph": "X", "name": "ReadAsync 309", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736189680292, "dur": 22, "ph": "X", "name": "ReadAsync 419", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736189680315, "dur": 21, "ph": "X", "name": "ReadAsync 294", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736189680338, "dur": 22, "ph": "X", "name": "ReadAsync 293", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736189680362, "dur": 18, "ph": "X", "name": "ReadAsync 390", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736189680381, "dur": 20, "ph": "X", "name": "ReadAsync 170", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736189680402, "dur": 43, "ph": "X", "name": "ReadAsync 274", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736189680447, "dur": 26, "ph": "X", "name": "ReadAsync 437", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736189680474, "dur": 23, "ph": "X", "name": "ReadAsync 781", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736189680498, "dur": 26, "ph": "X", "name": "ReadAsync 406", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736189680526, "dur": 21, "ph": "X", "name": "ReadAsync 417", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736189680548, "dur": 21, "ph": "X", "name": "ReadAsync 354", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736189680571, "dur": 25, "ph": "X", "name": "ReadAsync 335", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736189680597, "dur": 22, "ph": "X", "name": "ReadAsync 447", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736189680621, "dur": 22, "ph": "X", "name": "ReadAsync 359", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736189680644, "dur": 21, "ph": "X", "name": "ReadAsync 367", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736189680667, "dur": 18, "ph": "X", "name": "ReadAsync 321", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736189680686, "dur": 22, "ph": "X", "name": "ReadAsync 279", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736189680709, "dur": 22, "ph": "X", "name": "ReadAsync 230", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736189680733, "dur": 22, "ph": "X", "name": "ReadAsync 516", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736189680756, "dur": 22, "ph": "X", "name": "ReadAsync 334", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736189680780, "dur": 18, "ph": "X", "name": "ReadAsync 374", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736189680799, "dur": 18, "ph": "X", "name": "ReadAsync 358", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736189680818, "dur": 21, "ph": "X", "name": "ReadAsync 334", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736189680841, "dur": 21, "ph": "X", "name": "ReadAsync 355", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736189680863, "dur": 23, "ph": "X", "name": "ReadAsync 383", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736189680888, "dur": 21, "ph": "X", "name": "ReadAsync 469", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736189680910, "dur": 22, "ph": "X", "name": "ReadAsync 388", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736189680934, "dur": 23, "ph": "X", "name": "ReadAsync 330", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736189680958, "dur": 21, "ph": "X", "name": "ReadAsync 326", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736189680981, "dur": 21, "ph": "X", "name": "ReadAsync 321", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736189681004, "dur": 23, "ph": "X", "name": "ReadAsync 301", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736189681028, "dur": 22, "ph": "X", "name": "ReadAsync 345", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736189681052, "dur": 21, "ph": "X", "name": "ReadAsync 312", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736189681074, "dur": 23, "ph": "X", "name": "ReadAsync 288", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736189681097, "dur": 1, "ph": "X", "name": "ProcessMessages 402", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736189681098, "dur": 22, "ph": "X", "name": "ReadAsync 402", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736189681122, "dur": 24, "ph": "X", "name": "ReadAsync 295", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736189681148, "dur": 22, "ph": "X", "name": "ReadAsync 321", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736189681171, "dur": 23, "ph": "X", "name": "ReadAsync 345", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736189681195, "dur": 21, "ph": "X", "name": "ReadAsync 385", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736189681218, "dur": 22, "ph": "X", "name": "ReadAsync 266", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736189681241, "dur": 62, "ph": "X", "name": "ReadAsync 362", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736189681305, "dur": 22, "ph": "X", "name": "ReadAsync 219", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736189681328, "dur": 24, "ph": "X", "name": "ReadAsync 442", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736189681353, "dur": 22, "ph": "X", "name": "ReadAsync 408", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736189681377, "dur": 22, "ph": "X", "name": "ReadAsync 382", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736189681400, "dur": 22, "ph": "X", "name": "ReadAsync 423", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736189681424, "dur": 21, "ph": "X", "name": "ReadAsync 443", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736189681447, "dur": 22, "ph": "X", "name": "ReadAsync 354", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736189681470, "dur": 22, "ph": "X", "name": "ReadAsync 384", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736189681493, "dur": 22, "ph": "X", "name": "ReadAsync 394", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736189681517, "dur": 18, "ph": "X", "name": "ReadAsync 559", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736189681536, "dur": 18, "ph": "X", "name": "ReadAsync 366", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736189681555, "dur": 21, "ph": "X", "name": "ReadAsync 328", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736189681577, "dur": 21, "ph": "X", "name": "ReadAsync 392", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736189681600, "dur": 21, "ph": "X", "name": "ReadAsync 397", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736189681623, "dur": 21, "ph": "X", "name": "ReadAsync 272", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736189681645, "dur": 21, "ph": "X", "name": "ReadAsync 433", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736189681668, "dur": 21, "ph": "X", "name": "ReadAsync 427", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736189681691, "dur": 22, "ph": "X", "name": "ReadAsync 359", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736189681714, "dur": 21, "ph": "X", "name": "ReadAsync 400", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736189681737, "dur": 21, "ph": "X", "name": "ReadAsync 460", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736189681759, "dur": 18, "ph": "X", "name": "ReadAsync 228", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736189681779, "dur": 21, "ph": "X", "name": "ReadAsync 379", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736189681802, "dur": 21, "ph": "X", "name": "ReadAsync 333", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736189681824, "dur": 20, "ph": "X", "name": "ReadAsync 275", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736189681846, "dur": 22, "ph": "X", "name": "ReadAsync 241", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736189681870, "dur": 22, "ph": "X", "name": "ReadAsync 330", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736189681893, "dur": 23, "ph": "X", "name": "ReadAsync 295", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736189681919, "dur": 23, "ph": "X", "name": "ReadAsync 357", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736189681943, "dur": 1, "ph": "X", "name": "ProcessMessages 448", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736189681944, "dur": 23, "ph": "X", "name": "ReadAsync 448", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736189681969, "dur": 21, "ph": "X", "name": "ReadAsync 426", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736189681992, "dur": 21, "ph": "X", "name": "ReadAsync 286", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736189682014, "dur": 21, "ph": "X", "name": "ReadAsync 320", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736189682037, "dur": 21, "ph": "X", "name": "ReadAsync 280", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736189682059, "dur": 18, "ph": "X", "name": "ReadAsync 310", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736189682079, "dur": 18, "ph": "X", "name": "ReadAsync 323", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736189682098, "dur": 20, "ph": "X", "name": "ReadAsync 319", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736189682120, "dur": 21, "ph": "X", "name": "ReadAsync 235", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736189682142, "dur": 22, "ph": "X", "name": "ReadAsync 262", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736189682166, "dur": 22, "ph": "X", "name": "ReadAsync 394", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736189682189, "dur": 22, "ph": "X", "name": "ReadAsync 314", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736189682212, "dur": 22, "ph": "X", "name": "ReadAsync 496", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736189682236, "dur": 21, "ph": "X", "name": "ReadAsync 447", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736189682259, "dur": 21, "ph": "X", "name": "ReadAsync 349", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736189682281, "dur": 22, "ph": "X", "name": "ReadAsync 391", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736189682304, "dur": 18, "ph": "X", "name": "ReadAsync 391", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736189682323, "dur": 18, "ph": "X", "name": "ReadAsync 355", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736189682342, "dur": 18, "ph": "X", "name": "ReadAsync 378", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736189682362, "dur": 21, "ph": "X", "name": "ReadAsync 367", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736189682385, "dur": 21, "ph": "X", "name": "ReadAsync 355", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736189682408, "dur": 23, "ph": "X", "name": "ReadAsync 339", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736189682433, "dur": 22, "ph": "X", "name": "ReadAsync 384", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736189682456, "dur": 29, "ph": "X", "name": "ReadAsync 448", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736189682487, "dur": 23, "ph": "X", "name": "ReadAsync 223", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736189682511, "dur": 21, "ph": "X", "name": "ReadAsync 416", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736189682534, "dur": 21, "ph": "X", "name": "ReadAsync 371", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736189682557, "dur": 22, "ph": "X", "name": "ReadAsync 343", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736189682580, "dur": 21, "ph": "X", "name": "ReadAsync 383", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736189682603, "dur": 21, "ph": "X", "name": "ReadAsync 408", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736189682625, "dur": 23, "ph": "X", "name": "ReadAsync 188", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736189682649, "dur": 22, "ph": "X", "name": "ReadAsync 303", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736189682673, "dur": 21, "ph": "X", "name": "ReadAsync 292", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736189682695, "dur": 21, "ph": "X", "name": "ReadAsync 244", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736189682718, "dur": 22, "ph": "X", "name": "ReadAsync 224", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736189682741, "dur": 22, "ph": "X", "name": "ReadAsync 321", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736189682765, "dur": 23, "ph": "X", "name": "ReadAsync 291", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736189682789, "dur": 22, "ph": "X", "name": "ReadAsync 290", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736189682812, "dur": 22, "ph": "X", "name": "ReadAsync 278", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736189682836, "dur": 21, "ph": "X", "name": "ReadAsync 313", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736189682858, "dur": 21, "ph": "X", "name": "ReadAsync 324", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736189682881, "dur": 18, "ph": "X", "name": "ReadAsync 416", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736189682900, "dur": 22, "ph": "X", "name": "ReadAsync 190", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736189682923, "dur": 18, "ph": "X", "name": "ReadAsync 252", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736189682942, "dur": 21, "ph": "X", "name": "ReadAsync 255", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736189682965, "dur": 24, "ph": "X", "name": "ReadAsync 297", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736189682990, "dur": 21, "ph": "X", "name": "ReadAsync 322", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736189683012, "dur": 18, "ph": "X", "name": "ReadAsync 298", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736189683031, "dur": 14, "ph": "X", "name": "ReadAsync 101", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736189683047, "dur": 26, "ph": "X", "name": "ReadAsync 200", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736189683074, "dur": 23, "ph": "X", "name": "ReadAsync 92", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736189683098, "dur": 21, "ph": "X", "name": "ReadAsync 332", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736189683121, "dur": 21, "ph": "X", "name": "ReadAsync 369", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736189683143, "dur": 17, "ph": "X", "name": "ReadAsync 196", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736189683161, "dur": 21, "ph": "X", "name": "ReadAsync 88", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736189683184, "dur": 22, "ph": "X", "name": "ReadAsync 225", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736189683209, "dur": 22, "ph": "X", "name": "ReadAsync 265", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736189683232, "dur": 22, "ph": "X", "name": "ReadAsync 416", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736189683255, "dur": 20, "ph": "X", "name": "ReadAsync 412", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736189683277, "dur": 22, "ph": "X", "name": "ReadAsync 277", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736189683301, "dur": 22, "ph": "X", "name": "ReadAsync 370", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736189683324, "dur": 22, "ph": "X", "name": "ReadAsync 316", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736189683347, "dur": 22, "ph": "X", "name": "ReadAsync 255", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736189683370, "dur": 20, "ph": "X", "name": "ReadAsync 143", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736189683391, "dur": 22, "ph": "X", "name": "ReadAsync 95", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736189683414, "dur": 21, "ph": "X", "name": "ReadAsync 181", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736189683437, "dur": 21, "ph": "X", "name": "ReadAsync 278", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736189683459, "dur": 22, "ph": "X", "name": "ReadAsync 299", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736189683483, "dur": 21, "ph": "X", "name": "ReadAsync 376", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736189683505, "dur": 21, "ph": "X", "name": "ReadAsync 254", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736189683527, "dur": 21, "ph": "X", "name": "ReadAsync 274", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736189683550, "dur": 18, "ph": "X", "name": "ReadAsync 313", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736189683569, "dur": 18, "ph": "X", "name": "ReadAsync 209", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736189683600, "dur": 24, "ph": "X", "name": "ReadAsync 89", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736189683625, "dur": 125, "ph": "X", "name": "ReadAsync 303", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736189683755, "dur": 39, "ph": "X", "name": "ReadAsync 131", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736189683796, "dur": 1, "ph": "X", "name": "ProcessMessages 1575", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736189683798, "dur": 22, "ph": "X", "name": "ReadAsync 1575", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736189683821, "dur": 26, "ph": "X", "name": "ReadAsync 235", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736189683851, "dur": 45, "ph": "X", "name": "ReadAsync 237", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736189683897, "dur": 28, "ph": "X", "name": "ReadAsync 143", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736189683926, "dur": 1, "ph": "X", "name": "ProcessMessages 552", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736189683928, "dur": 23, "ph": "X", "name": "ReadAsync 552", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736189683953, "dur": 1, "ph": "X", "name": "ProcessMessages 245", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736189683955, "dur": 37, "ph": "X", "name": "ReadAsync 245", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736189683995, "dur": 36, "ph": "X", "name": "ReadAsync 182", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736189684034, "dur": 26, "ph": "X", "name": "ReadAsync 556", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736189684061, "dur": 22, "ph": "X", "name": "ReadAsync 260", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736189684085, "dur": 31, "ph": "X", "name": "ReadAsync 162", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736189684119, "dur": 1, "ph": "X", "name": "ProcessMessages 272", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736189684121, "dur": 35, "ph": "X", "name": "ReadAsync 272", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736189684157, "dur": 24, "ph": "X", "name": "ReadAsync 578", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736189684184, "dur": 33, "ph": "X", "name": "ReadAsync 171", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736189684221, "dur": 25, "ph": "X", "name": "ReadAsync 329", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736189684248, "dur": 19, "ph": "X", "name": "ReadAsync 491", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736189684268, "dur": 22, "ph": "X", "name": "ReadAsync 57", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736189684292, "dur": 19, "ph": "X", "name": "ReadAsync 179", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736189684312, "dur": 22, "ph": "X", "name": "ReadAsync 350", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736189684336, "dur": 18, "ph": "X", "name": "ReadAsync 442", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736189684356, "dur": 21, "ph": "X", "name": "ReadAsync 197", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736189684380, "dur": 29, "ph": "X", "name": "ReadAsync 202", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736189684411, "dur": 22, "ph": "X", "name": "ReadAsync 454", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736189684435, "dur": 18, "ph": "X", "name": "ReadAsync 503", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736189684455, "dur": 22, "ph": "X", "name": "ReadAsync 136", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736189684479, "dur": 24, "ph": "X", "name": "ReadAsync 231", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736189684504, "dur": 22, "ph": "X", "name": "ReadAsync 382", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736189684528, "dur": 21, "ph": "X", "name": "ReadAsync 443", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736189684550, "dur": 19, "ph": "X", "name": "ReadAsync 193", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736189684570, "dur": 23, "ph": "X", "name": "ReadAsync 40", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736189684595, "dur": 21, "ph": "X", "name": "ReadAsync 346", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736189684617, "dur": 21, "ph": "X", "name": "ReadAsync 253", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736189684640, "dur": 18, "ph": "X", "name": "ReadAsync 327", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736189684659, "dur": 21, "ph": "X", "name": "ReadAsync 140", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736189684682, "dur": 21, "ph": "X", "name": "ReadAsync 170", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736189684705, "dur": 22, "ph": "X", "name": "ReadAsync 306", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736189684728, "dur": 21, "ph": "X", "name": "ReadAsync 371", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736189684751, "dur": 25, "ph": "X", "name": "ReadAsync 237", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736189684780, "dur": 33, "ph": "X", "name": "ReadAsync 106", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736189684815, "dur": 21, "ph": "X", "name": "ReadAsync 487", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736189684838, "dur": 20, "ph": "X", "name": "ReadAsync 252", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736189684859, "dur": 34, "ph": "X", "name": "ReadAsync 197", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736189684895, "dur": 21, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736189684918, "dur": 26, "ph": "X", "name": "ReadAsync 296", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736189684945, "dur": 22, "ph": "X", "name": "ReadAsync 242", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736189684968, "dur": 19, "ph": "X", "name": "ReadAsync 392", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736189684989, "dur": 22, "ph": "X", "name": "ReadAsync 52", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736189685013, "dur": 22, "ph": "X", "name": "ReadAsync 151", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736189685037, "dur": 13, "ph": "X", "name": "ReadAsync 248", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736189685052, "dur": 22, "ph": "X", "name": "ReadAsync 58", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736189685076, "dur": 22, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736189685099, "dur": 22, "ph": "X", "name": "ReadAsync 343", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736189685122, "dur": 21, "ph": "X", "name": "ReadAsync 455", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736189685145, "dur": 18, "ph": "X", "name": "ReadAsync 315", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736189685165, "dur": 22, "ph": "X", "name": "ReadAsync 346", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736189685188, "dur": 22, "ph": "X", "name": "ReadAsync 295", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736189685211, "dur": 22, "ph": "X", "name": "ReadAsync 312", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736189685234, "dur": 18, "ph": "X", "name": "ReadAsync 245", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736189685254, "dur": 22, "ph": "X", "name": "ReadAsync 54", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736189685277, "dur": 20, "ph": "X", "name": "ReadAsync 225", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736189685299, "dur": 20, "ph": "X", "name": "ReadAsync 253", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736189685320, "dur": 21, "ph": "X", "name": "ReadAsync 386", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736189685343, "dur": 20, "ph": "X", "name": "ReadAsync 315", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736189685365, "dur": 21, "ph": "X", "name": "ReadAsync 379", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736189685387, "dur": 1, "ph": "X", "name": "ProcessMessages 330", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736189685389, "dur": 20, "ph": "X", "name": "ReadAsync 330", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736189685410, "dur": 22, "ph": "X", "name": "ReadAsync 184", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736189685434, "dur": 14, "ph": "X", "name": "ReadAsync 388", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736189685449, "dur": 18, "ph": "X", "name": "ReadAsync 178", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736189685469, "dur": 19, "ph": "X", "name": "ReadAsync 141", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736189685489, "dur": 24, "ph": "X", "name": "ReadAsync 94", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736189685514, "dur": 21, "ph": "X", "name": "ReadAsync 299", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736189685537, "dur": 22, "ph": "X", "name": "ReadAsync 237", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736189685560, "dur": 14, "ph": "X", "name": "ReadAsync 260", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736189685576, "dur": 18, "ph": "X", "name": "ReadAsync 358", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736189685595, "dur": 18, "ph": "X", "name": "ReadAsync 219", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736189685615, "dur": 21, "ph": "X", "name": "ReadAsync 21", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736189685637, "dur": 22, "ph": "X", "name": "ReadAsync 158", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736189685661, "dur": 22, "ph": "X", "name": "ReadAsync 247", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736189685684, "dur": 22, "ph": "X", "name": "ReadAsync 395", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736189685707, "dur": 21, "ph": "X", "name": "ReadAsync 343", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736189685730, "dur": 18, "ph": "X", "name": "ReadAsync 429", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736189685749, "dur": 21, "ph": "X", "name": "ReadAsync 167", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736189685772, "dur": 21, "ph": "X", "name": "ReadAsync 293", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736189685795, "dur": 22, "ph": "X", "name": "ReadAsync 386", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736189685819, "dur": 17, "ph": "X", "name": "ReadAsync 267", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736189685837, "dur": 17, "ph": "X", "name": "ReadAsync 144", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736189685855, "dur": 18, "ph": "X", "name": "ReadAsync 107", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736189685875, "dur": 21, "ph": "X", "name": "ReadAsync 235", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736189685898, "dur": 21, "ph": "X", "name": "ReadAsync 338", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736189685921, "dur": 17, "ph": "X", "name": "ReadAsync 292", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736189685940, "dur": 21, "ph": "X", "name": "ReadAsync 61", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736189685962, "dur": 18, "ph": "X", "name": "ReadAsync 201", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736189685981, "dur": 20, "ph": "X", "name": "ReadAsync 259", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736189686003, "dur": 21, "ph": "X", "name": "ReadAsync 351", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736189686025, "dur": 19, "ph": "X", "name": "ReadAsync 237", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736189686045, "dur": 22, "ph": "X", "name": "ReadAsync 102", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736189686069, "dur": 20, "ph": "X", "name": "ReadAsync 321", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736189686091, "dur": 22, "ph": "X", "name": "ReadAsync 384", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736189686115, "dur": 18, "ph": "X", "name": "ReadAsync 366", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736189686134, "dur": 21, "ph": "X", "name": "ReadAsync 306", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736189686157, "dur": 21, "ph": "X", "name": "ReadAsync 307", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736189686179, "dur": 21, "ph": "X", "name": "ReadAsync 290", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736189686202, "dur": 18, "ph": "X", "name": "ReadAsync 386", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736189686221, "dur": 1, "ph": "X", "name": "ProcessMessages 321", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736189686222, "dur": 20, "ph": "X", "name": "ReadAsync 321", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736189686243, "dur": 17, "ph": "X", "name": "ReadAsync 240", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736189686262, "dur": 21, "ph": "X", "name": "ReadAsync 40", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736189686285, "dur": 19, "ph": "X", "name": "ReadAsync 267", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736189686305, "dur": 21, "ph": "X", "name": "ReadAsync 137", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736189686327, "dur": 21, "ph": "X", "name": "ReadAsync 357", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736189686350, "dur": 18, "ph": "X", "name": "ReadAsync 299", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736189686369, "dur": 22, "ph": "X", "name": "ReadAsync 44", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736189686392, "dur": 20, "ph": "X", "name": "ReadAsync 310", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736189686414, "dur": 22, "ph": "X", "name": "ReadAsync 411", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736189686437, "dur": 17, "ph": "X", "name": "ReadAsync 308", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736189686456, "dur": 21, "ph": "X", "name": "ReadAsync 62", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736189686478, "dur": 28, "ph": "X", "name": "ReadAsync 201", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736189686508, "dur": 21, "ph": "X", "name": "ReadAsync 313", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736189686530, "dur": 18, "ph": "X", "name": "ReadAsync 449", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736189686549, "dur": 20, "ph": "X", "name": "ReadAsync 146", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736189686571, "dur": 18, "ph": "X", "name": "ReadAsync 40", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736189686590, "dur": 21, "ph": "X", "name": "ReadAsync 145", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736189686612, "dur": 21, "ph": "X", "name": "ReadAsync 269", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736189686635, "dur": 18, "ph": "X", "name": "ReadAsync 358", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736189686654, "dur": 17, "ph": "X", "name": "ReadAsync 245", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736189686673, "dur": 21, "ph": "X", "name": "ReadAsync 94", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736189686695, "dur": 20, "ph": "X", "name": "ReadAsync 238", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736189686716, "dur": 21, "ph": "X", "name": "ReadAsync 283", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736189686739, "dur": 17, "ph": "X", "name": "ReadAsync 399", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736189686757, "dur": 18, "ph": "X", "name": "ReadAsync 68", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736189686776, "dur": 18, "ph": "X", "name": "ReadAsync 184", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736189686795, "dur": 21, "ph": "X", "name": "ReadAsync 247", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736189686817, "dur": 20, "ph": "X", "name": "ReadAsync 354", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736189686839, "dur": 21, "ph": "X", "name": "ReadAsync 342", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736189686862, "dur": 21, "ph": "X", "name": "ReadAsync 367", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736189686884, "dur": 21, "ph": "X", "name": "ReadAsync 215", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736189686906, "dur": 18, "ph": "X", "name": "ReadAsync 318", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736189686925, "dur": 21, "ph": "X", "name": "ReadAsync 319", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736189686948, "dur": 20, "ph": "X", "name": "ReadAsync 295", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736189686970, "dur": 20, "ph": "X", "name": "ReadAsync 237", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736189686991, "dur": 22, "ph": "X", "name": "ReadAsync 103", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736189687014, "dur": 22, "ph": "X", "name": "ReadAsync 343", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736189687037, "dur": 22, "ph": "X", "name": "ReadAsync 407", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736189687061, "dur": 21, "ph": "X", "name": "ReadAsync 441", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736189687083, "dur": 21, "ph": "X", "name": "ReadAsync 388", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736189687105, "dur": 21, "ph": "X", "name": "ReadAsync 430", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736189687127, "dur": 21, "ph": "X", "name": "ReadAsync 320", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736189687150, "dur": 17, "ph": "X", "name": "ReadAsync 460", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736189687168, "dur": 17, "ph": "X", "name": "ReadAsync 109", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736189687187, "dur": 19, "ph": "X", "name": "ReadAsync 228", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736189687207, "dur": 21, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736189687230, "dur": 21, "ph": "X", "name": "ReadAsync 380", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736189687253, "dur": 21, "ph": "X", "name": "ReadAsync 301", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736189687275, "dur": 22, "ph": "X", "name": "ReadAsync 451", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736189687298, "dur": 21, "ph": "X", "name": "ReadAsync 398", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736189687321, "dur": 22, "ph": "X", "name": "ReadAsync 355", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736189687345, "dur": 21, "ph": "X", "name": "ReadAsync 310", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736189687367, "dur": 22, "ph": "X", "name": "ReadAsync 458", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736189687391, "dur": 17, "ph": "X", "name": "ReadAsync 234", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736189687409, "dur": 17, "ph": "X", "name": "ReadAsync 193", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736189687428, "dur": 18, "ph": "X", "name": "ReadAsync 191", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736189687447, "dur": 21, "ph": "X", "name": "ReadAsync 290", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736189687469, "dur": 21, "ph": "X", "name": "ReadAsync 359", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736189687492, "dur": 21, "ph": "X", "name": "ReadAsync 143", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736189687514, "dur": 17, "ph": "X", "name": "ReadAsync 240", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736189687532, "dur": 25, "ph": "X", "name": "ReadAsync 122", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736189687558, "dur": 18, "ph": "X", "name": "ReadAsync 373", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736189687578, "dur": 21, "ph": "X", "name": "ReadAsync 335", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736189687600, "dur": 17, "ph": "X", "name": "ReadAsync 298", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736189687618, "dur": 21, "ph": "X", "name": "ReadAsync 61", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736189687640, "dur": 22, "ph": "X", "name": "ReadAsync 248", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736189687663, "dur": 21, "ph": "X", "name": "ReadAsync 329", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736189687686, "dur": 18, "ph": "X", "name": "ReadAsync 515", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736189687705, "dur": 20, "ph": "X", "name": "ReadAsync 146", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736189687726, "dur": 21, "ph": "X", "name": "ReadAsync 129", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736189687749, "dur": 22, "ph": "X", "name": "ReadAsync 373", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736189687772, "dur": 22, "ph": "X", "name": "ReadAsync 384", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736189687795, "dur": 19, "ph": "X", "name": "ReadAsync 301", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736189687816, "dur": 23, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736189687840, "dur": 21, "ph": "X", "name": "ReadAsync 357", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736189687862, "dur": 18, "ph": "X", "name": "ReadAsync 192", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736189687881, "dur": 21, "ph": "X", "name": "ReadAsync 250", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736189687904, "dur": 17, "ph": "X", "name": "ReadAsync 247", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736189687922, "dur": 21, "ph": "X", "name": "ReadAsync 56", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736189687944, "dur": 21, "ph": "X", "name": "ReadAsync 240", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736189687966, "dur": 21, "ph": "X", "name": "ReadAsync 262", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736189687989, "dur": 21, "ph": "X", "name": "ReadAsync 382", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736189688011, "dur": 21, "ph": "X", "name": "ReadAsync 411", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736189688034, "dur": 21, "ph": "X", "name": "ReadAsync 384", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736189688057, "dur": 21, "ph": "X", "name": "ReadAsync 274", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736189688079, "dur": 21, "ph": "X", "name": "ReadAsync 351", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736189688102, "dur": 20, "ph": "X", "name": "ReadAsync 312", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736189688124, "dur": 17, "ph": "X", "name": "ReadAsync 237", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736189688142, "dur": 21, "ph": "X", "name": "ReadAsync 102", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736189688165, "dur": 21, "ph": "X", "name": "ReadAsync 258", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736189688187, "dur": 21, "ph": "X", "name": "ReadAsync 435", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736189688210, "dur": 21, "ph": "X", "name": "ReadAsync 233", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736189688232, "dur": 21, "ph": "X", "name": "ReadAsync 141", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736189688254, "dur": 18, "ph": "X", "name": "ReadAsync 267", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736189688274, "dur": 21, "ph": "X", "name": "ReadAsync 262", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736189688296, "dur": 21, "ph": "X", "name": "ReadAsync 354", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736189688319, "dur": 20, "ph": "X", "name": "ReadAsync 237", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736189688340, "dur": 21, "ph": "X", "name": "ReadAsync 171", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736189688362, "dur": 21, "ph": "X", "name": "ReadAsync 270", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736189688389, "dur": 19, "ph": "X", "name": "ReadAsync 431", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736189688410, "dur": 20, "ph": "X", "name": "ReadAsync 208", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736189688432, "dur": 17, "ph": "X", "name": "ReadAsync 53", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736189688450, "dur": 23, "ph": "X", "name": "ReadAsync 44", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736189688474, "dur": 21, "ph": "X", "name": "ReadAsync 205", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736189688497, "dur": 21, "ph": "X", "name": "ReadAsync 271", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736189688520, "dur": 18, "ph": "X", "name": "ReadAsync 407", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736189688539, "dur": 19, "ph": "X", "name": "ReadAsync 193", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736189688560, "dur": 22, "ph": "X", "name": "ReadAsync 105", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736189688583, "dur": 21, "ph": "X", "name": "ReadAsync 295", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736189688606, "dur": 18, "ph": "X", "name": "ReadAsync 319", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736189688626, "dur": 21, "ph": "X", "name": "ReadAsync 221", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736189688648, "dur": 20, "ph": "X", "name": "ReadAsync 305", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736189688670, "dur": 21, "ph": "X", "name": "ReadAsync 257", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736189688692, "dur": 21, "ph": "X", "name": "ReadAsync 250", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736189688715, "dur": 21, "ph": "X", "name": "ReadAsync 289", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736189688738, "dur": 17, "ph": "X", "name": "ReadAsync 253", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736189688757, "dur": 20, "ph": "X", "name": "ReadAsync 237", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736189688778, "dur": 20, "ph": "X", "name": "ReadAsync 89", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736189688800, "dur": 21, "ph": "X", "name": "ReadAsync 283", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736189688822, "dur": 21, "ph": "X", "name": "ReadAsync 309", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736189688845, "dur": 21, "ph": "X", "name": "ReadAsync 380", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736189688868, "dur": 21, "ph": "X", "name": "ReadAsync 363", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736189688891, "dur": 21, "ph": "X", "name": "ReadAsync 279", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736189688914, "dur": 18, "ph": "X", "name": "ReadAsync 363", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736189688933, "dur": 21, "ph": "X", "name": "ReadAsync 192", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736189688955, "dur": 21, "ph": "X", "name": "ReadAsync 289", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736189688978, "dur": 17, "ph": "X", "name": "ReadAsync 240", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736189688997, "dur": 17, "ph": "X", "name": "ReadAsync 97", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736189689015, "dur": 21, "ph": "X", "name": "ReadAsync 229", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736189689038, "dur": 21, "ph": "X", "name": "ReadAsync 222", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736189689061, "dur": 21, "ph": "X", "name": "ReadAsync 364", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736189689084, "dur": 21, "ph": "X", "name": "ReadAsync 303", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736189689107, "dur": 18, "ph": "X", "name": "ReadAsync 267", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736189689126, "dur": 22, "ph": "X", "name": "ReadAsync 241", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736189689149, "dur": 21, "ph": "X", "name": "ReadAsync 263", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736189689171, "dur": 14, "ph": "X", "name": "ReadAsync 247", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736189689186, "dur": 19, "ph": "X", "name": "ReadAsync 240", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736189689206, "dur": 22, "ph": "X", "name": "ReadAsync 40", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736189689230, "dur": 21, "ph": "X", "name": "ReadAsync 273", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736189689252, "dur": 29, "ph": "X", "name": "ReadAsync 428", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736189689282, "dur": 19, "ph": "X", "name": "ReadAsync 377", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736189689303, "dur": 22, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736189689326, "dur": 22, "ph": "X", "name": "ReadAsync 312", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736189689350, "dur": 17, "ph": "X", "name": "ReadAsync 409", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736189689369, "dur": 18, "ph": "X", "name": "ReadAsync 265", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736189689389, "dur": 17, "ph": "X", "name": "ReadAsync 237", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736189689407, "dur": 21, "ph": "X", "name": "ReadAsync 8", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736189689430, "dur": 18, "ph": "X", "name": "ReadAsync 305", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736189689449, "dur": 22, "ph": "X", "name": "ReadAsync 196", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736189689473, "dur": 22, "ph": "X", "name": "ReadAsync 337", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736189689496, "dur": 17, "ph": "X", "name": "ReadAsync 340", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736189689515, "dur": 22, "ph": "X", "name": "ReadAsync 60", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736189689538, "dur": 21, "ph": "X", "name": "ReadAsync 276", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736189689561, "dur": 21, "ph": "X", "name": "ReadAsync 374", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736189689583, "dur": 22, "ph": "X", "name": "ReadAsync 470", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736189689606, "dur": 22, "ph": "X", "name": "ReadAsync 410", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736189689630, "dur": 18, "ph": "X", "name": "ReadAsync 376", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736189689650, "dur": 22, "ph": "X", "name": "ReadAsync 322", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736189689673, "dur": 48, "ph": "X", "name": "ReadAsync 324", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736189689726, "dur": 2, "ph": "X", "name": "ProcessMessages 348", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736189689730, "dur": 51, "ph": "X", "name": "ReadAsync 348", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736189689784, "dur": 34, "ph": "X", "name": "ReadAsync 622", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736189689821, "dur": 32, "ph": "X", "name": "ReadAsync 477", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736189689856, "dur": 31, "ph": "X", "name": "ReadAsync 691", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736189689890, "dur": 1, "ph": "X", "name": "ProcessMessages 381", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736189689892, "dur": 40, "ph": "X", "name": "ReadAsync 381", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736189689935, "dur": 23, "ph": "X", "name": "ReadAsync 620", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736189689959, "dur": 14, "ph": "X", "name": "ReadAsync 343", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736189689975, "dur": 35, "ph": "X", "name": "ReadAsync 177", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736189690011, "dur": 24, "ph": "X", "name": "ReadAsync 63", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736189690036, "dur": 21, "ph": "X", "name": "ReadAsync 40", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736189690059, "dur": 21, "ph": "X", "name": "ReadAsync 428", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736189690082, "dur": 22, "ph": "X", "name": "ReadAsync 352", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736189690106, "dur": 20, "ph": "X", "name": "ReadAsync 480", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736189690128, "dur": 40, "ph": "X", "name": "ReadAsync 382", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736189690172, "dur": 31, "ph": "X", "name": "ReadAsync 345", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736189690205, "dur": 21, "ph": "X", "name": "ReadAsync 849", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736189690228, "dur": 21, "ph": "X", "name": "ReadAsync 89", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736189690252, "dur": 20, "ph": "X", "name": "ReadAsync 237", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736189690273, "dur": 21, "ph": "X", "name": "ReadAsync 111", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736189690296, "dur": 22, "ph": "X", "name": "ReadAsync 331", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736189690319, "dur": 29, "ph": "X", "name": "ReadAsync 287", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736189690349, "dur": 28, "ph": "X", "name": "ReadAsync 359", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736189690380, "dur": 1, "ph": "X", "name": "ProcessMessages 343", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736189690382, "dur": 40, "ph": "X", "name": "ReadAsync 343", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736189690424, "dur": 25, "ph": "X", "name": "ReadAsync 592", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736189690451, "dur": 22, "ph": "X", "name": "ReadAsync 387", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736189690475, "dur": 19, "ph": "X", "name": "ReadAsync 293", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736189690495, "dur": 21, "ph": "X", "name": "ReadAsync 8", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736189690517, "dur": 22, "ph": "X", "name": "ReadAsync 221", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736189690541, "dur": 22, "ph": "X", "name": "ReadAsync 408", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736189690565, "dur": 17, "ph": "X", "name": "ReadAsync 357", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736189690584, "dur": 21, "ph": "X", "name": "ReadAsync 134", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736189690606, "dur": 22, "ph": "X", "name": "ReadAsync 203", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736189690630, "dur": 21, "ph": "X", "name": "ReadAsync 341", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736189690653, "dur": 22, "ph": "X", "name": "ReadAsync 425", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736189690676, "dur": 18, "ph": "X", "name": "ReadAsync 456", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736189690696, "dur": 25, "ph": "X", "name": "ReadAsync 208", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736189690722, "dur": 18, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736189690741, "dur": 20, "ph": "X", "name": "ReadAsync 277", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736189690763, "dur": 20, "ph": "X", "name": "ReadAsync 292", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736189690785, "dur": 18, "ph": "X", "name": "ReadAsync 379", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736189690804, "dur": 14, "ph": "X", "name": "ReadAsync 275", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736189690820, "dur": 25, "ph": "X", "name": "ReadAsync 237", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736189690846, "dur": 25, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736189690874, "dur": 22, "ph": "X", "name": "ReadAsync 435", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736189690896, "dur": 21, "ph": "X", "name": "ReadAsync 375", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736189690919, "dur": 22, "ph": "X", "name": "ReadAsync 353", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736189690943, "dur": 29, "ph": "X", "name": "ReadAsync 387", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736189690975, "dur": 1, "ph": "X", "name": "ProcessMessages 318", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736189690977, "dur": 33, "ph": "X", "name": "ReadAsync 318", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736189691012, "dur": 14, "ph": "X", "name": "ReadAsync 675", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736189691027, "dur": 34, "ph": "X", "name": "ReadAsync 182", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736189691063, "dur": 29, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736189691095, "dur": 28, "ph": "X", "name": "ReadAsync 225", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736189691125, "dur": 23, "ph": "X", "name": "ReadAsync 254", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736189691150, "dur": 22, "ph": "X", "name": "ReadAsync 329", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736189691173, "dur": 21, "ph": "X", "name": "ReadAsync 397", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736189691196, "dur": 22, "ph": "X", "name": "ReadAsync 326", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736189691219, "dur": 20, "ph": "X", "name": "ReadAsync 288", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736189691241, "dur": 21, "ph": "X", "name": "ReadAsync 270", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736189691263, "dur": 20, "ph": "X", "name": "ReadAsync 377", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736189691285, "dur": 22, "ph": "X", "name": "ReadAsync 157", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736189691308, "dur": 17, "ph": "X", "name": "ReadAsync 240", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736189691326, "dur": 18, "ph": "X", "name": "ReadAsync 88", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736189691346, "dur": 21, "ph": "X", "name": "ReadAsync 223", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736189691368, "dur": 22, "ph": "X", "name": "ReadAsync 261", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736189691391, "dur": 21, "ph": "X", "name": "ReadAsync 337", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736189691414, "dur": 32, "ph": "X", "name": "ReadAsync 340", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736189691447, "dur": 22, "ph": "X", "name": "ReadAsync 291", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736189691471, "dur": 22, "ph": "X", "name": "ReadAsync 339", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736189691495, "dur": 21, "ph": "X", "name": "ReadAsync 348", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736189691517, "dur": 20, "ph": "X", "name": "ReadAsync 306", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736189691539, "dur": 24, "ph": "X", "name": "ReadAsync 237", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736189691564, "dur": 17, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736189691582, "dur": 17, "ph": "X", "name": "ReadAsync 89", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736189691601, "dur": 106, "ph": "X", "name": "ReadAsync 135", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736189691709, "dur": 33, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736189691744, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736189691747, "dur": 58, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736189691807, "dur": 1, "ph": "X", "name": "ProcessMessages 116", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736189691808, "dur": 27, "ph": "X", "name": "ReadAsync 116", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736189691838, "dur": 31, "ph": "X", "name": "ReadAsync 108", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736189691871, "dur": 1, "ph": "X", "name": "ProcessMessages 84", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736189691874, "dur": 34, "ph": "X", "name": "ReadAsync 84", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736189691912, "dur": 28, "ph": "X", "name": "ReadAsync 112", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736189691942, "dur": 24, "ph": "X", "name": "ReadAsync 156", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736189691969, "dur": 24, "ph": "X", "name": "ReadAsync 132", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736189691996, "dur": 24, "ph": "X", "name": "ReadAsync 108", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736189692022, "dur": 40, "ph": "X", "name": "ReadAsync 116", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736189692067, "dur": 1, "ph": "X", "name": "ProcessMessages 108", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736189692070, "dur": 44, "ph": "X", "name": "ReadAsync 108", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736189692115, "dur": 1, "ph": "X", "name": "ProcessMessages 196", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736189692117, "dur": 37, "ph": "X", "name": "ReadAsync 196", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736189692159, "dur": 1, "ph": "X", "name": "ProcessMessages 28", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736189692163, "dur": 49, "ph": "X", "name": "ReadAsync 28", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736189692214, "dur": 1, "ph": "X", "name": "ProcessMessages 228", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736189692216, "dur": 30, "ph": "X", "name": "ReadAsync 228", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736189692248, "dur": 23, "ph": "X", "name": "ReadAsync 140", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736189692273, "dur": 1, "ph": "X", "name": "ProcessMessages 128", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736189692274, "dur": 31, "ph": "X", "name": "ReadAsync 128", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736189692309, "dur": 28, "ph": "X", "name": "ReadAsync 128", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736189692339, "dur": 23, "ph": "X", "name": "ReadAsync 132", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736189692365, "dur": 24, "ph": "X", "name": "ReadAsync 144", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736189692391, "dur": 1, "ph": "X", "name": "ProcessMessages 112", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736189692392, "dur": 32, "ph": "X", "name": "ReadAsync 112", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736189692426, "dur": 27, "ph": "X", "name": "ReadAsync 144", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736189692455, "dur": 27, "ph": "X", "name": "ReadAsync 96", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736189692484, "dur": 1, "ph": "X", "name": "ProcessMessages 144", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736189692486, "dur": 21, "ph": "X", "name": "ReadAsync 144", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736189692510, "dur": 27, "ph": "X", "name": "ReadAsync 160", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736189692539, "dur": 22, "ph": "X", "name": "ReadAsync 108", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736189692563, "dur": 25, "ph": "X", "name": "ReadAsync 144", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736189692590, "dur": 21, "ph": "X", "name": "ReadAsync 84", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736189692612, "dur": 1, "ph": "X", "name": "ProcessMessages 128", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736189692614, "dur": 24, "ph": "X", "name": "ReadAsync 128", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736189692640, "dur": 29, "ph": "X", "name": "ReadAsync 112", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736189692672, "dur": 1, "ph": "X", "name": "ProcessMessages 96", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736189692673, "dur": 20, "ph": "X", "name": "ReadAsync 96", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736189692696, "dur": 16, "ph": "X", "name": "ReadAsync 128", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736189692714, "dur": 27, "ph": "X", "name": "ReadAsync 92", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736189692745, "dur": 29, "ph": "X", "name": "ReadAsync 96", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736189692777, "dur": 22, "ph": "X", "name": "ReadAsync 164", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736189692802, "dur": 30, "ph": "X", "name": "ReadAsync 128", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736189692834, "dur": 25, "ph": "X", "name": "ReadAsync 112", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736189692862, "dur": 28, "ph": "X", "name": "ReadAsync 144", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736189692891, "dur": 19, "ph": "X", "name": "ReadAsync 140", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736189692914, "dur": 26, "ph": "X", "name": "ReadAsync 112", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736189692943, "dur": 25, "ph": "X", "name": "ReadAsync 112", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736189692971, "dur": 25, "ph": "X", "name": "ReadAsync 116", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736189692999, "dur": 24, "ph": "X", "name": "ReadAsync 92", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736189693025, "dur": 28, "ph": "X", "name": "ReadAsync 128", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736189693055, "dur": 1, "ph": "X", "name": "ProcessMessages 128", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736189693057, "dur": 23, "ph": "X", "name": "ReadAsync 128", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736189693081, "dur": 1, "ph": "X", "name": "ProcessMessages 148", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736189693082, "dur": 23, "ph": "X", "name": "ReadAsync 148", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736189693107, "dur": 27, "ph": "X", "name": "ReadAsync 96", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736189693136, "dur": 24, "ph": "X", "name": "ReadAsync 124", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736189693162, "dur": 23, "ph": "X", "name": "ReadAsync 128", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736189693187, "dur": 24, "ph": "X", "name": "ReadAsync 84", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736189693214, "dur": 23, "ph": "X", "name": "ReadAsync 96", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736189693240, "dur": 22, "ph": "X", "name": "ReadAsync 112", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736189693264, "dur": 22, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736189693289, "dur": 62, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736189693354, "dur": 25, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736189693380, "dur": 29, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736189693412, "dur": 1, "ph": "X", "name": "ProcessMessages 100", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736189693414, "dur": 33, "ph": "X", "name": "ReadAsync 100", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736189693448, "dur": 1, "ph": "X", "name": "ProcessMessages 144", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736189693450, "dur": 23, "ph": "X", "name": "ReadAsync 144", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736189693474, "dur": 2, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736189693477, "dur": 409, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736189693888, "dur": 24, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736189693914, "dur": 757, "ph": "X", "name": "ProcessMessages 8", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736189694673, "dur": 34, "ph": "X", "name": "ReadAsync 8", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736189694708, "dur": 2486, "ph": "X", "name": "ProcessMessages 46", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736189697197, "dur": 49, "ph": "X", "name": "ReadAsync 46", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736189697251, "dur": 1, "ph": "X", "name": "ProcessMessages 112", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736189697253, "dur": 1269, "ph": "X", "name": "ReadAsync 112", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736189698527, "dur": 37, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736189698568, "dur": 33, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736189698604, "dur": 1, "ph": "X", "name": "ProcessMessages 36", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736189698607, "dur": 30, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736189698639, "dur": 171, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736189698812, "dur": 30, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736189698844, "dur": 86, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736189698933, "dur": 32, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736189698968, "dur": 23, "ph": "X", "name": "ReadAsync 128", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736189698993, "dur": 21, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736189699019, "dur": 21, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736189699043, "dur": 24, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736189699069, "dur": 20, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736189699091, "dur": 152, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736189699246, "dur": 20, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736189699268, "dur": 63, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736189699334, "dur": 19, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736189699355, "dur": 23, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736189699380, "dur": 1, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736189699382, "dur": 28, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736189699413, "dur": 21, "ph": "X", "name": "ReadAsync 76", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736189699436, "dur": 29, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736189699468, "dur": 29, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736189699499, "dur": 20, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736189699521, "dur": 20, "ph": "X", "name": "ReadAsync 52", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736189699543, "dur": 26, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736189699571, "dur": 23, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736189699596, "dur": 20, "ph": "X", "name": "ReadAsync 92", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736189699619, "dur": 19, "ph": "X", "name": "ReadAsync 52", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736189699639, "dur": 48, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736189699688, "dur": 22, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736189699713, "dur": 22, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736189699736, "dur": 71, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736189699810, "dur": 26, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736189699838, "dur": 101, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736189699940, "dur": 22, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736189699965, "dur": 52, "ph": "X", "name": "ReadAsync 68", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736189700019, "dur": 25, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736189700047, "dur": 26, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736189700075, "dur": 22, "ph": "X", "name": "ReadAsync 96", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736189700099, "dur": 46, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736189700147, "dur": 23, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736189700172, "dur": 26, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736189700199, "dur": 111, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736189700312, "dur": 23, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736189700339, "dur": 24, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736189700365, "dur": 23, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736189700391, "dur": 25, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736189700417, "dur": 23, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736189700444, "dur": 48, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736189700497, "dur": 34, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736189700534, "dur": 27, "ph": "X", "name": "ReadAsync 52", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736189700565, "dur": 52, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736189700619, "dur": 24, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736189700647, "dur": 68, "ph": "X", "name": "ReadAsync 52", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736189700717, "dur": 26, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736189700744, "dur": 31, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736189700778, "dur": 23, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736189700801, "dur": 2, "ph": "X", "name": "ProcessMessages 8", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736189700803, "dur": 19, "ph": "X", "name": "ReadAsync 8", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736189700824, "dur": 20, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736189700846, "dur": 27, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736189700874, "dur": 21, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736189700897, "dur": 18, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736189700916, "dur": 38, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736189700958, "dur": 23, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736189700982, "dur": 115, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736189701099, "dur": 21, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736189701122, "dur": 23, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736189701146, "dur": 49, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736189701198, "dur": 27, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736189701227, "dur": 203, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736189701434, "dur": 84, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736189701522, "dur": 1, "ph": "X", "name": "ProcessMessages 68", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736189701523, "dur": 41, "ph": "X", "name": "ReadAsync 68", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736189701567, "dur": 39, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736189701610, "dur": 44, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736189701659, "dur": 26, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736189701687, "dur": 90, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736189701781, "dur": 45, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736189701829, "dur": 27, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736189701860, "dur": 1, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736189701862, "dur": 41, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736189701907, "dur": 32, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736189701942, "dur": 29, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736189701975, "dur": 30, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736189702006, "dur": 163, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736189702174, "dur": 29, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736189702206, "dur": 22, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736189702229, "dur": 54, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736189702287, "dur": 25, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736189702314, "dur": 172, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736189702487, "dur": 19, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736189702507, "dur": 27, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736189702536, "dur": 29, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736189702566, "dur": 23, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736189702591, "dur": 51, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736189702643, "dur": 22, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736189702667, "dur": 42, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736189702710, "dur": 40, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736189702755, "dur": 33, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736189702791, "dur": 30, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736189702822, "dur": 22, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736189702846, "dur": 82, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736189702929, "dur": 29, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736189702962, "dur": 670, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736189703637, "dur": 29, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736189703667, "dur": 2, "ph": "X", "name": "ProcessMessages 8", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736189703670, "dur": 273324, "ph": "X", "name": "ReadAsync 8", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736189977008, "dur": 3, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736189977013, "dur": 46, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736189977062, "dur": 33, "ph": "X", "name": "ProcessMessages 192", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736189977097, "dur": 4596, "ph": "X", "name": "ReadAsync 192", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736189981705, "dur": 3, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736189981710, "dur": 42, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736189981753, "dur": 1, "ph": "X", "name": "ProcessMessages 36", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736189981755, "dur": 46, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736189981807, "dur": 40, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736189981851, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736189981853, "dur": 153, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736189982010, "dur": 27, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736189982040, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736189982042, "dur": 788, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736189982835, "dur": 59, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736189982897, "dur": 32, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736189982934, "dur": 45, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736189982983, "dur": 1, "ph": "X", "name": "ProcessMessages 36", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736189982984, "dur": 38, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736189983025, "dur": 40, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736189983069, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736189983071, "dur": 105, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736189983181, "dur": 30, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736189983213, "dur": 206, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736189983422, "dur": 32, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736189983457, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736189983459, "dur": 294, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736189983757, "dur": 34, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736189983795, "dur": 359, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736189984157, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736189984159, "dur": 46, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736189984209, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736189984210, "dur": 58, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736189984274, "dur": 45, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736189984322, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736189984324, "dur": 46, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736189984373, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736189984375, "dur": 653, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736189985033, "dur": 41, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736189985077, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736189985079, "dur": 239, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736189985322, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736189985324, "dur": 39, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736189985367, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736189985369, "dur": 86, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736189985461, "dur": 44, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736189985508, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736189985510, "dur": 48, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736189985562, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736189985564, "dur": 67, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736189985636, "dur": 41, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736189985680, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736189985682, "dur": 621, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736189986308, "dur": 41, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736189986352, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736189986354, "dur": 154, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736189986514, "dur": 38, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736189986555, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736189986557, "dur": 78, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736189986640, "dur": 42, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736189986685, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736189986688, "dur": 38, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736189986731, "dur": 25, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736189986758, "dur": 434, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736189987197, "dur": 46, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736189987246, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736189987248, "dur": 125, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736189987376, "dur": 35, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736189987415, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736189987417, "dur": 406, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736189987826, "dur": 39, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736189987867, "dur": 42, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736189987913, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736189987915, "dur": 41, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736189987960, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736189987961, "dur": 36, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736189988002, "dur": 384, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736189988392, "dur": 38, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736189988434, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736189988435, "dur": 336, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736189988774, "dur": 33, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736189988811, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736189988813, "dur": 256, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736189989074, "dur": 40, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736189989118, "dur": 1, "ph": "X", "name": "ProcessMessages 68", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736189989120, "dur": 41, "ph": "X", "name": "ReadAsync 68", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736189989165, "dur": 1, "ph": "X", "name": "ProcessMessages 96", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736189989167, "dur": 43, "ph": "X", "name": "ReadAsync 96", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736189989213, "dur": 1, "ph": "X", "name": "ProcessMessages 124", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736189989215, "dur": 44, "ph": "X", "name": "ReadAsync 124", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736189989262, "dur": 1, "ph": "X", "name": "ProcessMessages 144", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736189989265, "dur": 44, "ph": "X", "name": "ReadAsync 144", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736189989312, "dur": 1, "ph": "X", "name": "ProcessMessages 64", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736189989314, "dur": 38, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736189989359, "dur": 1, "ph": "X", "name": "ProcessMessages 112", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736189989361, "dur": 41, "ph": "X", "name": "ReadAsync 112", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736189989406, "dur": 1, "ph": "X", "name": "ProcessMessages 132", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736189989408, "dur": 36, "ph": "X", "name": "ReadAsync 132", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736189989447, "dur": 31, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736189989482, "dur": 1, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736189989484, "dur": 50, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736189989536, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736189989538, "dur": 139, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736189989694, "dur": 1, "ph": "X", "name": "ProcessMessages 60", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736189989697, "dur": 90, "ph": "X", "name": "ReadAsync 60", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736189989790, "dur": 3, "ph": "X", "name": "ProcessMessages 564", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736189989794, "dur": 44, "ph": "X", "name": "ReadAsync 564", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736189989842, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736189989844, "dur": 158, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736189990006, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736189990008, "dur": 42, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736189990053, "dur": 1, "ph": "X", "name": "ProcessMessages 112", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736189990055, "dur": 429, "ph": "X", "name": "ReadAsync 112", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736189990489, "dur": 37, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736189990530, "dur": 1, "ph": "X", "name": "ProcessMessages 52", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736189990532, "dur": 208, "ph": "X", "name": "ReadAsync 52", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736189990745, "dur": 35, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736189990783, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736189990784, "dur": 645, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736189991434, "dur": 73, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736189991510, "dur": 1, "ph": "X", "name": "ProcessMessages 36", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736189991512, "dur": 40, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736189991556, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736189991557, "dur": 29, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736189991589, "dur": 24, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736189991618, "dur": 340, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736189991964, "dur": 34, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736189992001, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736189992003, "dur": 514, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736189992522, "dur": 44, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736189992570, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736189992572, "dur": 39, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736189992615, "dur": 37, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736189992655, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736189992656, "dur": 977, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736189993638, "dur": 47, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736189993689, "dur": 1, "ph": "X", "name": "ProcessMessages 36", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736189993691, "dur": 78586, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736190072289, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736190072292, "dur": 54, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736190072348, "dur": 3889, "ph": "X", "name": "ProcessMessages 979", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736190076242, "dur": 4112, "ph": "X", "name": "ReadAsync 979", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736190080362, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736190080366, "dur": 58, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736190080429, "dur": 3, "ph": "X", "name": "ProcessMessages 8", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736190080433, "dur": 70744, "ph": "X", "name": "ReadAsync 8", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736190151187, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736190151191, "dur": 31, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736190151223, "dur": 2, "ph": "X", "name": "ProcessMessages 8", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736190151226, "dur": 222049, "ph": "X", "name": "ReadAsync 8", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736190373285, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736190373289, "dur": 55, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736190373347, "dur": 530, "ph": "X", "name": "ProcessMessages 353", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736190373878, "dur": 10811, "ph": "X", "name": "ReadAsync 353", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736190384697, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736190384699, "dur": 47, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736190384749, "dur": 3, "ph": "X", "name": "ProcessMessages 8", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736190384753, "dur": 36300, "ph": "X", "name": "ReadAsync 8", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736190421064, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736190421068, "dur": 32, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736190421101, "dur": 25, "ph": "X", "name": "ProcessMessages 416", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736190421127, "dur": 6063, "ph": "X", "name": "ReadAsync 416", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736190427200, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736190427203, "dur": 34, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736190427239, "dur": 3, "ph": "X", "name": "ProcessMessages 8", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736190427242, "dur": 1635, "ph": "X", "name": "ReadAsync 8", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736190428884, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736190428887, "dur": 41, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736190428931, "dur": 23, "ph": "X", "name": "ProcessMessages 34", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736190428956, "dur": 69580, "ph": "X", "name": "ReadAsync 34", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736190498547, "dur": 3, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736190498551, "dur": 66, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736190498623, "dur": 4, "ph": "X", "name": "ProcessMessages 8", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736190498629, "dur": 615, "ph": "X", "name": "ReadAsync 8", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736190499248, "dur": 26, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736190499276, "dur": 21, "ph": "X", "name": "ProcessMessages 34", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736190499301, "dur": 110482, "ph": "X", "name": "ReadAsync 34", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736190609794, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736190609798, "dur": 44, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736190609844, "dur": 28, "ph": "X", "name": "ProcessMessages 422", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736190609872, "dur": 5244, "ph": "X", "name": "ReadAsync 422", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736190615126, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736190615129, "dur": 31, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736190615161, "dur": 3, "ph": "X", "name": "ProcessMessages 8", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736190615165, "dur": 616, "ph": "X", "name": "ReadAsync 8", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736190615788, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736190615791, "dur": 37, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736190615831, "dur": 28, "ph": "X", "name": "ProcessMessages 34", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736190615860, "dur": 10566, "ph": "X", "name": "ReadAsync 34", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736190626433, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736190626435, "dur": 44, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736190626481, "dur": 3, "ph": "X", "name": "ProcessMessages 8", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736190626484, "dur": 597, "ph": "X", "name": "ReadAsync 8", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736190627088, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736190627089, "dur": 38, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736190627129, "dur": 25, "ph": "X", "name": "ProcessMessages 50", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736190627156, "dur": 481, "ph": "X", "name": "ReadAsync 50", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736190627644, "dur": 34, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754736190627681, "dur": 7083, "ph": "X", "name": "ReadAsync 13", "args": {}}, {"pid": 3100, "tid": 24780, "ts": 1754736190643579, "dur": 927, "ph": "X", "name": "ReadEntireBinlogFromIpcAsync", "args": {}}, {"pid": 3100, "tid": 17179869184, "ph": "M", "name": "thread_name", "args": {"name": "WaitForBuildProgramInputDataBeingWrittenAndSendDagReadyMessageAsync"}}, {"pid": 3100, "tid": 17179869184, "ts": 1754736189436684, "dur": 7, "ph": "X", "name": "await writeBuildProgramInputDataTask", "args": {}}, {"pid": 3100, "tid": 17179869184, "ts": 1754736189436692, "dur": 135903, "ph": "X", "name": "WritePipe.WaitConnectionAsync", "args": {}}, {"pid": 3100, "tid": 17179869184, "ts": 1754736189572596, "dur": 36, "ph": "X", "name": "WriteDagReadyMessage", "args": {}}, {"pid": 3100, "tid": 24780, "ts": 1754736190644507, "dur": 3, "ph": "X", "name": "WaitForBuildProgramInputDataBeingWrittenAndSendDagReadyMessageAsync", "args": {}}, {"pid": 3100, "tid": 1, "ph": "M", "name": "thread_name", "args": {"name": ""}}, {"pid": 3100, "tid": 1, "ts": 1754736188541875, "dur": 3249, "ph": "X", "name": "<Add>b__0", "args": {}}, {"pid": 3100, "tid": 1, "ts": 1754736188545129, "dur": 21700, "ph": "X", "name": "<Add>b__0", "args": {}}, {"pid": 3100, "tid": 1, "ts": 1754736188566837, "dur": 32001, "ph": "X", "name": "WriteJson", "args": {}}, {"pid": 3100, "tid": 24780, "ts": 1754736190644511, "dur": 3, "ph": "X", "name": "", "args": {}}, {"pid": 3100, "tid": 12884901888, "ph": "M", "name": "thread_name", "args": {"name": "ReadEntireBinlogFromIpcAsync"}}, {"pid": 3100, "tid": 12884901888, "ts": 1754736188540812, "dur": 144104, "ph": "X", "name": "WaitForConnectionAsync", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754736188684918, "dur": 29352, "ph": "X", "name": "UpdateFromStreamAsync", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754736188685653, "dur": 1133, "ph": "X", "name": "ReadAsync 0", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754736188686792, "dur": 770, "ph": "X", "name": "ProcessMessages 5107", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754736188687564, "dur": 133, "ph": "X", "name": "ReadAsync 5107", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754736188687701, "dur": 5, "ph": "X", "name": "ProcessMessages 15296", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754736188687706, "dur": 29, "ph": "X", "name": "ReadAsync 15296", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754736188687738, "dur": 21, "ph": "X", "name": "ReadAsync 729", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754736188687761, "dur": 23, "ph": "X", "name": "ReadAsync 369", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754736188687787, "dur": 23, "ph": "X", "name": "ReadAsync 338", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754736188687812, "dur": 22, "ph": "X", "name": "ReadAsync 399", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754736188687836, "dur": 18, "ph": "X", "name": "ReadAsync 271", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754736188687856, "dur": 21, "ph": "X", "name": "ReadAsync 94", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754736188687879, "dur": 22, "ph": "X", "name": "ReadAsync 321", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754736188687903, "dur": 22, "ph": "X", "name": "ReadAsync 342", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754736188687926, "dur": 27, "ph": "X", "name": "ReadAsync 500", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754736188687955, "dur": 23, "ph": "X", "name": "ReadAsync 466", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754736188687979, "dur": 21, "ph": "X", "name": "ReadAsync 459", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754736188688002, "dur": 22, "ph": "X", "name": "ReadAsync 503", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754736188688026, "dur": 23, "ph": "X", "name": "ReadAsync 506", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754736188688050, "dur": 23, "ph": "X", "name": "ReadAsync 452", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754736188688075, "dur": 23, "ph": "X", "name": "ReadAsync 414", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754736188688099, "dur": 22, "ph": "X", "name": "ReadAsync 488", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754736188688123, "dur": 21, "ph": "X", "name": "ReadAsync 436", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754736188688145, "dur": 21, "ph": "X", "name": "ReadAsync 405", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754736188688168, "dur": 22, "ph": "X", "name": "ReadAsync 289", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754736188688192, "dur": 21, "ph": "X", "name": "ReadAsync 416", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754736188688215, "dur": 22, "ph": "X", "name": "ReadAsync 327", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754736188688238, "dur": 21, "ph": "X", "name": "ReadAsync 462", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754736188688261, "dur": 21, "ph": "X", "name": "ReadAsync 481", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754736188688283, "dur": 22, "ph": "X", "name": "ReadAsync 238", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754736188688306, "dur": 23, "ph": "X", "name": "ReadAsync 472", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754736188688331, "dur": 22, "ph": "X", "name": "ReadAsync 371", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754736188688355, "dur": 21, "ph": "X", "name": "ReadAsync 448", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754736188688377, "dur": 29, "ph": "X", "name": "ReadAsync 502", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754736188688408, "dur": 18, "ph": "X", "name": "ReadAsync 551", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754736188688428, "dur": 24, "ph": "X", "name": "ReadAsync 219", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754736188688453, "dur": 23, "ph": "X", "name": "ReadAsync 343", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754736188688478, "dur": 22, "ph": "X", "name": "ReadAsync 287", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754736188688502, "dur": 20, "ph": "X", "name": "ReadAsync 318", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754736188688523, "dur": 18, "ph": "X", "name": "ReadAsync 302", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754736188688543, "dur": 32, "ph": "X", "name": "ReadAsync 286", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754736188688580, "dur": 32, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754736188688614, "dur": 31, "ph": "X", "name": "ReadAsync 660", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754736188688647, "dur": 29, "ph": "X", "name": "ReadAsync 446", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754736188688677, "dur": 21, "ph": "X", "name": "ReadAsync 371", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754736188688700, "dur": 16, "ph": "X", "name": "ReadAsync 259", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754736188688717, "dur": 18, "ph": "X", "name": "ReadAsync 290", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754736188688736, "dur": 28, "ph": "X", "name": "ReadAsync 198", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754736188688768, "dur": 27, "ph": "X", "name": "ReadAsync 170", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754736188688796, "dur": 23, "ph": "X", "name": "ReadAsync 315", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754736188688821, "dur": 23, "ph": "X", "name": "ReadAsync 283", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754736188688846, "dur": 22, "ph": "X", "name": "ReadAsync 336", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754736188688869, "dur": 22, "ph": "X", "name": "ReadAsync 286", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754736188688892, "dur": 21, "ph": "X", "name": "ReadAsync 137", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754736188688915, "dur": 22, "ph": "X", "name": "ReadAsync 213", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754736188688939, "dur": 22, "ph": "X", "name": "ReadAsync 266", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754736188688963, "dur": 65, "ph": "X", "name": "ReadAsync 61", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754736188689029, "dur": 22, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754736188689052, "dur": 22, "ph": "X", "name": "ReadAsync 303", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754736188689076, "dur": 22, "ph": "X", "name": "ReadAsync 283", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754736188689100, "dur": 22, "ph": "X", "name": "ReadAsync 332", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754736188689123, "dur": 23, "ph": "X", "name": "ReadAsync 290", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754736188689148, "dur": 21, "ph": "X", "name": "ReadAsync 245", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754736188689170, "dur": 17, "ph": "X", "name": "ReadAsync 282", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754736188689188, "dur": 17, "ph": "X", "name": "ReadAsync 150", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754736188689206, "dur": 23, "ph": "X", "name": "ReadAsync 119", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754736188689231, "dur": 22, "ph": "X", "name": "ReadAsync 240", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754736188689254, "dur": 1, "ph": "X", "name": "ProcessMessages 302", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754736188689255, "dur": 45, "ph": "X", "name": "ReadAsync 302", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754736188689301, "dur": 21, "ph": "X", "name": "ReadAsync 130", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754736188689325, "dur": 22, "ph": "X", "name": "ReadAsync 221", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754736188689348, "dur": 22, "ph": "X", "name": "ReadAsync 263", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754736188689371, "dur": 22, "ph": "X", "name": "ReadAsync 289", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754736188689394, "dur": 20, "ph": "X", "name": "ReadAsync 252", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754736188689415, "dur": 26, "ph": "X", "name": "ReadAsync 77", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754736188689442, "dur": 20, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754736188689464, "dur": 17, "ph": "X", "name": "ReadAsync 241", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754736188689482, "dur": 21, "ph": "X", "name": "ReadAsync 133", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754736188689505, "dur": 22, "ph": "X", "name": "ReadAsync 264", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754736188689529, "dur": 21, "ph": "X", "name": "ReadAsync 394", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754736188689552, "dur": 23, "ph": "X", "name": "ReadAsync 328", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754736188689576, "dur": 22, "ph": "X", "name": "ReadAsync 331", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754736188689600, "dur": 23, "ph": "X", "name": "ReadAsync 437", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754736188689624, "dur": 22, "ph": "X", "name": "ReadAsync 306", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754736188689648, "dur": 38, "ph": "X", "name": "ReadAsync 353", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754736188689691, "dur": 2, "ph": "X", "name": "ProcessMessages 280", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754736188689695, "dur": 64, "ph": "X", "name": "ReadAsync 280", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754736188689761, "dur": 1, "ph": "X", "name": "ProcessMessages 841", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754736188689764, "dur": 31, "ph": "X", "name": "ReadAsync 841", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754736188689797, "dur": 25, "ph": "X", "name": "ReadAsync 75", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754736188689824, "dur": 20, "ph": "X", "name": "ReadAsync 304", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754736188689845, "dur": 22, "ph": "X", "name": "ReadAsync 315", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754736188689869, "dur": 40, "ph": "X", "name": "ReadAsync 293", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754736188689914, "dur": 1, "ph": "X", "name": "ProcessMessages 249", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754736188689917, "dur": 45, "ph": "X", "name": "ReadAsync 249", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754736188689967, "dur": 30, "ph": "X", "name": "ReadAsync 781", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754736188690001, "dur": 45, "ph": "X", "name": "ReadAsync 21", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754736188690048, "dur": 1, "ph": "X", "name": "ProcessMessages 425", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754736188690050, "dur": 35, "ph": "X", "name": "ReadAsync 425", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754736188690087, "dur": 29, "ph": "X", "name": "ReadAsync 736", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754736188690117, "dur": 29, "ph": "X", "name": "ReadAsync 244", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754736188690150, "dur": 33, "ph": "X", "name": "ReadAsync 397", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754736188690185, "dur": 36, "ph": "X", "name": "ReadAsync 161", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754736188690224, "dur": 24, "ph": "X", "name": "ReadAsync 619", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754736188690249, "dur": 24, "ph": "X", "name": "ReadAsync 406", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754736188690275, "dur": 23, "ph": "X", "name": "ReadAsync 319", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754736188690300, "dur": 22, "ph": "X", "name": "ReadAsync 328", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754736188690324, "dur": 20, "ph": "X", "name": "ReadAsync 291", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754736188690345, "dur": 22, "ph": "X", "name": "ReadAsync 222", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754736188690369, "dur": 23, "ph": "X", "name": "ReadAsync 275", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754736188690393, "dur": 24, "ph": "X", "name": "ReadAsync 322", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754736188690418, "dur": 21, "ph": "X", "name": "ReadAsync 283", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754736188690441, "dur": 30, "ph": "X", "name": "ReadAsync 295", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754736188690474, "dur": 1, "ph": "X", "name": "ProcessMessages 346", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754736188690475, "dur": 30, "ph": "X", "name": "ReadAsync 346", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754736188690507, "dur": 30, "ph": "X", "name": "ReadAsync 391", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754736188690541, "dur": 30, "ph": "X", "name": "ReadAsync 279", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754736188690572, "dur": 28, "ph": "X", "name": "ReadAsync 514", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754736188690601, "dur": 23, "ph": "X", "name": "ReadAsync 310", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754736188690626, "dur": 27, "ph": "X", "name": "ReadAsync 269", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754736188690655, "dur": 23, "ph": "X", "name": "ReadAsync 346", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754736188690679, "dur": 24, "ph": "X", "name": "ReadAsync 156", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754736188690705, "dur": 21, "ph": "X", "name": "ReadAsync 531", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754736188690728, "dur": 20, "ph": "X", "name": "ReadAsync 390", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754736188690750, "dur": 21, "ph": "X", "name": "ReadAsync 313", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754736188690773, "dur": 22, "ph": "X", "name": "ReadAsync 335", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754736188690796, "dur": 28, "ph": "X", "name": "ReadAsync 342", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754736188690828, "dur": 40, "ph": "X", "name": "ReadAsync 278", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754736188690871, "dur": 31, "ph": "X", "name": "ReadAsync 551", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754736188690904, "dur": 24, "ph": "X", "name": "ReadAsync 438", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754736188690931, "dur": 23, "ph": "X", "name": "ReadAsync 21", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754736188690956, "dur": 24, "ph": "X", "name": "ReadAsync 379", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754736188690980, "dur": 1, "ph": "X", "name": "ProcessMessages 383", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754736188690983, "dur": 22, "ph": "X", "name": "ReadAsync 383", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754736188691006, "dur": 20, "ph": "X", "name": "ReadAsync 368", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754736188691028, "dur": 20, "ph": "X", "name": "ReadAsync 21", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754736188691050, "dur": 22, "ph": "X", "name": "ReadAsync 291", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754736188691073, "dur": 21, "ph": "X", "name": "ReadAsync 448", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754736188691096, "dur": 22, "ph": "X", "name": "ReadAsync 443", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754736188691119, "dur": 21, "ph": "X", "name": "ReadAsync 309", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754736188691142, "dur": 22, "ph": "X", "name": "ReadAsync 346", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754736188691165, "dur": 21, "ph": "X", "name": "ReadAsync 408", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754736188691188, "dur": 21, "ph": "X", "name": "ReadAsync 312", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754736188691210, "dur": 22, "ph": "X", "name": "ReadAsync 295", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754736188691233, "dur": 21, "ph": "X", "name": "ReadAsync 452", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754736188691256, "dur": 21, "ph": "X", "name": "ReadAsync 350", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754736188691279, "dur": 22, "ph": "X", "name": "ReadAsync 362", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754736188691302, "dur": 22, "ph": "X", "name": "ReadAsync 410", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754736188691325, "dur": 17, "ph": "X", "name": "ReadAsync 513", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754736188691343, "dur": 17, "ph": "X", "name": "ReadAsync 219", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754736188691362, "dur": 22, "ph": "X", "name": "ReadAsync 260", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754736188691386, "dur": 35, "ph": "X", "name": "ReadAsync 327", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754736188691422, "dur": 23, "ph": "X", "name": "ReadAsync 493", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754736188691446, "dur": 21, "ph": "X", "name": "ReadAsync 412", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754736188691469, "dur": 21, "ph": "X", "name": "ReadAsync 405", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754736188691492, "dur": 17, "ph": "X", "name": "ReadAsync 332", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754736188691511, "dur": 21, "ph": "X", "name": "ReadAsync 86", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754736188691533, "dur": 21, "ph": "X", "name": "ReadAsync 246", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754736188691556, "dur": 22, "ph": "X", "name": "ReadAsync 286", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754736188691579, "dur": 21, "ph": "X", "name": "ReadAsync 354", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754736188691602, "dur": 21, "ph": "X", "name": "ReadAsync 327", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754736188691624, "dur": 21, "ph": "X", "name": "ReadAsync 300", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754736188691647, "dur": 21, "ph": "X", "name": "ReadAsync 374", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754736188691670, "dur": 20, "ph": "X", "name": "ReadAsync 164", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754736188691691, "dur": 22, "ph": "X", "name": "ReadAsync 141", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754736188691715, "dur": 21, "ph": "X", "name": "ReadAsync 329", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754736188691738, "dur": 20, "ph": "X", "name": "ReadAsync 456", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754736188691760, "dur": 77, "ph": "X", "name": "ReadAsync 305", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754736188691838, "dur": 22, "ph": "X", "name": "ReadAsync 181", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754736188691862, "dur": 22, "ph": "X", "name": "ReadAsync 382", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754736188691885, "dur": 19, "ph": "X", "name": "ReadAsync 319", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754736188691905, "dur": 21, "ph": "X", "name": "ReadAsync 82", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754736188691928, "dur": 21, "ph": "X", "name": "ReadAsync 360", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754736188691951, "dur": 21, "ph": "X", "name": "ReadAsync 403", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754736188691972, "dur": 1, "ph": "X", "name": "ProcessMessages 427", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754736188691974, "dur": 21, "ph": "X", "name": "ReadAsync 427", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754736188691998, "dur": 22, "ph": "X", "name": "ReadAsync 382", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754736188692021, "dur": 22, "ph": "X", "name": "ReadAsync 391", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754736188692044, "dur": 141, "ph": "X", "name": "ReadAsync 340", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754736188692187, "dur": 21, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754736188692209, "dur": 22, "ph": "X", "name": "ReadAsync 339", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754736188692233, "dur": 21, "ph": "X", "name": "ReadAsync 375", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754736188692255, "dur": 22, "ph": "X", "name": "ReadAsync 399", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754736188692278, "dur": 22, "ph": "X", "name": "ReadAsync 354", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754736188692302, "dur": 22, "ph": "X", "name": "ReadAsync 307", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754736188692325, "dur": 51, "ph": "X", "name": "ReadAsync 375", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754736188692377, "dur": 37, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754736188692415, "dur": 21, "ph": "X", "name": "ReadAsync 505", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754736188692440, "dur": 31, "ph": "X", "name": "ReadAsync 256", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754736188692474, "dur": 23, "ph": "X", "name": "ReadAsync 494", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754736188692498, "dur": 22, "ph": "X", "name": "ReadAsync 304", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754736188692521, "dur": 1, "ph": "X", "name": "ProcessMessages 324", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754736188692523, "dur": 22, "ph": "X", "name": "ReadAsync 324", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754736188692547, "dur": 19, "ph": "X", "name": "ReadAsync 461", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754736188692567, "dur": 20, "ph": "X", "name": "ReadAsync 91", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754736188692589, "dur": 23, "ph": "X", "name": "ReadAsync 234", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754736188692613, "dur": 22, "ph": "X", "name": "ReadAsync 282", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754736188692636, "dur": 22, "ph": "X", "name": "ReadAsync 334", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754736188692660, "dur": 27, "ph": "X", "name": "ReadAsync 303", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754736188692689, "dur": 19, "ph": "X", "name": "ReadAsync 268", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754736188692710, "dur": 20, "ph": "X", "name": "ReadAsync 493", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754736188692732, "dur": 36, "ph": "X", "name": "ReadAsync 71", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754736188692769, "dur": 22, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754736188692793, "dur": 32, "ph": "X", "name": "ReadAsync 333", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754736188692827, "dur": 22, "ph": "X", "name": "ReadAsync 367", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754736188692851, "dur": 23, "ph": "X", "name": "ReadAsync 385", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754736188692875, "dur": 31, "ph": "X", "name": "ReadAsync 263", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754736188692908, "dur": 29, "ph": "X", "name": "ReadAsync 271", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754736188692942, "dur": 35, "ph": "X", "name": "ReadAsync 419", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754736188692980, "dur": 36, "ph": "X", "name": "ReadAsync 318", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754736188693019, "dur": 29, "ph": "X", "name": "ReadAsync 461", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754736188693050, "dur": 18, "ph": "X", "name": "ReadAsync 479", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754736188693069, "dur": 23, "ph": "X", "name": "ReadAsync 175", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754736188693094, "dur": 21, "ph": "X", "name": "ReadAsync 270", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754736188693117, "dur": 20, "ph": "X", "name": "ReadAsync 399", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754736188693139, "dur": 21, "ph": "X", "name": "ReadAsync 82", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754736188693162, "dur": 22, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754736188693185, "dur": 14, "ph": "X", "name": "ReadAsync 372", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754736188693200, "dur": 21, "ph": "X", "name": "ReadAsync 359", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754736188693222, "dur": 23, "ph": "X", "name": "ReadAsync 251", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754736188693247, "dur": 21, "ph": "X", "name": "ReadAsync 295", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754736188693269, "dur": 22, "ph": "X", "name": "ReadAsync 301", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754736188693293, "dur": 21, "ph": "X", "name": "ReadAsync 386", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754736188693316, "dur": 19, "ph": "X", "name": "ReadAsync 339", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754736188693336, "dur": 21, "ph": "X", "name": "ReadAsync 131", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754736188693358, "dur": 22, "ph": "X", "name": "ReadAsync 338", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754736188693382, "dur": 21, "ph": "X", "name": "ReadAsync 360", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754736188693405, "dur": 22, "ph": "X", "name": "ReadAsync 393", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754736188693428, "dur": 21, "ph": "X", "name": "ReadAsync 273", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754736188693450, "dur": 21, "ph": "X", "name": "ReadAsync 366", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754736188693473, "dur": 21, "ph": "X", "name": "ReadAsync 322", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754736188693495, "dur": 22, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754736188693519, "dur": 22, "ph": "X", "name": "ReadAsync 375", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754736188693542, "dur": 22, "ph": "X", "name": "ReadAsync 423", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754736188693565, "dur": 22, "ph": "X", "name": "ReadAsync 447", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754736188693590, "dur": 21, "ph": "X", "name": "ReadAsync 402", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754736188693612, "dur": 21, "ph": "X", "name": "ReadAsync 334", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754736188693635, "dur": 17, "ph": "X", "name": "ReadAsync 351", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754736188693653, "dur": 21, "ph": "X", "name": "ReadAsync 81", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754736188693676, "dur": 21, "ph": "X", "name": "ReadAsync 272", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754736188693699, "dur": 22, "ph": "X", "name": "ReadAsync 407", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754736188693723, "dur": 22, "ph": "X", "name": "ReadAsync 403", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754736188693746, "dur": 21, "ph": "X", "name": "ReadAsync 344", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754736188693769, "dur": 21, "ph": "X", "name": "ReadAsync 375", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754736188693791, "dur": 22, "ph": "X", "name": "ReadAsync 214", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754736188693814, "dur": 22, "ph": "X", "name": "ReadAsync 322", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754736188693837, "dur": 23, "ph": "X", "name": "ReadAsync 240", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754736188693862, "dur": 22, "ph": "X", "name": "ReadAsync 386", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754736188693886, "dur": 21, "ph": "X", "name": "ReadAsync 334", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754736188693908, "dur": 22, "ph": "X", "name": "ReadAsync 359", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754736188693932, "dur": 22, "ph": "X", "name": "ReadAsync 362", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754736188693956, "dur": 20, "ph": "X", "name": "ReadAsync 297", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754736188693978, "dur": 21, "ph": "X", "name": "ReadAsync 73", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754736188694001, "dur": 25, "ph": "X", "name": "ReadAsync 342", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754736188694028, "dur": 22, "ph": "X", "name": "ReadAsync 445", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754736188694051, "dur": 18, "ph": "X", "name": "ReadAsync 387", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754736188694071, "dur": 22, "ph": "X", "name": "ReadAsync 255", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754736188694094, "dur": 21, "ph": "X", "name": "ReadAsync 289", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754736188694117, "dur": 20, "ph": "X", "name": "ReadAsync 214", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754736188694139, "dur": 21, "ph": "X", "name": "ReadAsync 163", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754736188694162, "dur": 21, "ph": "X", "name": "ReadAsync 373", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754736188694184, "dur": 23, "ph": "X", "name": "ReadAsync 407", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754736188694209, "dur": 22, "ph": "X", "name": "ReadAsync 493", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754736188694232, "dur": 23, "ph": "X", "name": "ReadAsync 412", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754736188694256, "dur": 22, "ph": "X", "name": "ReadAsync 348", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754736188694280, "dur": 17, "ph": "X", "name": "ReadAsync 416", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754736188694299, "dur": 22, "ph": "X", "name": "ReadAsync 176", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754736188694322, "dur": 21, "ph": "X", "name": "ReadAsync 317", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754736188694345, "dur": 21, "ph": "X", "name": "ReadAsync 546", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754736188694368, "dur": 21, "ph": "X", "name": "ReadAsync 392", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754736188694390, "dur": 23, "ph": "X", "name": "ReadAsync 341", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754736188694414, "dur": 21, "ph": "X", "name": "ReadAsync 359", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754736188694436, "dur": 24, "ph": "X", "name": "ReadAsync 512", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754736188694462, "dur": 22, "ph": "X", "name": "ReadAsync 110", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754736188694485, "dur": 21, "ph": "X", "name": "ReadAsync 382", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754736188694508, "dur": 22, "ph": "X", "name": "ReadAsync 383", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754736188694532, "dur": 21, "ph": "X", "name": "ReadAsync 415", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754736188694555, "dur": 23, "ph": "X", "name": "ReadAsync 396", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754736188694579, "dur": 21, "ph": "X", "name": "ReadAsync 434", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754736188694602, "dur": 17, "ph": "X", "name": "ReadAsync 355", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754736188694620, "dur": 22, "ph": "X", "name": "ReadAsync 94", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754736188694644, "dur": 22, "ph": "X", "name": "ReadAsync 327", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754736188694667, "dur": 22, "ph": "X", "name": "ReadAsync 327", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754736188694691, "dur": 14, "ph": "X", "name": "ReadAsync 371", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754736188694706, "dur": 20, "ph": "X", "name": "ReadAsync 240", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754736188694727, "dur": 18, "ph": "X", "name": "ReadAsync 144", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754736188694747, "dur": 21, "ph": "X", "name": "ReadAsync 330", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754736188694769, "dur": 21, "ph": "X", "name": "ReadAsync 234", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754736188694791, "dur": 21, "ph": "X", "name": "ReadAsync 252", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754736188694814, "dur": 17, "ph": "X", "name": "ReadAsync 290", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754736188694833, "dur": 21, "ph": "X", "name": "ReadAsync 357", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754736188694855, "dur": 23, "ph": "X", "name": "ReadAsync 282", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754736188694879, "dur": 22, "ph": "X", "name": "ReadAsync 264", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754736188694903, "dur": 25, "ph": "X", "name": "ReadAsync 296", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754736188694929, "dur": 20, "ph": "X", "name": "ReadAsync 376", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754736188694951, "dur": 21, "ph": "X", "name": "ReadAsync 231", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754736188694973, "dur": 21, "ph": "X", "name": "ReadAsync 295", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754736188694995, "dur": 22, "ph": "X", "name": "ReadAsync 337", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754736188695019, "dur": 21, "ph": "X", "name": "ReadAsync 316", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754736188695042, "dur": 22, "ph": "X", "name": "ReadAsync 238", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754736188695065, "dur": 21, "ph": "X", "name": "ReadAsync 313", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754736188695087, "dur": 18, "ph": "X", "name": "ReadAsync 277", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754736188695107, "dur": 21, "ph": "X", "name": "ReadAsync 388", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754736188695129, "dur": 21, "ph": "X", "name": "ReadAsync 407", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754736188695151, "dur": 21, "ph": "X", "name": "ReadAsync 384", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754736188695174, "dur": 23, "ph": "X", "name": "ReadAsync 346", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754736188695198, "dur": 21, "ph": "X", "name": "ReadAsync 462", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754736188695221, "dur": 17, "ph": "X", "name": "ReadAsync 385", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754736188695239, "dur": 30, "ph": "X", "name": "ReadAsync 207", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754736188695270, "dur": 1, "ph": "X", "name": "ProcessMessages 378", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754736188695272, "dur": 25, "ph": "X", "name": "ReadAsync 378", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754736188695299, "dur": 22, "ph": "X", "name": "ReadAsync 411", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754736188695322, "dur": 22, "ph": "X", "name": "ReadAsync 351", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754736188695345, "dur": 22, "ph": "X", "name": "ReadAsync 379", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754736188695369, "dur": 14, "ph": "X", "name": "ReadAsync 415", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754736188695384, "dur": 84, "ph": "X", "name": "ReadAsync 343", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754736188695471, "dur": 29, "ph": "X", "name": "ReadAsync 82", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754736188695501, "dur": 21, "ph": "X", "name": "ReadAsync 1384", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754736188695523, "dur": 22, "ph": "X", "name": "ReadAsync 189", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754736188695547, "dur": 23, "ph": "X", "name": "ReadAsync 390", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754736188695571, "dur": 42, "ph": "X", "name": "ReadAsync 176", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754736188695615, "dur": 24, "ph": "X", "name": "ReadAsync 166", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754736188695640, "dur": 25, "ph": "X", "name": "ReadAsync 534", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754736188695666, "dur": 18, "ph": "X", "name": "ReadAsync 185", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754736188695686, "dur": 20, "ph": "X", "name": "ReadAsync 160", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754736188695707, "dur": 33, "ph": "X", "name": "ReadAsync 281", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754736188695745, "dur": 37, "ph": "X", "name": "ReadAsync 231", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754736188695784, "dur": 1, "ph": "X", "name": "ProcessMessages 534", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754736188695786, "dur": 38, "ph": "X", "name": "ReadAsync 534", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754736188695826, "dur": 24, "ph": "X", "name": "ReadAsync 337", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754736188695851, "dur": 22, "ph": "X", "name": "ReadAsync 396", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754736188695879, "dur": 32, "ph": "X", "name": "ReadAsync 61", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754736188695913, "dur": 27, "ph": "X", "name": "ReadAsync 511", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754736188695943, "dur": 34, "ph": "X", "name": "ReadAsync 268", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754736188695980, "dur": 24, "ph": "X", "name": "ReadAsync 430", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754736188696006, "dur": 24, "ph": "X", "name": "ReadAsync 436", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754736188696031, "dur": 22, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754736188696055, "dur": 19, "ph": "X", "name": "ReadAsync 392", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754736188696075, "dur": 25, "ph": "X", "name": "ReadAsync 134", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754736188696102, "dur": 21, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754736188696125, "dur": 39, "ph": "X", "name": "ReadAsync 228", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754736188696165, "dur": 22, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754736188696189, "dur": 18, "ph": "X", "name": "ReadAsync 192", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754736188696208, "dur": 21, "ph": "X", "name": "ReadAsync 220", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754736188696231, "dur": 18, "ph": "X", "name": "ReadAsync 325", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754736188696251, "dur": 90, "ph": "X", "name": "ReadAsync 240", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754736188696342, "dur": 34, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754736188696380, "dur": 32, "ph": "X", "name": "ReadAsync 372", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754736188696414, "dur": 23, "ph": "X", "name": "ReadAsync 706", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754736188696440, "dur": 22, "ph": "X", "name": "ReadAsync 329", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754736188696463, "dur": 23, "ph": "X", "name": "ReadAsync 371", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754736188696487, "dur": 20, "ph": "X", "name": "ReadAsync 366", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754736188696509, "dur": 20, "ph": "X", "name": "ReadAsync 294", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754736188696530, "dur": 22, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754736188696553, "dur": 65, "ph": "X", "name": "ReadAsync 237", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754736188696620, "dur": 59, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754736188696681, "dur": 30, "ph": "X", "name": "ReadAsync 224", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754736188696712, "dur": 19, "ph": "X", "name": "ReadAsync 904", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754736188696732, "dur": 21, "ph": "X", "name": "ReadAsync 268", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754736188696756, "dur": 22, "ph": "X", "name": "ReadAsync 289", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754736188696779, "dur": 21, "ph": "X", "name": "ReadAsync 263", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754736188696802, "dur": 76, "ph": "X", "name": "ReadAsync 255", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754736188696880, "dur": 18, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754736188696909, "dur": 23, "ph": "X", "name": "ReadAsync 89", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754736188696934, "dur": 22, "ph": "X", "name": "ReadAsync 435", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754736188696957, "dur": 21, "ph": "X", "name": "ReadAsync 210", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754736188696980, "dur": 22, "ph": "X", "name": "ReadAsync 347", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754736188697003, "dur": 18, "ph": "X", "name": "ReadAsync 291", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754736188697022, "dur": 19, "ph": "X", "name": "ReadAsync 259", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754736188697042, "dur": 15, "ph": "X", "name": "ReadAsync 208", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754736188697058, "dur": 21, "ph": "X", "name": "ReadAsync 198", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754736188697081, "dur": 21, "ph": "X", "name": "ReadAsync 233", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754736188697103, "dur": 21, "ph": "X", "name": "ReadAsync 63", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754736188697126, "dur": 58, "ph": "X", "name": "ReadAsync 237", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754736188697185, "dur": 50, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754736188697237, "dur": 23, "ph": "X", "name": "ReadAsync 257", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754736188697262, "dur": 19, "ph": "X", "name": "ReadAsync 650", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754736188697282, "dur": 50, "ph": "X", "name": "ReadAsync 21", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754736188697333, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754736188697334, "dur": 22, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754736188697357, "dur": 22, "ph": "X", "name": "ReadAsync 281", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754736188697380, "dur": 21, "ph": "X", "name": "ReadAsync 348", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754736188697404, "dur": 17, "ph": "X", "name": "ReadAsync 295", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754736188697422, "dur": 55, "ph": "X", "name": "ReadAsync 62", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754736188697478, "dur": 21, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754736188697501, "dur": 23, "ph": "X", "name": "ReadAsync 299", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754736188697525, "dur": 21, "ph": "X", "name": "ReadAsync 171", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754736188697548, "dur": 18, "ph": "X", "name": "ReadAsync 391", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754736188697567, "dur": 52, "ph": "X", "name": "ReadAsync 139", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754736188697621, "dur": 21, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754736188697643, "dur": 20, "ph": "X", "name": "ReadAsync 305", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754736188697665, "dur": 25, "ph": "X", "name": "ReadAsync 155", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754736188697692, "dur": 20, "ph": "X", "name": "ReadAsync 383", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754736188697713, "dur": 54, "ph": "X", "name": "ReadAsync 193", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754736188697769, "dur": 21, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754736188697791, "dur": 24, "ph": "X", "name": "ReadAsync 271", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754736188697816, "dur": 22, "ph": "X", "name": "ReadAsync 390", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754736188697839, "dur": 21, "ph": "X", "name": "ReadAsync 438", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754736188697861, "dur": 47, "ph": "X", "name": "ReadAsync 57", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754736188697910, "dur": 21, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754736188697933, "dur": 22, "ph": "X", "name": "ReadAsync 403", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754736188697956, "dur": 22, "ph": "X", "name": "ReadAsync 357", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754736188697979, "dur": 20, "ph": "X", "name": "ReadAsync 456", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754736188698001, "dur": 50, "ph": "X", "name": "ReadAsync 67", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754736188698053, "dur": 22, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754736188698076, "dur": 21, "ph": "X", "name": "ReadAsync 358", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754736188698100, "dur": 22, "ph": "X", "name": "ReadAsync 386", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754736188698123, "dur": 20, "ph": "X", "name": "ReadAsync 432", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754736188698145, "dur": 46, "ph": "X", "name": "ReadAsync 61", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754736188698193, "dur": 21, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754736188698215, "dur": 21, "ph": "X", "name": "ReadAsync 275", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754736188698238, "dur": 33, "ph": "X", "name": "ReadAsync 233", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754736188698272, "dur": 18, "ph": "X", "name": "ReadAsync 446", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754736188698291, "dur": 55, "ph": "X", "name": "ReadAsync 140", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754736188698347, "dur": 29, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754736188698377, "dur": 1, "ph": "X", "name": "ProcessMessages 403", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754736188698379, "dur": 22, "ph": "X", "name": "ReadAsync 403", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754736188698402, "dur": 21, "ph": "X", "name": "ReadAsync 411", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754736188698425, "dur": 62, "ph": "X", "name": "ReadAsync 258", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754736188698488, "dur": 20, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754736188698510, "dur": 23, "ph": "X", "name": "ReadAsync 329", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754736188698535, "dur": 21, "ph": "X", "name": "ReadAsync 256", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754736188698557, "dur": 20, "ph": "X", "name": "ReadAsync 384", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754736188698578, "dur": 50, "ph": "X", "name": "ReadAsync 61", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754736188698629, "dur": 21, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754736188698652, "dur": 22, "ph": "X", "name": "ReadAsync 296", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754736188698676, "dur": 23, "ph": "X", "name": "ReadAsync 242", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754736188698700, "dur": 20, "ph": "X", "name": "ReadAsync 378", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754736188698721, "dur": 50, "ph": "X", "name": "ReadAsync 66", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754736188698772, "dur": 52, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754736188698826, "dur": 21, "ph": "X", "name": "ReadAsync 244", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754736188698849, "dur": 21, "ph": "X", "name": "ReadAsync 241", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754736188698872, "dur": 21, "ph": "X", "name": "ReadAsync 424", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754736188698895, "dur": 22, "ph": "X", "name": "ReadAsync 356", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754736188698919, "dur": 23, "ph": "X", "name": "ReadAsync 389", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754736188698943, "dur": 21, "ph": "X", "name": "ReadAsync 331", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754736188698966, "dur": 20, "ph": "X", "name": "ReadAsync 303", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754736188698987, "dur": 17, "ph": "X", "name": "ReadAsync 303", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754736188699006, "dur": 20, "ph": "X", "name": "ReadAsync 171", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754736188699027, "dur": 55, "ph": "X", "name": "ReadAsync 60", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754736188699084, "dur": 21, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754736188699106, "dur": 22, "ph": "X", "name": "ReadAsync 245", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754736188699130, "dur": 21, "ph": "X", "name": "ReadAsync 340", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754736188699153, "dur": 14, "ph": "X", "name": "ReadAsync 341", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754736188699168, "dur": 21, "ph": "X", "name": "ReadAsync 421", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754736188699191, "dur": 21, "ph": "X", "name": "ReadAsync 198", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754736188699214, "dur": 20, "ph": "X", "name": "ReadAsync 335", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754736188699235, "dur": 21, "ph": "X", "name": "ReadAsync 65", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754736188699257, "dur": 17, "ph": "X", "name": "ReadAsync 299", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754736188699276, "dur": 21, "ph": "X", "name": "ReadAsync 283", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754736188699299, "dur": 19, "ph": "X", "name": "ReadAsync 177", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754736188699319, "dur": 48, "ph": "X", "name": "ReadAsync 63", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754736188699369, "dur": 21, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754736188699391, "dur": 21, "ph": "X", "name": "ReadAsync 313", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754736188699414, "dur": 21, "ph": "X", "name": "ReadAsync 295", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754736188699437, "dur": 14, "ph": "X", "name": "ReadAsync 236", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754736188699452, "dur": 21, "ph": "X", "name": "ReadAsync 284", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754736188699474, "dur": 20, "ph": "X", "name": "ReadAsync 208", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754736188699496, "dur": 50, "ph": "X", "name": "ReadAsync 140", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754736188699547, "dur": 21, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754736188699570, "dur": 21, "ph": "X", "name": "ReadAsync 309", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754736188699593, "dur": 17, "ph": "X", "name": "ReadAsync 277", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754736188699612, "dur": 22, "ph": "X", "name": "ReadAsync 262", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754736188699635, "dur": 24, "ph": "X", "name": "ReadAsync 323", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754736188699661, "dur": 22, "ph": "X", "name": "ReadAsync 399", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754736188699684, "dur": 21, "ph": "X", "name": "ReadAsync 145", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754736188699707, "dur": 23, "ph": "X", "name": "ReadAsync 277", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754736188699732, "dur": 17, "ph": "X", "name": "ReadAsync 419", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754736188699751, "dur": 22, "ph": "X", "name": "ReadAsync 169", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754736188699774, "dur": 43, "ph": "X", "name": "ReadAsync 229", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754736188699818, "dur": 19, "ph": "X", "name": "ReadAsync 8", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754736188699838, "dur": 45, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754736188699884, "dur": 24, "ph": "X", "name": "ReadAsync 270", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754736188699910, "dur": 18, "ph": "X", "name": "ReadAsync 613", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754736188699929, "dur": 55, "ph": "X", "name": "ReadAsync 138", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754736188699986, "dur": 22, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754736188700010, "dur": 23, "ph": "X", "name": "ReadAsync 305", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754736188700034, "dur": 21, "ph": "X", "name": "ReadAsync 373", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754736188700057, "dur": 19, "ph": "X", "name": "ReadAsync 302", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754736188700078, "dur": 44, "ph": "X", "name": "ReadAsync 56", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754736188700123, "dur": 23, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754736188700147, "dur": 22, "ph": "X", "name": "ReadAsync 331", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754736188700170, "dur": 22, "ph": "X", "name": "ReadAsync 405", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754736188700194, "dur": 19, "ph": "X", "name": "ReadAsync 350", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754736188700214, "dur": 20, "ph": "X", "name": "ReadAsync 131", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754736188700236, "dur": 21, "ph": "X", "name": "ReadAsync 343", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754736188700259, "dur": 23, "ph": "X", "name": "ReadAsync 398", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754736188700283, "dur": 22, "ph": "X", "name": "ReadAsync 171", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754736188700307, "dur": 21, "ph": "X", "name": "ReadAsync 454", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754736188700329, "dur": 20, "ph": "X", "name": "ReadAsync 188", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754736188700351, "dur": 55, "ph": "X", "name": "ReadAsync 240", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754736188700408, "dur": 22, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754736188700431, "dur": 24, "ph": "X", "name": "ReadAsync 354", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754736188700457, "dur": 21, "ph": "X", "name": "ReadAsync 370", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754736188700479, "dur": 19, "ph": "X", "name": "ReadAsync 301", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754736188700499, "dur": 46, "ph": "X", "name": "ReadAsync 63", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754736188700547, "dur": 21, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754736188700570, "dur": 22, "ph": "X", "name": "ReadAsync 337", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754736188700593, "dur": 22, "ph": "X", "name": "ReadAsync 303", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754736188700616, "dur": 20, "ph": "X", "name": "ReadAsync 417", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754736188700637, "dur": 51, "ph": "X", "name": "ReadAsync 66", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754736188700689, "dur": 20, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754736188700710, "dur": 22, "ph": "X", "name": "ReadAsync 260", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754736188700734, "dur": 23, "ph": "X", "name": "ReadAsync 254", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754736188700758, "dur": 22, "ph": "X", "name": "ReadAsync 383", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754736188700781, "dur": 89, "ph": "X", "name": "ReadAsync 200", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754736188700875, "dur": 34, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754736188700911, "dur": 33, "ph": "X", "name": "ReadAsync 463", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754736188700946, "dur": 31, "ph": "X", "name": "ReadAsync 439", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754736188700980, "dur": 1, "ph": "X", "name": "ProcessMessages 143", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754736188700981, "dur": 63, "ph": "X", "name": "ReadAsync 143", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754736188701045, "dur": 26, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754736188701073, "dur": 23, "ph": "X", "name": "ReadAsync 325", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754736188701100, "dur": 26, "ph": "X", "name": "ReadAsync 144", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754736188701129, "dur": 21, "ph": "X", "name": "ReadAsync 455", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754736188701151, "dur": 74, "ph": "X", "name": "ReadAsync 146", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754736188701227, "dur": 32, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754736188701260, "dur": 25, "ph": "X", "name": "ReadAsync 248", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754736188701287, "dur": 24, "ph": "X", "name": "ReadAsync 570", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754736188701312, "dur": 23, "ph": "X", "name": "ReadAsync 326", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754736188701336, "dur": 21, "ph": "X", "name": "ReadAsync 338", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754736188701359, "dur": 22, "ph": "X", "name": "ReadAsync 121", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754736188701383, "dur": 26, "ph": "X", "name": "ReadAsync 347", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754736188701411, "dur": 1, "ph": "X", "name": "ProcessMessages 299", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754736188701413, "dur": 42, "ph": "X", "name": "ReadAsync 299", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754736188701457, "dur": 1, "ph": "X", "name": "ProcessMessages 380", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754736188701459, "dur": 38, "ph": "X", "name": "ReadAsync 380", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754736188701502, "dur": 55, "ph": "X", "name": "ReadAsync 237", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754736188701558, "dur": 26, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754736188701586, "dur": 24, "ph": "X", "name": "ReadAsync 355", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754736188701612, "dur": 38, "ph": "X", "name": "ReadAsync 486", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754736188701652, "dur": 24, "ph": "X", "name": "ReadAsync 474", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754736188701677, "dur": 21, "ph": "X", "name": "ReadAsync 581", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754736188701700, "dur": 99, "ph": "X", "name": "ReadAsync 125", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754736188701802, "dur": 1, "ph": "X", "name": "ProcessMessages 278", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754736188701804, "dur": 39, "ph": "X", "name": "ReadAsync 278", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754736188701846, "dur": 1, "ph": "X", "name": "ProcessMessages 918", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754736188701847, "dur": 43, "ph": "X", "name": "ReadAsync 918", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754736188701894, "dur": 36, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754736188701932, "dur": 25, "ph": "X", "name": "ReadAsync 541", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754736188701959, "dur": 1, "ph": "X", "name": "ProcessMessages 378", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754736188701961, "dur": 37, "ph": "X", "name": "ReadAsync 378", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754736188702001, "dur": 26, "ph": "X", "name": "ReadAsync 681", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754736188702028, "dur": 21, "ph": "X", "name": "ReadAsync 350", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754736188702051, "dur": 22, "ph": "X", "name": "ReadAsync 367", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754736188702074, "dur": 19, "ph": "X", "name": "ReadAsync 341", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754736188702094, "dur": 27, "ph": "X", "name": "ReadAsync 185", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754736188702122, "dur": 21, "ph": "X", "name": "ReadAsync 40", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754736188702144, "dur": 72, "ph": "X", "name": "ReadAsync 197", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754736188702221, "dur": 50, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754736188702275, "dur": 27, "ph": "X", "name": "ReadAsync 600", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754736188702303, "dur": 23, "ph": "X", "name": "ReadAsync 466", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754736188702327, "dur": 23, "ph": "X", "name": "ReadAsync 124", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754736188702351, "dur": 64, "ph": "X", "name": "ReadAsync 21", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754736188702417, "dur": 23, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754736188702441, "dur": 79, "ph": "X", "name": "ReadAsync 330", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754736188702523, "dur": 24, "ph": "X", "name": "ReadAsync 273", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754736188702548, "dur": 28, "ph": "X", "name": "ReadAsync 574", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754736188702579, "dur": 27, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754736188702608, "dur": 21, "ph": "X", "name": "ReadAsync 545", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754736188702630, "dur": 20, "ph": "X", "name": "ReadAsync 360", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754736188702652, "dur": 21, "ph": "X", "name": "ReadAsync 300", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754736188702675, "dur": 271, "ph": "X", "name": "ReadAsync 21", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754736188702949, "dur": 42, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754736188702992, "dur": 1, "ph": "X", "name": "ProcessMessages 2297", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754736188702994, "dur": 25, "ph": "X", "name": "ReadAsync 2297", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754736188703022, "dur": 30, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754736188703055, "dur": 46, "ph": "X", "name": "ReadAsync 412", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754736188703103, "dur": 21, "ph": "X", "name": "ReadAsync 357", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754736188703126, "dur": 22, "ph": "X", "name": "ReadAsync 365", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754736188703149, "dur": 23, "ph": "X", "name": "ReadAsync 335", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754736188703174, "dur": 22, "ph": "X", "name": "ReadAsync 314", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754736188703198, "dur": 25, "ph": "X", "name": "ReadAsync 339", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754736188703225, "dur": 18, "ph": "X", "name": "ReadAsync 411", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754736188703244, "dur": 20, "ph": "X", "name": "ReadAsync 71", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754736188703266, "dur": 61, "ph": "X", "name": "ReadAsync 237", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754736188703329, "dur": 22, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754736188703353, "dur": 22, "ph": "X", "name": "ReadAsync 274", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754736188703376, "dur": 21, "ph": "X", "name": "ReadAsync 385", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754736188703399, "dur": 39, "ph": "X", "name": "ReadAsync 270", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754736188703441, "dur": 55, "ph": "X", "name": "ReadAsync 228", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754736188703497, "dur": 22, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754736188703521, "dur": 22, "ph": "X", "name": "ReadAsync 372", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754736188703545, "dur": 22, "ph": "X", "name": "ReadAsync 277", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754736188703568, "dur": 18, "ph": "X", "name": "ReadAsync 397", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754736188703588, "dur": 49, "ph": "X", "name": "ReadAsync 62", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754736188703639, "dur": 21, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754736188703662, "dur": 23, "ph": "X", "name": "ReadAsync 336", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754736188703686, "dur": 22, "ph": "X", "name": "ReadAsync 299", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754736188703709, "dur": 21, "ph": "X", "name": "ReadAsync 350", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754736188703732, "dur": 51, "ph": "X", "name": "ReadAsync 136", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754736188703784, "dur": 21, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754736188703806, "dur": 22, "ph": "X", "name": "ReadAsync 297", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754736188703829, "dur": 26, "ph": "X", "name": "ReadAsync 336", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754736188703857, "dur": 23, "ph": "X", "name": "ReadAsync 337", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754736188703882, "dur": 45, "ph": "X", "name": "ReadAsync 138", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754736188703928, "dur": 21, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754736188703951, "dur": 21, "ph": "X", "name": "ReadAsync 323", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754736188703974, "dur": 21, "ph": "X", "name": "ReadAsync 234", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754736188703996, "dur": 24, "ph": "X", "name": "ReadAsync 240", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754736188704022, "dur": 22, "ph": "X", "name": "ReadAsync 355", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754736188704046, "dur": 21, "ph": "X", "name": "ReadAsync 333", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754736188704070, "dur": 23, "ph": "X", "name": "ReadAsync 220", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754736188704094, "dur": 18, "ph": "X", "name": "ReadAsync 303", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754736188704114, "dur": 23, "ph": "X", "name": "ReadAsync 274", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754736188704138, "dur": 17, "ph": "X", "name": "ReadAsync 103", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754736188704157, "dur": 54, "ph": "X", "name": "ReadAsync 134", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754736188704212, "dur": 21, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754736188704234, "dur": 23, "ph": "X", "name": "ReadAsync 235", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754736188704258, "dur": 21, "ph": "X", "name": "ReadAsync 321", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754736188704281, "dur": 24, "ph": "X", "name": "ReadAsync 338", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754736188704307, "dur": 22, "ph": "X", "name": "ReadAsync 441", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754736188704331, "dur": 22, "ph": "X", "name": "ReadAsync 323", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754736188704354, "dur": 22, "ph": "X", "name": "ReadAsync 212", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754736188704377, "dur": 22, "ph": "X", "name": "ReadAsync 302", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754736188704400, "dur": 18, "ph": "X", "name": "ReadAsync 288", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754736188704420, "dur": 22, "ph": "X", "name": "ReadAsync 75", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754736188704443, "dur": 62, "ph": "X", "name": "ReadAsync 240", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754736188704507, "dur": 22, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754736188704530, "dur": 22, "ph": "X", "name": "ReadAsync 271", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754736188704553, "dur": 22, "ph": "X", "name": "ReadAsync 266", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754736188704577, "dur": 22, "ph": "X", "name": "ReadAsync 364", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754736188704601, "dur": 21, "ph": "X", "name": "ReadAsync 311", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754736188704623, "dur": 18, "ph": "X", "name": "ReadAsync 279", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754736188704643, "dur": 22, "ph": "X", "name": "ReadAsync 264", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754736188704666, "dur": 21, "ph": "X", "name": "ReadAsync 263", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754736188704688, "dur": 21, "ph": "X", "name": "ReadAsync 203", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754736188704711, "dur": 18, "ph": "X", "name": "ReadAsync 220", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754736188704730, "dur": 45, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754736188704776, "dur": 21, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754736188704799, "dur": 22, "ph": "X", "name": "ReadAsync 229", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754736188704822, "dur": 21, "ph": "X", "name": "ReadAsync 376", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754736188704845, "dur": 21, "ph": "X", "name": "ReadAsync 361", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754736188704867, "dur": 56, "ph": "X", "name": "ReadAsync 140", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754736188704925, "dur": 21, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754736188704948, "dur": 22, "ph": "X", "name": "ReadAsync 316", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754736188704971, "dur": 22, "ph": "X", "name": "ReadAsync 409", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754736188704995, "dur": 19, "ph": "X", "name": "ReadAsync 440", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754736188705016, "dur": 50, "ph": "X", "name": "ReadAsync 66", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754736188705067, "dur": 21, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754736188705089, "dur": 22, "ph": "X", "name": "ReadAsync 337", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754736188705113, "dur": 18, "ph": "X", "name": "ReadAsync 365", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754736188705132, "dur": 21, "ph": "X", "name": "ReadAsync 350", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754736188705156, "dur": 49, "ph": "X", "name": "ReadAsync 134", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754736188705206, "dur": 21, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754736188705229, "dur": 22, "ph": "X", "name": "ReadAsync 304", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754736188705252, "dur": 20, "ph": "X", "name": "ReadAsync 374", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754736188705273, "dur": 24, "ph": "X", "name": "ReadAsync 379", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754736188705298, "dur": 21, "ph": "X", "name": "ReadAsync 502", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754736188705321, "dur": 22, "ph": "X", "name": "ReadAsync 327", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754736188705344, "dur": 21, "ph": "X", "name": "ReadAsync 370", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754736188705367, "dur": 18, "ph": "X", "name": "ReadAsync 344", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754736188705387, "dur": 20, "ph": "X", "name": "ReadAsync 328", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754736188705408, "dur": 19, "ph": "X", "name": "ReadAsync 180", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754736188705429, "dur": 49, "ph": "X", "name": "ReadAsync 57", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754736188705479, "dur": 21, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754736188705502, "dur": 22, "ph": "X", "name": "ReadAsync 310", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754736188705526, "dur": 20, "ph": "X", "name": "ReadAsync 365", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754736188705548, "dur": 35, "ph": "X", "name": "ReadAsync 465", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754736188705584, "dur": 33, "ph": "X", "name": "ReadAsync 402", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754736188705619, "dur": 22, "ph": "X", "name": "ReadAsync 367", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754736188705642, "dur": 21, "ph": "X", "name": "ReadAsync 316", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754736188705665, "dur": 22, "ph": "X", "name": "ReadAsync 465", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754736188705688, "dur": 21, "ph": "X", "name": "ReadAsync 195", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754736188705711, "dur": 64, "ph": "X", "name": "ReadAsync 240", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754736188705776, "dur": 21, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754736188705799, "dur": 22, "ph": "X", "name": "ReadAsync 370", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754736188705822, "dur": 21, "ph": "X", "name": "ReadAsync 311", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754736188705845, "dur": 22, "ph": "X", "name": "ReadAsync 475", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754736188705868, "dur": 21, "ph": "X", "name": "ReadAsync 415", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754736188705890, "dur": 20, "ph": "X", "name": "ReadAsync 379", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754736188705912, "dur": 22, "ph": "X", "name": "ReadAsync 325", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754736188705935, "dur": 21, "ph": "X", "name": "ReadAsync 391", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754736188705958, "dur": 21, "ph": "X", "name": "ReadAsync 287", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754736188705981, "dur": 59, "ph": "X", "name": "ReadAsync 237", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754736188706041, "dur": 21, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754736188706064, "dur": 22, "ph": "X", "name": "ReadAsync 269", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754736188706087, "dur": 22, "ph": "X", "name": "ReadAsync 336", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754736188706110, "dur": 22, "ph": "X", "name": "ReadAsync 318", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754736188706134, "dur": 21, "ph": "X", "name": "ReadAsync 320", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754736188706156, "dur": 22, "ph": "X", "name": "ReadAsync 276", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754736188706180, "dur": 21, "ph": "X", "name": "ReadAsync 252", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754736188706202, "dur": 22, "ph": "X", "name": "ReadAsync 343", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754736188706225, "dur": 17, "ph": "X", "name": "ReadAsync 288", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754736188706243, "dur": 21, "ph": "X", "name": "ReadAsync 104", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754736188706266, "dur": 58, "ph": "X", "name": "ReadAsync 193", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754736188706325, "dur": 22, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754736188706349, "dur": 20, "ph": "X", "name": "ReadAsync 317", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754736188706371, "dur": 22, "ph": "X", "name": "ReadAsync 305", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754736188706395, "dur": 18, "ph": "X", "name": "ReadAsync 352", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754736188706414, "dur": 49, "ph": "X", "name": "ReadAsync 134", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754736188706464, "dur": 21, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754736188706486, "dur": 22, "ph": "X", "name": "ReadAsync 303", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754736188706510, "dur": 21, "ph": "X", "name": "ReadAsync 361", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754736188706533, "dur": 22, "ph": "X", "name": "ReadAsync 375", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754736188706556, "dur": 17, "ph": "X", "name": "ReadAsync 486", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754736188706575, "dur": 22, "ph": "X", "name": "ReadAsync 222", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754736188706598, "dur": 22, "ph": "X", "name": "ReadAsync 290", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754736188706622, "dur": 23, "ph": "X", "name": "ReadAsync 411", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754736188706646, "dur": 20, "ph": "X", "name": "ReadAsync 400", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754736188706667, "dur": 21, "ph": "X", "name": "ReadAsync 102", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754736188706689, "dur": 51, "ph": "X", "name": "ReadAsync 143", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754736188706742, "dur": 19, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754736188706763, "dur": 22, "ph": "X", "name": "ReadAsync 182", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754736188706786, "dur": 21, "ph": "X", "name": "ReadAsync 268", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754736188706809, "dur": 21, "ph": "X", "name": "ReadAsync 419", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754736188706831, "dur": 21, "ph": "X", "name": "ReadAsync 347", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754736188706855, "dur": 21, "ph": "X", "name": "ReadAsync 445", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754736188706877, "dur": 22, "ph": "X", "name": "ReadAsync 285", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754736188706900, "dur": 22, "ph": "X", "name": "ReadAsync 386", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754736188706924, "dur": 19, "ph": "X", "name": "ReadAsync 322", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754736188706944, "dur": 21, "ph": "X", "name": "ReadAsync 71", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754736188706966, "dur": 59, "ph": "X", "name": "ReadAsync 237", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754736188707027, "dur": 21, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754736188707049, "dur": 22, "ph": "X", "name": "ReadAsync 286", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754736188707073, "dur": 21, "ph": "X", "name": "ReadAsync 289", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754736188707096, "dur": 22, "ph": "X", "name": "ReadAsync 388", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754736188707120, "dur": 22, "ph": "X", "name": "ReadAsync 327", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754736188707143, "dur": 20, "ph": "X", "name": "ReadAsync 292", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754736188707165, "dur": 18, "ph": "X", "name": "ReadAsync 258", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754736188707184, "dur": 21, "ph": "X", "name": "ReadAsync 321", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754736188707206, "dur": 21, "ph": "X", "name": "ReadAsync 225", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754736188707229, "dur": 35, "ph": "X", "name": "ReadAsync 178", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754736188707265, "dur": 31, "ph": "X", "name": "ReadAsync 62", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754736188707297, "dur": 21, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754736188707320, "dur": 28, "ph": "X", "name": "ReadAsync 247", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754736188707350, "dur": 32, "ph": "X", "name": "ReadAsync 317", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754736188707384, "dur": 1, "ph": "X", "name": "ProcessMessages 530", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754736188707386, "dur": 32, "ph": "X", "name": "ReadAsync 530", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754736188707420, "dur": 25, "ph": "X", "name": "ReadAsync 510", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754736188707447, "dur": 19, "ph": "X", "name": "ReadAsync 268", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754736188707467, "dur": 45, "ph": "X", "name": "ReadAsync 343", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754736188707518, "dur": 1, "ph": "X", "name": "ProcessMessages 286", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754736188707521, "dur": 43, "ph": "X", "name": "ReadAsync 286", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754736188707568, "dur": 54, "ph": "X", "name": "ReadAsync 257", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754736188707626, "dur": 1, "ph": "X", "name": "ProcessMessages 167", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754736188707629, "dur": 83, "ph": "X", "name": "ReadAsync 167", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754736188707719, "dur": 2, "ph": "X", "name": "ProcessMessages 69", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754736188707723, "dur": 64, "ph": "X", "name": "ReadAsync 69", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754736188707789, "dur": 618, "ph": "X", "name": "ProcessMessages 60", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754736188708410, "dur": 46, "ph": "X", "name": "ReadAsync 60", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754736188708457, "dur": 2, "ph": "X", "name": "ProcessMessages 884", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754736188708461, "dur": 72, "ph": "X", "name": "ReadAsync 884", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754736188708537, "dur": 24, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754736188708562, "dur": 171, "ph": "X", "name": "ProcessMessages 13", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754736188708734, "dur": 5476, "ph": "X", "name": "ReadAsync 13", "args": {}}, {"pid": 3100, "tid": 24780, "ts": 1754736190644515, "dur": 518, "ph": "X", "name": "ReadEntireBinlogFromIpcAsync", "args": {}}, {"pid": 3100, "tid": 8589934592, "ph": "M", "name": "thread_name", "args": {"name": "WaitForBuildProgramInputDataBeingWrittenAndSendDagReadyMessageAsync"}}, {"pid": 3100, "tid": 8589934592, "ts": 1754736188539252, "dur": 59738, "ph": "X", "name": "await writeBuildProgramInputDataTask", "args": {}}, {"pid": 3100, "tid": 8589934592, "ts": 1754736188598994, "dur": 85916, "ph": "X", "name": "WritePipe.WaitConnectionAsync", "args": {}}, {"pid": 3100, "tid": 8589934592, "ts": 1754736188684914, "dur": 1497, "ph": "X", "name": "WriteDagReadyMessage", "args": {}}, {"pid": 3100, "tid": 24780, "ts": 1754736190645034, "dur": 2, "ph": "X", "name": "WaitForBuildProgramInputDataBeingWrittenAndSendDagReadyMessageAsync", "args": {}}, {"pid": 3100, "tid": 4294967296, "ph": "M", "name": "thread_name", "args": {"name": "BuildAsync"}}, {"pid": 3100, "tid": 4294967296, "ts": 1754736188413892, "dur": 301086, "ph": "X", "name": "RunBackend", "args": {}}, {"pid": 3100, "tid": 4294967296, "ts": 1754736188416187, "dur": 119726, "ph": "X", "name": "BackendProgram.Start", "args": {}}, {"pid": 3100, "tid": 4294967296, "ts": 1754736188715075, "dur": 604655, "ph": "X", "name": "await ExecuteBuildProgram", "args": {}}, {"pid": 3100, "tid": 4294967296, "ts": 1754736189320007, "dur": 1314815, "ph": "X", "name": "RunBackend", "args": {}}, {"pid": 3100, "tid": 4294967296, "ts": 1754736189320199, "dur": 116445, "ph": "X", "name": "BackendProgram.Start", "args": {}}, {"pid": 3100, "tid": 4294967296, "ts": 1754736190634837, "dur": 3396, "ph": "X", "name": "await WaitForAndApplyScriptUpdaters", "args": {}}, {"pid": 3100, "tid": 4294967296, "ts": 1754736190637464, "dur": 37, "ph": "X", "name": "await <PERSON><PERSON>tUp<PERSON><PERSON>", "args": {}}, {"pid": 3100, "tid": 4294967296, "ts": 1754736190638238, "dur": 9, "ph": "X", "name": "await taskToReadBuildProgramOutput", "args": {}}, {"pid": 3100, "tid": 24780, "ts": 1754736190645037, "dur": 12, "ph": "X", "name": "BuildAsync", "args": {}}, {"cat": "", "pid": 12345, "tid": 0, "ts": 0, "ph": "M", "name": "process_name", "args": {"name": "bee_backend"}}, {"pid": 12345, "tid": 0, "ts": 1754736189572555, "dur": 100422, "ph": "X", "name": "DriverInitData", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1754736189672983, "dur": 258, "ph": "X", "name": "RemoveStaleOutputs", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1754736189673338, "dur": 52, "ph": "X", "name": "<PERSON><PERSON>", "args": {"detail": "PrepareNodes"}}, {"pid": 12345, "tid": 0, "ts": 1754736189673390, "dur": 390, "ph": "X", "name": "BuildQueueInit", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1754736189678422, "dur": 121, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipeline.Universal.ShaderLibrary.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 0, "ts": 1754736189681214, "dur": 59, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.InputSystem.ForUI.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 0, "ts": 1754736189673796, "dur": 17769, "ph": "X", "name": "EnqueueRequestedNodes", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1754736189691578, "dur": 935539, "ph": "X", "name": "WaitForBuildFinished", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1754736190627118, "dur": 209, "ph": "X", "name": "JoinBuildThread", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1754736190627601, "dur": 1073, "ph": "X", "name": "<PERSON><PERSON>", "args": {"detail": "Write AllBuiltNodes"}}, {"pid": 12345, "tid": 1, "ts": 1754736189674186, "dur": 17403, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1754736189691596, "dur": 2553, "ph": "X", "name": "CheckDagSignatures", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1754736189694150, "dur": 160, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1754736189694311, "dur": 187, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1754736189694498, "dur": 162, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1754736189694661, "dur": 510, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1754736189695171, "dur": 175, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1754736189695346, "dur": 170, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1754736189695517, "dur": 250, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1754736189695767, "dur": 138, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1754736189695905, "dur": 218, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1754736189696124, "dur": 233, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1754736189696358, "dur": 323, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1754736189696682, "dur": 271, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1754736189696953, "dur": 640, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1754736189697593, "dur": 314, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1754736189697908, "dur": 200, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1754736189698108, "dur": 101, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1754736189698282, "dur": 249, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1754736189698531, "dur": 295, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1754736189698828, "dur": 80, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.ShaderGraph.Utilities.dll.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1754736189698925, "dur": 320, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.ShaderGraph.Utilities.dll (+2 others)"}}, {"pid": 12345, "tid": 1, "ts": 1754736189699245, "dur": 118, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1754736189699384, "dur": 119, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Tests.dll.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1754736189699577, "dur": 303, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Tests.dll (+2 others)"}}, {"pid": 12345, "tid": 1, "ts": 1754736189699936, "dur": 83, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.InputSystem.ForUI.dll.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1754736189700030, "dur": 271, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.InputSystem.ForUI.dll (+2 others)"}}, {"pid": 12345, "tid": 1, "ts": 1754736189700353, "dur": 168, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1754736189700521, "dur": 610, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1754736189701132, "dur": 200, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/com.crashkonijn.docs.getting_started.dll (+2 others)"}}, {"pid": 12345, "tid": 1, "ts": 1754736189701359, "dur": 280115, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1754736189981476, "dur": 1273, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/CrashKonijn.Goap.Runtime.dll (+pdb)"}}, {"pid": 12345, "tid": 1, "ts": 1754736189982785, "dur": 1237, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.RenderPipelines.Universal.Runtime.dll (+pdb)"}}, {"pid": 12345, "tid": 1, "ts": 1754736189984023, "dur": 78, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1754736189984205, "dur": 7275, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\GameSoft\\Unity\\Editor\\2022.3.38f1c1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.TerrainPhysicsModule.dll"}}, {"pid": 12345, "tid": 1, "ts": 1754736189984115, "dur": 8328, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/CrashKonijn.Agent.Runtime.dll (+pdb)"}}, {"pid": 12345, "tid": 1, "ts": 1754736189992536, "dur": 68, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1754736189992610, "dur": 634523, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1754736189674330, "dur": 17340, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1754736189691690, "dur": 68, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.VFXModule.dll_7FCD2258FCB2D3F4.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1754736189691759, "dur": 77, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1754736189692029, "dur": 78, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1754736189692162, "dur": 98, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/UnityEditor.UI.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 2, "ts": 1754736189692385, "dur": 81, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Universal.Config.Runtime.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 2, "ts": 1754736189692495, "dur": 274, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Searcher.Editor.rsp"}}, {"pid": 12345, "tid": 2, "ts": 1754736189692855, "dur": 75, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/URPWizard.rsp2"}}, {"pid": 12345, "tid": 2, "ts": 1754736189693249, "dur": 90, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1754736189693376, "dur": 182, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1754736189693558, "dur": 189, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1754736189693747, "dur": 184, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1754736189693932, "dur": 173, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1754736189694106, "dur": 1160, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1754736189695267, "dur": 175, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1754736189695443, "dur": 661, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1754736189696105, "dur": 228, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1754736189696333, "dur": 197, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1754736189696530, "dur": 309, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1754736189696840, "dur": 424, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1754736189697264, "dur": 167, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1754736189697432, "dur": 189, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1754736189697621, "dur": 178, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1754736189697799, "dur": 229, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1754736189698051, "dur": 55, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1754736189698107, "dur": 146, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1754736189698274, "dur": 240, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1754736189698514, "dur": 312, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1754736189698826, "dur": 66, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.Searcher.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1754736189698930, "dur": 120, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.Searcher.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1754736189699051, "dur": 323, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Searcher.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 2, "ts": 1754736189699374, "dur": 91, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1754736189699505, "dur": 102, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.Mathematics.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1754736189699663, "dur": 319, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Mathematics.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 2, "ts": 1754736189700064, "dur": 66, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/com.crashkonijn.goap.demos.turn-based.dll.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1754736189700142, "dur": 177, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1754736189700319, "dur": 189, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1754736189700508, "dur": 624, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1754736189701133, "dur": 195, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/com.crashkonijn.goap.demos.turn-based.dll (+2 others)"}}, {"pid": 12345, "tid": 2, "ts": 1754736189701368, "dur": 280317, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1754736189981695, "dur": 1171, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.InputSystem.dll (+pdb)"}}, {"pid": 12345, "tid": 2, "ts": 1754736189982937, "dur": 1305, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.VisualScripting.Flow.dll (+pdb)"}}, {"pid": 12345, "tid": 2, "ts": 1754736189984243, "dur": 56, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1754736189984308, "dur": 1147, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.RenderPipelines.Universal.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 2, "ts": 1754736189985488, "dur": 1193, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.TextMeshPro.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 2, "ts": 1754736189986720, "dur": 1166, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/URPWizard.dll (+pdb)"}}, {"pid": 12345, "tid": 2, "ts": 1754736189987920, "dur": 1217, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Mathematics.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 2, "ts": 1754736189989365, "dur": 51, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "CopyFiles Library/ScriptAssemblies/PPv2URPConverters.dll"}}, {"pid": 12345, "tid": 2, "ts": 1754736189989439, "dur": 520, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.Collections.BurstCompatibilityGen.pdb"}}, {"pid": 12345, "tid": 2, "ts": 1754736189989960, "dur": 3744, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1754736189993704, "dur": 633427, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1754736189674240, "dur": 17376, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1754736189691798, "dur": 71, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.SceneTemplateModule.dll_188EDD6374EAFAAD.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1754736189691940, "dur": 69, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.PhysicsModule.dll_06AF3D16206C6E73.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1754736189692059, "dur": 100, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.visualscripting@1.9.4\\Runtime\\VisualScripting.Flow\\Dependencies\\NCalc\\Unity.VisualScripting.Antlr3.Runtime.dll"}}, {"pid": 12345, "tid": 3, "ts": 1754736189692058, "dur": 103, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.VisualScripting.Antlr3.Runtime.dll_FBC62C826EB2B4B0.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1754736189692171, "dur": 67, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.VisualScripting.Antlr3.Runtime.dll_FBC62C826EB2B4B0.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1754736189692352, "dur": 79, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Universal.Config.Runtime.rsp2"}}, {"pid": 12345, "tid": 3, "ts": 1754736189692735, "dur": 100, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.InputSystem.ForUI.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 3, "ts": 1754736189692919, "dur": 87, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/URPWizard.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 3, "ts": 1754736189693014, "dur": 81, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.InputSystem.TestFramework.rsp2"}}, {"pid": 12345, "tid": 3, "ts": 1754736189693219, "dur": 103, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1754736189693381, "dur": 184, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1754736189693565, "dur": 161, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1754736189693726, "dur": 165, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1754736189693891, "dur": 137, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1754736189694028, "dur": 430, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1754736189694459, "dur": 161, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1754736189694620, "dur": 484, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1754736189695104, "dur": 249, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1754736189695354, "dur": 159, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1754736189695513, "dur": 601, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1754736189696114, "dur": 207, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1754736189696321, "dur": 307, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1754736189696628, "dur": 214, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1754736189696842, "dur": 164, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1754736189697006, "dur": 216, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1754736189697222, "dur": 213, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1754736189697435, "dur": 192, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1754736189697627, "dur": 177, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1754736189697804, "dur": 186, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1754736189697990, "dur": 191, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1754736189698273, "dur": 239, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1754736189698512, "dur": 317, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1754736189698830, "dur": 66, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.Timeline.dll.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1754736189698912, "dur": 413, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Timeline.dll (+2 others)"}}, {"pid": 12345, "tid": 3, "ts": 1754736189699326, "dur": 74, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1754736189699419, "dur": 98, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.Timeline.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1754736189699518, "dur": 116, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1754736189699669, "dur": 592, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Timeline.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 3, "ts": 1754736189700328, "dur": 177, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1754736189700506, "dur": 67, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Universal.Config.Runtime.dll.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1754736189700587, "dur": 201, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Universal.Config.Runtime.dll (+2 others)"}}, {"pid": 12345, "tid": 3, "ts": 1754736189700788, "dur": 70, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1754736189700925, "dur": 353, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Universal.Runtime.dll (+2 others)"}}, {"pid": 12345, "tid": 3, "ts": 1754736189701327, "dur": 70, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Cinemachine.dll.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1754736189701398, "dur": 114, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1754736189701518, "dur": 292, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Cinemachine.dll (+2 others)"}}, {"pid": 12345, "tid": 3, "ts": 1754736189701837, "dur": 279852, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1754736189981690, "dur": 1168, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/com.crashkonijn.goap.demos.turn-based.dll (+pdb)"}}, {"pid": 12345, "tid": 3, "ts": 1754736189982901, "dur": 1258, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Collections.dll (+pdb)"}}, {"pid": 12345, "tid": 3, "ts": 1754736189984160, "dur": 60, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1754736189984237, "dur": 1301, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.VisualScripting.SettingsProvider.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 3, "ts": 1754736189986626, "dur": 2608, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\GameSoft\\Unity\\Editor\\2022.3.38f1c1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Diagnostics.Debug.dll"}}, {"pid": 12345, "tid": 3, "ts": 1754736189985597, "dur": 3783, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Collections.BurstCompatibilityGen.dll (+pdb)"}}, {"pid": 12345, "tid": 3, "ts": 1754736189989430, "dur": 81, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.Collections.BurstCompatibilityGen.dll"}}, {"pid": 12345, "tid": 3, "ts": 1754736189989695, "dur": 83, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.VisualScripting.State.pdb"}}, {"pid": 12345, "tid": 3, "ts": 1754736189989822, "dur": 162, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "CopyFiles Library/ScriptAssemblies/CrashKonijn.Goap.Resolver.pdb"}}, {"pid": 12345, "tid": 3, "ts": 1754736189989984, "dur": 436992, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1754736190426979, "dur": 127, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\Bee\\artifacts\\1900b0aE.dag\\post-processed\\Assembly-CSharp.pdb"}}, {"pid": 12345, "tid": 3, "ts": 1754736190426978, "dur": 129, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Assembly-CSharp.pdb"}}, {"pid": 12345, "tid": 3, "ts": 1754736190427134, "dur": 1718, "ph": "X", "name": "CopyFiles", "args": {"detail": "Library/ScriptAssemblies/Assembly-CSharp.pdb"}}, {"pid": 12345, "tid": 3, "ts": 1754736190428855, "dur": 198280, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1754736189674255, "dur": 17371, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1754736189691633, "dur": 53, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.UIElementsSamplesModule.dll_623E7535F4BD3DF5.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1754736189691820, "dur": 200, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ARModule.dll_969BEA2E4A09BAD9.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1754736189692022, "dur": 71, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\GameSoft\\Unity\\Editor\\2022.3.38f1c1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.UnityWebRequestAudioModule.dll"}}, {"pid": 12345, "tid": 4, "ts": 1754736189692021, "dur": 94, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityWebRequestAudioModule.dll_180837F1985FCEBE.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1754736189692159, "dur": 154, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/UnityEditor.UI.rsp2"}}, {"pid": 12345, "tid": 4, "ts": 1754736189692355, "dur": 65, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/CrashKonijn.Agent.Core.rsp"}}, {"pid": 12345, "tid": 4, "ts": 1754736189692585, "dur": 52, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.ShaderGraph.Editor.rsp"}}, {"pid": 12345, "tid": 4, "ts": 1754736189692662, "dur": 186, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.PlasticSCM.Editor.rsp2"}}, {"pid": 12345, "tid": 4, "ts": 1754736189692912, "dur": 69, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/com.crashkonijn.docs.getting_started.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 4, "ts": 1754736189693067, "dur": 79, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.InputSystem.TestFramework.rsp"}}, {"pid": 12345, "tid": 4, "ts": 1754736189693254, "dur": 89, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1754736189693388, "dur": 156, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1754736189693545, "dur": 149, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1754736189693694, "dur": 179, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1754736189693874, "dur": 187, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1754736189694061, "dur": 146, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1754736189694207, "dur": 240, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1754736189694447, "dur": 200, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1754736189694648, "dur": 484, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1754736189695133, "dur": 619, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1754736189695753, "dur": 144, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1754736189695897, "dur": 448, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1754736189696346, "dur": 162, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1754736189696508, "dur": 205, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1754736189696714, "dur": 163, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1754736189696877, "dur": 181, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1754736189697058, "dur": 172, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1754736189697230, "dur": 288, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1754736189697518, "dur": 192, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1754736189697711, "dur": 208, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1754736189697919, "dur": 185, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1754736189698105, "dur": 224, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1754736189698329, "dur": 193, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1754736189698522, "dur": 299, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1754736189698822, "dur": 61, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.InputSystem.dll.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1754736189698901, "dur": 979, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.InputSystem.dll (+2 others)"}}, {"pid": 12345, "tid": 4, "ts": 1754736189699934, "dur": 83, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Core.Runtime.dll.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1754736189700033, "dur": 397, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Core.Runtime.dll (+2 others)"}}, {"pid": 12345, "tid": 4, "ts": 1754736189700595, "dur": 168, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipeline.Universal.ShaderLibrary.dll (+2 others)"}}, {"pid": 12345, "tid": 4, "ts": 1754736189700798, "dur": 337, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1754736189701135, "dur": 1067, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1754736189702267, "dur": 166, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.SettingsProvider.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 4, "ts": 1754736189702472, "dur": 279226, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1754736189981698, "dur": 1227, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.RenderPipelines.Universal.Shaders.dll (+pdb)"}}, {"pid": 12345, "tid": 4, "ts": 1754736189984048, "dur": 233, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\GameSoft\\Unity\\Editor\\2022.3.38f1c1\\Editor\\Data\\Tools\\ilpp\\Unity.ILPP.Runner\\System.Diagnostics.DiagnosticSource.dll"}}, {"pid": 12345, "tid": 4, "ts": 1754736189982955, "dur": 3263, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Burst.dll (+pdb)"}}, {"pid": 12345, "tid": 4, "ts": 1754736189986625, "dur": 3676, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\GameSoft\\Unity\\Editor\\2022.3.38f1c1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.ServiceModel.Primitives.dll"}}, {"pid": 12345, "tid": 4, "ts": 1754736189990405, "dur": 303, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\GameSoft\\Unity\\Editor\\2022.3.38f1c1\\Editor\\Data\\Tools\\ilpp\\Unity.ILPP.Runner\\hostfxr.dll"}}, {"pid": 12345, "tid": 4, "ts": 1754736189991038, "dur": 111, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\GameSoft\\Unity\\Editor\\2022.3.38f1c1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.ClusterInputModule.dll"}}, {"pid": 12345, "tid": 4, "ts": 1754736189986262, "dur": 5198, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Searcher.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 4, "ts": 1754736189991519, "dur": 395, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1754736189991924, "dur": 635200, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1754736189674207, "dur": 17393, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1754736189691765, "dur": 55, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1754736189692031, "dur": 82, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\GameSoft\\Unity\\Editor\\2022.3.38f1c1\\Editor\\Data\\PlaybackEngines\\WindowsStandaloneSupport\\UnityEditor.WindowsStandalone.Extensions.dll"}}, {"pid": 12345, "tid": 5, "ts": 1754736189692030, "dur": 85, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.WindowsStandalone.Extensions.dll_35385077F0CEC424.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1754736189692172, "dur": 70, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Burst.rsp2"}}, {"pid": 12345, "tid": 5, "ts": 1754736189692419, "dur": 129, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Flow.rsp"}}, {"pid": 12345, "tid": 5, "ts": 1754736189692614, "dur": 75, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Universal.Editor.rsp2"}}, {"pid": 12345, "tid": 5, "ts": 1754736189692858, "dur": 87, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/GlassSystem.rsp2"}}, {"pid": 12345, "tid": 5, "ts": 1754736189692971, "dur": 62, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/PPv2URPConverters.rsp"}}, {"pid": 12345, "tid": 5, "ts": 1754736189693242, "dur": 95, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1754736189693391, "dur": 173, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1754736189693564, "dur": 170, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1754736189693734, "dur": 195, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1754736189693929, "dur": 192, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1754736189694121, "dur": 138, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1754736189694259, "dur": 219, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1754736189694478, "dur": 149, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1754736189694628, "dur": 504, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1754736189695132, "dur": 567, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1754736189695699, "dur": 219, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1754736189695918, "dur": 204, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1754736189696123, "dur": 446, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1754736189696569, "dur": 605, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1754736189697174, "dur": 160, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1754736189697334, "dur": 195, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1754736189697529, "dur": 169, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1754736189697698, "dur": 199, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1754736189697898, "dur": 182, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1754736189698080, "dur": 247, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1754736189698327, "dur": 183, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1754736189698528, "dur": 315, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1754736189698849, "dur": 74, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/CrashKonijn.Goap.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1754736189698924, "dur": 402, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1754736189699331, "dur": 167, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/GlassSystem.dll.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1754736189699498, "dur": 199, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1754736189699701, "dur": 256, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/GlassSystem.dll (+2 others)"}}, {"pid": 12345, "tid": 5, "ts": 1754736189699993, "dur": 69, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1754736189700063, "dur": 66, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/com.crashkonijn.goap.demos.simple.dll.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1754736189700139, "dur": 159, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1754736189700328, "dur": 179, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1754736189700508, "dur": 627, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1754736189701135, "dur": 202, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1754736189701338, "dur": 52, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/URPWizard.dll.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1754736189701412, "dur": 253, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/URPWizard.dll (+2 others)"}}, {"pid": 12345, "tid": 5, "ts": 1754736189701666, "dur": 58, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1754736189701733, "dur": 279739, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1754736189981677, "dur": 1725, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\GameSoft\\Unity\\Editor\\2022.3.38f1c1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Runtime.CompilerServices.VisualC.dll"}}, {"pid": 12345, "tid": 5, "ts": 1754736189983852, "dur": 496, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\GameSoft\\Unity\\Editor\\2022.3.38f1c1\\Editor\\Data\\Tools\\ilpp\\Unity.ILPP.Runner\\Microsoft.Extensions.Configuration.CommandLine.dll"}}, {"pid": 12345, "tid": 5, "ts": 1754736189981474, "dur": 3461, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.RenderPipelines.ShaderGraph.ShaderGraphLibrary.dll (+pdb)"}}, {"pid": 12345, "tid": 5, "ts": 1754736189985670, "dur": 139, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\GameSoft\\Unity\\Editor\\2022.3.38f1c1\\Editor\\Data\\Tools\\ilpp\\Unity.ILPP.Runner\\System.Globalization.dll"}}, {"pid": 12345, "tid": 5, "ts": 1754736189986288, "dur": 237, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\GameSoft\\Unity\\Editor\\2022.3.38f1c1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.IO.FileSystem.Primitives.dll"}}, {"pid": 12345, "tid": 5, "ts": 1754736189986625, "dur": 3641, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\GameSoft\\Unity\\Editor\\2022.3.38f1c1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.ComponentModel.Annotations.dll"}}, {"pid": 12345, "tid": 5, "ts": 1754736189984989, "dur": 5449, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/CrashKonijn.Goap.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 5, "ts": 1754736189991564, "dur": 635543, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1754736189674302, "dur": 17347, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1754736189691797, "dur": 82, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.AccessibilityModule.dll_4B3448FA7561224A.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1754736189691899, "dur": 75, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.HotReloadModule.dll_3D201F391D2079F2.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1754736189692028, "dur": 83, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\GameSoft\\Unity\\Editor\\2022.3.38f1c1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.UnityWebRequestTextureModule.dll"}}, {"pid": 12345, "tid": 6, "ts": 1754736189692027, "dur": 87, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityWebRequestTextureModule.dll_3AB3AEE512D0C853.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1754736189692130, "dur": 170, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityWebRequestTextureModule.dll_3AB3AEE512D0C853.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1754736189692307, "dur": 53, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/UnityEngine.TestRunner.dll.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1754736189693215, "dur": 106, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\GameSoft\\Unity\\Editor\\2022.3.38f1c1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Runtime.Handles.dll"}}, {"pid": 12345, "tid": 6, "ts": 1754736189693496, "dur": 59, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\GameSoft\\Unity\\Editor\\2022.3.38f1c1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Text.Encoding.Extensions.dll"}}, {"pid": 12345, "tid": 6, "ts": 1754736189693872, "dur": 146, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.test-framework@1.1.33\\UnityEngine.TestRunner\\Assertions\\UnityTestTimeoutException.cs"}}, {"pid": 12345, "tid": 6, "ts": 1754736189694391, "dur": 765, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.test-framework@1.1.33\\UnityEngine.TestRunner\\NUnitExtensions\\IAsyncTestAssemblyBuilder.cs"}}, {"pid": 12345, "tid": 6, "ts": 1754736189695530, "dur": 383, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.test-framework@1.1.33\\UnityEngine.TestRunner\\TestRunner\\TestListenerWrapper.cs"}}, {"pid": 12345, "tid": 6, "ts": 1754736189696176, "dur": 99, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.test-framework@1.1.33\\UnityEngine.TestRunner\\Utils\\TestRunCallbackListener.cs"}}, {"pid": 12345, "tid": 6, "ts": 1754736189692375, "dur": 3973, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/UnityEngine.TestRunner.dll (+2 others)"}}, {"pid": 12345, "tid": 6, "ts": 1754736189696403, "dur": 114, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/UnityEditor.TestRunner.dll.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1754736189696704, "dur": 58, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\GameSoft\\Unity\\Editor\\2022.3.38f1c1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.UnityWebRequestAudioModule.dll"}}, {"pid": 12345, "tid": 6, "ts": 1754736189697990, "dur": 51, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\GameSoft\\Unity\\Editor\\2022.3.38f1c1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Xml.XPath.dll"}}, {"pid": 12345, "tid": 6, "ts": 1754736189698127, "dur": 64, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\GameSoft\\Unity\\Editor\\2022.3.38f1c1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\System.Drawing.dll"}}, {"pid": 12345, "tid": 6, "ts": 1754736189696528, "dur": 1935, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/UnityEditor.TestRunner.dll (+2 others)"}}, {"pid": 12345, "tid": 6, "ts": 1754736189698582, "dur": 182, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/UnityEditor.UI.dll (+2 others)"}}, {"pid": 12345, "tid": 6, "ts": 1754736189698824, "dur": 58, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.Burst.dll.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1754736189698904, "dur": 390, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Burst.dll (+2 others)"}}, {"pid": 12345, "tid": 6, "ts": 1754736189699360, "dur": 159, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.Burst.CodeGen.dll.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1754736189699531, "dur": 319, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Burst.CodeGen.dll (+2 others)"}}, {"pid": 12345, "tid": 6, "ts": 1754736189699850, "dur": 431, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1754736189700301, "dur": 339, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPP-Configuration Library/ilpp-configuration.nevergeneratedoutput"}}, {"pid": 12345, "tid": 6, "ts": 1754736189700669, "dur": 72, "ph": "X", "name": "StoreTimestampsOfNonGeneratedInputFiles", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1754736189700750, "dur": 276187, "ph": "X", "name": "ILPP-Configuration", "args": {"detail": "Library/ilpp-configuration.nevergeneratedoutput"}}, {"pid": 12345, "tid": 6, "ts": 1754736189980085, "dur": 1489, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/com.crashkonijn.goap.demos.complex.dll (+pdb)"}}, {"pid": 12345, "tid": 6, "ts": 1754736189981575, "dur": 54, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1754736189981637, "dur": 1235, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/CrashKonijn.Goap.Core.dll (+pdb)"}}, {"pid": 12345, "tid": 6, "ts": 1754736189982941, "dur": 1252, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Cinemachine.dll (+pdb)"}}, {"pid": 12345, "tid": 6, "ts": 1754736189984249, "dur": 1137, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.RenderPipelines.Core.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 6, "ts": 1754736189985424, "dur": 1140, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.VisualScripting.Core.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 6, "ts": 1754736189986603, "dur": 1203, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.ShaderGraph.Utilities.dll (+pdb)"}}, {"pid": 12345, "tid": 6, "ts": 1754736189987854, "dur": 1154, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.ShaderGraph.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 6, "ts": 1754736189989168, "dur": 83, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "CopyFiles Library/ScriptAssemblies/UnityEngine.UI.dll"}}, {"pid": 12345, "tid": 6, "ts": 1754736189989285, "dur": 69, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1754736189989373, "dur": 124, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "CopyFiles Library/ScriptAssemblies/PPv2URPConverters.pdb"}}, {"pid": 12345, "tid": 6, "ts": 1754736189989704, "dur": 240, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Tests.pdb"}}, {"pid": 12345, "tid": 6, "ts": 1754736189989945, "dur": 2588, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1754736189992562, "dur": 634563, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1754736189674280, "dur": 17358, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1754736189691748, "dur": 50, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1754736189691833, "dur": 103, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.AssetBundleModule.dll_022E81689B98A39E.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1754736189691957, "dur": 108, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.SpriteMaskModule.dll_F2FD8C610219B039.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1754736189692067, "dur": 64, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.visualscripting@1.9.4\\Editor\\VisualScripting.Core\\Dependencies\\YamlDotNet\\Unity.VisualScripting.YamlDotNet.dll"}}, {"pid": 12345, "tid": 7, "ts": 1754736189692066, "dur": 67, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.VisualScripting.YamlDotNet.dll_4A5905A13DE9A491.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1754736189692141, "dur": 98, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.VisualScripting.YamlDotNet.dll_4A5905A13DE9A491.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1754736189692584, "dur": 206, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.ShaderGraph.Editor.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 7, "ts": 1754736189693233, "dur": 102, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1754736189693335, "dur": 92, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/17766336155681823506.rsp"}}, {"pid": 12345, "tid": 7, "ts": 1754736189693428, "dur": 184, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1754736189693612, "dur": 186, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1754736189693798, "dur": 159, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1754736189693957, "dur": 331, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1754736189694288, "dur": 190, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1754736189694479, "dur": 150, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1754736189694630, "dur": 460, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1754736189695090, "dur": 195, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1754736189695285, "dur": 164, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1754736189695449, "dur": 291, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1754736189695740, "dur": 156, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1754736189695897, "dur": 183, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1754736189696080, "dur": 245, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1754736189696325, "dur": 187, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1754736189696512, "dur": 194, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1754736189696706, "dur": 163, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1754736189696870, "dur": 179, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1754736189697049, "dur": 208, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1754736189697257, "dur": 166, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1754736189697424, "dur": 292, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1754736189697716, "dur": 228, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1754736189697944, "dur": 150, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1754736189698094, "dur": 201, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1754736189698295, "dur": 239, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1754736189698534, "dur": 304, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1754736189698840, "dur": 94, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.VSCode.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1754736189698947, "dur": 315, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.VSCode.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 7, "ts": 1754736189699307, "dur": 115, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/com.crashkonijn.docs.getting_started.dll.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1754736189699423, "dur": 66, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1754736189699508, "dur": 92, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/CrashKonijn.Goap.Runtime.dll.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1754736189699650, "dur": 312, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1754736189699962, "dur": 120, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1754736189700082, "dur": 220, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1754736189700344, "dur": 165, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1754736189700509, "dur": 630, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1754736189701139, "dur": 1581, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1754736189702721, "dur": 59, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Assembly-CSharp-Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1754736189702799, "dur": 277918, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1754736189980718, "dur": 1219, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.VisualScripting.State.dll (+pdb)"}}, {"pid": 12345, "tid": 7, "ts": 1754736189982779, "dur": 140, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\GameSoft\\Unity\\Editor\\2022.3.38f1c1\\Editor\\Data\\Managed\\UnityEngine\\UnityEditor.UIElementsSamplesModule.dll"}}, {"pid": 12345, "tid": 7, "ts": 1754736189981982, "dur": 1374, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/GlassSystem.dll (+pdb)"}}, {"pid": 12345, "tid": 7, "ts": 1754736189983789, "dur": 67, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\GameSoft\\Unity\\Editor\\2022.3.38f1c1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\System.IO.Compression.FileSystem.dll"}}, {"pid": 12345, "tid": 7, "ts": 1754736189983868, "dur": 115, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.collab-proxy@2.8.2\\Lib\\Editor\\unityplastic.dll"}}, {"pid": 12345, "tid": 7, "ts": 1754736189984196, "dur": 219, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\GameSoft\\Unity\\Editor\\2022.3.38f1c1\\Editor\\Data\\Tools\\ilpp\\Unity.ILPP.Runner\\Microsoft.AspNetCore.Authorization.dll"}}, {"pid": 12345, "tid": 7, "ts": 1754736189983389, "dur": 1851, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Mathematics.dll (+pdb)"}}, {"pid": 12345, "tid": 7, "ts": 1754736189985405, "dur": 83, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\GameSoft\\Unity\\Editor\\2022.3.38f1c1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netfx\\System.ServiceModel.Web.dll"}}, {"pid": 12345, "tid": 7, "ts": 1754736189986249, "dur": 83, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\GameSoft\\Unity\\Editor\\2022.3.38f1c1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.dll"}}, {"pid": 12345, "tid": 7, "ts": 1754736189986625, "dur": 2807, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\GameSoft\\Unity\\Editor\\2022.3.38f1c1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.IO.Compression.ZipFile.dll"}}, {"pid": 12345, "tid": 7, "ts": 1754736189985296, "dur": 4287, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.CollabProxy.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 7, "ts": 1754736189989696, "dur": 167, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "CopyFiles Library/ScriptAssemblies/com.crashkonijn.goap.demos.turn-based.pdb"}}, {"pid": 12345, "tid": 7, "ts": 1754736189989865, "dur": 1661, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1754736189991526, "dur": 635592, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1754736189674409, "dur": 17306, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1754736189691745, "dur": 55, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1754736189691865, "dur": 88, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.CoreModule.dll_D9FC4B6329D94867.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1754736189692024, "dur": 82, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1754736189692172, "dur": 163, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/UnityEngine.UI.rsp"}}, {"pid": 12345, "tid": 8, "ts": 1754736189692703, "dur": 113, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Rider.Editor.rsp2"}}, {"pid": 12345, "tid": 8, "ts": 1754736189693021, "dur": 61, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Collections.BurstCompatibilityGen.rsp2"}}, {"pid": 12345, "tid": 8, "ts": 1754736189693226, "dur": 102, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1754736189693328, "dur": 73, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/15312589170787849123.rsp"}}, {"pid": 12345, "tid": 8, "ts": 1754736189693402, "dur": 193, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1754736189693595, "dur": 196, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1754736189693791, "dur": 299, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1754736189694091, "dur": 140, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1754736189694318, "dur": 827, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.ide.rider@3.0.31\\Rider\\Editor\\ProjectGeneration\\PackageManagerTracker.cs"}}, {"pid": 12345, "tid": 8, "ts": 1754736189694231, "dur": 968, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1754736189695199, "dur": 154, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1754736189695353, "dur": 629, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1754736189695983, "dur": 166, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1754736189696149, "dur": 185, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1754736189696335, "dur": 167, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1754736189696502, "dur": 194, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1754736189696696, "dur": 164, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1754736189696860, "dur": 159, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1754736189697019, "dur": 189, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1754736189697208, "dur": 182, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1754736189697390, "dur": 281, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1754736189697671, "dur": 152, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1754736189697824, "dur": 196, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1754736189698020, "dur": 69, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1754736189698089, "dur": 268, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1754736189698357, "dur": 170, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1754736189698527, "dur": 312, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1754736189698840, "dur": 66, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.VisualStudio.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1754736189698925, "dur": 79, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.ShaderGraph.ShaderGraphLibrary.dll.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1754736189699016, "dur": 306, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.ShaderGraph.ShaderGraphLibrary.dll (+2 others)"}}, {"pid": 12345, "tid": 8, "ts": 1754736189699322, "dur": 71, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1754736189699416, "dur": 51, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1754736189699555, "dur": 351, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/CrashKonijn.Agent.Runtime.dll (+2 others)"}}, {"pid": 12345, "tid": 8, "ts": 1754736189699936, "dur": 90, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Core.dll.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1754736189700062, "dur": 688, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Core.dll (+2 others)"}}, {"pid": 12345, "tid": 8, "ts": 1754736189700846, "dur": 623, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Flow.dll (+2 others)"}}, {"pid": 12345, "tid": 8, "ts": 1754736189701545, "dur": 63, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.State.dll.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1754736189701624, "dur": 275, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.State.dll (+2 others)"}}, {"pid": 12345, "tid": 8, "ts": 1754736189701946, "dur": 279668, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1754736189981677, "dur": 7438, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\GameSoft\\Unity\\Editor\\2022.3.38f1c1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.PropertiesModule.dll"}}, {"pid": 12345, "tid": 8, "ts": 1754736189989456, "dur": 2011, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\GameSoft\\Unity\\Editor\\2022.3.38f1c1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Reflection.Emit.dll"}}, {"pid": 12345, "tid": 8, "ts": 1754736189991525, "dur": 1212, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\GameSoft\\Unity\\Editor\\2022.3.38f1c1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\System.Core.dll"}}, {"pid": 12345, "tid": 8, "ts": 1754736189981615, "dur": 11935, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.InputSystem.ForUI.dll (+pdb)"}}, {"pid": 12345, "tid": 8, "ts": 1754736189993624, "dur": 633509, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1754736189674367, "dur": 17324, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1754736189691945, "dur": 100, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ProfilerModule.dll_6E85C30F177A7F51.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1754736189692073, "dur": 52, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1754736189692264, "dur": 119, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.Burst.Cecil.Mdb.dll_825BAFEC5BACF8AB.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1754736189692418, "dur": 179, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Searcher.Editor.rsp2"}}, {"pid": 12345, "tid": 9, "ts": 1754736189692628, "dur": 145, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.TextMeshPro.rsp"}}, {"pid": 12345, "tid": 9, "ts": 1754736189692862, "dur": 112, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Universal.Shaders.rsp"}}, {"pid": 12345, "tid": 9, "ts": 1754736189693900, "dur": 402, "ph": "X", "name": "WriteText", "args": {"detail": "Library/Bee/artifacts/1900b0aE.dag/Assembly-CSharp.rsp"}}, {"pid": 12345, "tid": 9, "ts": 1754736189694303, "dur": 258, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1754736189694561, "dur": 192, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1754736189694753, "dur": 550, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1754736189695305, "dur": 164, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1754736189695470, "dur": 232, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1754736189695703, "dur": 298, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1754736189696001, "dur": 160, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1754736189696161, "dur": 273, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1754736189696434, "dur": 167, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1754736189696602, "dur": 617, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1754736189697220, "dur": 208, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1754736189697428, "dur": 192, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1754736189697620, "dur": 179, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1754736189697800, "dur": 546, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1754736189698347, "dur": 179, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1754736189698526, "dur": 311, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1754736189698838, "dur": 61, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.PlasticSCM.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1754736189699033, "dur": 54, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\GameSoft\\Unity\\Editor\\2022.3.38f1c1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Collections.Specialized.dll"}}, {"pid": 12345, "tid": 9, "ts": 1754736189699303, "dur": 56, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.collab-proxy@2.8.2\\Editor\\AssetMenu\\AssetCopyPathOperation.cs"}}, {"pid": 12345, "tid": 9, "ts": 1754736189699423, "dur": 55, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.collab-proxy@2.8.2\\Editor\\AssetsUtils\\Processor\\UnityCloudProjectLinkMonitor.cs"}}, {"pid": 12345, "tid": 9, "ts": 1754736189698916, "dur": 1056, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.PlasticSCM.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 9, "ts": 1754736189700025, "dur": 73, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.CollabProxy.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1754736189700145, "dur": 279, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.CollabProxy.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 9, "ts": 1754736189700511, "dur": 623, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1754736189701134, "dur": 345, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/com.crashkonijn.goap.demos.complex.dll (+2 others)"}}, {"pid": 12345, "tid": 9, "ts": 1754736189701527, "dur": 279123, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1754736189981678, "dur": 2388, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\GameSoft\\Unity\\Editor\\2022.3.38f1c1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Diagnostics.FileVersionInfo.dll"}}, {"pid": 12345, "tid": 9, "ts": 1754736189984196, "dur": 5181, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.collab-proxy@2.8.2\\Lib\\Editor\\Unity.Plastic.Antlr3.Runtime.dll"}}, {"pid": 12345, "tid": 9, "ts": 1754736189980651, "dur": 8758, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.RenderPipeline.Universal.ShaderLibrary.dll (+pdb)"}}, {"pid": 12345, "tid": 9, "ts": 1754736189989410, "dur": 1957, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1754736189991458, "dur": 635652, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1754736189674397, "dur": 17306, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1754736189691761, "dur": 60, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1754736189691931, "dur": 110, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.Physics2DModule.dll_087D5123E2EA5605.mvfrm"}}, {"pid": 12345, "tid": 10, "ts": 1754736189692050, "dur": 69, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1754736189692132, "dur": 63, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/UnityEditor.TestRunner.rsp2"}}, {"pid": 12345, "tid": 10, "ts": 1754736189692351, "dur": 150, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipeline.Universal.ShaderLibrary.rsp2"}}, {"pid": 12345, "tid": 10, "ts": 1754736189692618, "dur": 88, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Universal.Editor.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 10, "ts": 1754736189692934, "dur": 90, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/PPv2URPConverters.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 10, "ts": 1754736189693230, "dur": 105, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1754736189693375, "dur": 194, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1754736189693569, "dur": 177, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1754736189693747, "dur": 177, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1754736189693925, "dur": 165, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1754736189694090, "dur": 255, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1754736189694345, "dur": 196, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1754736189694541, "dur": 165, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1754736189694707, "dur": 466, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1754736189695174, "dur": 168, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1754736189695342, "dur": 156, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1754736189695498, "dur": 202, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1754736189695700, "dur": 157, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1754736189695857, "dur": 186, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1754736189696043, "dur": 201, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1754736189696244, "dur": 200, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1754736189696445, "dur": 179, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1754736189696624, "dur": 172, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1754736189696796, "dur": 186, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1754736189696982, "dur": 177, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1754736189697159, "dur": 174, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1754736189697333, "dur": 167, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1754736189697500, "dur": 187, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1754736189697689, "dur": 173, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1754736189697862, "dur": 214, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1754736189698076, "dur": 309, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1754736189698385, "dur": 131, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1754736189698516, "dur": 318, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1754736189698835, "dur": 71, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.TextMeshPro.dll.mvfrm"}}, {"pid": 12345, "tid": 10, "ts": 1754736189699033, "dur": 353, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\GameSoft\\Unity\\Editor\\2022.3.38f1c1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netfx\\System.ComponentModel.Composition.dll"}}, {"pid": 12345, "tid": 10, "ts": 1754736189699422, "dur": 81, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\GameSoft\\Unity\\Editor\\2022.3.38f1c1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.ComponentModel.dll"}}, {"pid": 12345, "tid": 10, "ts": 1754736189698928, "dur": 870, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.TextMeshPro.dll (+2 others)"}}, {"pid": 12345, "tid": 10, "ts": 1754736189699799, "dur": 237, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1754736189700064, "dur": 74, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.TextMeshPro.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 10, "ts": 1754736189700148, "dur": 299, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.TextMeshPro.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 10, "ts": 1754736189700520, "dur": 616, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1754736189701136, "dur": 1499, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1754736189702700, "dur": 165, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/PPv2URPConverters.dll (+2 others)"}}, {"pid": 12345, "tid": 10, "ts": 1754736189702894, "dur": 278597, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1754736189981678, "dur": 2607, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\GameSoft\\Unity\\Editor\\2022.3.38f1c1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Net.NameResolution.dll"}}, {"pid": 12345, "tid": 10, "ts": 1754736189981495, "dur": 3871, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/CrashKonijn.Agent.Core.dll (+pdb)"}}, {"pid": 12345, "tid": 10, "ts": 1754736189985416, "dur": 1190, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Burst.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 10, "ts": 1754736189986658, "dur": 1212, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/PPv2URPConverters.dll (+pdb)"}}, {"pid": 12345, "tid": 10, "ts": 1754736189987917, "dur": 1156, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.VisualScripting.Shared.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 10, "ts": 1754736189989223, "dur": 750, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Cinemachine.dll"}}, {"pid": 12345, "tid": 10, "ts": 1754736189989973, "dur": 84036, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1754736190074012, "dur": 74895, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\Bee\\artifacts\\1900b0aE.dag\\Assembly-CSharp.dll"}}, {"pid": 12345, "tid": 10, "ts": 1754736190074011, "dur": 76117, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Assembly-CSharp.dll (+pdb)"}}, {"pid": 12345, "tid": 10, "ts": 1754736190150963, "dur": 129, "ph": "X", "name": "StoreTimestampsOfNonGeneratedInputFiles", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1754736190151131, "dur": 269886, "ph": "X", "name": "ILPostProcess", "args": {"detail": "Library/Bee/artifacts/1900b0aE.dag/post-processed/Assembly-CSharp.dll (+pdb)"}}, {"pid": 12345, "tid": 10, "ts": 1754736190426971, "dur": 71459, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\Bee\\artifacts\\1900b0aE.dag\\post-processed\\Assembly-CSharp.dll"}}, {"pid": 12345, "tid": 10, "ts": 1754736190426970, "dur": 71463, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Assembly-CSharp.dll"}}, {"pid": 12345, "tid": 10, "ts": 1754736190498462, "dur": 757, "ph": "X", "name": "CopyFiles", "args": {"detail": "Library/ScriptAssemblies/Assembly-CSharp.dll"}}, {"pid": 12345, "tid": 10, "ts": 1754736190499222, "dur": 127912, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1754736189674431, "dur": 17294, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1754736189691750, "dur": 59, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1754736189691809, "dur": 231, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.GraphViewModule.dll_ED51E50A679089CA.mvfrm"}}, {"pid": 12345, "tid": 11, "ts": 1754736189692044, "dur": 74, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1754736189692138, "dur": 183, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/UnityEditor.TestRunner.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 11, "ts": 1754736189692613, "dur": 57, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Mathematics.Editor.rsp"}}, {"pid": 12345, "tid": 11, "ts": 1754736189692701, "dur": 125, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Rider.Editor.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 11, "ts": 1754736189693214, "dur": 103, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1754736189693379, "dur": 201, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1754736189693581, "dur": 154, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1754736189693736, "dur": 184, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1754736189693920, "dur": 233, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1754736189694153, "dur": 147, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1754736189694300, "dur": 236, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1754736189694536, "dur": 152, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1754736189694688, "dur": 481, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1754736189695170, "dur": 180, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1754736189695350, "dur": 161, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1754736189695511, "dur": 268, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1754736189695780, "dur": 293, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1754736189696073, "dur": 247, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1754736189696320, "dur": 170, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1754736189696491, "dur": 236, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1754736189696727, "dur": 170, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1754736189696898, "dur": 218, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1754736189697116, "dur": 185, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1754736189697301, "dur": 179, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1754736189697480, "dur": 153, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1754736189697633, "dur": 183, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1754736189697816, "dur": 211, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1754736189698052, "dur": 294, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1754736189698346, "dur": 177, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1754736189698523, "dur": 323, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1754736189698847, "dur": 70, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.Rider.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 11, "ts": 1754736189699272, "dur": 100, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\GameSoft\\Unity\\Editor\\2022.3.38f1c1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Threading.Timer.dll"}}, {"pid": 12345, "tid": 11, "ts": 1754736189698976, "dur": 524, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Rider.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 11, "ts": 1754736189699581, "dur": 391, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1754736189699972, "dur": 121, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1754736189700094, "dur": 207, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1754736189700302, "dur": 68, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.Collections.DocCodeSamples.dll.mvfrm"}}, {"pid": 12345, "tid": 11, "ts": 1754736189700382, "dur": 241, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Collections.DocCodeSamples.dll (+2 others)"}}, {"pid": 12345, "tid": 11, "ts": 1754736189700624, "dur": 62, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1754736189700692, "dur": 451, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1754736189701144, "dur": 278940, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1754736189980086, "dur": 1482, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Tests.dll (+pdb)"}}, {"pid": 12345, "tid": 11, "ts": 1754736189981568, "dur": 190, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1754736189981770, "dur": 1185, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Timeline.dll (+pdb)"}}, {"pid": 12345, "tid": 11, "ts": 1754736189983033, "dur": 51, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\GameSoft\\Unity\\Editor\\2022.3.38f1c1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.AccessibilityModule.dll"}}, {"pid": 12345, "tid": 11, "ts": 1754736189983812, "dur": 106, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\GameSoft\\Unity\\Editor\\2022.3.38f1c1\\Editor\\Data\\Tools\\ilpp\\Unity.ILPP.Runner\\System.Net.Mail.dll"}}, {"pid": 12345, "tid": 11, "ts": 1754736189984048, "dur": 2589, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\GameSoft\\Unity\\Editor\\2022.3.38f1c1\\Editor\\Data\\Managed\\UnityEngine\\UnityEditor.TextCoreTextEngineModule.dll"}}, {"pid": 12345, "tid": 11, "ts": 1754736189983009, "dur": 4007, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.VisualScripting.Core.dll (+pdb)"}}, {"pid": 12345, "tid": 11, "ts": 1754736189987016, "dur": 122, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1754736189987149, "dur": 1176, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.PlasticSCM.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 11, "ts": 1754736189988351, "dur": 1204, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/UnityEditor.UI.dll (+pdb)"}}, {"pid": 12345, "tid": 11, "ts": 1754736189989703, "dur": 228, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "CopyFiles Library/ScriptAssemblies/CrashKonijn.Goap.Runtime.pdb"}}, {"pid": 12345, "tid": 11, "ts": 1754736189989932, "dur": 1658, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1754736189991591, "dur": 635529, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1754736189674467, "dur": 17273, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1754736189691799, "dur": 90, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.SceneViewModule.dll_2C01C8125CB356D8.mvfrm"}}, {"pid": 12345, "tid": 12, "ts": 1754736189692025, "dur": 71, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\GameSoft\\Unity\\Editor\\2022.3.38f1c1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.UnityWebRequestModule.dll"}}, {"pid": 12345, "tid": 12, "ts": 1754736189692024, "dur": 73, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityWebRequestModule.dll_55F435D8463A793C.mvfrm"}}, {"pid": 12345, "tid": 12, "ts": 1754736189692282, "dur": 70, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Core.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 12, "ts": 1754736189692361, "dur": 291, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Core.Editor.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 12, "ts": 1754736189692661, "dur": 132, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.PlasticSCM.Editor.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 12, "ts": 1754736189692806, "dur": 85, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.SettingsProvider.Editor.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 12, "ts": 1754736189692962, "dur": 79, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/com.crashkonijn.docs.getting_started.rsp"}}, {"pid": 12345, "tid": 12, "ts": 1754736189693237, "dur": 103, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1754736189693340, "dur": 73, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/9675442845102135732.rsp"}}, {"pid": 12345, "tid": 12, "ts": 1754736189693414, "dur": 169, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1754736189693584, "dur": 176, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1754736189693760, "dur": 174, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1754736189693935, "dur": 187, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1754736189694122, "dur": 119, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1754736189694241, "dur": 445, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1754736189694686, "dur": 574, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1754736189695261, "dur": 182, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1754736189695443, "dur": 286, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1754736189695729, "dur": 164, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1754736189695893, "dur": 760, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1754736189696653, "dur": 237, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1754736189696890, "dur": 178, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1754736189697069, "dur": 209, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1754736189697278, "dur": 219, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1754736189697498, "dur": 175, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1754736189697673, "dur": 160, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1754736189697833, "dur": 308, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1754736189698170, "dur": 60, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1754736189698230, "dur": 50, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1754736189698281, "dur": 236, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1754736189698517, "dur": 308, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1754736189698825, "dur": 65, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/CrashKonijn.Goap.Core.dll.mvfrm"}}, {"pid": 12345, "tid": 12, "ts": 1754736189698940, "dur": 365, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.VisualStudio.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 12, "ts": 1754736189699370, "dur": 177, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.Burst.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 12, "ts": 1754736189699562, "dur": 349, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Burst.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 12, "ts": 1754736189699911, "dur": 139, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1754736189700115, "dur": 185, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1754736189700301, "dur": 63, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.Collections.BurstCompatibilityGen.dll.mvfrm"}}, {"pid": 12345, "tid": 12, "ts": 1754736189700376, "dur": 205, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Collections.BurstCompatibilityGen.dll (+2 others)"}}, {"pid": 12345, "tid": 12, "ts": 1754736189700601, "dur": 539, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1754736189701140, "dur": 280029, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1754736189981678, "dur": 2406, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\GameSoft\\Unity\\Editor\\2022.3.38f1c1\\Editor\\Data\\Tools\\ilpp\\Unity.ILPP.Runner\\Microsoft.AspNetCore.dll"}}, {"pid": 12345, "tid": 12, "ts": 1754736189984202, "dur": 2433, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\GameSoft\\Unity\\Editor\\2022.3.38f1c1\\Editor\\Data\\Tools\\ilpp\\Unity.ILPP.Runner\\Microsoft.Extensions.Configuration.EnvironmentVariables.dll"}}, {"pid": 12345, "tid": 12, "ts": 1754736189981171, "dur": 6120, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/com.crashkonijn.goap.demos.simple.dll (+pdb)"}}, {"pid": 12345, "tid": 12, "ts": 1754736189987332, "dur": 1886, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/com.crashkonijn.goap.demos.simple.dll (+pdb)"}}, {"pid": 12345, "tid": 12, "ts": 1754736189989223, "dur": 148, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1754736189989371, "dur": 111, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.VisualScripting.Flow.dll"}}, {"pid": 12345, "tid": 12, "ts": 1754736189989486, "dur": 369, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1754736189989861, "dur": 1731, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1754736189991592, "dur": 635531, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1754736189674489, "dur": 17260, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1754736189691836, "dur": 150, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ClothModule.dll_5F9E4D3CAA1E8246.mvfrm"}}, {"pid": 12345, "tid": 13, "ts": 1754736189692032, "dur": 79, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\GameSoft\\Unity\\Editor\\2022.3.38f1c1\\Editor\\Data\\Managed\\UnityEditor.Graphs.dll"}}, {"pid": 12345, "tid": 13, "ts": 1754736189692031, "dur": 81, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.Graphs.dll_34C604C212D62B6E.mvfrm"}}, {"pid": 12345, "tid": 13, "ts": 1754736189692167, "dur": 277, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/UnityEditor.UI.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 13, "ts": 1754736189692487, "dur": 51, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/CrashKonijn.Goap.Resolver.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 13, "ts": 1754736189692572, "dur": 58, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Burst.Editor.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 13, "ts": 1754736189692682, "dur": 75, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualStudio.Editor.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 13, "ts": 1754736189692862, "dur": 187, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/com.crashkonijn.docs.getting_started.rsp2"}}, {"pid": 12345, "tid": 13, "ts": 1754736189693209, "dur": 119, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1754736189693394, "dur": 192, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1754736189693586, "dur": 174, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1754736189693760, "dur": 183, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1754736189693943, "dur": 200, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1754736189694144, "dur": 173, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1754736189694317, "dur": 354, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1754736189694671, "dur": 506, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1754736189695177, "dur": 155, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1754736189695332, "dur": 925, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1754736189696257, "dur": 178, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1754736189696435, "dur": 206, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1754736189696641, "dur": 212, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1754736189696853, "dur": 219, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1754736189697072, "dur": 185, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1754736189697257, "dur": 161, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1754736189697418, "dur": 220, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1754736189697638, "dur": 173, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1754736189697811, "dur": 203, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1754736189698014, "dur": 195, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1754736189698280, "dur": 231, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1754736189698533, "dur": 377, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1754736189698910, "dur": 75, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Core.ShaderLibrary.dll.mvfrm"}}, {"pid": 12345, "tid": 13, "ts": 1754736189698985, "dur": 226, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1754736189699363, "dur": 84, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\GameSoft\\Unity\\Editor\\2022.3.38f1c1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.ComponentModel.TypeConverter.dll"}}, {"pid": 12345, "tid": 13, "ts": 1754736189699449, "dur": 80, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\GameSoft\\Unity\\Editor\\2022.3.38f1c1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Data.Common.dll"}}, {"pid": 12345, "tid": 13, "ts": 1754736189699216, "dur": 469, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Core.ShaderLibrary.dll (+2 others)"}}, {"pid": 12345, "tid": 13, "ts": 1754736189699685, "dur": 85, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1754736189699792, "dur": 144, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1754736189699937, "dur": 79, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.InputSystem.TestFramework.dll.mvfrm"}}, {"pid": 12345, "tid": 13, "ts": 1754736189700048, "dur": 214, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.InputSystem.TestFramework.dll (+2 others)"}}, {"pid": 12345, "tid": 13, "ts": 1754736189700262, "dur": 53, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1754736189700350, "dur": 172, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1754736189700522, "dur": 619, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1754736189701141, "dur": 278939, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1754736189980083, "dur": 1525, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.RenderPipelines.Core.ShaderLibrary.dll (+pdb)"}}, {"pid": 12345, "tid": 13, "ts": 1754736189981609, "dur": 105, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1754736189981722, "dur": 1364, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.RenderPipelines.Core.Runtime.dll (+pdb)"}}, {"pid": 12345, "tid": 13, "ts": 1754736189983769, "dur": 307, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\GameSoft\\Unity\\Editor\\2022.3.38f1c1\\Editor\\Data\\Tools\\ilpp\\Unity.ILPP.Runner\\Microsoft.Extensions.Logging.dll"}}, {"pid": 12345, "tid": 13, "ts": 1754736189984268, "dur": 3075, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\GameSoft\\Unity\\Editor\\2022.3.38f1c1\\Editor\\Data\\Tools\\ilpp\\Unity.ILPP.Runner\\System.Threading.Channels.dll"}}, {"pid": 12345, "tid": 13, "ts": 1754736189983144, "dur": 4618, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.TextMeshPro.dll (+pdb)"}}, {"pid": 12345, "tid": 13, "ts": 1754736189987804, "dur": 1197, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/com.unity.cinemachine.editor.dll (+pdb)"}}, {"pid": 12345, "tid": 13, "ts": 1754736189989270, "dur": 69, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1754736189989476, "dur": 56, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1754736189989606, "dur": 1093, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "CopyFiles Library/ScriptAssemblies/UnityEditor.UI.dll"}}, {"pid": 12345, "tid": 13, "ts": 1754736189990701, "dur": 636407, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1754736189674519, "dur": 17240, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1754736189692038, "dur": 77, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1754736189692174, "dur": 76, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Burst.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 14, "ts": 1754736189692307, "dur": 63, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/UnityEngine.UI.dll.mvfrm"}}, {"pid": 12345, "tid": 14, "ts": 1754736189693215, "dur": 111, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\GameSoft\\Unity\\Editor\\2022.3.38f1c1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Runtime.Handles.dll"}}, {"pid": 12345, "tid": 14, "ts": 1754736189693602, "dur": 191, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\GameSoft\\Unity\\Editor\\2022.3.38f1c1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.ValueTuple.dll"}}, {"pid": 12345, "tid": 14, "ts": 1754736189693853, "dur": 141, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.shadergraph@14.0.11\\Editor\\Generation\\Targets\\BuiltIn\\Editor\\ShaderGUI\\MaterialAssemblyReference\\RawRenderQueue.cs"}}, {"pid": 12345, "tid": 14, "ts": 1754736189694241, "dur": 98, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.ugui@1.0.0\\Runtime\\UI\\Core\\Button.cs"}}, {"pid": 12345, "tid": 14, "ts": 1754736189694784, "dur": 310, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.ugui@1.0.0\\Runtime\\UI\\Core\\SpriteState.cs"}}, {"pid": 12345, "tid": 14, "ts": 1754736189692387, "dur": 2865, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/UnityEngine.UI.dll (+2 others)"}}, {"pid": 12345, "tid": 14, "ts": 1754736189695305, "dur": 161, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1754736189695466, "dur": 196, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1754736189695662, "dur": 169, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1754736189695831, "dur": 230, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1754736189696062, "dur": 562, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1754736189696625, "dur": 173, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1754736189696798, "dur": 181, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1754736189696979, "dur": 191, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1754736189697170, "dur": 175, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1754736189697345, "dur": 638, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1754736189697984, "dur": 231, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1754736189698272, "dur": 243, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1754736189698515, "dur": 309, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1754736189698837, "dur": 68, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/CrashKonijn.Agent.Core.dll.mvfrm"}}, {"pid": 12345, "tid": 14, "ts": 1754736189698914, "dur": 131, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/CrashKonijn.Agent.Core.dll.mvfrm"}}, {"pid": 12345, "tid": 14, "ts": 1754736189699360, "dur": 58, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.crashkonijn.goap@601802d6e1\\Runtime\\CrashKonijn.Agent.Core\\Interfaces\\IActionRunState.cs"}}, {"pid": 12345, "tid": 14, "ts": 1754736189699045, "dur": 423, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/CrashKonijn.Agent.Core.dll (+2 others)"}}, {"pid": 12345, "tid": 14, "ts": 1754736189699469, "dur": 77, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1754736189699551, "dur": 423, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/CrashKonijn.Goap.Core.dll (+2 others)"}}, {"pid": 12345, "tid": 14, "ts": 1754736189700004, "dur": 59, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1754736189700063, "dur": 84, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/com.crashkonijn.goap.demos.complex.dll.mvfrm"}}, {"pid": 12345, "tid": 14, "ts": 1754736189700164, "dur": 152, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1754736189700316, "dur": 190, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1754736189700507, "dur": 67, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Core.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 14, "ts": 1754736189700590, "dur": 432, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Core.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 14, "ts": 1754736189701086, "dur": 65, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.ShaderGraph.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 14, "ts": 1754736189701164, "dur": 926, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.ShaderGraph.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 14, "ts": 1754736189702198, "dur": 397, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Universal.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 14, "ts": 1754736189702683, "dur": 200, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/com.unity.cinemachine.editor.dll (+2 others)"}}, {"pid": 12345, "tid": 14, "ts": 1754736189703050, "dur": 222, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\Bee\\artifacts\\1900b0aE.dag\\Assembly-CSharp.rsp"}}, {"pid": 12345, "tid": 14, "ts": 1754736189702901, "dur": 371, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Assembly-CSharp.dll (+2 others)"}}, {"pid": 12345, "tid": 14, "ts": 1754736189703597, "dur": 368700, "ph": "X", "name": "Csc", "args": {"detail": "Library/Bee/artifacts/1900b0aE.dag/Assembly-CSharp.dll (+2 others)"}}, {"pid": 12345, "tid": 14, "ts": 1754736190074336, "dur": 5590, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\Bee\\artifacts\\1900b0aE.dag\\Assembly-CSharp.ref.dll"}}, {"pid": 12345, "tid": 14, "ts": 1754736190074004, "dur": 5984, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Assembly-CSharp-Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 14, "ts": 1754736190080230, "dur": 52, "ph": "X", "name": "StoreTimestampsOfNonGeneratedInputFiles", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1754736190080295, "dur": 292910, "ph": "X", "name": "Csc", "args": {"detail": "Library/Bee/artifacts/1900b0aE.dag/Assembly-CSharp-Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 14, "ts": 1754736190374466, "dur": 8243, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\Bee\\artifacts\\1900b0aE.dag\\Assembly-CSharp-Editor.dll"}}, {"pid": 12345, "tid": 14, "ts": 1754736190374464, "dur": 9203, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Assembly-CSharp-Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 14, "ts": 1754736190384479, "dur": 137, "ph": "X", "name": "StoreTimestampsOfNonGeneratedInputFiles", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1754736190384634, "dur": 225096, "ph": "X", "name": "ILPostProcess", "args": {"detail": "Library/Bee/artifacts/1900b0aE.dag/post-processed/Assembly-CSharp-Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 14, "ts": 1754736190614955, "dur": 95, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\Bee\\artifacts\\1900b0aE.dag\\post-processed\\Assembly-CSharp-Editor.pdb"}}, {"pid": 12345, "tid": 14, "ts": 1754736190614953, "dur": 98, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Assembly-CSharp-Editor.pdb"}}, {"pid": 12345, "tid": 14, "ts": 1754736190615066, "dur": 677, "ph": "X", "name": "CopyFiles", "args": {"detail": "Library/ScriptAssemblies/Assembly-CSharp-Editor.pdb"}}, {"pid": 12345, "tid": 14, "ts": 1754736190615746, "dur": 11369, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1754736189674541, "dur": 17228, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1754736189691780, "dur": 70, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\GameSoft\\Unity\\Editor\\2022.3.38f1c1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.AndroidJNIModule.dll"}}, {"pid": 12345, "tid": 15, "ts": 1754736189691776, "dur": 78, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.AndroidJNIModule.dll_DF83CFB61A6929CE.mvfrm"}}, {"pid": 12345, "tid": 15, "ts": 1754736189692053, "dur": 93, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.collections@1.2.4\\Unity.Collections.LowLevel.ILSupport\\Unity.Collections.LowLevel.ILSupport.dll"}}, {"pid": 12345, "tid": 15, "ts": 1754736189692052, "dur": 96, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.Collections.LowLevel.ILSupport.dll_2E40CE80E145E521.mvfrm"}}, {"pid": 12345, "tid": 15, "ts": 1754736189692156, "dur": 66, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.Collections.LowLevel.ILSupport.dll_2E40CE80E145E521.mvfrm"}}, {"pid": 12345, "tid": 15, "ts": 1754736189692376, "dur": 50, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Core.Editor.rsp"}}, {"pid": 12345, "tid": 15, "ts": 1754736189692433, "dur": 55, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Core.Editor.rsp"}}, {"pid": 12345, "tid": 15, "ts": 1754736189692519, "dur": 51, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Burst.Editor.rsp2"}}, {"pid": 12345, "tid": 15, "ts": 1754736189692904, "dur": 53, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/com.unity.cinemachine.editor.rsp2"}}, {"pid": 12345, "tid": 15, "ts": 1754736189692985, "dur": 123, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/com.unity.cinemachine.editor.rsp"}}, {"pid": 12345, "tid": 15, "ts": 1754736189693216, "dur": 102, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1754736189693388, "dur": 162, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1754736189693550, "dur": 161, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1754736189693711, "dur": 228, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1754736189693940, "dur": 225, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1754736189694165, "dur": 179, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1754736189694344, "dur": 201, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1754736189694545, "dur": 178, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1754736189694723, "dur": 565, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1754736189695288, "dur": 184, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1754736189695473, "dur": 578, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1754736189696052, "dur": 189, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1754736189696241, "dur": 154, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1754736189696429, "dur": 152, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1754736189696581, "dur": 182, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1754736189696764, "dur": 171, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1754736189696935, "dur": 198, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1754736189697133, "dur": 214, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1754736189697347, "dur": 158, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1754736189697505, "dur": 160, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1754736189697666, "dur": 179, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1754736189697845, "dur": 196, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1754736189698075, "dur": 192, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1754736189698280, "dur": 233, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1754736189698513, "dur": 314, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1754736189698828, "dur": 62, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/CrashKonijn.Agent.Runtime.dll.mvfrm"}}, {"pid": 12345, "tid": 15, "ts": 1754736189698913, "dur": 68, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Universal.Shaders.dll.mvfrm"}}, {"pid": 12345, "tid": 15, "ts": 1754736189699011, "dur": 335, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Universal.Shaders.dll (+2 others)"}}, {"pid": 12345, "tid": 15, "ts": 1754736189699346, "dur": 191, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1754736189699571, "dur": 396, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1754736189699967, "dur": 102, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1754736189700069, "dur": 236, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1754736189700305, "dur": 202, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1754736189700507, "dur": 290, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1754736189700797, "dur": 66, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Core.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 15, "ts": 1754736189700878, "dur": 888, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Core.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 15, "ts": 1754736189701819, "dur": 53, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Flow.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 15, "ts": 1754736189701880, "dur": 286, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Flow.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 15, "ts": 1754736189702250, "dur": 212, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.State.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 15, "ts": 1754736189702539, "dur": 142, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Shared.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 15, "ts": 1754736189702715, "dur": 50, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Assembly-CSharp.dll.mvfrm"}}, {"pid": 12345, "tid": 15, "ts": 1754736189702773, "dur": 277303, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1754736189980079, "dur": 1481, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/CrashKonijn.Goap.Resolver.dll (+pdb)"}}, {"pid": 12345, "tid": 15, "ts": 1754736189981678, "dur": 84, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\GameSoft\\Unity\\Editor\\2022.3.38f1c1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.ProfilerModule.dll"}}, {"pid": 12345, "tid": 15, "ts": 1754736189981619, "dur": 1599, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/com.crashkonijn.docs.getting_started.dll (+pdb)"}}, {"pid": 12345, "tid": 15, "ts": 1754736189983219, "dur": 184, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1754736189984048, "dur": 2611, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\GameSoft\\Unity\\Editor\\2022.3.38f1c1\\Editor\\Data\\Tools\\ilpp\\Unity.ILPP.Runner\\Microsoft.Extensions.ObjectPool.dll"}}, {"pid": 12345, "tid": 15, "ts": 1754736189983408, "dur": 3882, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.InputSystem.TestFramework.dll (+pdb)"}}, {"pid": 12345, "tid": 15, "ts": 1754736189987794, "dur": 84, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\GameSoft\\Unity\\Editor\\2022.3.38f1c1\\Editor\\Data\\Tools\\ilpp\\Unity.ILPP.Runner\\api-ms-win-crt-private-l1-1-0.dll"}}, {"pid": 12345, "tid": 15, "ts": 1754736189987348, "dur": 1339, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Collections.DocCodeSamples.dll (+pdb)"}}, {"pid": 12345, "tid": 15, "ts": 1754736189989456, "dur": 274, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\GameSoft\\Unity\\Editor\\2022.3.38f1c1\\Editor\\Data\\Tools\\ilpp\\Unity.ILPP.Runner\\Microsoft.Net.Http.Headers.dll"}}, {"pid": 12345, "tid": 15, "ts": 1754736189989771, "dur": 68, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\GameSoft\\Unity\\Editor\\2022.3.38f1c1\\Editor\\Data\\Tools\\ilpp\\Unity.ILPP.Runner\\mscordaccore_amd64_amd64_6.0.1322.58009.dll"}}, {"pid": 12345, "tid": 15, "ts": 1754736189988768, "dur": 1616, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.VisualScripting.Flow.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 15, "ts": 1754736189990481, "dur": 624479, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1754736190614962, "dur": 11379, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\Bee\\artifacts\\1900b0aE.dag\\post-processed\\Assembly-CSharp-Editor.dll"}}, {"pid": 12345, "tid": 15, "ts": 1754736190614961, "dur": 11381, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Assembly-CSharp-Editor.dll"}}, {"pid": 12345, "tid": 15, "ts": 1754736190626362, "dur": 692, "ph": "X", "name": "CopyFiles", "args": {"detail": "Library/ScriptAssemblies/Assembly-CSharp-Editor.dll"}}, {"pid": 12345, "tid": 16, "ts": 1754736189674581, "dur": 17199, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1754736189691834, "dur": 65, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.AnimationModule.dll_BBD7613341ED5E70.mvfrm"}}, {"pid": 12345, "tid": 16, "ts": 1754736189691997, "dur": 52, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.TLSModule.dll_FEEE89EA26456A87.mvfrm"}}, {"pid": 12345, "tid": 16, "ts": 1754736189692051, "dur": 76, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.collab-proxy@2.8.2\\Lib\\Editor\\Unity.Plastic.Newtonsoft.Json.dll"}}, {"pid": 12345, "tid": 16, "ts": 1754736189692050, "dur": 79, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.Plastic.Newtonsoft.Json.dll_CF17D4CA2A31CD8C.mvfrm"}}, {"pid": 12345, "tid": 16, "ts": 1754736189692609, "dur": 92, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.State.Editor.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 16, "ts": 1754736189692854, "dur": 66, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Shared.Editor.rsp"}}, {"pid": 12345, "tid": 16, "ts": 1754736189692934, "dur": 135, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/com.crashkonijn.goap.demos.turn-based.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 16, "ts": 1754736189693226, "dur": 105, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1754736189693331, "dur": 92, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/12248871281822510316.rsp"}}, {"pid": 12345, "tid": 16, "ts": 1754736189693423, "dur": 177, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1754736189693601, "dur": 169, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1754736189693770, "dur": 178, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1754736189693948, "dur": 204, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1754736189694152, "dur": 169, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1754736189694321, "dur": 191, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1754736189694513, "dur": 160, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1754736189694673, "dur": 550, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1754736189695223, "dur": 187, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1754736189695411, "dur": 190, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1754736189695601, "dur": 163, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1754736189695764, "dur": 201, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1754736189695965, "dur": 180, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1754736189696146, "dur": 212, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1754736189696358, "dur": 166, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1754736189696524, "dur": 192, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1754736189696716, "dur": 239, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1754736189696955, "dur": 201, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1754736189697156, "dur": 203, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1754736189697359, "dur": 197, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1754736189697556, "dur": 167, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1754736189697724, "dur": 203, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1754736189697927, "dur": 227, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1754736189698282, "dur": 239, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1754736189698521, "dur": 302, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1754736189698823, "dur": 65, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.Mathematics.dll.mvfrm"}}, {"pid": 12345, "tid": 16, "ts": 1754736189698889, "dur": 55, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1754736189698950, "dur": 448, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Mathematics.dll (+2 others)"}}, {"pid": 12345, "tid": 16, "ts": 1754736189699398, "dur": 76, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1754736189699503, "dur": 103, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.Collections.dll.mvfrm"}}, {"pid": 12345, "tid": 16, "ts": 1754736189699607, "dur": 307, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1754736189699919, "dur": 330, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Collections.dll (+2 others)"}}, {"pid": 12345, "tid": 16, "ts": 1754736189700300, "dur": 77, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/CrashKonijn.Goap.Resolver.dll.mvfrm"}}, {"pid": 12345, "tid": 16, "ts": 1754736189700389, "dur": 268, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/CrashKonijn.Goap.Resolver.dll (+2 others)"}}, {"pid": 12345, "tid": 16, "ts": 1754736189700690, "dur": 395, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/CrashKonijn.Goap.Runtime.dll (+2 others)"}}, {"pid": 12345, "tid": 16, "ts": 1754736189701138, "dur": 344, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/CrashKonijn.Goap.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 16, "ts": 1754736189701506, "dur": 264, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/com.crashkonijn.goap.demos.simple.dll (+2 others)"}}, {"pid": 12345, "tid": 16, "ts": 1754736189701814, "dur": 278272, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1754736189980088, "dur": 1473, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.RenderPipelines.Universal.Config.Runtime.dll (+pdb)"}}, {"pid": 12345, "tid": 16, "ts": 1754736189981604, "dur": 2115, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.RenderPipelines.Universal.Config.Runtime.dll (+pdb)"}}, {"pid": 12345, "tid": 16, "ts": 1754736189984048, "dur": 1633, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\GameSoft\\Unity\\Editor\\2022.3.38f1c1\\Editor\\Data\\Tools\\ilpp\\Unity.ILPP.Runner\\Microsoft.AspNetCore.Hosting.Abstractions.dll"}}, {"pid": 12345, "tid": 16, "ts": 1754736189983722, "dur": 2707, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/UnityEngine.UI.dll (+pdb)"}}, {"pid": 12345, "tid": 16, "ts": 1754736189986472, "dur": 1276, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.VisualScripting.State.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 16, "ts": 1754736189987787, "dur": 1288, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Timeline.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 16, "ts": 1754736189989096, "dur": 100, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Timeline.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 16, "ts": 1754736189989366, "dur": 60, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "CopyFiles Library/ScriptAssemblies/URPWizard.pdb"}}, {"pid": 12345, "tid": 16, "ts": 1754736189989440, "dur": 355, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.TextMeshPro.pdb"}}, {"pid": 12345, "tid": 16, "ts": 1754736189989847, "dur": 1759, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1754736189991606, "dur": 635523, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1754736190632909, "dur": 1505, "ph": "X", "name": "ProfilerWriteOutput"}, {"pid": 35942, "tid": -1, "ph": "M", "name": "process_name", "args": {"name": "netcorerun.dll"}}, {"pid": 35942, "tid": -1, "ph": "M", "name": "process_sort_index", "args": {"sort_index": "-1"}}, {"pid": 35942, "tid": 1, "ph": "M", "name": "thread_name", "args": {"name": ""}}, {"pid": 35942, "tid": 1, "ts": 1754736189021070, "dur": 279192, "ph": "X", "name": "BuildProgram", "args": {}}, {"pid": 35942, "tid": 1, "ts": 1754736189021804, "dur": 46644, "ph": "X", "name": "BuildProgramContextConstructor", "args": {}}, {"pid": 35942, "tid": 1, "ts": 1754736189248137, "dur": 2741, "ph": "X", "name": "OutputData.Write", "args": {}}, {"pid": 35942, "tid": 1, "ts": 1754736189250880, "dur": 49372, "ph": "X", "name": "Backend.Write", "args": {}}, {"pid": 35942, "tid": 1, "ts": 1754736189252215, "dur": 29500, "ph": "X", "name": "JsonToString", "args": {}}, {"pid": 35942, "tid": 1, "ts": 1754736189305110, "dur": 862, "ph": "X", "name": "", "args": {}}, {"pid": 35942, "tid": 1, "ts": 1754736189304750, "dur": 1394, "ph": "X", "name": "Write chrome-trace events", "args": {}}, {"cat": "", "pid": 12345, "tid": 0, "ts": 0, "ph": "M", "name": "process_name", "args": {"name": "bee_backend"}}, {"pid": 12345, "tid": 0, "ts": 1754736188682683, "dur": 1483, "ph": "X", "name": "DriverInitData", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1754736188684179, "dur": 819, "ph": "X", "name": "RemoveStaleOutputs", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1754736188685109, "dur": 58, "ph": "X", "name": "<PERSON><PERSON>", "args": {"detail": "PrepareNodes"}}, {"pid": 12345, "tid": 0, "ts": 1754736188685167, "dur": 406, "ph": "X", "name": "BuildQueueInit", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1754736188685725, "dur": 54, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.EditorToolbarModule.dll_F4EF6CA575FE5075.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1754736188685939, "dur": 60, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.UIElementsModule.dll_613AE6E2D149BAF8.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1754736188686972, "dur": 116, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.TerrainPhysicsModule.dll_34719EFEEA08287A.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1754736188688640, "dur": 62, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Burst.dll (+2 others)"}}, {"pid": 12345, "tid": 0, "ts": 1754736188691157, "dur": 82, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.ShaderGraph.Utilities.rsp2"}}, {"pid": 12345, "tid": 0, "ts": 1754736188685589, "dur": 21413, "ph": "X", "name": "EnqueueRequestedNodes", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1754736188707016, "dur": 383, "ph": "X", "name": "WaitForBuildFinished", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1754736188707399, "dur": 331, "ph": "X", "name": "JoinBuildThread", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1754736188707926, "dur": 1271, "ph": "X", "name": "<PERSON><PERSON>", "args": {"detail": "Write AllBuiltNodes"}}, {"pid": 12345, "tid": 1, "ts": 1754736188685891, "dur": 21136, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1754736188707035, "dur": 358, "ph": "X", "name": "CheckDagSignatures", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1754736188685970, "dur": 21094, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1754736188707074, "dur": 56, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.UIElementsSamplesModule.dll_623E7535F4BD3DF5.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1754736188707365, "dur": 73, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ScreenCaptureModule.dll_C5337FBB8908AF36.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1754736188685919, "dur": 21119, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1754736188707065, "dur": 62, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1754736188707151, "dur": 56, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\GameSoft\\Unity\\Editor\\2022.3.38f1c1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.VirtualTexturingModule.dll"}}, {"pid": 12345, "tid": 3, "ts": 1754736188707139, "dur": 72, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.VirtualTexturingModule.dll_38C1DD3B59418845.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1754736188707212, "dur": 117, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1754736188685953, "dur": 21100, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1754736188707369, "dur": 61, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ParticleSystemModule.dll_33F6F62BD0C52DED.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1754736188686003, "dur": 21076, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1754736188686029, "dur": 21077, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1754736188707142, "dur": 60, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\GameSoft\\Unity\\Editor\\2022.3.38f1c1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.VehiclesModule.dll"}}, {"pid": 12345, "tid": 6, "ts": 1754736188707124, "dur": 82, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.VehiclesModule.dll_B4028357F84AC7DF.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1754736188707207, "dur": 54, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1754736188707261, "dur": 85, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.VehiclesModule.dll_B4028357F84AC7DF.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1754736188686058, "dur": 21068, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1754736188707239, "dur": 76, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\GameSoft\\Unity\\Editor\\2022.3.38f1c1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.AndroidJNIModule.dll"}}, {"pid": 12345, "tid": 7, "ts": 1754736188707238, "dur": 79, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.AndroidJNIModule.dll_DF83CFB61A6929CE.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1754736188686084, "dur": 21056, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1754736188686120, "dur": 21033, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1754736188707371, "dur": 50, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.XRModule.dll_5E2DACE274283492.mvfrm"}}, {"pid": 12345, "tid": 10, "ts": 1754736188686147, "dur": 21022, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1754736188707303, "dur": 106, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ClusterRendererModule.dll_D863307DB92126C9.mvfrm"}}, {"pid": 12345, "tid": 11, "ts": 1754736188686181, "dur": 21012, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1754736188707215, "dur": 70, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.QuickSearchModule.dll_BE454E773931EEAC.mvfrm"}}, {"pid": 12345, "tid": 11, "ts": 1754736188707286, "dur": 81, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1754736188686217, "dur": 21009, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1754736188707245, "dur": 54, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.InputLegacyModule.dll_93739C87338A598F.mvfrm"}}, {"pid": 12345, "tid": 12, "ts": 1754736188707315, "dur": 120, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.InputLegacyModule.dll_93739C87338A598F.mvfrm"}}, {"pid": 12345, "tid": 13, "ts": 1754736188686250, "dur": 20996, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1754736188686285, "dur": 20970, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1754736188686321, "dur": 20949, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1754736188707306, "dur": 118, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ClothModule.dll_5F9E4D3CAA1E8246.mvfrm"}}, {"pid": 12345, "tid": 16, "ts": 1754736188686351, "dur": 20959, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1754736188712968, "dur": 320, "ph": "X", "name": "ProfilerWriteOutput"}, {"pid": 3100, "tid": 24780, "ts": 1754736190645395, "dur": 1519, "ph": "X", "name": "Wait for external events", "args": {"First to finish": "backend2.traceevents"}}, {"pid": 3100, "tid": 24780, "ts": 1754736190648592, "dur": 46, "ph": "X", "name": "Wait for external events", "args": {"First to finish": "buildprogram0.traceevents"}}, {"pid": 3100, "tid": 24780, "ts": 1754736190648909, "dur": 22, "ph": "X", "name": "Wait for external events", "args": {"First to finish": "backend1.traceevents"}}, {"pid": 3100, "tid": 24780, "ts": 1754736190647023, "dur": 1566, "ph": "X", "name": "backend2.traceevents", "args": {}}, {"pid": 3100, "tid": 24780, "ts": 1754736190648732, "dur": 176, "ph": "X", "name": "buildprogram0.traceevents", "args": {}}, {"pid": 3100, "tid": 24780, "ts": 1754736190649003, "dur": 151, "ph": "X", "name": "backend1.traceevents", "args": {}}, {"pid": 3100, "tid": 24780, "ts": 1754736190642064, "dur": 7777, "ph": "X", "name": "Write chrome-trace events", "args": {}}, {}]}