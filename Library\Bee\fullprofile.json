{"Instructions Readme": "1) Open Chrome or Edge, 2) go to chrome://tracing, 3) click Load, 4) navigate to this file.", "processInfo": {}, "traceEvents": [{"pid": 3100, "tid": -1, "ph": "M", "name": "process_name", "args": {"name": "BeeDriver"}}, {"pid": 3100, "tid": -1, "ph": "M", "name": "process_sort_index", "args": {"sort_index": "-2"}}, {"pid": 3100, "tid": 24783, "ph": "M", "name": "thread_name", "args": {"name": "Thread Pool Worker"}}, {"pid": 3100, "tid": 24783, "ts": 1754737498388258, "dur": 8, "ph": "X", "name": "ChromeTraceHeader", "args": {}}, {"pid": 3100, "tid": 24783, "ts": 1754737498388278, "dur": 2, "ph": "X", "name": "Thread Pool Worker", "args": {}}, {"pid": 3100, "tid": 1, "ph": "M", "name": "thread_name", "args": {"name": ""}}, {"pid": 3100, "tid": 1, "ts": 1754737497859442, "dur": 1014, "ph": "X", "name": "<Add>b__0", "args": {}}, {"pid": 3100, "tid": 1, "ts": 1754737497860458, "dur": 17516, "ph": "X", "name": "<Add>b__0", "args": {}}, {"pid": 3100, "tid": 1, "ts": 1754737497877976, "dur": 19240, "ph": "X", "name": "WriteJson", "args": {}}, {"pid": 3100, "tid": 24783, "ts": 1754737498388281, "dur": 8, "ph": "X", "name": "", "args": {}}, {"pid": 3100, "tid": 201863462912, "ph": "M", "name": "thread_name", "args": {"name": "ReadEntireBinlogFromIpcAsync"}}, {"pid": 3100, "tid": 201863462912, "ts": 1754737497859415, "dur": 144894, "ph": "X", "name": "WaitForConnectionAsync", "args": {}}, {"pid": 3100, "tid": 201863462912, "ts": 1754737498004309, "dur": 383320, "ph": "X", "name": "UpdateFromStreamAsync", "args": {}}, {"pid": 3100, "tid": 201863462912, "ts": 1754737498004324, "dur": 47, "ph": "X", "name": "ReadAsync 0", "args": {}}, {"pid": 3100, "tid": 201863462912, "ts": 1754737498004374, "dur": 268, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 3100, "tid": 201863462912, "ts": 1754737498004648, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 3100, "tid": 201863462912, "ts": 1754737498004650, "dur": 41, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3100, "tid": 201863462912, "ts": 1754737498004694, "dur": 5, "ph": "X", "name": "ProcessMessages 41", "args": {}}, {"pid": 3100, "tid": 201863462912, "ts": 1754737498004700, "dur": 2350, "ph": "X", "name": "ReadAsync 41", "args": {}}, {"pid": 3100, "tid": 201863462912, "ts": 1754737498007056, "dur": 34, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3100, "tid": 201863462912, "ts": 1754737498007093, "dur": 1, "ph": "X", "name": "ProcessMessages 409", "args": {}}, {"pid": 3100, "tid": 201863462912, "ts": 1754737498007095, "dur": 41, "ph": "X", "name": "ReadAsync 409", "args": {}}, {"pid": 3100, "tid": 201863462912, "ts": 1754737498007139, "dur": 1, "ph": "X", "name": "ProcessMessages 470", "args": {}}, {"pid": 3100, "tid": 201863462912, "ts": 1754737498007142, "dur": 35, "ph": "X", "name": "ReadAsync 470", "args": {}}, {"pid": 3100, "tid": 201863462912, "ts": 1754737498007180, "dur": 22, "ph": "X", "name": "ReadAsync 565", "args": {}}, {"pid": 3100, "tid": 201863462912, "ts": 1754737498007205, "dur": 44, "ph": "X", "name": "ReadAsync 306", "args": {}}, {"pid": 3100, "tid": 201863462912, "ts": 1754737498007251, "dur": 1, "ph": "X", "name": "ProcessMessages 586", "args": {}}, {"pid": 3100, "tid": 201863462912, "ts": 1754737498007253, "dur": 37, "ph": "X", "name": "ReadAsync 586", "args": {}}, {"pid": 3100, "tid": 201863462912, "ts": 1754737498007293, "dur": 1, "ph": "X", "name": "ProcessMessages 694", "args": {}}, {"pid": 3100, "tid": 201863462912, "ts": 1754737498007294, "dur": 58, "ph": "X", "name": "ReadAsync 694", "args": {}}, {"pid": 3100, "tid": 201863462912, "ts": 1754737498007355, "dur": 1, "ph": "X", "name": "ProcessMessages 448", "args": {}}, {"pid": 3100, "tid": 201863462912, "ts": 1754737498007357, "dur": 35, "ph": "X", "name": "ReadAsync 448", "args": {}}, {"pid": 3100, "tid": 201863462912, "ts": 1754737498007395, "dur": 30, "ph": "X", "name": "ReadAsync 703", "args": {}}, {"pid": 3100, "tid": 201863462912, "ts": 1754737498007426, "dur": 17, "ph": "X", "name": "ReadAsync 377", "args": {}}, {"pid": 3100, "tid": 201863462912, "ts": 1754737498007445, "dur": 27, "ph": "X", "name": "ReadAsync 476", "args": {}}, {"pid": 3100, "tid": 201863462912, "ts": 1754737498007474, "dur": 1, "ph": "X", "name": "ProcessMessages 211", "args": {}}, {"pid": 3100, "tid": 201863462912, "ts": 1754737498007476, "dur": 58, "ph": "X", "name": "ReadAsync 211", "args": {}}, {"pid": 3100, "tid": 201863462912, "ts": 1754737498007537, "dur": 38, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3100, "tid": 201863462912, "ts": 1754737498007579, "dur": 1, "ph": "X", "name": "ProcessMessages 599", "args": {}}, {"pid": 3100, "tid": 201863462912, "ts": 1754737498007580, "dur": 36, "ph": "X", "name": "ReadAsync 599", "args": {}}, {"pid": 3100, "tid": 201863462912, "ts": 1754737498007620, "dur": 54, "ph": "X", "name": "ReadAsync 460", "args": {}}, {"pid": 3100, "tid": 201863462912, "ts": 1754737498007675, "dur": 22, "ph": "X", "name": "ReadAsync 235", "args": {}}, {"pid": 3100, "tid": 201863462912, "ts": 1754737498007698, "dur": 23, "ph": "X", "name": "ReadAsync 328", "args": {}}, {"pid": 3100, "tid": 201863462912, "ts": 1754737498007723, "dur": 23, "ph": "X", "name": "ReadAsync 348", "args": {}}, {"pid": 3100, "tid": 201863462912, "ts": 1754737498007748, "dur": 117, "ph": "X", "name": "ReadAsync 397", "args": {}}, {"pid": 3100, "tid": 201863462912, "ts": 1754737498007867, "dur": 25, "ph": "X", "name": "ReadAsync 369", "args": {}}, {"pid": 3100, "tid": 201863462912, "ts": 1754737498007893, "dur": 25, "ph": "X", "name": "ReadAsync 453", "args": {}}, {"pid": 3100, "tid": 201863462912, "ts": 1754737498007920, "dur": 21, "ph": "X", "name": "ReadAsync 247", "args": {}}, {"pid": 3100, "tid": 201863462912, "ts": 1754737498007943, "dur": 22, "ph": "X", "name": "ReadAsync 355", "args": {}}, {"pid": 3100, "tid": 201863462912, "ts": 1754737498007966, "dur": 22, "ph": "X", "name": "ReadAsync 446", "args": {}}, {"pid": 3100, "tid": 201863462912, "ts": 1754737498007989, "dur": 21, "ph": "X", "name": "ReadAsync 345", "args": {}}, {"pid": 3100, "tid": 201863462912, "ts": 1754737498008012, "dur": 23, "ph": "X", "name": "ReadAsync 342", "args": {}}, {"pid": 3100, "tid": 201863462912, "ts": 1754737498008036, "dur": 21, "ph": "X", "name": "ReadAsync 457", "args": {}}, {"pid": 3100, "tid": 201863462912, "ts": 1754737498008059, "dur": 17, "ph": "X", "name": "ReadAsync 314", "args": {}}, {"pid": 3100, "tid": 201863462912, "ts": 1754737498008077, "dur": 48, "ph": "X", "name": "ReadAsync 77", "args": {}}, {"pid": 3100, "tid": 201863462912, "ts": 1754737498008129, "dur": 32, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3100, "tid": 201863462912, "ts": 1754737498008164, "dur": 23, "ph": "X", "name": "ReadAsync 574", "args": {}}, {"pid": 3100, "tid": 201863462912, "ts": 1754737498008189, "dur": 27, "ph": "X", "name": "ReadAsync 328", "args": {}}, {"pid": 3100, "tid": 201863462912, "ts": 1754737498008218, "dur": 1, "ph": "X", "name": "ProcessMessages 330", "args": {}}, {"pid": 3100, "tid": 201863462912, "ts": 1754737498008220, "dur": 30, "ph": "X", "name": "ReadAsync 330", "args": {}}, {"pid": 3100, "tid": 201863462912, "ts": 1754737498008251, "dur": 23, "ph": "X", "name": "ReadAsync 568", "args": {}}, {"pid": 3100, "tid": 201863462912, "ts": 1754737498008277, "dur": 23, "ph": "X", "name": "ReadAsync 351", "args": {}}, {"pid": 3100, "tid": 201863462912, "ts": 1754737498008301, "dur": 19, "ph": "X", "name": "ReadAsync 445", "args": {}}, {"pid": 3100, "tid": 201863462912, "ts": 1754737498008321, "dur": 24, "ph": "X", "name": "ReadAsync 341", "args": {}}, {"pid": 3100, "tid": 201863462912, "ts": 1754737498008346, "dur": 42, "ph": "X", "name": "ReadAsync 350", "args": {}}, {"pid": 3100, "tid": 201863462912, "ts": 1754737498008390, "dur": 21, "ph": "X", "name": "ReadAsync 89", "args": {}}, {"pid": 3100, "tid": 201863462912, "ts": 1754737498008414, "dur": 21, "ph": "X", "name": "ReadAsync 371", "args": {}}, {"pid": 3100, "tid": 201863462912, "ts": 1754737498008436, "dur": 21, "ph": "X", "name": "ReadAsync 483", "args": {}}, {"pid": 3100, "tid": 201863462912, "ts": 1754737498008459, "dur": 21, "ph": "X", "name": "ReadAsync 374", "args": {}}, {"pid": 3100, "tid": 201863462912, "ts": 1754737498008482, "dur": 21, "ph": "X", "name": "ReadAsync 440", "args": {}}, {"pid": 3100, "tid": 201863462912, "ts": 1754737498008505, "dur": 21, "ph": "X", "name": "ReadAsync 343", "args": {}}, {"pid": 3100, "tid": 201863462912, "ts": 1754737498008527, "dur": 19, "ph": "X", "name": "ReadAsync 421", "args": {}}, {"pid": 3100, "tid": 201863462912, "ts": 1754737498008548, "dur": 24, "ph": "X", "name": "ReadAsync 21", "args": {}}, {"pid": 3100, "tid": 201863462912, "ts": 1754737498008575, "dur": 20, "ph": "X", "name": "ReadAsync 574", "args": {}}, {"pid": 3100, "tid": 201863462912, "ts": 1754737498008596, "dur": 22, "ph": "X", "name": "ReadAsync 469", "args": {}}, {"pid": 3100, "tid": 201863462912, "ts": 1754737498008619, "dur": 21, "ph": "X", "name": "ReadAsync 351", "args": {}}, {"pid": 3100, "tid": 201863462912, "ts": 1754737498008642, "dur": 21, "ph": "X", "name": "ReadAsync 356", "args": {}}, {"pid": 3100, "tid": 201863462912, "ts": 1754737498008664, "dur": 21, "ph": "X", "name": "ReadAsync 416", "args": {}}, {"pid": 3100, "tid": 201863462912, "ts": 1754737498008687, "dur": 21, "ph": "X", "name": "ReadAsync 363", "args": {}}, {"pid": 3100, "tid": 201863462912, "ts": 1754737498008710, "dur": 22, "ph": "X", "name": "ReadAsync 385", "args": {}}, {"pid": 3100, "tid": 201863462912, "ts": 1754737498008733, "dur": 22, "ph": "X", "name": "ReadAsync 433", "args": {}}, {"pid": 3100, "tid": 201863462912, "ts": 1754737498008756, "dur": 22, "ph": "X", "name": "ReadAsync 366", "args": {}}, {"pid": 3100, "tid": 201863462912, "ts": 1754737498008780, "dur": 23, "ph": "X", "name": "ReadAsync 486", "args": {}}, {"pid": 3100, "tid": 201863462912, "ts": 1754737498008805, "dur": 21, "ph": "X", "name": "ReadAsync 368", "args": {}}, {"pid": 3100, "tid": 201863462912, "ts": 1754737498008826, "dur": 1, "ph": "X", "name": "ProcessMessages 420", "args": {}}, {"pid": 3100, "tid": 201863462912, "ts": 1754737498008828, "dur": 20, "ph": "X", "name": "ReadAsync 420", "args": {}}, {"pid": 3100, "tid": 201863462912, "ts": 1754737498008850, "dur": 21, "ph": "X", "name": "ReadAsync 344", "args": {}}, {"pid": 3100, "tid": 201863462912, "ts": 1754737498008872, "dur": 25, "ph": "X", "name": "ReadAsync 333", "args": {}}, {"pid": 3100, "tid": 201863462912, "ts": 1754737498008899, "dur": 22, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3100, "tid": 201863462912, "ts": 1754737498008923, "dur": 32, "ph": "X", "name": "ReadAsync 349", "args": {}}, {"pid": 3100, "tid": 201863462912, "ts": 1754737498008957, "dur": 1, "ph": "X", "name": "ProcessMessages 330", "args": {}}, {"pid": 3100, "tid": 201863462912, "ts": 1754737498008959, "dur": 47, "ph": "X", "name": "ReadAsync 330", "args": {}}, {"pid": 3100, "tid": 201863462912, "ts": 1754737498009009, "dur": 1, "ph": "X", "name": "ProcessMessages 732", "args": {}}, {"pid": 3100, "tid": 201863462912, "ts": 1754737498009011, "dur": 38, "ph": "X", "name": "ReadAsync 732", "args": {}}, {"pid": 3100, "tid": 201863462912, "ts": 1754737498009051, "dur": 28, "ph": "X", "name": "ReadAsync 290", "args": {}}, {"pid": 3100, "tid": 201863462912, "ts": 1754737498009082, "dur": 36, "ph": "X", "name": "ReadAsync 261", "args": {}}, {"pid": 3100, "tid": 201863462912, "ts": 1754737498009121, "dur": 1, "ph": "X", "name": "ProcessMessages 243", "args": {}}, {"pid": 3100, "tid": 201863462912, "ts": 1754737498009123, "dur": 46, "ph": "X", "name": "ReadAsync 243", "args": {}}, {"pid": 3100, "tid": 201863462912, "ts": 1754737498009171, "dur": 1, "ph": "X", "name": "ProcessMessages 618", "args": {}}, {"pid": 3100, "tid": 201863462912, "ts": 1754737498009173, "dur": 133, "ph": "X", "name": "ReadAsync 618", "args": {}}, {"pid": 3100, "tid": 201863462912, "ts": 1754737498009308, "dur": 31, "ph": "X", "name": "ReadAsync 735", "args": {}}, {"pid": 3100, "tid": 201863462912, "ts": 1754737498009341, "dur": 36, "ph": "X", "name": "ReadAsync 639", "args": {}}, {"pid": 3100, "tid": 201863462912, "ts": 1754737498009380, "dur": 1, "ph": "X", "name": "ProcessMessages 353", "args": {}}, {"pid": 3100, "tid": 201863462912, "ts": 1754737498009382, "dur": 35, "ph": "X", "name": "ReadAsync 353", "args": {}}, {"pid": 3100, "tid": 201863462912, "ts": 1754737498009420, "dur": 113, "ph": "X", "name": "ReadAsync 550", "args": {}}, {"pid": 3100, "tid": 201863462912, "ts": 1754737498009535, "dur": 22, "ph": "X", "name": "ReadAsync 564", "args": {}}, {"pid": 3100, "tid": 201863462912, "ts": 1754737498009558, "dur": 24, "ph": "X", "name": "ReadAsync 321", "args": {}}, {"pid": 3100, "tid": 201863462912, "ts": 1754737498009583, "dur": 22, "ph": "X", "name": "ReadAsync 239", "args": {}}, {"pid": 3100, "tid": 201863462912, "ts": 1754737498009607, "dur": 21, "ph": "X", "name": "ReadAsync 323", "args": {}}, {"pid": 3100, "tid": 201863462912, "ts": 1754737498009630, "dur": 20, "ph": "X", "name": "ReadAsync 366", "args": {}}, {"pid": 3100, "tid": 201863462912, "ts": 1754737498009652, "dur": 24, "ph": "X", "name": "ReadAsync 357", "args": {}}, {"pid": 3100, "tid": 201863462912, "ts": 1754737498009677, "dur": 21, "ph": "X", "name": "ReadAsync 429", "args": {}}, {"pid": 3100, "tid": 201863462912, "ts": 1754737498009699, "dur": 23, "ph": "X", "name": "ReadAsync 377", "args": {}}, {"pid": 3100, "tid": 201863462912, "ts": 1754737498009723, "dur": 21, "ph": "X", "name": "ReadAsync 391", "args": {}}, {"pid": 3100, "tid": 201863462912, "ts": 1754737498009745, "dur": 21, "ph": "X", "name": "ReadAsync 357", "args": {}}, {"pid": 3100, "tid": 201863462912, "ts": 1754737498009768, "dur": 21, "ph": "X", "name": "ReadAsync 462", "args": {}}, {"pid": 3100, "tid": 201863462912, "ts": 1754737498009790, "dur": 21, "ph": "X", "name": "ReadAsync 360", "args": {}}, {"pid": 3100, "tid": 201863462912, "ts": 1754737498009813, "dur": 21, "ph": "X", "name": "ReadAsync 224", "args": {}}, {"pid": 3100, "tid": 201863462912, "ts": 1754737498009836, "dur": 22, "ph": "X", "name": "ReadAsync 315", "args": {}}, {"pid": 3100, "tid": 201863462912, "ts": 1754737498009859, "dur": 22, "ph": "X", "name": "ReadAsync 316", "args": {}}, {"pid": 3100, "tid": 201863462912, "ts": 1754737498009882, "dur": 21, "ph": "X", "name": "ReadAsync 350", "args": {}}, {"pid": 3100, "tid": 201863462912, "ts": 1754737498009905, "dur": 21, "ph": "X", "name": "ReadAsync 264", "args": {}}, {"pid": 3100, "tid": 201863462912, "ts": 1754737498009927, "dur": 21, "ph": "X", "name": "ReadAsync 191", "args": {}}, {"pid": 3100, "tid": 201863462912, "ts": 1754737498009950, "dur": 20, "ph": "X", "name": "ReadAsync 265", "args": {}}, {"pid": 3100, "tid": 201863462912, "ts": 1754737498009971, "dur": 17, "ph": "X", "name": "ReadAsync 21", "args": {}}, {"pid": 3100, "tid": 201863462912, "ts": 1754737498009989, "dur": 23, "ph": "X", "name": "ReadAsync 233", "args": {}}, {"pid": 3100, "tid": 201863462912, "ts": 1754737498010013, "dur": 21, "ph": "X", "name": "ReadAsync 337", "args": {}}, {"pid": 3100, "tid": 201863462912, "ts": 1754737498010035, "dur": 20, "ph": "X", "name": "ReadAsync 213", "args": {}}, {"pid": 3100, "tid": 201863462912, "ts": 1754737498010057, "dur": 22, "ph": "X", "name": "ReadAsync 339", "args": {}}, {"pid": 3100, "tid": 201863462912, "ts": 1754737498010081, "dur": 21, "ph": "X", "name": "ReadAsync 296", "args": {}}, {"pid": 3100, "tid": 201863462912, "ts": 1754737498010104, "dur": 21, "ph": "X", "name": "ReadAsync 330", "args": {}}, {"pid": 3100, "tid": 201863462912, "ts": 1754737498010126, "dur": 21, "ph": "X", "name": "ReadAsync 294", "args": {}}, {"pid": 3100, "tid": 201863462912, "ts": 1754737498010148, "dur": 21, "ph": "X", "name": "ReadAsync 289", "args": {}}, {"pid": 3100, "tid": 201863462912, "ts": 1754737498010171, "dur": 20, "ph": "X", "name": "ReadAsync 75", "args": {}}, {"pid": 3100, "tid": 201863462912, "ts": 1754737498010193, "dur": 20, "ph": "X", "name": "ReadAsync 315", "args": {}}, {"pid": 3100, "tid": 201863462912, "ts": 1754737498010215, "dur": 20, "ph": "X", "name": "ReadAsync 283", "args": {}}, {"pid": 3100, "tid": 201863462912, "ts": 1754737498010237, "dur": 21, "ph": "X", "name": "ReadAsync 292", "args": {}}, {"pid": 3100, "tid": 201863462912, "ts": 1754737498010260, "dur": 20, "ph": "X", "name": "ReadAsync 276", "args": {}}, {"pid": 3100, "tid": 201863462912, "ts": 1754737498010281, "dur": 21, "ph": "X", "name": "ReadAsync 191", "args": {}}, {"pid": 3100, "tid": 201863462912, "ts": 1754737498010303, "dur": 20, "ph": "X", "name": "ReadAsync 278", "args": {}}, {"pid": 3100, "tid": 201863462912, "ts": 1754737498010325, "dur": 82, "ph": "X", "name": "ReadAsync 262", "args": {}}, {"pid": 3100, "tid": 201863462912, "ts": 1754737498010408, "dur": 20, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3100, "tid": 201863462912, "ts": 1754737498010429, "dur": 22, "ph": "X", "name": "ReadAsync 303", "args": {}}, {"pid": 3100, "tid": 201863462912, "ts": 1754737498010453, "dur": 21, "ph": "X", "name": "ReadAsync 283", "args": {}}, {"pid": 3100, "tid": 201863462912, "ts": 1754737498010475, "dur": 22, "ph": "X", "name": "ReadAsync 336", "args": {}}, {"pid": 3100, "tid": 201863462912, "ts": 1754737498010498, "dur": 21, "ph": "X", "name": "ReadAsync 286", "args": {}}, {"pid": 3100, "tid": 201863462912, "ts": 1754737498010521, "dur": 20, "ph": "X", "name": "ReadAsync 241", "args": {}}, {"pid": 3100, "tid": 201863462912, "ts": 1754737498010543, "dur": 20, "ph": "X", "name": "ReadAsync 286", "args": {}}, {"pid": 3100, "tid": 201863462912, "ts": 1754737498010564, "dur": 17, "ph": "X", "name": "ReadAsync 150", "args": {}}, {"pid": 3100, "tid": 201863462912, "ts": 1754737498010582, "dur": 20, "ph": "X", "name": "ReadAsync 128", "args": {}}, {"pid": 3100, "tid": 201863462912, "ts": 1754737498010604, "dur": 23, "ph": "X", "name": "ReadAsync 308", "args": {}}, {"pid": 3100, "tid": 201863462912, "ts": 1754737498010628, "dur": 21, "ph": "X", "name": "ReadAsync 284", "args": {}}, {"pid": 3100, "tid": 201863462912, "ts": 1754737498010650, "dur": 21, "ph": "X", "name": "ReadAsync 291", "args": {}}, {"pid": 3100, "tid": 201863462912, "ts": 1754737498010673, "dur": 21, "ph": "X", "name": "ReadAsync 264", "args": {}}, {"pid": 3100, "tid": 201863462912, "ts": 1754737498010695, "dur": 21, "ph": "X", "name": "ReadAsync 228", "args": {}}, {"pid": 3100, "tid": 201863462912, "ts": 1754737498010717, "dur": 20, "ph": "X", "name": "ReadAsync 313", "args": {}}, {"pid": 3100, "tid": 201863462912, "ts": 1754737498010738, "dur": 24, "ph": "X", "name": "ReadAsync 77", "args": {}}, {"pid": 3100, "tid": 201863462912, "ts": 1754737498010764, "dur": 20, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3100, "tid": 201863462912, "ts": 1754737498010786, "dur": 20, "ph": "X", "name": "ReadAsync 241", "args": {}}, {"pid": 3100, "tid": 201863462912, "ts": 1754737498010807, "dur": 22, "ph": "X", "name": "ReadAsync 44", "args": {}}, {"pid": 3100, "tid": 201863462912, "ts": 1754737498010831, "dur": 22, "ph": "X", "name": "ReadAsync 435", "args": {}}, {"pid": 3100, "tid": 201863462912, "ts": 1754737498010854, "dur": 33, "ph": "X", "name": "ReadAsync 316", "args": {}}, {"pid": 3100, "tid": 201863462912, "ts": 1754737498010890, "dur": 22, "ph": "X", "name": "ReadAsync 325", "args": {}}, {"pid": 3100, "tid": 201863462912, "ts": 1754737498010913, "dur": 25, "ph": "X", "name": "ReadAsync 434", "args": {}}, {"pid": 3100, "tid": 201863462912, "ts": 1754737498010940, "dur": 21, "ph": "X", "name": "ReadAsync 435", "args": {}}, {"pid": 3100, "tid": 201863462912, "ts": 1754737498010963, "dur": 21, "ph": "X", "name": "ReadAsync 359", "args": {}}, {"pid": 3100, "tid": 201863462912, "ts": 1754737498010985, "dur": 22, "ph": "X", "name": "ReadAsync 390", "args": {}}, {"pid": 3100, "tid": 201863462912, "ts": 1754737498011008, "dur": 21, "ph": "X", "name": "ReadAsync 265", "args": {}}, {"pid": 3100, "tid": 201863462912, "ts": 1754737498011031, "dur": 21, "ph": "X", "name": "ReadAsync 366", "args": {}}, {"pid": 3100, "tid": 201863462912, "ts": 1754737498011053, "dur": 27, "ph": "X", "name": "ReadAsync 297", "args": {}}, {"pid": 3100, "tid": 201863462912, "ts": 1754737498011083, "dur": 282, "ph": "X", "name": "ReadAsync 5", "args": {}}, {"pid": 3100, "tid": 201863462912, "ts": 1754737498011369, "dur": 30, "ph": "X", "name": "ReadAsync 1", "args": {}}, {"pid": 3100, "tid": 201863462912, "ts": 1754737498011401, "dur": 1, "ph": "X", "name": "ProcessMessages 70", "args": {}}, {"pid": 3100, "tid": 201863462912, "ts": 1754737498011403, "dur": 28, "ph": "X", "name": "ReadAsync 70", "args": {}}, {"pid": 3100, "tid": 201863462912, "ts": 1754737498011433, "dur": 43, "ph": "X", "name": "ReadAsync 425", "args": {}}, {"pid": 3100, "tid": 201863462912, "ts": 1754737498011478, "dur": 31, "ph": "X", "name": "ReadAsync 194", "args": {}}, {"pid": 3100, "tid": 201863462912, "ts": 1754737498011511, "dur": 17, "ph": "X", "name": "ReadAsync 483", "args": {}}, {"pid": 3100, "tid": 201863462912, "ts": 1754737498011529, "dur": 33, "ph": "X", "name": "ReadAsync 9", "args": {}}, {"pid": 3100, "tid": 201863462912, "ts": 1754737498011565, "dur": 1, "ph": "X", "name": "ProcessMessages 94", "args": {}}, {"pid": 3100, "tid": 201863462912, "ts": 1754737498011566, "dur": 30, "ph": "X", "name": "ReadAsync 94", "args": {}}, {"pid": 3100, "tid": 201863462912, "ts": 1754737498011598, "dur": 22, "ph": "X", "name": "ReadAsync 307", "args": {}}, {"pid": 3100, "tid": 201863462912, "ts": 1754737498011623, "dur": 21, "ph": "X", "name": "ReadAsync 180", "args": {}}, {"pid": 3100, "tid": 201863462912, "ts": 1754737498011645, "dur": 14, "ph": "X", "name": "ReadAsync 196", "args": {}}, {"pid": 3100, "tid": 201863462912, "ts": 1754737498011661, "dur": 46, "ph": "X", "name": "ReadAsync 75", "args": {}}, {"pid": 3100, "tid": 201863462912, "ts": 1754737498011708, "dur": 22, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3100, "tid": 201863462912, "ts": 1754737498011732, "dur": 21, "ph": "X", "name": "ReadAsync 220", "args": {}}, {"pid": 3100, "tid": 201863462912, "ts": 1754737498011755, "dur": 1, "ph": "X", "name": "ProcessMessages 132", "args": {}}, {"pid": 3100, "tid": 201863462912, "ts": 1754737498011757, "dur": 29, "ph": "X", "name": "ReadAsync 132", "args": {}}, {"pid": 3100, "tid": 201863462912, "ts": 1754737498011787, "dur": 23, "ph": "X", "name": "ReadAsync 295", "args": {}}, {"pid": 3100, "tid": 201863462912, "ts": 1754737498011812, "dur": 21, "ph": "X", "name": "ReadAsync 367", "args": {}}, {"pid": 3100, "tid": 201863462912, "ts": 1754737498011834, "dur": 21, "ph": "X", "name": "ReadAsync 313", "args": {}}, {"pid": 3100, "tid": 201863462912, "ts": 1754737498011857, "dur": 29, "ph": "X", "name": "ReadAsync 270", "args": {}}, {"pid": 3100, "tid": 201863462912, "ts": 1754737498011888, "dur": 19, "ph": "X", "name": "ReadAsync 285", "args": {}}, {"pid": 3100, "tid": 201863462912, "ts": 1754737498011908, "dur": 26, "ph": "X", "name": "ReadAsync 69", "args": {}}, {"pid": 3100, "tid": 201863462912, "ts": 1754737498011936, "dur": 22, "ph": "X", "name": "ReadAsync 324", "args": {}}, {"pid": 3100, "tid": 201863462912, "ts": 1754737498011960, "dur": 23, "ph": "X", "name": "ReadAsync 295", "args": {}}, {"pid": 3100, "tid": 201863462912, "ts": 1754737498011985, "dur": 22, "ph": "X", "name": "ReadAsync 301", "args": {}}, {"pid": 3100, "tid": 201863462912, "ts": 1754737498012008, "dur": 42, "ph": "X", "name": "ReadAsync 281", "args": {}}, {"pid": 3100, "tid": 201863462912, "ts": 1754737498012051, "dur": 21, "ph": "X", "name": "ReadAsync 149", "args": {}}, {"pid": 3100, "tid": 201863462912, "ts": 1754737498012073, "dur": 18, "ph": "X", "name": "ReadAsync 342", "args": {}}, {"pid": 3100, "tid": 201863462912, "ts": 1754737498012093, "dur": 1, "ph": "X", "name": "ProcessMessages 271", "args": {}}, {"pid": 3100, "tid": 201863462912, "ts": 1754737498012094, "dur": 18, "ph": "X", "name": "ReadAsync 271", "args": {}}, {"pid": 3100, "tid": 201863462912, "ts": 1754737498012114, "dur": 23, "ph": "X", "name": "ReadAsync 118", "args": {}}, {"pid": 3100, "tid": 201863462912, "ts": 1754737498012138, "dur": 25, "ph": "X", "name": "ReadAsync 304", "args": {}}, {"pid": 3100, "tid": 201863462912, "ts": 1754737498012165, "dur": 20, "ph": "X", "name": "ReadAsync 391", "args": {}}, {"pid": 3100, "tid": 201863462912, "ts": 1754737498012187, "dur": 21, "ph": "X", "name": "ReadAsync 252", "args": {}}, {"pid": 3100, "tid": 201863462912, "ts": 1754737498012209, "dur": 20, "ph": "X", "name": "ReadAsync 239", "args": {}}, {"pid": 3100, "tid": 201863462912, "ts": 1754737498012231, "dur": 20, "ph": "X", "name": "ReadAsync 266", "args": {}}, {"pid": 3100, "tid": 201863462912, "ts": 1754737498012253, "dur": 17, "ph": "X", "name": "ReadAsync 265", "args": {}}, {"pid": 3100, "tid": 201863462912, "ts": 1754737498012271, "dur": 20, "ph": "X", "name": "ReadAsync 62", "args": {}}, {"pid": 3100, "tid": 201863462912, "ts": 1754737498012293, "dur": 23, "ph": "X", "name": "ReadAsync 304", "args": {}}, {"pid": 3100, "tid": 201863462912, "ts": 1754737498012318, "dur": 20, "ph": "X", "name": "ReadAsync 295", "args": {}}, {"pid": 3100, "tid": 201863462912, "ts": 1754737498012340, "dur": 19, "ph": "X", "name": "ReadAsync 313", "args": {}}, {"pid": 3100, "tid": 201863462912, "ts": 1754737498012360, "dur": 20, "ph": "X", "name": "ReadAsync 194", "args": {}}, {"pid": 3100, "tid": 201863462912, "ts": 1754737498012382, "dur": 20, "ph": "X", "name": "ReadAsync 248", "args": {}}, {"pid": 3100, "tid": 201863462912, "ts": 1754737498012403, "dur": 24, "ph": "X", "name": "ReadAsync 261", "args": {}}, {"pid": 3100, "tid": 201863462912, "ts": 1754737498012429, "dur": 20, "ph": "X", "name": "ReadAsync 328", "args": {}}, {"pid": 3100, "tid": 201863462912, "ts": 1754737498012451, "dur": 20, "ph": "X", "name": "ReadAsync 60", "args": {}}, {"pid": 3100, "tid": 201863462912, "ts": 1754737498012472, "dur": 20, "ph": "X", "name": "ReadAsync 347", "args": {}}, {"pid": 3100, "tid": 201863462912, "ts": 1754737498012494, "dur": 20, "ph": "X", "name": "ReadAsync 363", "args": {}}, {"pid": 3100, "tid": 201863462912, "ts": 1754737498012516, "dur": 22, "ph": "X", "name": "ReadAsync 384", "args": {}}, {"pid": 3100, "tid": 201863462912, "ts": 1754737498012539, "dur": 20, "ph": "X", "name": "ReadAsync 288", "args": {}}, {"pid": 3100, "tid": 201863462912, "ts": 1754737498012560, "dur": 21, "ph": "X", "name": "ReadAsync 267", "args": {}}, {"pid": 3100, "tid": 201863462912, "ts": 1754737498012582, "dur": 21, "ph": "X", "name": "ReadAsync 326", "args": {}}, {"pid": 3100, "tid": 201863462912, "ts": 1754737498012604, "dur": 24, "ph": "X", "name": "ReadAsync 322", "args": {}}, {"pid": 3100, "tid": 201863462912, "ts": 1754737498012634, "dur": 37, "ph": "X", "name": "ReadAsync 130", "args": {}}, {"pid": 3100, "tid": 201863462912, "ts": 1754737498012672, "dur": 29, "ph": "X", "name": "ReadAsync 532", "args": {}}, {"pid": 3100, "tid": 201863462912, "ts": 1754737498012703, "dur": 22, "ph": "X", "name": "ReadAsync 260", "args": {}}, {"pid": 3100, "tid": 201863462912, "ts": 1754737498012727, "dur": 23, "ph": "X", "name": "ReadAsync 202", "args": {}}, {"pid": 3100, "tid": 201863462912, "ts": 1754737498012751, "dur": 21, "ph": "X", "name": "ReadAsync 351", "args": {}}, {"pid": 3100, "tid": 201863462912, "ts": 1754737498012774, "dur": 21, "ph": "X", "name": "ReadAsync 311", "args": {}}, {"pid": 3100, "tid": 201863462912, "ts": 1754737498012797, "dur": 19, "ph": "X", "name": "ReadAsync 354", "args": {}}, {"pid": 3100, "tid": 201863462912, "ts": 1754737498012817, "dur": 20, "ph": "X", "name": "ReadAsync 21", "args": {}}, {"pid": 3100, "tid": 201863462912, "ts": 1754737498012839, "dur": 21, "ph": "X", "name": "ReadAsync 158", "args": {}}, {"pid": 3100, "tid": 201863462912, "ts": 1754737498012862, "dur": 23, "ph": "X", "name": "ReadAsync 484", "args": {}}, {"pid": 3100, "tid": 201863462912, "ts": 1754737498012887, "dur": 21, "ph": "X", "name": "ReadAsync 442", "args": {}}, {"pid": 3100, "tid": 201863462912, "ts": 1754737498012910, "dur": 1, "ph": "X", "name": "ProcessMessages 428", "args": {}}, {"pid": 3100, "tid": 201863462912, "ts": 1754737498012912, "dur": 29, "ph": "X", "name": "ReadAsync 428", "args": {}}, {"pid": 3100, "tid": 201863462912, "ts": 1754737498012944, "dur": 1, "ph": "X", "name": "ProcessMessages 584", "args": {}}, {"pid": 3100, "tid": 201863462912, "ts": 1754737498012945, "dur": 31, "ph": "X", "name": "ReadAsync 584", "args": {}}, {"pid": 3100, "tid": 201863462912, "ts": 1754737498012978, "dur": 24, "ph": "X", "name": "ReadAsync 256", "args": {}}, {"pid": 3100, "tid": 201863462912, "ts": 1754737498013005, "dur": 1, "ph": "X", "name": "ProcessMessages 205", "args": {}}, {"pid": 3100, "tid": 201863462912, "ts": 1754737498013007, "dur": 31, "ph": "X", "name": "ReadAsync 205", "args": {}}, {"pid": 3100, "tid": 201863462912, "ts": 1754737498013041, "dur": 145, "ph": "X", "name": "ReadAsync 546", "args": {}}, {"pid": 3100, "tid": 201863462912, "ts": 1754737498013187, "dur": 21, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 3100, "tid": 201863462912, "ts": 1754737498013210, "dur": 22, "ph": "X", "name": "ReadAsync 272", "args": {}}, {"pid": 3100, "tid": 201863462912, "ts": 1754737498013233, "dur": 67, "ph": "X", "name": "ReadAsync 284", "args": {}}, {"pid": 3100, "tid": 201863462912, "ts": 1754737498013303, "dur": 23, "ph": "X", "name": "ReadAsync 238", "args": {}}, {"pid": 3100, "tid": 201863462912, "ts": 1754737498013327, "dur": 21, "ph": "X", "name": "ReadAsync 441", "args": {}}, {"pid": 3100, "tid": 201863462912, "ts": 1754737498013350, "dur": 20, "ph": "X", "name": "ReadAsync 437", "args": {}}, {"pid": 3100, "tid": 201863462912, "ts": 1754737498013371, "dur": 19, "ph": "X", "name": "ReadAsync 359", "args": {}}, {"pid": 3100, "tid": 201863462912, "ts": 1754737498013392, "dur": 19, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 3100, "tid": 201863462912, "ts": 1754737498013412, "dur": 20, "ph": "X", "name": "ReadAsync 270", "args": {}}, {"pid": 3100, "tid": 201863462912, "ts": 1754737498013434, "dur": 34, "ph": "X", "name": "ReadAsync 322", "args": {}}, {"pid": 3100, "tid": 201863462912, "ts": 1754737498013469, "dur": 21, "ph": "X", "name": "ReadAsync 402", "args": {}}, {"pid": 3100, "tid": 201863462912, "ts": 1754737498013491, "dur": 20, "ph": "X", "name": "ReadAsync 309", "args": {}}, {"pid": 3100, "tid": 201863462912, "ts": 1754737498013513, "dur": 20, "ph": "X", "name": "ReadAsync 350", "args": {}}, {"pid": 3100, "tid": 201863462912, "ts": 1754737498013534, "dur": 21, "ph": "X", "name": "ReadAsync 323", "args": {}}, {"pid": 3100, "tid": 201863462912, "ts": 1754737498013557, "dur": 19, "ph": "X", "name": "ReadAsync 319", "args": {}}, {"pid": 3100, "tid": 201863462912, "ts": 1754737498013578, "dur": 21, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 3100, "tid": 201863462912, "ts": 1754737498013599, "dur": 1, "ph": "X", "name": "ProcessMessages 336", "args": {}}, {"pid": 3100, "tid": 201863462912, "ts": 1754737498013601, "dur": 20, "ph": "X", "name": "ReadAsync 336", "args": {}}, {"pid": 3100, "tid": 201863462912, "ts": 1754737498013623, "dur": 18, "ph": "X", "name": "ReadAsync 303", "args": {}}, {"pid": 3100, "tid": 201863462912, "ts": 1754737498013642, "dur": 20, "ph": "X", "name": "ReadAsync 321", "args": {}}, {"pid": 3100, "tid": 201863462912, "ts": 1754737498013664, "dur": 21, "ph": "X", "name": "ReadAsync 253", "args": {}}, {"pid": 3100, "tid": 201863462912, "ts": 1754737498013686, "dur": 21, "ph": "X", "name": "ReadAsync 296", "args": {}}, {"pid": 3100, "tid": 201863462912, "ts": 1754737498013709, "dur": 19, "ph": "X", "name": "ReadAsync 373", "args": {}}, {"pid": 3100, "tid": 201863462912, "ts": 1754737498013729, "dur": 20, "ph": "X", "name": "ReadAsync 169", "args": {}}, {"pid": 3100, "tid": 201863462912, "ts": 1754737498013750, "dur": 21, "ph": "X", "name": "ReadAsync 257", "args": {}}, {"pid": 3100, "tid": 201863462912, "ts": 1754737498013773, "dur": 21, "ph": "X", "name": "ReadAsync 305", "args": {}}, {"pid": 3100, "tid": 201863462912, "ts": 1754737498013795, "dur": 20, "ph": "X", "name": "ReadAsync 373", "args": {}}, {"pid": 3100, "tid": 201863462912, "ts": 1754737498013816, "dur": 22, "ph": "X", "name": "ReadAsync 296", "args": {}}, {"pid": 3100, "tid": 201863462912, "ts": 1754737498013840, "dur": 20, "ph": "X", "name": "ReadAsync 334", "args": {}}, {"pid": 3100, "tid": 201863462912, "ts": 1754737498013863, "dur": 20, "ph": "X", "name": "ReadAsync 329", "args": {}}, {"pid": 3100, "tid": 201863462912, "ts": 1754737498013885, "dur": 19, "ph": "X", "name": "ReadAsync 289", "args": {}}, {"pid": 3100, "tid": 201863462912, "ts": 1754737498013905, "dur": 19, "ph": "X", "name": "ReadAsync 264", "args": {}}, {"pid": 3100, "tid": 201863462912, "ts": 1754737498013926, "dur": 22, "ph": "X", "name": "ReadAsync 224", "args": {}}, {"pid": 3100, "tid": 201863462912, "ts": 1754737498013949, "dur": 23, "ph": "X", "name": "ReadAsync 379", "args": {}}, {"pid": 3100, "tid": 201863462912, "ts": 1754737498013973, "dur": 19, "ph": "X", "name": "ReadAsync 421", "args": {}}, {"pid": 3100, "tid": 201863462912, "ts": 1754737498013994, "dur": 21, "ph": "X", "name": "ReadAsync 301", "args": {}}, {"pid": 3100, "tid": 201863462912, "ts": 1754737498014016, "dur": 20, "ph": "X", "name": "ReadAsync 318", "args": {}}, {"pid": 3100, "tid": 201863462912, "ts": 1754737498014038, "dur": 19, "ph": "X", "name": "ReadAsync 332", "args": {}}, {"pid": 3100, "tid": 201863462912, "ts": 1754737498014059, "dur": 131, "ph": "X", "name": "ReadAsync 76", "args": {}}, {"pid": 3100, "tid": 201863462912, "ts": 1754737498014191, "dur": 22, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3100, "tid": 201863462912, "ts": 1754737498014215, "dur": 22, "ph": "X", "name": "ReadAsync 351", "args": {}}, {"pid": 3100, "tid": 201863462912, "ts": 1754737498014238, "dur": 23, "ph": "X", "name": "ReadAsync 448", "args": {}}, {"pid": 3100, "tid": 201863462912, "ts": 1754737498014263, "dur": 24, "ph": "X", "name": "ReadAsync 393", "args": {}}, {"pid": 3100, "tid": 201863462912, "ts": 1754737498014288, "dur": 21, "ph": "X", "name": "ReadAsync 324", "args": {}}, {"pid": 3100, "tid": 201863462912, "ts": 1754737498014311, "dur": 21, "ph": "X", "name": "ReadAsync 335", "args": {}}, {"pid": 3100, "tid": 201863462912, "ts": 1754737498014335, "dur": 45, "ph": "X", "name": "ReadAsync 298", "args": {}}, {"pid": 3100, "tid": 201863462912, "ts": 1754737498014381, "dur": 21, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3100, "tid": 201863462912, "ts": 1754737498014403, "dur": 23, "ph": "X", "name": "ReadAsync 378", "args": {}}, {"pid": 3100, "tid": 201863462912, "ts": 1754737498014429, "dur": 29, "ph": "X", "name": "ReadAsync 395", "args": {}}, {"pid": 3100, "tid": 201863462912, "ts": 1754737498014460, "dur": 23, "ph": "X", "name": "ReadAsync 581", "args": {}}, {"pid": 3100, "tid": 201863462912, "ts": 1754737498014488, "dur": 26, "ph": "X", "name": "ReadAsync 304", "args": {}}, {"pid": 3100, "tid": 201863462912, "ts": 1754737498014516, "dur": 20, "ph": "X", "name": "ReadAsync 440", "args": {}}, {"pid": 3100, "tid": 201863462912, "ts": 1754737498014537, "dur": 1, "ph": "X", "name": "ProcessMessages 337", "args": {}}, {"pid": 3100, "tid": 201863462912, "ts": 1754737498014538, "dur": 26, "ph": "X", "name": "ReadAsync 337", "args": {}}, {"pid": 3100, "tid": 201863462912, "ts": 1754737498014566, "dur": 22, "ph": "X", "name": "ReadAsync 123", "args": {}}, {"pid": 3100, "tid": 201863462912, "ts": 1754737498014590, "dur": 23, "ph": "X", "name": "ReadAsync 306", "args": {}}, {"pid": 3100, "tid": 201863462912, "ts": 1754737498014614, "dur": 22, "ph": "X", "name": "ReadAsync 154", "args": {}}, {"pid": 3100, "tid": 201863462912, "ts": 1754737498014638, "dur": 23, "ph": "X", "name": "ReadAsync 339", "args": {}}, {"pid": 3100, "tid": 201863462912, "ts": 1754737498014663, "dur": 22, "ph": "X", "name": "ReadAsync 296", "args": {}}, {"pid": 3100, "tid": 201863462912, "ts": 1754737498014687, "dur": 28, "ph": "X", "name": "ReadAsync 316", "args": {}}, {"pid": 3100, "tid": 201863462912, "ts": 1754737498014716, "dur": 20, "ph": "X", "name": "ReadAsync 385", "args": {}}, {"pid": 3100, "tid": 201863462912, "ts": 1754737498014738, "dur": 22, "ph": "X", "name": "ReadAsync 66", "args": {}}, {"pid": 3100, "tid": 201863462912, "ts": 1754737498014761, "dur": 22, "ph": "X", "name": "ReadAsync 247", "args": {}}, {"pid": 3100, "tid": 201863462912, "ts": 1754737498014785, "dur": 20, "ph": "X", "name": "ReadAsync 367", "args": {}}, {"pid": 3100, "tid": 201863462912, "ts": 1754737498014806, "dur": 22, "ph": "X", "name": "ReadAsync 356", "args": {}}, {"pid": 3100, "tid": 201863462912, "ts": 1754737498014830, "dur": 21, "ph": "X", "name": "ReadAsync 274", "args": {}}, {"pid": 3100, "tid": 201863462912, "ts": 1754737498014852, "dur": 23, "ph": "X", "name": "ReadAsync 273", "args": {}}, {"pid": 3100, "tid": 201863462912, "ts": 1754737498014877, "dur": 22, "ph": "X", "name": "ReadAsync 426", "args": {}}, {"pid": 3100, "tid": 201863462912, "ts": 1754737498014900, "dur": 22, "ph": "X", "name": "ReadAsync 174", "args": {}}, {"pid": 3100, "tid": 201863462912, "ts": 1754737498014923, "dur": 22, "ph": "X", "name": "ReadAsync 324", "args": {}}, {"pid": 3100, "tid": 201863462912, "ts": 1754737498014946, "dur": 22, "ph": "X", "name": "ReadAsync 324", "args": {}}, {"pid": 3100, "tid": 201863462912, "ts": 1754737498014971, "dur": 22, "ph": "X", "name": "ReadAsync 342", "args": {}}, {"pid": 3100, "tid": 201863462912, "ts": 1754737498014994, "dur": 22, "ph": "X", "name": "ReadAsync 371", "args": {}}, {"pid": 3100, "tid": 201863462912, "ts": 1754737498015018, "dur": 21, "ph": "X", "name": "ReadAsync 374", "args": {}}, {"pid": 3100, "tid": 201863462912, "ts": 1754737498015041, "dur": 20, "ph": "X", "name": "ReadAsync 306", "args": {}}, {"pid": 3100, "tid": 201863462912, "ts": 1754737498015062, "dur": 18, "ph": "X", "name": "ReadAsync 76", "args": {}}, {"pid": 3100, "tid": 201863462912, "ts": 1754737498015082, "dur": 22, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3100, "tid": 201863462912, "ts": 1754737498015106, "dur": 22, "ph": "X", "name": "ReadAsync 360", "args": {}}, {"pid": 3100, "tid": 201863462912, "ts": 1754737498015129, "dur": 23, "ph": "X", "name": "ReadAsync 403", "args": {}}, {"pid": 3100, "tid": 201863462912, "ts": 1754737498015153, "dur": 23, "ph": "X", "name": "ReadAsync 427", "args": {}}, {"pid": 3100, "tid": 201863462912, "ts": 1754737498015177, "dur": 22, "ph": "X", "name": "ReadAsync 383", "args": {}}, {"pid": 3100, "tid": 201863462912, "ts": 1754737498015200, "dur": 20, "ph": "X", "name": "ReadAsync 327", "args": {}}, {"pid": 3100, "tid": 201863462912, "ts": 1754737498015222, "dur": 20, "ph": "X", "name": "ReadAsync 382", "args": {}}, {"pid": 3100, "tid": 201863462912, "ts": 1754737498015244, "dur": 22, "ph": "X", "name": "ReadAsync 21", "args": {}}, {"pid": 3100, "tid": 201863462912, "ts": 1754737498015267, "dur": 22, "ph": "X", "name": "ReadAsync 394", "args": {}}, {"pid": 3100, "tid": 201863462912, "ts": 1754737498015290, "dur": 22, "ph": "X", "name": "ReadAsync 426", "args": {}}, {"pid": 3100, "tid": 201863462912, "ts": 1754737498015314, "dur": 22, "ph": "X", "name": "ReadAsync 396", "args": {}}, {"pid": 3100, "tid": 201863462912, "ts": 1754737498015337, "dur": 22, "ph": "X", "name": "ReadAsync 283", "args": {}}, {"pid": 3100, "tid": 201863462912, "ts": 1754737498015361, "dur": 21, "ph": "X", "name": "ReadAsync 375", "args": {}}, {"pid": 3100, "tid": 201863462912, "ts": 1754737498015384, "dur": 23, "ph": "X", "name": "ReadAsync 309", "args": {}}, {"pid": 3100, "tid": 201863462912, "ts": 1754737498015408, "dur": 21, "ph": "X", "name": "ReadAsync 142", "args": {}}, {"pid": 3100, "tid": 201863462912, "ts": 1754737498015431, "dur": 20, "ph": "X", "name": "ReadAsync 371", "args": {}}, {"pid": 3100, "tid": 201863462912, "ts": 1754737498015453, "dur": 25, "ph": "X", "name": "ReadAsync 399", "args": {}}, {"pid": 3100, "tid": 201863462912, "ts": 1754737498015479, "dur": 29, "ph": "X", "name": "ReadAsync 441", "args": {}}, {"pid": 3100, "tid": 201863462912, "ts": 1754737498015512, "dur": 1, "ph": "X", "name": "ProcessMessages 204", "args": {}}, {"pid": 3100, "tid": 201863462912, "ts": 1754737498015513, "dur": 38, "ph": "X", "name": "ReadAsync 204", "args": {}}, {"pid": 3100, "tid": 201863462912, "ts": 1754737498015553, "dur": 21, "ph": "X", "name": "ReadAsync 666", "args": {}}, {"pid": 3100, "tid": 201863462912, "ts": 1754737498015575, "dur": 24, "ph": "X", "name": "ReadAsync 202", "args": {}}, {"pid": 3100, "tid": 201863462912, "ts": 1754737498015600, "dur": 27, "ph": "X", "name": "ReadAsync 271", "args": {}}, {"pid": 3100, "tid": 201863462912, "ts": 1754737498015628, "dur": 25, "ph": "X", "name": "ReadAsync 447", "args": {}}, {"pid": 3100, "tid": 201863462912, "ts": 1754737498015655, "dur": 19, "ph": "X", "name": "ReadAsync 352", "args": {}}, {"pid": 3100, "tid": 201863462912, "ts": 1754737498015676, "dur": 25, "ph": "X", "name": "ReadAsync 430", "args": {}}, {"pid": 3100, "tid": 201863462912, "ts": 1754737498015702, "dur": 23, "ph": "X", "name": "ReadAsync 310", "args": {}}, {"pid": 3100, "tid": 201863462912, "ts": 1754737498015726, "dur": 21, "ph": "X", "name": "ReadAsync 337", "args": {}}, {"pid": 3100, "tid": 201863462912, "ts": 1754737498015748, "dur": 34, "ph": "X", "name": "ReadAsync 190", "args": {}}, {"pid": 3100, "tid": 201863462912, "ts": 1754737498015784, "dur": 23, "ph": "X", "name": "ReadAsync 336", "args": {}}, {"pid": 3100, "tid": 201863462912, "ts": 1754737498015809, "dur": 62, "ph": "X", "name": "ReadAsync 351", "args": {}}, {"pid": 3100, "tid": 201863462912, "ts": 1754737498015872, "dur": 27, "ph": "X", "name": "ReadAsync 457", "args": {}}, {"pid": 3100, "tid": 201863462912, "ts": 1754737498015902, "dur": 1, "ph": "X", "name": "ProcessMessages 255", "args": {}}, {"pid": 3100, "tid": 201863462912, "ts": 1754737498015904, "dur": 53, "ph": "X", "name": "ReadAsync 255", "args": {}}, {"pid": 3100, "tid": 201863462912, "ts": 1754737498015959, "dur": 26, "ph": "X", "name": "ReadAsync 652", "args": {}}, {"pid": 3100, "tid": 201863462912, "ts": 1754737498015988, "dur": 25, "ph": "X", "name": "ReadAsync 342", "args": {}}, {"pid": 3100, "tid": 201863462912, "ts": 1754737498016015, "dur": 23, "ph": "X", "name": "ReadAsync 450", "args": {}}, {"pid": 3100, "tid": 201863462912, "ts": 1754737498016041, "dur": 25, "ph": "X", "name": "ReadAsync 377", "args": {}}, {"pid": 3100, "tid": 201863462912, "ts": 1754737498016069, "dur": 25, "ph": "X", "name": "ReadAsync 312", "args": {}}, {"pid": 3100, "tid": 201863462912, "ts": 1754737498016097, "dur": 1, "ph": "X", "name": "ProcessMessages 317", "args": {}}, {"pid": 3100, "tid": 201863462912, "ts": 1754737498016098, "dur": 34, "ph": "X", "name": "ReadAsync 317", "args": {}}, {"pid": 3100, "tid": 201863462912, "ts": 1754737498016134, "dur": 24, "ph": "X", "name": "ReadAsync 297", "args": {}}, {"pid": 3100, "tid": 201863462912, "ts": 1754737498016160, "dur": 25, "ph": "X", "name": "ReadAsync 393", "args": {}}, {"pid": 3100, "tid": 201863462912, "ts": 1754737498016188, "dur": 23, "ph": "X", "name": "ReadAsync 431", "args": {}}, {"pid": 3100, "tid": 201863462912, "ts": 1754737498016213, "dur": 22, "ph": "X", "name": "ReadAsync 559", "args": {}}, {"pid": 3100, "tid": 201863462912, "ts": 1754737498016236, "dur": 22, "ph": "X", "name": "ReadAsync 390", "args": {}}, {"pid": 3100, "tid": 201863462912, "ts": 1754737498016260, "dur": 21, "ph": "X", "name": "ReadAsync 396", "args": {}}, {"pid": 3100, "tid": 201863462912, "ts": 1754737498016282, "dur": 1, "ph": "X", "name": "ProcessMessages 300", "args": {}}, {"pid": 3100, "tid": 201863462912, "ts": 1754737498016284, "dur": 21, "ph": "X", "name": "ReadAsync 300", "args": {}}, {"pid": 3100, "tid": 201863462912, "ts": 1754737498016306, "dur": 15, "ph": "X", "name": "ReadAsync 312", "args": {}}, {"pid": 3100, "tid": 201863462912, "ts": 1754737498016322, "dur": 20, "ph": "X", "name": "ReadAsync 475", "args": {}}, {"pid": 3100, "tid": 201863462912, "ts": 1754737498016344, "dur": 23, "ph": "X", "name": "ReadAsync 363", "args": {}}, {"pid": 3100, "tid": 201863462912, "ts": 1754737498016369, "dur": 22, "ph": "X", "name": "ReadAsync 377", "args": {}}, {"pid": 3100, "tid": 201863462912, "ts": 1754737498016392, "dur": 21, "ph": "X", "name": "ReadAsync 347", "args": {}}, {"pid": 3100, "tid": 201863462912, "ts": 1754737498016415, "dur": 22, "ph": "X", "name": "ReadAsync 371", "args": {}}, {"pid": 3100, "tid": 201863462912, "ts": 1754737498016438, "dur": 21, "ph": "X", "name": "ReadAsync 393", "args": {}}, {"pid": 3100, "tid": 201863462912, "ts": 1754737498016461, "dur": 21, "ph": "X", "name": "ReadAsync 135", "args": {}}, {"pid": 3100, "tid": 201863462912, "ts": 1754737498016483, "dur": 15, "ph": "X", "name": "ReadAsync 473", "args": {}}, {"pid": 3100, "tid": 201863462912, "ts": 1754737498016500, "dur": 22, "ph": "X", "name": "ReadAsync 408", "args": {}}, {"pid": 3100, "tid": 201863462912, "ts": 1754737498016523, "dur": 22, "ph": "X", "name": "ReadAsync 342", "args": {}}, {"pid": 3100, "tid": 201863462912, "ts": 1754737498016547, "dur": 22, "ph": "X", "name": "ReadAsync 313", "args": {}}, {"pid": 3100, "tid": 201863462912, "ts": 1754737498016570, "dur": 22, "ph": "X", "name": "ReadAsync 434", "args": {}}, {"pid": 3100, "tid": 201863462912, "ts": 1754737498016593, "dur": 20, "ph": "X", "name": "ReadAsync 351", "args": {}}, {"pid": 3100, "tid": 201863462912, "ts": 1754737498016615, "dur": 22, "ph": "X", "name": "ReadAsync 126", "args": {}}, {"pid": 3100, "tid": 201863462912, "ts": 1754737498016638, "dur": 21, "ph": "X", "name": "ReadAsync 393", "args": {}}, {"pid": 3100, "tid": 201863462912, "ts": 1754737498016661, "dur": 20, "ph": "X", "name": "ReadAsync 324", "args": {}}, {"pid": 3100, "tid": 201863462912, "ts": 1754737498016683, "dur": 20, "ph": "X", "name": "ReadAsync 357", "args": {}}, {"pid": 3100, "tid": 201863462912, "ts": 1754737498016705, "dur": 24, "ph": "X", "name": "ReadAsync 172", "args": {}}, {"pid": 3100, "tid": 201863462912, "ts": 1754737498016731, "dur": 22, "ph": "X", "name": "ReadAsync 351", "args": {}}, {"pid": 3100, "tid": 201863462912, "ts": 1754737498016754, "dur": 20, "ph": "X", "name": "ReadAsync 327", "args": {}}, {"pid": 3100, "tid": 201863462912, "ts": 1754737498016776, "dur": 23, "ph": "X", "name": "ReadAsync 33", "args": {}}, {"pid": 3100, "tid": 201863462912, "ts": 1754737498016800, "dur": 24, "ph": "X", "name": "ReadAsync 445", "args": {}}, {"pid": 3100, "tid": 201863462912, "ts": 1754737498016826, "dur": 15, "ph": "X", "name": "ReadAsync 451", "args": {}}, {"pid": 3100, "tid": 201863462912, "ts": 1754737498016842, "dur": 17, "ph": "X", "name": "ReadAsync 323", "args": {}}, {"pid": 3100, "tid": 201863462912, "ts": 1754737498016860, "dur": 20, "ph": "X", "name": "ReadAsync 61", "args": {}}, {"pid": 3100, "tid": 201863462912, "ts": 1754737498016882, "dur": 18, "ph": "X", "name": "ReadAsync 237", "args": {}}, {"pid": 3100, "tid": 201863462912, "ts": 1754737498016902, "dur": 21, "ph": "X", "name": "ReadAsync 336", "args": {}}, {"pid": 3100, "tid": 201863462912, "ts": 1754737498016924, "dur": 20, "ph": "X", "name": "ReadAsync 252", "args": {}}, {"pid": 3100, "tid": 201863462912, "ts": 1754737498016945, "dur": 21, "ph": "X", "name": "ReadAsync 232", "args": {}}, {"pid": 3100, "tid": 201863462912, "ts": 1754737498016968, "dur": 21, "ph": "X", "name": "ReadAsync 294", "args": {}}, {"pid": 3100, "tid": 201863462912, "ts": 1754737498016990, "dur": 18, "ph": "X", "name": "ReadAsync 342", "args": {}}, {"pid": 3100, "tid": 201863462912, "ts": 1754737498017010, "dur": 16, "ph": "X", "name": "ReadAsync 311", "args": {}}, {"pid": 3100, "tid": 201863462912, "ts": 1754737498017027, "dur": 21, "ph": "X", "name": "ReadAsync 195", "args": {}}, {"pid": 3100, "tid": 201863462912, "ts": 1754737498017050, "dur": 22, "ph": "X", "name": "ReadAsync 272", "args": {}}, {"pid": 3100, "tid": 201863462912, "ts": 1754737498017073, "dur": 23, "ph": "X", "name": "ReadAsync 294", "args": {}}, {"pid": 3100, "tid": 201863462912, "ts": 1754737498017097, "dur": 44, "ph": "X", "name": "ReadAsync 107", "args": {}}, {"pid": 3100, "tid": 201863462912, "ts": 1754737498017144, "dur": 1, "ph": "X", "name": "ProcessMessages 408", "args": {}}, {"pid": 3100, "tid": 201863462912, "ts": 1754737498017146, "dur": 42, "ph": "X", "name": "ReadAsync 408", "args": {}}, {"pid": 3100, "tid": 201863462912, "ts": 1754737498017191, "dur": 1, "ph": "X", "name": "ProcessMessages 995", "args": {}}, {"pid": 3100, "tid": 201863462912, "ts": 1754737498017193, "dur": 37, "ph": "X", "name": "ReadAsync 995", "args": {}}, {"pid": 3100, "tid": 201863462912, "ts": 1754737498017232, "dur": 2, "ph": "X", "name": "ProcessMessages 410", "args": {}}, {"pid": 3100, "tid": 201863462912, "ts": 1754737498017235, "dur": 38, "ph": "X", "name": "ReadAsync 410", "args": {}}, {"pid": 3100, "tid": 201863462912, "ts": 1754737498017275, "dur": 31, "ph": "X", "name": "ReadAsync 726", "args": {}}, {"pid": 3100, "tid": 201863462912, "ts": 1754737498017310, "dur": 31, "ph": "X", "name": "ReadAsync 378", "args": {}}, {"pid": 3100, "tid": 201863462912, "ts": 1754737498017343, "dur": 29, "ph": "X", "name": "ReadAsync 624", "args": {}}, {"pid": 3100, "tid": 201863462912, "ts": 1754737498017375, "dur": 27, "ph": "X", "name": "ReadAsync 98", "args": {}}, {"pid": 3100, "tid": 201863462912, "ts": 1754737498017404, "dur": 26, "ph": "X", "name": "ReadAsync 420", "args": {}}, {"pid": 3100, "tid": 201863462912, "ts": 1754737498017432, "dur": 23, "ph": "X", "name": "ReadAsync 213", "args": {}}, {"pid": 3100, "tid": 201863462912, "ts": 1754737498017457, "dur": 21, "ph": "X", "name": "ReadAsync 538", "args": {}}, {"pid": 3100, "tid": 201863462912, "ts": 1754737498017479, "dur": 22, "ph": "X", "name": "ReadAsync 88", "args": {}}, {"pid": 3100, "tid": 201863462912, "ts": 1754737498017503, "dur": 23, "ph": "X", "name": "ReadAsync 348", "args": {}}, {"pid": 3100, "tid": 201863462912, "ts": 1754737498017528, "dur": 23, "ph": "X", "name": "ReadAsync 367", "args": {}}, {"pid": 3100, "tid": 201863462912, "ts": 1754737498017551, "dur": 1, "ph": "X", "name": "ProcessMessages 399", "args": {}}, {"pid": 3100, "tid": 201863462912, "ts": 1754737498017553, "dur": 19, "ph": "X", "name": "ReadAsync 399", "args": {}}, {"pid": 3100, "tid": 201863462912, "ts": 1754737498017573, "dur": 21, "ph": "X", "name": "ReadAsync 346", "args": {}}, {"pid": 3100, "tid": 201863462912, "ts": 1754737498017596, "dur": 22, "ph": "X", "name": "ReadAsync 294", "args": {}}, {"pid": 3100, "tid": 201863462912, "ts": 1754737498017619, "dur": 21, "ph": "X", "name": "ReadAsync 313", "args": {}}, {"pid": 3100, "tid": 201863462912, "ts": 1754737498017641, "dur": 23, "ph": "X", "name": "ReadAsync 72", "args": {}}, {"pid": 3100, "tid": 201863462912, "ts": 1754737498017666, "dur": 21, "ph": "X", "name": "ReadAsync 332", "args": {}}, {"pid": 3100, "tid": 201863462912, "ts": 1754737498017689, "dur": 21, "ph": "X", "name": "ReadAsync 306", "args": {}}, {"pid": 3100, "tid": 201863462912, "ts": 1754737498017711, "dur": 28, "ph": "X", "name": "ReadAsync 247", "args": {}}, {"pid": 3100, "tid": 201863462912, "ts": 1754737498017741, "dur": 25, "ph": "X", "name": "ReadAsync 71", "args": {}}, {"pid": 3100, "tid": 201863462912, "ts": 1754737498017767, "dur": 22, "ph": "X", "name": "ReadAsync 415", "args": {}}, {"pid": 3100, "tid": 201863462912, "ts": 1754737498017790, "dur": 22, "ph": "X", "name": "ReadAsync 228", "args": {}}, {"pid": 3100, "tid": 201863462912, "ts": 1754737498017814, "dur": 23, "ph": "X", "name": "ReadAsync 247", "args": {}}, {"pid": 3100, "tid": 201863462912, "ts": 1754737498017839, "dur": 22, "ph": "X", "name": "ReadAsync 348", "args": {}}, {"pid": 3100, "tid": 201863462912, "ts": 1754737498017863, "dur": 21, "ph": "X", "name": "ReadAsync 297", "args": {}}, {"pid": 3100, "tid": 201863462912, "ts": 1754737498017886, "dur": 21, "ph": "X", "name": "ReadAsync 329", "args": {}}, {"pid": 3100, "tid": 201863462912, "ts": 1754737498017908, "dur": 21, "ph": "X", "name": "ReadAsync 335", "args": {}}, {"pid": 3100, "tid": 201863462912, "ts": 1754737498017931, "dur": 23, "ph": "X", "name": "ReadAsync 328", "args": {}}, {"pid": 3100, "tid": 201863462912, "ts": 1754737498017955, "dur": 37, "ph": "X", "name": "ReadAsync 351", "args": {}}, {"pid": 3100, "tid": 201863462912, "ts": 1754737498017994, "dur": 25, "ph": "X", "name": "ReadAsync 364", "args": {}}, {"pid": 3100, "tid": 201863462912, "ts": 1754737498018020, "dur": 20, "ph": "X", "name": "ReadAsync 352", "args": {}}, {"pid": 3100, "tid": 201863462912, "ts": 1754737498018042, "dur": 24, "ph": "X", "name": "ReadAsync 96", "args": {}}, {"pid": 3100, "tid": 201863462912, "ts": 1754737498018068, "dur": 18, "ph": "X", "name": "ReadAsync 368", "args": {}}, {"pid": 3100, "tid": 201863462912, "ts": 1754737498018087, "dur": 18, "ph": "X", "name": "ReadAsync 62", "args": {}}, {"pid": 3100, "tid": 201863462912, "ts": 1754737498018106, "dur": 21, "ph": "X", "name": "ReadAsync 94", "args": {}}, {"pid": 3100, "tid": 201863462912, "ts": 1754737498018128, "dur": 43, "ph": "X", "name": "ReadAsync 146", "args": {}}, {"pid": 3100, "tid": 201863462912, "ts": 1754737498018173, "dur": 24, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3100, "tid": 201863462912, "ts": 1754737498018199, "dur": 24, "ph": "X", "name": "ReadAsync 355", "args": {}}, {"pid": 3100, "tid": 201863462912, "ts": 1754737498018226, "dur": 1, "ph": "X", "name": "ProcessMessages 57", "args": {}}, {"pid": 3100, "tid": 201863462912, "ts": 1754737498018227, "dur": 29, "ph": "X", "name": "ReadAsync 57", "args": {}}, {"pid": 3100, "tid": 201863462912, "ts": 1754737498018258, "dur": 22, "ph": "X", "name": "ReadAsync 276", "args": {}}, {"pid": 3100, "tid": 201863462912, "ts": 1754737498018282, "dur": 104, "ph": "X", "name": "ReadAsync 289", "args": {}}, {"pid": 3100, "tid": 201863462912, "ts": 1754737498018390, "dur": 26, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3100, "tid": 201863462912, "ts": 1754737498018418, "dur": 26, "ph": "X", "name": "ReadAsync 436", "args": {}}, {"pid": 3100, "tid": 201863462912, "ts": 1754737498018446, "dur": 25, "ph": "X", "name": "ReadAsync 454", "args": {}}, {"pid": 3100, "tid": 201863462912, "ts": 1754737498018473, "dur": 23, "ph": "X", "name": "ReadAsync 335", "args": {}}, {"pid": 3100, "tid": 201863462912, "ts": 1754737498018498, "dur": 22, "ph": "X", "name": "ReadAsync 387", "args": {}}, {"pid": 3100, "tid": 201863462912, "ts": 1754737498018522, "dur": 21, "ph": "X", "name": "ReadAsync 247", "args": {}}, {"pid": 3100, "tid": 201863462912, "ts": 1754737498018545, "dur": 79, "ph": "X", "name": "ReadAsync 296", "args": {}}, {"pid": 3100, "tid": 201863462912, "ts": 1754737498018625, "dur": 23, "ph": "X", "name": "ReadAsync 343", "args": {}}, {"pid": 3100, "tid": 201863462912, "ts": 1754737498018650, "dur": 29, "ph": "X", "name": "ReadAsync 257", "args": {}}, {"pid": 3100, "tid": 201863462912, "ts": 1754737498018681, "dur": 20, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3100, "tid": 201863462912, "ts": 1754737498018702, "dur": 20, "ph": "X", "name": "ReadAsync 224", "args": {}}, {"pid": 3100, "tid": 201863462912, "ts": 1754737498018724, "dur": 21, "ph": "X", "name": "ReadAsync 203", "args": {}}, {"pid": 3100, "tid": 201863462912, "ts": 1754737498018747, "dur": 21, "ph": "X", "name": "ReadAsync 315", "args": {}}, {"pid": 3100, "tid": 201863462912, "ts": 1754737498018769, "dur": 21, "ph": "X", "name": "ReadAsync 311", "args": {}}, {"pid": 3100, "tid": 201863462912, "ts": 1754737498018792, "dur": 24, "ph": "X", "name": "ReadAsync 338", "args": {}}, {"pid": 3100, "tid": 201863462912, "ts": 1754737498018817, "dur": 21, "ph": "X", "name": "ReadAsync 299", "args": {}}, {"pid": 3100, "tid": 201863462912, "ts": 1754737498018840, "dur": 23, "ph": "X", "name": "ReadAsync 257", "args": {}}, {"pid": 3100, "tid": 201863462912, "ts": 1754737498018865, "dur": 20, "ph": "X", "name": "ReadAsync 262", "args": {}}, {"pid": 3100, "tid": 201863462912, "ts": 1754737498018904, "dur": 130, "ph": "X", "name": "ReadAsync 147", "args": {}}, {"pid": 3100, "tid": 201863462912, "ts": 1754737498019036, "dur": 32, "ph": "X", "name": "ReadAsync 148", "args": {}}, {"pid": 3100, "tid": 201863462912, "ts": 1754737498019071, "dur": 22, "ph": "X", "name": "ReadAsync 1285", "args": {}}, {"pid": 3100, "tid": 201863462912, "ts": 1754737498019095, "dur": 21, "ph": "X", "name": "ReadAsync 117", "args": {}}, {"pid": 3100, "tid": 201863462912, "ts": 1754737498019117, "dur": 22, "ph": "X", "name": "ReadAsync 208", "args": {}}, {"pid": 3100, "tid": 201863462912, "ts": 1754737498019141, "dur": 29, "ph": "X", "name": "ReadAsync 181", "args": {}}, {"pid": 3100, "tid": 201863462912, "ts": 1754737498019174, "dur": 1, "ph": "X", "name": "ProcessMessages 248", "args": {}}, {"pid": 3100, "tid": 201863462912, "ts": 1754737498019175, "dur": 26, "ph": "X", "name": "ReadAsync 248", "args": {}}, {"pid": 3100, "tid": 201863462912, "ts": 1754737498019203, "dur": 27, "ph": "X", "name": "ReadAsync 232", "args": {}}, {"pid": 3100, "tid": 201863462912, "ts": 1754737498019232, "dur": 83, "ph": "X", "name": "ReadAsync 62", "args": {}}, {"pid": 3100, "tid": 201863462912, "ts": 1754737498019316, "dur": 23, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3100, "tid": 201863462912, "ts": 1754737498019342, "dur": 1, "ph": "X", "name": "ProcessMessages 185", "args": {}}, {"pid": 3100, "tid": 201863462912, "ts": 1754737498019344, "dur": 33, "ph": "X", "name": "ReadAsync 185", "args": {}}, {"pid": 3100, "tid": 201863462912, "ts": 1754737498019379, "dur": 29, "ph": "X", "name": "ReadAsync 300", "args": {}}, {"pid": 3100, "tid": 201863462912, "ts": 1754737498019410, "dur": 24, "ph": "X", "name": "ReadAsync 299", "args": {}}, {"pid": 3100, "tid": 201863462912, "ts": 1754737498019437, "dur": 1, "ph": "X", "name": "ProcessMessages 144", "args": {}}, {"pid": 3100, "tid": 201863462912, "ts": 1754737498019438, "dur": 63, "ph": "X", "name": "ReadAsync 144", "args": {}}, {"pid": 3100, "tid": 201863462912, "ts": 1754737498019503, "dur": 22, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3100, "tid": 201863462912, "ts": 1754737498019526, "dur": 20, "ph": "X", "name": "ReadAsync 203", "args": {}}, {"pid": 3100, "tid": 201863462912, "ts": 1754737498019548, "dur": 19, "ph": "X", "name": "ReadAsync 249", "args": {}}, {"pid": 3100, "tid": 201863462912, "ts": 1754737498019569, "dur": 21, "ph": "X", "name": "ReadAsync 294", "args": {}}, {"pid": 3100, "tid": 201863462912, "ts": 1754737498019591, "dur": 71, "ph": "X", "name": "ReadAsync 240", "args": {}}, {"pid": 3100, "tid": 201863462912, "ts": 1754737498019665, "dur": 25, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3100, "tid": 201863462912, "ts": 1754737498019692, "dur": 23, "ph": "X", "name": "ReadAsync 221", "args": {}}, {"pid": 3100, "tid": 201863462912, "ts": 1754737498019717, "dur": 21, "ph": "X", "name": "ReadAsync 329", "args": {}}, {"pid": 3100, "tid": 201863462912, "ts": 1754737498019740, "dur": 14, "ph": "X", "name": "ReadAsync 311", "args": {}}, {"pid": 3100, "tid": 201863462912, "ts": 1754737498019755, "dur": 59, "ph": "X", "name": "ReadAsync 139", "args": {}}, {"pid": 3100, "tid": 201863462912, "ts": 1754737498019816, "dur": 43, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3100, "tid": 201863462912, "ts": 1754737498019860, "dur": 19, "ph": "X", "name": "ReadAsync 155", "args": {}}, {"pid": 3100, "tid": 201863462912, "ts": 1754737498019881, "dur": 19, "ph": "X", "name": "ReadAsync 237", "args": {}}, {"pid": 3100, "tid": 201863462912, "ts": 1754737498019902, "dur": 21, "ph": "X", "name": "ReadAsync 182", "args": {}}, {"pid": 3100, "tid": 201863462912, "ts": 1754737498019925, "dur": 19, "ph": "X", "name": "ReadAsync 328", "args": {}}, {"pid": 3100, "tid": 201863462912, "ts": 1754737498019946, "dur": 56, "ph": "X", "name": "ReadAsync 134", "args": {}}, {"pid": 3100, "tid": 201863462912, "ts": 1754737498020003, "dur": 21, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3100, "tid": 201863462912, "ts": 1754737498020025, "dur": 22, "ph": "X", "name": "ReadAsync 227", "args": {}}, {"pid": 3100, "tid": 201863462912, "ts": 1754737498020048, "dur": 21, "ph": "X", "name": "ReadAsync 330", "args": {}}, {"pid": 3100, "tid": 201863462912, "ts": 1754737498020072, "dur": 19, "ph": "X", "name": "ReadAsync 456", "args": {}}, {"pid": 3100, "tid": 201863462912, "ts": 1754737498020092, "dur": 59, "ph": "X", "name": "ReadAsync 143", "args": {}}, {"pid": 3100, "tid": 201863462912, "ts": 1754737498020152, "dur": 21, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3100, "tid": 201863462912, "ts": 1754737498020175, "dur": 22, "ph": "X", "name": "ReadAsync 290", "args": {}}, {"pid": 3100, "tid": 201863462912, "ts": 1754737498020198, "dur": 21, "ph": "X", "name": "ReadAsync 358", "args": {}}, {"pid": 3100, "tid": 201863462912, "ts": 1754737498020221, "dur": 21, "ph": "X", "name": "ReadAsync 439", "args": {}}, {"pid": 3100, "tid": 201863462912, "ts": 1754737498020243, "dur": 56, "ph": "X", "name": "ReadAsync 196", "args": {}}, {"pid": 3100, "tid": 201863462912, "ts": 1754737498020301, "dur": 24, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3100, "tid": 201863462912, "ts": 1754737498020327, "dur": 21, "ph": "X", "name": "ReadAsync 326", "args": {}}, {"pid": 3100, "tid": 201863462912, "ts": 1754737498020349, "dur": 21, "ph": "X", "name": "ReadAsync 417", "args": {}}, {"pid": 3100, "tid": 201863462912, "ts": 1754737498020371, "dur": 20, "ph": "X", "name": "ReadAsync 351", "args": {}}, {"pid": 3100, "tid": 201863462912, "ts": 1754737498020393, "dur": 56, "ph": "X", "name": "ReadAsync 143", "args": {}}, {"pid": 3100, "tid": 201863462912, "ts": 1754737498020450, "dur": 25, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3100, "tid": 201863462912, "ts": 1754737498020476, "dur": 17, "ph": "X", "name": "ReadAsync 287", "args": {}}, {"pid": 3100, "tid": 201863462912, "ts": 1754737498020495, "dur": 21, "ph": "X", "name": "ReadAsync 221", "args": {}}, {"pid": 3100, "tid": 201863462912, "ts": 1754737498020518, "dur": 20, "ph": "X", "name": "ReadAsync 326", "args": {}}, {"pid": 3100, "tid": 201863462912, "ts": 1754737498020540, "dur": 61, "ph": "X", "name": "ReadAsync 260", "args": {}}, {"pid": 3100, "tid": 201863462912, "ts": 1754737498020602, "dur": 21, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3100, "tid": 201863462912, "ts": 1754737498020625, "dur": 22, "ph": "X", "name": "ReadAsync 233", "args": {}}, {"pid": 3100, "tid": 201863462912, "ts": 1754737498020648, "dur": 20, "ph": "X", "name": "ReadAsync 365", "args": {}}, {"pid": 3100, "tid": 201863462912, "ts": 1754737498020670, "dur": 21, "ph": "X", "name": "ReadAsync 281", "args": {}}, {"pid": 3100, "tid": 201863462912, "ts": 1754737498020692, "dur": 55, "ph": "X", "name": "ReadAsync 193", "args": {}}, {"pid": 3100, "tid": 201863462912, "ts": 1754737498020749, "dur": 20, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3100, "tid": 201863462912, "ts": 1754737498020770, "dur": 40, "ph": "X", "name": "ReadAsync 251", "args": {}}, {"pid": 3100, "tid": 201863462912, "ts": 1754737498020812, "dur": 19, "ph": "X", "name": "ReadAsync 154", "args": {}}, {"pid": 3100, "tid": 201863462912, "ts": 1754737498020833, "dur": 20, "ph": "X", "name": "ReadAsync 336", "args": {}}, {"pid": 3100, "tid": 201863462912, "ts": 1754737498020854, "dur": 19, "ph": "X", "name": "ReadAsync 269", "args": {}}, {"pid": 3100, "tid": 201863462912, "ts": 1754737498020874, "dur": 46, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 3100, "tid": 201863462912, "ts": 1754737498020922, "dur": 25, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3100, "tid": 201863462912, "ts": 1754737498020948, "dur": 22, "ph": "X", "name": "ReadAsync 249", "args": {}}, {"pid": 3100, "tid": 201863462912, "ts": 1754737498020972, "dur": 21, "ph": "X", "name": "ReadAsync 384", "args": {}}, {"pid": 3100, "tid": 201863462912, "ts": 1754737498020994, "dur": 16, "ph": "X", "name": "ReadAsync 292", "args": {}}, {"pid": 3100, "tid": 201863462912, "ts": 1754737498021012, "dur": 51, "ph": "X", "name": "ReadAsync 57", "args": {}}, {"pid": 3100, "tid": 201863462912, "ts": 1754737498021064, "dur": 21, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3100, "tid": 201863462912, "ts": 1754737498021087, "dur": 20, "ph": "X", "name": "ReadAsync 212", "args": {}}, {"pid": 3100, "tid": 201863462912, "ts": 1754737498021108, "dur": 52, "ph": "X", "name": "ReadAsync 233", "args": {}}, {"pid": 3100, "tid": 201863462912, "ts": 1754737498021161, "dur": 26, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3100, "tid": 201863462912, "ts": 1754737498021188, "dur": 21, "ph": "X", "name": "ReadAsync 343", "args": {}}, {"pid": 3100, "tid": 201863462912, "ts": 1754737498021211, "dur": 20, "ph": "X", "name": "ReadAsync 559", "args": {}}, {"pid": 3100, "tid": 201863462912, "ts": 1754737498021233, "dur": 20, "ph": "X", "name": "ReadAsync 290", "args": {}}, {"pid": 3100, "tid": 201863462912, "ts": 1754737498021254, "dur": 20, "ph": "X", "name": "ReadAsync 272", "args": {}}, {"pid": 3100, "tid": 201863462912, "ts": 1754737498021275, "dur": 21, "ph": "X", "name": "ReadAsync 294", "args": {}}, {"pid": 3100, "tid": 201863462912, "ts": 1754737498021298, "dur": 19, "ph": "X", "name": "ReadAsync 303", "args": {}}, {"pid": 3100, "tid": 201863462912, "ts": 1754737498021318, "dur": 20, "ph": "X", "name": "ReadAsync 73", "args": {}}, {"pid": 3100, "tid": 201863462912, "ts": 1754737498021340, "dur": 64, "ph": "X", "name": "ReadAsync 231", "args": {}}, {"pid": 3100, "tid": 201863462912, "ts": 1754737498021405, "dur": 22, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3100, "tid": 201863462912, "ts": 1754737498021428, "dur": 22, "ph": "X", "name": "ReadAsync 277", "args": {}}, {"pid": 3100, "tid": 201863462912, "ts": 1754737498021452, "dur": 33, "ph": "X", "name": "ReadAsync 314", "args": {}}, {"pid": 3100, "tid": 201863462912, "ts": 1754737498021487, "dur": 21, "ph": "X", "name": "ReadAsync 412", "args": {}}, {"pid": 3100, "tid": 201863462912, "ts": 1754737498021509, "dur": 32, "ph": "X", "name": "ReadAsync 473", "args": {}}, {"pid": 3100, "tid": 201863462912, "ts": 1754737498021544, "dur": 31, "ph": "X", "name": "ReadAsync 318", "args": {}}, {"pid": 3100, "tid": 201863462912, "ts": 1754737498021577, "dur": 19, "ph": "X", "name": "ReadAsync 450", "args": {}}, {"pid": 3100, "tid": 201863462912, "ts": 1754737498021598, "dur": 20, "ph": "X", "name": "ReadAsync 283", "args": {}}, {"pid": 3100, "tid": 201863462912, "ts": 1754737498021619, "dur": 26, "ph": "X", "name": "ReadAsync 183", "args": {}}, {"pid": 3100, "tid": 201863462912, "ts": 1754737498021647, "dur": 42, "ph": "X", "name": "ReadAsync 57", "args": {}}, {"pid": 3100, "tid": 201863462912, "ts": 1754737498021690, "dur": 25, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3100, "tid": 201863462912, "ts": 1754737498021717, "dur": 20, "ph": "X", "name": "ReadAsync 314", "args": {}}, {"pid": 3100, "tid": 201863462912, "ts": 1754737498021738, "dur": 21, "ph": "X", "name": "ReadAsync 304", "args": {}}, {"pid": 3100, "tid": 201863462912, "ts": 1754737498021761, "dur": 22, "ph": "X", "name": "ReadAsync 260", "args": {}}, {"pid": 3100, "tid": 201863462912, "ts": 1754737498021784, "dur": 20, "ph": "X", "name": "ReadAsync 358", "args": {}}, {"pid": 3100, "tid": 201863462912, "ts": 1754737498021806, "dur": 57, "ph": "X", "name": "ReadAsync 240", "args": {}}, {"pid": 3100, "tid": 201863462912, "ts": 1754737498021864, "dur": 26, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3100, "tid": 201863462912, "ts": 1754737498021892, "dur": 21, "ph": "X", "name": "ReadAsync 256", "args": {}}, {"pid": 3100, "tid": 201863462912, "ts": 1754737498021914, "dur": 1, "ph": "X", "name": "ProcessMessages 434", "args": {}}, {"pid": 3100, "tid": 201863462912, "ts": 1754737498021915, "dur": 20, "ph": "X", "name": "ReadAsync 434", "args": {}}, {"pid": 3100, "tid": 201863462912, "ts": 1754737498021937, "dur": 20, "ph": "X", "name": "ReadAsync 324", "args": {}}, {"pid": 3100, "tid": 201863462912, "ts": 1754737498021959, "dur": 21, "ph": "X", "name": "ReadAsync 358", "args": {}}, {"pid": 3100, "tid": 201863462912, "ts": 1754737498021981, "dur": 20, "ph": "X", "name": "ReadAsync 189", "args": {}}, {"pid": 3100, "tid": 201863462912, "ts": 1754737498022002, "dur": 21, "ph": "X", "name": "ReadAsync 194", "args": {}}, {"pid": 3100, "tid": 201863462912, "ts": 1754737498022024, "dur": 21, "ph": "X", "name": "ReadAsync 269", "args": {}}, {"pid": 3100, "tid": 201863462912, "ts": 1754737498022046, "dur": 17, "ph": "X", "name": "ReadAsync 381", "args": {}}, {"pid": 3100, "tid": 201863462912, "ts": 1754737498022065, "dur": 20, "ph": "X", "name": "ReadAsync 175", "args": {}}, {"pid": 3100, "tid": 201863462912, "ts": 1754737498022086, "dur": 16, "ph": "X", "name": "ReadAsync 180", "args": {}}, {"pid": 3100, "tid": 201863462912, "ts": 1754737498022104, "dur": 52, "ph": "X", "name": "ReadAsync 57", "args": {}}, {"pid": 3100, "tid": 201863462912, "ts": 1754737498022158, "dur": 22, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3100, "tid": 201863462912, "ts": 1754737498022181, "dur": 20, "ph": "X", "name": "ReadAsync 242", "args": {}}, {"pid": 3100, "tid": 201863462912, "ts": 1754737498022202, "dur": 19, "ph": "X", "name": "ReadAsync 325", "args": {}}, {"pid": 3100, "tid": 201863462912, "ts": 1754737498022223, "dur": 21, "ph": "X", "name": "ReadAsync 315", "args": {}}, {"pid": 3100, "tid": 201863462912, "ts": 1754737498022246, "dur": 52, "ph": "X", "name": "ReadAsync 139", "args": {}}, {"pid": 3100, "tid": 201863462912, "ts": 1754737498022299, "dur": 22, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3100, "tid": 201863462912, "ts": 1754737498022322, "dur": 22, "ph": "X", "name": "ReadAsync 249", "args": {}}, {"pid": 3100, "tid": 201863462912, "ts": 1754737498022346, "dur": 21, "ph": "X", "name": "ReadAsync 321", "args": {}}, {"pid": 3100, "tid": 201863462912, "ts": 1754737498022369, "dur": 19, "ph": "X", "name": "ReadAsync 332", "args": {}}, {"pid": 3100, "tid": 201863462912, "ts": 1754737498022390, "dur": 47, "ph": "X", "name": "ReadAsync 134", "args": {}}, {"pid": 3100, "tid": 201863462912, "ts": 1754737498022439, "dur": 21, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3100, "tid": 201863462912, "ts": 1754737498022461, "dur": 22, "ph": "X", "name": "ReadAsync 267", "args": {}}, {"pid": 3100, "tid": 201863462912, "ts": 1754737498022485, "dur": 21, "ph": "X", "name": "ReadAsync 267", "args": {}}, {"pid": 3100, "tid": 201863462912, "ts": 1754737498022508, "dur": 28, "ph": "X", "name": "ReadAsync 451", "args": {}}, {"pid": 3100, "tid": 201863462912, "ts": 1754737498022538, "dur": 28, "ph": "X", "name": "ReadAsync 276", "args": {}}, {"pid": 3100, "tid": 201863462912, "ts": 1754737498022567, "dur": 21, "ph": "X", "name": "ReadAsync 514", "args": {}}, {"pid": 3100, "tid": 201863462912, "ts": 1754737498022590, "dur": 22, "ph": "X", "name": "ReadAsync 398", "args": {}}, {"pid": 3100, "tid": 201863462912, "ts": 1754737498022613, "dur": 21, "ph": "X", "name": "ReadAsync 414", "args": {}}, {"pid": 3100, "tid": 201863462912, "ts": 1754737498022636, "dur": 28, "ph": "X", "name": "ReadAsync 184", "args": {}}, {"pid": 3100, "tid": 201863462912, "ts": 1754737498022667, "dur": 1, "ph": "X", "name": "ProcessMessages 240", "args": {}}, {"pid": 3100, "tid": 201863462912, "ts": 1754737498022668, "dur": 51, "ph": "X", "name": "ReadAsync 240", "args": {}}, {"pid": 3100, "tid": 201863462912, "ts": 1754737498022721, "dur": 28, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3100, "tid": 201863462912, "ts": 1754737498022750, "dur": 21, "ph": "X", "name": "ReadAsync 354", "args": {}}, {"pid": 3100, "tid": 201863462912, "ts": 1754737498022772, "dur": 21, "ph": "X", "name": "ReadAsync 380", "args": {}}, {"pid": 3100, "tid": 201863462912, "ts": 1754737498022795, "dur": 357, "ph": "X", "name": "ReadAsync 301", "args": {}}, {"pid": 3100, "tid": 201863462912, "ts": 1754737498023156, "dur": 55, "ph": "X", "name": "ReadAsync 53", "args": {}}, {"pid": 3100, "tid": 201863462912, "ts": 1754737498023213, "dur": 2, "ph": "X", "name": "ProcessMessages 2944", "args": {}}, {"pid": 3100, "tid": 201863462912, "ts": 1754737498023216, "dur": 37, "ph": "X", "name": "ReadAsync 2944", "args": {}}, {"pid": 3100, "tid": 201863462912, "ts": 1754737498023254, "dur": 28, "ph": "X", "name": "ReadAsync 337", "args": {}}, {"pid": 3100, "tid": 201863462912, "ts": 1754737498023286, "dur": 34, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 3100, "tid": 201863462912, "ts": 1754737498023322, "dur": 25, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3100, "tid": 201863462912, "ts": 1754737498023349, "dur": 22, "ph": "X", "name": "ReadAsync 265", "args": {}}, {"pid": 3100, "tid": 201863462912, "ts": 1754737498023372, "dur": 21, "ph": "X", "name": "ReadAsync 444", "args": {}}, {"pid": 3100, "tid": 201863462912, "ts": 1754737498023395, "dur": 18, "ph": "X", "name": "ReadAsync 304", "args": {}}, {"pid": 3100, "tid": 201863462912, "ts": 1754737498023417, "dur": 44, "ph": "X", "name": "ReadAsync 57", "args": {}}, {"pid": 3100, "tid": 201863462912, "ts": 1754737498023462, "dur": 28, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3100, "tid": 201863462912, "ts": 1754737498023492, "dur": 20, "ph": "X", "name": "ReadAsync 276", "args": {}}, {"pid": 3100, "tid": 201863462912, "ts": 1754737498023513, "dur": 20, "ph": "X", "name": "ReadAsync 448", "args": {}}, {"pid": 3100, "tid": 201863462912, "ts": 1754737498023536, "dur": 17, "ph": "X", "name": "ReadAsync 322", "args": {}}, {"pid": 3100, "tid": 201863462912, "ts": 1754737498023555, "dur": 20, "ph": "X", "name": "ReadAsync 163", "args": {}}, {"pid": 3100, "tid": 201863462912, "ts": 1754737498023576, "dur": 21, "ph": "X", "name": "ReadAsync 281", "args": {}}, {"pid": 3100, "tid": 201863462912, "ts": 1754737498023598, "dur": 21, "ph": "X", "name": "ReadAsync 277", "args": {}}, {"pid": 3100, "tid": 201863462912, "ts": 1754737498023621, "dur": 21, "ph": "X", "name": "ReadAsync 370", "args": {}}, {"pid": 3100, "tid": 201863462912, "ts": 1754737498023643, "dur": 19, "ph": "X", "name": "ReadAsync 320", "args": {}}, {"pid": 3100, "tid": 201863462912, "ts": 1754737498023664, "dur": 20, "ph": "X", "name": "ReadAsync 172", "args": {}}, {"pid": 3100, "tid": 201863462912, "ts": 1754737498023686, "dur": 28, "ph": "X", "name": "ReadAsync 217", "args": {}}, {"pid": 3100, "tid": 201863462912, "ts": 1754737498023716, "dur": 37, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 3100, "tid": 201863462912, "ts": 1754737498023755, "dur": 28, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3100, "tid": 201863462912, "ts": 1754737498023784, "dur": 22, "ph": "X", "name": "ReadAsync 346", "args": {}}, {"pid": 3100, "tid": 201863462912, "ts": 1754737498023807, "dur": 20, "ph": "X", "name": "ReadAsync 441", "args": {}}, {"pid": 3100, "tid": 201863462912, "ts": 1754737498023829, "dur": 24, "ph": "X", "name": "ReadAsync 447", "args": {}}, {"pid": 3100, "tid": 201863462912, "ts": 1754737498023854, "dur": 20, "ph": "X", "name": "ReadAsync 340", "args": {}}, {"pid": 3100, "tid": 201863462912, "ts": 1754737498023876, "dur": 21, "ph": "X", "name": "ReadAsync 383", "args": {}}, {"pid": 3100, "tid": 201863462912, "ts": 1754737498023899, "dur": 20, "ph": "X", "name": "ReadAsync 330", "args": {}}, {"pid": 3100, "tid": 201863462912, "ts": 1754737498023921, "dur": 14, "ph": "X", "name": "ReadAsync 365", "args": {}}, {"pid": 3100, "tid": 201863462912, "ts": 1754737498023936, "dur": 20, "ph": "X", "name": "ReadAsync 325", "args": {}}, {"pid": 3100, "tid": 201863462912, "ts": 1754737498023958, "dur": 24, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3100, "tid": 201863462912, "ts": 1754737498023984, "dur": 55, "ph": "X", "name": "ReadAsync 228", "args": {}}, {"pid": 3100, "tid": 201863462912, "ts": 1754737498024040, "dur": 22, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3100, "tid": 201863462912, "ts": 1754737498024063, "dur": 30, "ph": "X", "name": "ReadAsync 380", "args": {}}, {"pid": 3100, "tid": 201863462912, "ts": 1754737498024098, "dur": 235, "ph": "X", "name": "ReadAsync 141", "args": {}}, {"pid": 3100, "tid": 201863462912, "ts": 1754737498024336, "dur": 1, "ph": "X", "name": "ProcessMessages 745", "args": {}}, {"pid": 3100, "tid": 201863462912, "ts": 1754737498024339, "dur": 33, "ph": "X", "name": "ReadAsync 745", "args": {}}, {"pid": 3100, "tid": 201863462912, "ts": 1754737498024375, "dur": 24, "ph": "X", "name": "ReadAsync 475", "args": {}}, {"pid": 3100, "tid": 201863462912, "ts": 1754737498024402, "dur": 23, "ph": "X", "name": "ReadAsync 281", "args": {}}, {"pid": 3100, "tid": 201863462912, "ts": 1754737498024426, "dur": 21, "ph": "X", "name": "ReadAsync 407", "args": {}}, {"pid": 3100, "tid": 201863462912, "ts": 1754737498024449, "dur": 21, "ph": "X", "name": "ReadAsync 325", "args": {}}, {"pid": 3100, "tid": 201863462912, "ts": 1754737498024472, "dur": 26, "ph": "X", "name": "ReadAsync 89", "args": {}}, {"pid": 3100, "tid": 201863462912, "ts": 1754737498024501, "dur": 28, "ph": "X", "name": "ReadAsync 180", "args": {}}, {"pid": 3100, "tid": 201863462912, "ts": 1754737498024531, "dur": 45, "ph": "X", "name": "ReadAsync 57", "args": {}}, {"pid": 3100, "tid": 201863462912, "ts": 1754737498024579, "dur": 27, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3100, "tid": 201863462912, "ts": 1754737498024608, "dur": 31, "ph": "X", "name": "ReadAsync 474", "args": {}}, {"pid": 3100, "tid": 201863462912, "ts": 1754737498024641, "dur": 1, "ph": "X", "name": "ProcessMessages 354", "args": {}}, {"pid": 3100, "tid": 201863462912, "ts": 1754737498024643, "dur": 29, "ph": "X", "name": "ReadAsync 354", "args": {}}, {"pid": 3100, "tid": 201863462912, "ts": 1754737498024675, "dur": 61, "ph": "X", "name": "ReadAsync 383", "args": {}}, {"pid": 3100, "tid": 201863462912, "ts": 1754737498024737, "dur": 25, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3100, "tid": 201863462912, "ts": 1754737498024764, "dur": 22, "ph": "X", "name": "ReadAsync 396", "args": {}}, {"pid": 3100, "tid": 201863462912, "ts": 1754737498024787, "dur": 20, "ph": "X", "name": "ReadAsync 418", "args": {}}, {"pid": 3100, "tid": 201863462912, "ts": 1754737498024809, "dur": 20, "ph": "X", "name": "ReadAsync 219", "args": {}}, {"pid": 3100, "tid": 201863462912, "ts": 1754737498024831, "dur": 68, "ph": "X", "name": "ReadAsync 144", "args": {}}, {"pid": 3100, "tid": 201863462912, "ts": 1754737498024901, "dur": 31, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3100, "tid": 201863462912, "ts": 1754737498024934, "dur": 68, "ph": "X", "name": "ReadAsync 471", "args": {}}, {"pid": 3100, "tid": 201863462912, "ts": 1754737498025004, "dur": 90, "ph": "X", "name": "ReadAsync 86", "args": {}}, {"pid": 3100, "tid": 201863462912, "ts": 1754737498025098, "dur": 1, "ph": "X", "name": "ProcessMessages 8", "args": {}}, {"pid": 3100, "tid": 201863462912, "ts": 1754737498025099, "dur": 43, "ph": "X", "name": "ReadAsync 8", "args": {}}, {"pid": 3100, "tid": 201863462912, "ts": 1754737498025146, "dur": 1, "ph": "X", "name": "ProcessMessages 516", "args": {}}, {"pid": 3100, "tid": 201863462912, "ts": 1754737498025148, "dur": 40, "ph": "X", "name": "ReadAsync 516", "args": {}}, {"pid": 3100, "tid": 201863462912, "ts": 1754737498025192, "dur": 64, "ph": "X", "name": "ReadAsync 145", "args": {}}, {"pid": 3100, "tid": 201863462912, "ts": 1754737498025260, "dur": 38, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3100, "tid": 201863462912, "ts": 1754737498025301, "dur": 1, "ph": "X", "name": "ProcessMessages 489", "args": {}}, {"pid": 3100, "tid": 201863462912, "ts": 1754737498025302, "dur": 31, "ph": "X", "name": "ReadAsync 489", "args": {}}, {"pid": 3100, "tid": 201863462912, "ts": 1754737498025336, "dur": 21, "ph": "X", "name": "ReadAsync 437", "args": {}}, {"pid": 3100, "tid": 201863462912, "ts": 1754737498025360, "dur": 73, "ph": "X", "name": "ReadAsync 257", "args": {}}, {"pid": 3100, "tid": 201863462912, "ts": 1754737498025437, "dur": 30, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3100, "tid": 201863462912, "ts": 1754737498025469, "dur": 22, "ph": "X", "name": "ReadAsync 441", "args": {}}, {"pid": 3100, "tid": 201863462912, "ts": 1754737498025493, "dur": 21, "ph": "X", "name": "ReadAsync 189", "args": {}}, {"pid": 3100, "tid": 201863462912, "ts": 1754737498025515, "dur": 22, "ph": "X", "name": "ReadAsync 329", "args": {}}, {"pid": 3100, "tid": 201863462912, "ts": 1754737498025539, "dur": 60, "ph": "X", "name": "ReadAsync 143", "args": {}}, {"pid": 3100, "tid": 201863462912, "ts": 1754737498025602, "dur": 50, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3100, "tid": 201863462912, "ts": 1754737498025654, "dur": 22, "ph": "X", "name": "ReadAsync 421", "args": {}}, {"pid": 3100, "tid": 201863462912, "ts": 1754737498025678, "dur": 30, "ph": "X", "name": "ReadAsync 411", "args": {}}, {"pid": 3100, "tid": 201863462912, "ts": 1754737498025711, "dur": 1, "ph": "X", "name": "ProcessMessages 367", "args": {}}, {"pid": 3100, "tid": 201863462912, "ts": 1754737498025713, "dur": 31, "ph": "X", "name": "ReadAsync 367", "args": {}}, {"pid": 3100, "tid": 201863462912, "ts": 1754737498025745, "dur": 26, "ph": "X", "name": "ReadAsync 584", "args": {}}, {"pid": 3100, "tid": 201863462912, "ts": 1754737498025774, "dur": 24, "ph": "X", "name": "ReadAsync 265", "args": {}}, {"pid": 3100, "tid": 201863462912, "ts": 1754737498025800, "dur": 20, "ph": "X", "name": "ReadAsync 484", "args": {}}, {"pid": 3100, "tid": 201863462912, "ts": 1754737498025821, "dur": 1, "ph": "X", "name": "ProcessMessages 72", "args": {}}, {"pid": 3100, "tid": 201863462912, "ts": 1754737498025823, "dur": 20, "ph": "X", "name": "ReadAsync 72", "args": {}}, {"pid": 3100, "tid": 201863462912, "ts": 1754737498025845, "dur": 68, "ph": "X", "name": "ReadAsync 237", "args": {}}, {"pid": 3100, "tid": 201863462912, "ts": 1754737498025917, "dur": 40, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3100, "tid": 201863462912, "ts": 1754737498025959, "dur": 28, "ph": "X", "name": "ReadAsync 557", "args": {}}, {"pid": 3100, "tid": 201863462912, "ts": 1754737498025989, "dur": 23, "ph": "X", "name": "ReadAsync 459", "args": {}}, {"pid": 3100, "tid": 201863462912, "ts": 1754737498026014, "dur": 50, "ph": "X", "name": "ReadAsync 141", "args": {}}, {"pid": 3100, "tid": 201863462912, "ts": 1754737498026068, "dur": 33, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3100, "tid": 201863462912, "ts": 1754737498026103, "dur": 23, "ph": "X", "name": "ReadAsync 509", "args": {}}, {"pid": 3100, "tid": 201863462912, "ts": 1754737498026128, "dur": 21, "ph": "X", "name": "ReadAsync 374", "args": {}}, {"pid": 3100, "tid": 201863462912, "ts": 1754737498026150, "dur": 80, "ph": "X", "name": "ReadAsync 225", "args": {}}, {"pid": 3100, "tid": 201863462912, "ts": 1754737498026236, "dur": 29, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3100, "tid": 201863462912, "ts": 1754737498026267, "dur": 23, "ph": "X", "name": "ReadAsync 511", "args": {}}, {"pid": 3100, "tid": 201863462912, "ts": 1754737498026293, "dur": 30, "ph": "X", "name": "ReadAsync 298", "args": {}}, {"pid": 3100, "tid": 201863462912, "ts": 1754737498026325, "dur": 18, "ph": "X", "name": "ReadAsync 255", "args": {}}, {"pid": 3100, "tid": 201863462912, "ts": 1754737498026345, "dur": 67, "ph": "X", "name": "ReadAsync 57", "args": {}}, {"pid": 3100, "tid": 201863462912, "ts": 1754737498026414, "dur": 25, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3100, "tid": 201863462912, "ts": 1754737498026442, "dur": 24, "ph": "X", "name": "ReadAsync 269", "args": {}}, {"pid": 3100, "tid": 201863462912, "ts": 1754737498026468, "dur": 26, "ph": "X", "name": "ReadAsync 279", "args": {}}, {"pid": 3100, "tid": 201863462912, "ts": 1754737498026496, "dur": 32, "ph": "X", "name": "ReadAsync 335", "args": {}}, {"pid": 3100, "tid": 201863462912, "ts": 1754737498026532, "dur": 1, "ph": "X", "name": "ProcessMessages 225", "args": {}}, {"pid": 3100, "tid": 201863462912, "ts": 1754737498026533, "dur": 79, "ph": "X", "name": "ReadAsync 225", "args": {}}, {"pid": 3100, "tid": 201863462912, "ts": 1754737498026614, "dur": 29, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3100, "tid": 201863462912, "ts": 1754737498026644, "dur": 34, "ph": "X", "name": "ReadAsync 239", "args": {}}, {"pid": 3100, "tid": 201863462912, "ts": 1754737498026683, "dur": 31, "ph": "X", "name": "ReadAsync 468", "args": {}}, {"pid": 3100, "tid": 201863462912, "ts": 1754737498026717, "dur": 24, "ph": "X", "name": "ReadAsync 144", "args": {}}, {"pid": 3100, "tid": 201863462912, "ts": 1754737498026743, "dur": 25, "ph": "X", "name": "ReadAsync 439", "args": {}}, {"pid": 3100, "tid": 201863462912, "ts": 1754737498026771, "dur": 1, "ph": "X", "name": "ProcessMessages 244", "args": {}}, {"pid": 3100, "tid": 201863462912, "ts": 1754737498026772, "dur": 30, "ph": "X", "name": "ReadAsync 244", "args": {}}, {"pid": 3100, "tid": 201863462912, "ts": 1754737498026804, "dur": 31, "ph": "X", "name": "ReadAsync 495", "args": {}}, {"pid": 3100, "tid": 201863462912, "ts": 1754737498026838, "dur": 1, "ph": "X", "name": "ProcessMessages 253", "args": {}}, {"pid": 3100, "tid": 201863462912, "ts": 1754737498026839, "dur": 29, "ph": "X", "name": "ReadAsync 253", "args": {}}, {"pid": 3100, "tid": 201863462912, "ts": 1754737498026871, "dur": 81, "ph": "X", "name": "ReadAsync 237", "args": {}}, {"pid": 3100, "tid": 201863462912, "ts": 1754737498026953, "dur": 30, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3100, "tid": 201863462912, "ts": 1754737498026986, "dur": 22, "ph": "X", "name": "ReadAsync 360", "args": {}}, {"pid": 3100, "tid": 201863462912, "ts": 1754737498027010, "dur": 24, "ph": "X", "name": "ReadAsync 305", "args": {}}, {"pid": 3100, "tid": 201863462912, "ts": 1754737498027035, "dur": 18, "ph": "X", "name": "ReadAsync 415", "args": {}}, {"pid": 3100, "tid": 201863462912, "ts": 1754737498027055, "dur": 21, "ph": "X", "name": "ReadAsync 320", "args": {}}, {"pid": 3100, "tid": 201863462912, "ts": 1754737498027077, "dur": 14, "ph": "X", "name": "ReadAsync 258", "args": {}}, {"pid": 3100, "tid": 201863462912, "ts": 1754737498027093, "dur": 22, "ph": "X", "name": "ReadAsync 283", "args": {}}, {"pid": 3100, "tid": 201863462912, "ts": 1754737498027117, "dur": 24, "ph": "X", "name": "ReadAsync 285", "args": {}}, {"pid": 3100, "tid": 201863462912, "ts": 1754737498027144, "dur": 27, "ph": "X", "name": "ReadAsync 234", "args": {}}, {"pid": 3100, "tid": 201863462912, "ts": 1754737498027173, "dur": 21, "ph": "X", "name": "ReadAsync 248", "args": {}}, {"pid": 3100, "tid": 201863462912, "ts": 1754737498027196, "dur": 54, "ph": "X", "name": "ReadAsync 67", "args": {}}, {"pid": 3100, "tid": 201863462912, "ts": 1754737498027252, "dur": 22, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3100, "tid": 201863462912, "ts": 1754737498027275, "dur": 21, "ph": "X", "name": "ReadAsync 309", "args": {}}, {"pid": 3100, "tid": 201863462912, "ts": 1754737498027297, "dur": 21, "ph": "X", "name": "ReadAsync 227", "args": {}}, {"pid": 3100, "tid": 201863462912, "ts": 1754737498027320, "dur": 22, "ph": "X", "name": "ReadAsync 296", "args": {}}, {"pid": 3100, "tid": 201863462912, "ts": 1754737498027344, "dur": 21, "ph": "X", "name": "ReadAsync 370", "args": {}}, {"pid": 3100, "tid": 201863462912, "ts": 1754737498027366, "dur": 21, "ph": "X", "name": "ReadAsync 243", "args": {}}, {"pid": 3100, "tid": 201863462912, "ts": 1754737498027389, "dur": 21, "ph": "X", "name": "ReadAsync 246", "args": {}}, {"pid": 3100, "tid": 201863462912, "ts": 1754737498027412, "dur": 20, "ph": "X", "name": "ReadAsync 295", "args": {}}, {"pid": 3100, "tid": 201863462912, "ts": 1754737498027434, "dur": 21, "ph": "X", "name": "ReadAsync 235", "args": {}}, {"pid": 3100, "tid": 201863462912, "ts": 1754737498027456, "dur": 19, "ph": "X", "name": "ReadAsync 183", "args": {}}, {"pid": 3100, "tid": 201863462912, "ts": 1754737498027477, "dur": 46, "ph": "X", "name": "ReadAsync 57", "args": {}}, {"pid": 3100, "tid": 201863462912, "ts": 1754737498027524, "dur": 20, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3100, "tid": 201863462912, "ts": 1754737498027546, "dur": 23, "ph": "X", "name": "ReadAsync 225", "args": {}}, {"pid": 3100, "tid": 201863462912, "ts": 1754737498027570, "dur": 21, "ph": "X", "name": "ReadAsync 381", "args": {}}, {"pid": 3100, "tid": 201863462912, "ts": 1754737498027593, "dur": 19, "ph": "X", "name": "ReadAsync 364", "args": {}}, {"pid": 3100, "tid": 201863462912, "ts": 1754737498027614, "dur": 62, "ph": "X", "name": "ReadAsync 136", "args": {}}, {"pid": 3100, "tid": 201863462912, "ts": 1754737498027677, "dur": 26, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3100, "tid": 201863462912, "ts": 1754737498027705, "dur": 22, "ph": "X", "name": "ReadAsync 316", "args": {}}, {"pid": 3100, "tid": 201863462912, "ts": 1754737498027728, "dur": 20, "ph": "X", "name": "ReadAsync 535", "args": {}}, {"pid": 3100, "tid": 201863462912, "ts": 1754737498027750, "dur": 19, "ph": "X", "name": "ReadAsync 318", "args": {}}, {"pid": 3100, "tid": 201863462912, "ts": 1754737498027770, "dur": 50, "ph": "X", "name": "ReadAsync 62", "args": {}}, {"pid": 3100, "tid": 201863462912, "ts": 1754737498027822, "dur": 25, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3100, "tid": 201863462912, "ts": 1754737498027848, "dur": 26, "ph": "X", "name": "ReadAsync 337", "args": {}}, {"pid": 3100, "tid": 201863462912, "ts": 1754737498027876, "dur": 20, "ph": "X", "name": "ReadAsync 479", "args": {}}, {"pid": 3100, "tid": 201863462912, "ts": 1754737498027898, "dur": 17, "ph": "X", "name": "ReadAsync 133", "args": {}}, {"pid": 3100, "tid": 201863462912, "ts": 1754737498027916, "dur": 80, "ph": "X", "name": "ReadAsync 237", "args": {}}, {"pid": 3100, "tid": 201863462912, "ts": 1754737498027998, "dur": 23, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3100, "tid": 201863462912, "ts": 1754737498028023, "dur": 23, "ph": "X", "name": "ReadAsync 369", "args": {}}, {"pid": 3100, "tid": 201863462912, "ts": 1754737498028047, "dur": 21, "ph": "X", "name": "ReadAsync 425", "args": {}}, {"pid": 3100, "tid": 201863462912, "ts": 1754737498028070, "dur": 21, "ph": "X", "name": "ReadAsync 360", "args": {}}, {"pid": 3100, "tid": 201863462912, "ts": 1754737498028093, "dur": 23, "ph": "X", "name": "ReadAsync 405", "args": {}}, {"pid": 3100, "tid": 201863462912, "ts": 1754737498028119, "dur": 22, "ph": "X", "name": "ReadAsync 327", "args": {}}, {"pid": 3100, "tid": 201863462912, "ts": 1754737498028143, "dur": 21, "ph": "X", "name": "ReadAsync 370", "args": {}}, {"pid": 3100, "tid": 201863462912, "ts": 1754737498028166, "dur": 22, "ph": "X", "name": "ReadAsync 253", "args": {}}, {"pid": 3100, "tid": 201863462912, "ts": 1754737498028190, "dur": 17, "ph": "X", "name": "ReadAsync 419", "args": {}}, {"pid": 3100, "tid": 201863462912, "ts": 1754737498028209, "dur": 19, "ph": "X", "name": "ReadAsync 93", "args": {}}, {"pid": 3100, "tid": 201863462912, "ts": 1754737498028230, "dur": 68, "ph": "X", "name": "ReadAsync 144", "args": {}}, {"pid": 3100, "tid": 201863462912, "ts": 1754737498028300, "dur": 24, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3100, "tid": 201863462912, "ts": 1754737498028326, "dur": 21, "ph": "X", "name": "ReadAsync 374", "args": {}}, {"pid": 3100, "tid": 201863462912, "ts": 1754737498028349, "dur": 20, "ph": "X", "name": "ReadAsync 302", "args": {}}, {"pid": 3100, "tid": 201863462912, "ts": 1754737498028370, "dur": 20, "ph": "X", "name": "ReadAsync 374", "args": {}}, {"pid": 3100, "tid": 201863462912, "ts": 1754737498028393, "dur": 39, "ph": "X", "name": "ReadAsync 383", "args": {}}, {"pid": 3100, "tid": 201863462912, "ts": 1754737498028433, "dur": 23, "ph": "X", "name": "ReadAsync 249", "args": {}}, {"pid": 3100, "tid": 201863462912, "ts": 1754737498028457, "dur": 21, "ph": "X", "name": "ReadAsync 470", "args": {}}, {"pid": 3100, "tid": 201863462912, "ts": 1754737498028479, "dur": 21, "ph": "X", "name": "ReadAsync 390", "args": {}}, {"pid": 3100, "tid": 201863462912, "ts": 1754737498028502, "dur": 17, "ph": "X", "name": "ReadAsync 335", "args": {}}, {"pid": 3100, "tid": 201863462912, "ts": 1754737498028520, "dur": 26, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 3100, "tid": 201863462912, "ts": 1754737498028548, "dur": 62, "ph": "X", "name": "ReadAsync 200", "args": {}}, {"pid": 3100, "tid": 201863462912, "ts": 1754737498028611, "dur": 24, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3100, "tid": 201863462912, "ts": 1754737498028637, "dur": 24, "ph": "X", "name": "ReadAsync 300", "args": {}}, {"pid": 3100, "tid": 201863462912, "ts": 1754737498028662, "dur": 21, "ph": "X", "name": "ReadAsync 386", "args": {}}, {"pid": 3100, "tid": 201863462912, "ts": 1754737498028685, "dur": 21, "ph": "X", "name": "ReadAsync 475", "args": {}}, {"pid": 3100, "tid": 201863462912, "ts": 1754737498028708, "dur": 26, "ph": "X", "name": "ReadAsync 410", "args": {}}, {"pid": 3100, "tid": 201863462912, "ts": 1754737498028736, "dur": 22, "ph": "X", "name": "ReadAsync 400", "args": {}}, {"pid": 3100, "tid": 201863462912, "ts": 1754737498028760, "dur": 22, "ph": "X", "name": "ReadAsync 430", "args": {}}, {"pid": 3100, "tid": 201863462912, "ts": 1754737498028783, "dur": 20, "ph": "X", "name": "ReadAsync 361", "args": {}}, {"pid": 3100, "tid": 201863462912, "ts": 1754737498028804, "dur": 18, "ph": "X", "name": "ReadAsync 191", "args": {}}, {"pid": 3100, "tid": 201863462912, "ts": 1754737498028824, "dur": 64, "ph": "X", "name": "ReadAsync 237", "args": {}}, {"pid": 3100, "tid": 201863462912, "ts": 1754737498028890, "dur": 22, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3100, "tid": 201863462912, "ts": 1754737498028913, "dur": 26, "ph": "X", "name": "ReadAsync 360", "args": {}}, {"pid": 3100, "tid": 201863462912, "ts": 1754737498028943, "dur": 36, "ph": "X", "name": "ReadAsync 251", "args": {}}, {"pid": 3100, "tid": 201863462912, "ts": 1754737498028982, "dur": 24, "ph": "X", "name": "ReadAsync 507", "args": {}}, {"pid": 3100, "tid": 201863462912, "ts": 1754737498029008, "dur": 23, "ph": "X", "name": "ReadAsync 482", "args": {}}, {"pid": 3100, "tid": 201863462912, "ts": 1754737498029033, "dur": 23, "ph": "X", "name": "ReadAsync 283", "args": {}}, {"pid": 3100, "tid": 201863462912, "ts": 1754737498029058, "dur": 22, "ph": "X", "name": "ReadAsync 127", "args": {}}, {"pid": 3100, "tid": 201863462912, "ts": 1754737498029081, "dur": 21, "ph": "X", "name": "ReadAsync 221", "args": {}}, {"pid": 3100, "tid": 201863462912, "ts": 1754737498029103, "dur": 26, "ph": "X", "name": "ReadAsync 231", "args": {}}, {"pid": 3100, "tid": 201863462912, "ts": 1754737498029133, "dur": 36, "ph": "X", "name": "ReadAsync 44", "args": {}}, {"pid": 3100, "tid": 201863462912, "ts": 1754737498029171, "dur": 50, "ph": "X", "name": "ReadAsync 193", "args": {}}, {"pid": 3100, "tid": 201863462912, "ts": 1754737498029223, "dur": 11, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3100, "tid": 201863462912, "ts": 1754737498029235, "dur": 18, "ph": "X", "name": "ReadAsync 209", "args": {}}, {"pid": 3100, "tid": 201863462912, "ts": 1754737498029254, "dur": 20, "ph": "X", "name": "ReadAsync 192", "args": {}}, {"pid": 3100, "tid": 201863462912, "ts": 1754737498029276, "dur": 22, "ph": "X", "name": "ReadAsync 221", "args": {}}, {"pid": 3100, "tid": 201863462912, "ts": 1754737498029299, "dur": 20, "ph": "X", "name": "ReadAsync 289", "args": {}}, {"pid": 3100, "tid": 201863462912, "ts": 1754737498029321, "dur": 62, "ph": "X", "name": "ReadAsync 197", "args": {}}, {"pid": 3100, "tid": 201863462912, "ts": 1754737498029384, "dur": 21, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3100, "tid": 201863462912, "ts": 1754737498029407, "dur": 21, "ph": "X", "name": "ReadAsync 263", "args": {}}, {"pid": 3100, "tid": 201863462912, "ts": 1754737498029429, "dur": 21, "ph": "X", "name": "ReadAsync 309", "args": {}}, {"pid": 3100, "tid": 201863462912, "ts": 1754737498029452, "dur": 20, "ph": "X", "name": "ReadAsync 353", "args": {}}, {"pid": 3100, "tid": 201863462912, "ts": 1754737498029473, "dur": 20, "ph": "X", "name": "ReadAsync 347", "args": {}}, {"pid": 3100, "tid": 201863462912, "ts": 1754737498029495, "dur": 93, "ph": "X", "name": "ReadAsync 349", "args": {}}, {"pid": 3100, "tid": 201863462912, "ts": 1754737498029592, "dur": 27, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3100, "tid": 201863462912, "ts": 1754737498029621, "dur": 21, "ph": "X", "name": "ReadAsync 201", "args": {}}, {"pid": 3100, "tid": 201863462912, "ts": 1754737498029643, "dur": 20, "ph": "X", "name": "ReadAsync 296", "args": {}}, {"pid": 3100, "tid": 201863462912, "ts": 1754737498029665, "dur": 22, "ph": "X", "name": "ReadAsync 319", "args": {}}, {"pid": 3100, "tid": 201863462912, "ts": 1754737498029688, "dur": 18, "ph": "X", "name": "ReadAsync 386", "args": {}}, {"pid": 3100, "tid": 201863462912, "ts": 1754737498029708, "dur": 44, "ph": "X", "name": "ReadAsync 33", "args": {}}, {"pid": 3100, "tid": 201863462912, "ts": 1754737498029754, "dur": 19, "ph": "X", "name": "ReadAsync 87", "args": {}}, {"pid": 3100, "tid": 201863462912, "ts": 1754737498029774, "dur": 57, "ph": "X", "name": "ReadAsync 138", "args": {}}, {"pid": 3100, "tid": 201863462912, "ts": 1754737498029833, "dur": 21, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3100, "tid": 201863462912, "ts": 1754737498029856, "dur": 20, "ph": "X", "name": "ReadAsync 257", "args": {}}, {"pid": 3100, "tid": 201863462912, "ts": 1754737498029877, "dur": 21, "ph": "X", "name": "ReadAsync 266", "args": {}}, {"pid": 3100, "tid": 201863462912, "ts": 1754737498029900, "dur": 23, "ph": "X", "name": "ReadAsync 366", "args": {}}, {"pid": 3100, "tid": 201863462912, "ts": 1754737498029924, "dur": 21, "ph": "X", "name": "ReadAsync 327", "args": {}}, {"pid": 3100, "tid": 201863462912, "ts": 1754737498029947, "dur": 22, "ph": "X", "name": "ReadAsync 374", "args": {}}, {"pid": 3100, "tid": 201863462912, "ts": 1754737498029970, "dur": 21, "ph": "X", "name": "ReadAsync 278", "args": {}}, {"pid": 3100, "tid": 201863462912, "ts": 1754737498029992, "dur": 17, "ph": "X", "name": "ReadAsync 350", "args": {}}, {"pid": 3100, "tid": 201863462912, "ts": 1754737498030011, "dur": 20, "ph": "X", "name": "ReadAsync 247", "args": {}}, {"pid": 3100, "tid": 201863462912, "ts": 1754737498030033, "dur": 47, "ph": "X", "name": "ReadAsync 260", "args": {}}, {"pid": 3100, "tid": 201863462912, "ts": 1754737498030081, "dur": 20, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3100, "tid": 201863462912, "ts": 1754737498030103, "dur": 62, "ph": "X", "name": "ReadAsync 225", "args": {}}, {"pid": 3100, "tid": 201863462912, "ts": 1754737498030166, "dur": 26, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3100, "tid": 201863462912, "ts": 1754737498030194, "dur": 21, "ph": "X", "name": "ReadAsync 254", "args": {}}, {"pid": 3100, "tid": 201863462912, "ts": 1754737498030216, "dur": 23, "ph": "X", "name": "ReadAsync 326", "args": {}}, {"pid": 3100, "tid": 201863462912, "ts": 1754737498030242, "dur": 22, "ph": "X", "name": "ReadAsync 309", "args": {}}, {"pid": 3100, "tid": 201863462912, "ts": 1754737498030266, "dur": 22, "ph": "X", "name": "ReadAsync 396", "args": {}}, {"pid": 3100, "tid": 201863462912, "ts": 1754737498030289, "dur": 18, "ph": "X", "name": "ReadAsync 259", "args": {}}, {"pid": 3100, "tid": 201863462912, "ts": 1754737498030309, "dur": 22, "ph": "X", "name": "ReadAsync 242", "args": {}}, {"pid": 3100, "tid": 201863462912, "ts": 1754737498030332, "dur": 21, "ph": "X", "name": "ReadAsync 259", "args": {}}, {"pid": 3100, "tid": 201863462912, "ts": 1754737498030355, "dur": 16, "ph": "X", "name": "ReadAsync 274", "args": {}}, {"pid": 3100, "tid": 201863462912, "ts": 1754737498030373, "dur": 20, "ph": "X", "name": "ReadAsync 67", "args": {}}, {"pid": 3100, "tid": 201863462912, "ts": 1754737498030394, "dur": 20, "ph": "X", "name": "ReadAsync 220", "args": {}}, {"pid": 3100, "tid": 201863462912, "ts": 1754737498030415, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 3100, "tid": 201863462912, "ts": 1754737498030417, "dur": 44, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 3100, "tid": 201863462912, "ts": 1754737498030462, "dur": 22, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3100, "tid": 201863462912, "ts": 1754737498030485, "dur": 21, "ph": "X", "name": "ReadAsync 77", "args": {}}, {"pid": 3100, "tid": 201863462912, "ts": 1754737498030509, "dur": 20, "ph": "X", "name": "ReadAsync 227", "args": {}}, {"pid": 3100, "tid": 201863462912, "ts": 1754737498030530, "dur": 21, "ph": "X", "name": "ReadAsync 362", "args": {}}, {"pid": 3100, "tid": 201863462912, "ts": 1754737498030552, "dur": 22, "ph": "X", "name": "ReadAsync 313", "args": {}}, {"pid": 3100, "tid": 201863462912, "ts": 1754737498030576, "dur": 20, "ph": "X", "name": "ReadAsync 349", "args": {}}, {"pid": 3100, "tid": 201863462912, "ts": 1754737498030597, "dur": 20, "ph": "X", "name": "ReadAsync 134", "args": {}}, {"pid": 3100, "tid": 201863462912, "ts": 1754737498030619, "dur": 17, "ph": "X", "name": "ReadAsync 319", "args": {}}, {"pid": 3100, "tid": 201863462912, "ts": 1754737498030637, "dur": 21, "ph": "X", "name": "ReadAsync 263", "args": {}}, {"pid": 3100, "tid": 201863462912, "ts": 1754737498030659, "dur": 17, "ph": "X", "name": "ReadAsync 203", "args": {}}, {"pid": 3100, "tid": 201863462912, "ts": 1754737498030678, "dur": 22, "ph": "X", "name": "ReadAsync 274", "args": {}}, {"pid": 3100, "tid": 201863462912, "ts": 1754737498030701, "dur": 76, "ph": "X", "name": "ReadAsync 181", "args": {}}, {"pid": 3100, "tid": 201863462912, "ts": 1754737498030779, "dur": 24, "ph": "X", "name": "ReadAsync 56", "args": {}}, {"pid": 3100, "tid": 201863462912, "ts": 1754737498030805, "dur": 291, "ph": "X", "name": "ReadAsync 236", "args": {}}, {"pid": 3100, "tid": 201863462912, "ts": 1754737498031099, "dur": 26, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3100, "tid": 201863462912, "ts": 1754737498031127, "dur": 29, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 3100, "tid": 201863462912, "ts": 1754737498031159, "dur": 1, "ph": "X", "name": "ProcessMessages 44", "args": {}}, {"pid": 3100, "tid": 201863462912, "ts": 1754737498031160, "dur": 40, "ph": "X", "name": "ReadAsync 44", "args": {}}, {"pid": 3100, "tid": 201863462912, "ts": 1754737498031202, "dur": 1, "ph": "X", "name": "ProcessMessages 116", "args": {}}, {"pid": 3100, "tid": 201863462912, "ts": 1754737498031204, "dur": 26, "ph": "X", "name": "ReadAsync 116", "args": {}}, {"pid": 3100, "tid": 201863462912, "ts": 1754737498031232, "dur": 24, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 3100, "tid": 201863462912, "ts": 1754737498031259, "dur": 59, "ph": "X", "name": "ReadAsync 76", "args": {}}, {"pid": 3100, "tid": 201863462912, "ts": 1754737498031324, "dur": 2, "ph": "X", "name": "ProcessMessages 112", "args": {}}, {"pid": 3100, "tid": 201863462912, "ts": 1754737498031328, "dur": 43, "ph": "X", "name": "ReadAsync 112", "args": {}}, {"pid": 3100, "tid": 201863462912, "ts": 1754737498031372, "dur": 2, "ph": "X", "name": "ProcessMessages 292", "args": {}}, {"pid": 3100, "tid": 201863462912, "ts": 1754737498031375, "dur": 30, "ph": "X", "name": "ReadAsync 292", "args": {}}, {"pid": 3100, "tid": 201863462912, "ts": 1754737498031407, "dur": 1, "ph": "X", "name": "ProcessMessages 112", "args": {}}, {"pid": 3100, "tid": 201863462912, "ts": 1754737498031409, "dur": 30, "ph": "X", "name": "ReadAsync 112", "args": {}}, {"pid": 3100, "tid": 201863462912, "ts": 1754737498031441, "dur": 1, "ph": "X", "name": "ProcessMessages 124", "args": {}}, {"pid": 3100, "tid": 201863462912, "ts": 1754737498031443, "dur": 33, "ph": "X", "name": "ReadAsync 124", "args": {}}, {"pid": 3100, "tid": 201863462912, "ts": 1754737498031479, "dur": 24, "ph": "X", "name": "ReadAsync 148", "args": {}}, {"pid": 3100, "tid": 201863462912, "ts": 1754737498031505, "dur": 23, "ph": "X", "name": "ReadAsync 128", "args": {}}, {"pid": 3100, "tid": 201863462912, "ts": 1754737498031530, "dur": 24, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 3100, "tid": 201863462912, "ts": 1754737498031557, "dur": 34, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 3100, "tid": 201863462912, "ts": 1754737498031594, "dur": 1, "ph": "X", "name": "ProcessMessages 64", "args": {}}, {"pid": 3100, "tid": 201863462912, "ts": 1754737498031597, "dur": 55, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 3100, "tid": 201863462912, "ts": 1754737498031655, "dur": 27, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 3100, "tid": 201863462912, "ts": 1754737498031683, "dur": 1, "ph": "X", "name": "ProcessMessages 172", "args": {}}, {"pid": 3100, "tid": 201863462912, "ts": 1754737498031684, "dur": 42, "ph": "X", "name": "ReadAsync 172", "args": {}}, {"pid": 3100, "tid": 201863462912, "ts": 1754737498031728, "dur": 1, "ph": "X", "name": "ProcessMessages 112", "args": {}}, {"pid": 3100, "tid": 201863462912, "ts": 1754737498031731, "dur": 36, "ph": "X", "name": "ReadAsync 112", "args": {}}, {"pid": 3100, "tid": 201863462912, "ts": 1754737498031770, "dur": 1, "ph": "X", "name": "ProcessMessages 212", "args": {}}, {"pid": 3100, "tid": 201863462912, "ts": 1754737498031773, "dur": 34, "ph": "X", "name": "ReadAsync 212", "args": {}}, {"pid": 3100, "tid": 201863462912, "ts": 1754737498031809, "dur": 49, "ph": "X", "name": "ReadAsync 96", "args": {}}, {"pid": 3100, "tid": 201863462912, "ts": 1754737498031863, "dur": 2, "ph": "X", "name": "ProcessMessages 112", "args": {}}, {"pid": 3100, "tid": 201863462912, "ts": 1754737498031867, "dur": 44, "ph": "X", "name": "ReadAsync 112", "args": {}}, {"pid": 3100, "tid": 201863462912, "ts": 1754737498031914, "dur": 2, "ph": "X", "name": "ProcessMessages 300", "args": {}}, {"pid": 3100, "tid": 201863462912, "ts": 1754737498031916, "dur": 35, "ph": "X", "name": "ReadAsync 300", "args": {}}, {"pid": 3100, "tid": 201863462912, "ts": 1754737498031955, "dur": 30, "ph": "X", "name": "ReadAsync 100", "args": {}}, {"pid": 3100, "tid": 201863462912, "ts": 1754737498031987, "dur": 30, "ph": "X", "name": "ReadAsync 160", "args": {}}, {"pid": 3100, "tid": 201863462912, "ts": 1754737498032020, "dur": 50, "ph": "X", "name": "ReadAsync 144", "args": {}}, {"pid": 3100, "tid": 201863462912, "ts": 1754737498032071, "dur": 1, "ph": "X", "name": "ProcessMessages 172", "args": {}}, {"pid": 3100, "tid": 201863462912, "ts": 1754737498032073, "dur": 31, "ph": "X", "name": "ReadAsync 172", "args": {}}, {"pid": 3100, "tid": 201863462912, "ts": 1754737498032105, "dur": 1, "ph": "X", "name": "ProcessMessages 240", "args": {}}, {"pid": 3100, "tid": 201863462912, "ts": 1754737498032107, "dur": 33, "ph": "X", "name": "ReadAsync 240", "args": {}}, {"pid": 3100, "tid": 201863462912, "ts": 1754737498032143, "dur": 29, "ph": "X", "name": "ReadAsync 116", "args": {}}, {"pid": 3100, "tid": 201863462912, "ts": 1754737498032174, "dur": 26, "ph": "X", "name": "ReadAsync 156", "args": {}}, {"pid": 3100, "tid": 201863462912, "ts": 1754737498032202, "dur": 28, "ph": "X", "name": "ReadAsync 112", "args": {}}, {"pid": 3100, "tid": 201863462912, "ts": 1754737498032233, "dur": 1, "ph": "X", "name": "ProcessMessages 144", "args": {}}, {"pid": 3100, "tid": 201863462912, "ts": 1754737498032235, "dur": 39, "ph": "X", "name": "ReadAsync 144", "args": {}}, {"pid": 3100, "tid": 201863462912, "ts": 1754737498032276, "dur": 1, "ph": "X", "name": "ProcessMessages 192", "args": {}}, {"pid": 3100, "tid": 201863462912, "ts": 1754737498032278, "dur": 25, "ph": "X", "name": "ReadAsync 192", "args": {}}, {"pid": 3100, "tid": 201863462912, "ts": 1754737498032305, "dur": 1, "ph": "X", "name": "ProcessMessages 148", "args": {}}, {"pid": 3100, "tid": 201863462912, "ts": 1754737498032307, "dur": 28, "ph": "X", "name": "ReadAsync 148", "args": {}}, {"pid": 3100, "tid": 201863462912, "ts": 1754737498032337, "dur": 1, "ph": "X", "name": "ProcessMessages 128", "args": {}}, {"pid": 3100, "tid": 201863462912, "ts": 1754737498032339, "dur": 50, "ph": "X", "name": "ReadAsync 128", "args": {}}, {"pid": 3100, "tid": 201863462912, "ts": 1754737498032392, "dur": 1, "ph": "X", "name": "ProcessMessages 140", "args": {}}, {"pid": 3100, "tid": 201863462912, "ts": 1754737498032393, "dur": 29, "ph": "X", "name": "ReadAsync 140", "args": {}}, {"pid": 3100, "tid": 201863462912, "ts": 1754737498032424, "dur": 1, "ph": "X", "name": "ProcessMessages 240", "args": {}}, {"pid": 3100, "tid": 201863462912, "ts": 1754737498032426, "dur": 72, "ph": "X", "name": "ReadAsync 240", "args": {}}, {"pid": 3100, "tid": 201863462912, "ts": 1754737498032499, "dur": 19, "ph": "X", "name": "ReadAsync 84", "args": {}}, {"pid": 3100, "tid": 201863462912, "ts": 1754737498032522, "dur": 26, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 3100, "tid": 201863462912, "ts": 1754737498032550, "dur": 21, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 3100, "tid": 201863462912, "ts": 1754737498032573, "dur": 1, "ph": "X", "name": "ProcessMessages 96", "args": {}}, {"pid": 3100, "tid": 201863462912, "ts": 1754737498032575, "dur": 26, "ph": "X", "name": "ReadAsync 96", "args": {}}, {"pid": 3100, "tid": 201863462912, "ts": 1754737498032603, "dur": 53, "ph": "X", "name": "ReadAsync 96", "args": {}}, {"pid": 3100, "tid": 201863462912, "ts": 1754737498032657, "dur": 1, "ph": "X", "name": "ProcessMessages 140", "args": {}}, {"pid": 3100, "tid": 201863462912, "ts": 1754737498032659, "dur": 51, "ph": "X", "name": "ReadAsync 140", "args": {}}, {"pid": 3100, "tid": 201863462912, "ts": 1754737498032712, "dur": 1, "ph": "X", "name": "ProcessMessages 212", "args": {}}, {"pid": 3100, "tid": 201863462912, "ts": 1754737498032713, "dur": 27, "ph": "X", "name": "ReadAsync 212", "args": {}}, {"pid": 3100, "tid": 201863462912, "ts": 1754737498032744, "dur": 25, "ph": "X", "name": "ReadAsync 60", "args": {}}, {"pid": 3100, "tid": 201863462912, "ts": 1754737498032772, "dur": 30, "ph": "X", "name": "ReadAsync 112", "args": {}}, {"pid": 3100, "tid": 201863462912, "ts": 1754737498032805, "dur": 1, "ph": "X", "name": "ProcessMessages 116", "args": {}}, {"pid": 3100, "tid": 201863462912, "ts": 1754737498032807, "dur": 30, "ph": "X", "name": "ReadAsync 116", "args": {}}, {"pid": 3100, "tid": 201863462912, "ts": 1754737498032839, "dur": 1, "ph": "X", "name": "ProcessMessages 176", "args": {}}, {"pid": 3100, "tid": 201863462912, "ts": 1754737498032840, "dur": 29, "ph": "X", "name": "ReadAsync 176", "args": {}}, {"pid": 3100, "tid": 201863462912, "ts": 1754737498032873, "dur": 1, "ph": "X", "name": "ProcessMessages 92", "args": {}}, {"pid": 3100, "tid": 201863462912, "ts": 1754737498032875, "dur": 37, "ph": "X", "name": "ReadAsync 92", "args": {}}, {"pid": 3100, "tid": 201863462912, "ts": 1754737498032914, "dur": 1, "ph": "X", "name": "ProcessMessages 144", "args": {}}, {"pid": 3100, "tid": 201863462912, "ts": 1754737498032916, "dur": 25, "ph": "X", "name": "ReadAsync 144", "args": {}}, {"pid": 3100, "tid": 201863462912, "ts": 1754737498032943, "dur": 19, "ph": "X", "name": "ReadAsync 84", "args": {}}, {"pid": 3100, "tid": 201863462912, "ts": 1754737498032964, "dur": 21, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 3100, "tid": 201863462912, "ts": 1754737498032987, "dur": 76, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 3100, "tid": 201863462912, "ts": 1754737498033067, "dur": 34, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3100, "tid": 201863462912, "ts": 1754737498033104, "dur": 24, "ph": "X", "name": "ReadAsync 100", "args": {}}, {"pid": 3100, "tid": 201863462912, "ts": 1754737498033130, "dur": 1, "ph": "X", "name": "ProcessMessages 112", "args": {}}, {"pid": 3100, "tid": 201863462912, "ts": 1754737498033132, "dur": 30, "ph": "X", "name": "ReadAsync 112", "args": {}}, {"pid": 3100, "tid": 201863462912, "ts": 1754737498033164, "dur": 22, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 3100, "tid": 201863462912, "ts": 1754737498033188, "dur": 1630, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 3100, "tid": 201863462912, "ts": 1754737498034822, "dur": 53, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3100, "tid": 201863462912, "ts": 1754737498034879, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 3100, "tid": 201863462912, "ts": 1754737498034880, "dur": 544, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 3100, "tid": 201863462912, "ts": 1754737498035429, "dur": 32, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3100, "tid": 201863462912, "ts": 1754737498035464, "dur": 1, "ph": "X", "name": "ProcessMessages 36", "args": {}}, {"pid": 3100, "tid": 201863462912, "ts": 1754737498035466, "dur": 37, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 3100, "tid": 201863462912, "ts": 1754737498035506, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 3100, "tid": 201863462912, "ts": 1754737498035508, "dur": 43, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 3100, "tid": 201863462912, "ts": 1754737498035556, "dur": 2658, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 3100, "tid": 201863462912, "ts": 1754737498038220, "dur": 34, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3100, "tid": 201863462912, "ts": 1754737498038259, "dur": 28, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 3100, "tid": 201863462912, "ts": 1754737498038290, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 3100, "tid": 201863462912, "ts": 1754737498038291, "dur": 32, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 3100, "tid": 201863462912, "ts": 1754737498038326, "dur": 330, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 3100, "tid": 201863462912, "ts": 1754737498038658, "dur": 77, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3100, "tid": 201863462912, "ts": 1754737498038739, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 3100, "tid": 201863462912, "ts": 1754737498038741, "dur": 57, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 3100, "tid": 201863462912, "ts": 1754737498038801, "dur": 49, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3100, "tid": 201863462912, "ts": 1754737498038854, "dur": 1, "ph": "X", "name": "ProcessMessages 36", "args": {}}, {"pid": 3100, "tid": 201863462912, "ts": 1754737498038855, "dur": 31, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 3100, "tid": 201863462912, "ts": 1754737498038889, "dur": 1, "ph": "X", "name": "ProcessMessages 112", "args": {}}, {"pid": 3100, "tid": 201863462912, "ts": 1754737498038890, "dur": 25, "ph": "X", "name": "ReadAsync 112", "args": {}}, {"pid": 3100, "tid": 201863462912, "ts": 1754737498038918, "dur": 25, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 3100, "tid": 201863462912, "ts": 1754737498038945, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 3100, "tid": 201863462912, "ts": 1754737498038947, "dur": 29, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 3100, "tid": 201863462912, "ts": 1754737498038980, "dur": 28, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 3100, "tid": 201863462912, "ts": 1754737498039012, "dur": 190, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 3100, "tid": 201863462912, "ts": 1754737498039204, "dur": 27, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3100, "tid": 201863462912, "ts": 1754737498039234, "dur": 73, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 3100, "tid": 201863462912, "ts": 1754737498039309, "dur": 22, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3100, "tid": 201863462912, "ts": 1754737498039334, "dur": 24, "ph": "X", "name": "ReadAsync 100", "args": {}}, {"pid": 3100, "tid": 201863462912, "ts": 1754737498039361, "dur": 25, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 3100, "tid": 201863462912, "ts": 1754737498039389, "dur": 21, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 3100, "tid": 201863462912, "ts": 1754737498039412, "dur": 53, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 3100, "tid": 201863462912, "ts": 1754737498039468, "dur": 27, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3100, "tid": 201863462912, "ts": 1754737498039501, "dur": 36, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 3100, "tid": 201863462912, "ts": 1754737498039540, "dur": 20, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 3100, "tid": 201863462912, "ts": 1754737498039562, "dur": 25, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 3100, "tid": 201863462912, "ts": 1754737498039591, "dur": 43, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 3100, "tid": 201863462912, "ts": 1754737498039635, "dur": 30, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 3100, "tid": 201863462912, "ts": 1754737498039670, "dur": 30, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 3100, "tid": 201863462912, "ts": 1754737498039703, "dur": 25, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 3100, "tid": 201863462912, "ts": 1754737498039732, "dur": 30, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 3100, "tid": 201863462912, "ts": 1754737498039763, "dur": 24, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 3100, "tid": 201863462912, "ts": 1754737498039790, "dur": 28, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 3100, "tid": 201863462912, "ts": 1754737498039820, "dur": 18, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 3100, "tid": 201863462912, "ts": 1754737498039840, "dur": 23, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 3100, "tid": 201863462912, "ts": 1754737498039865, "dur": 21, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3100, "tid": 201863462912, "ts": 1754737498039888, "dur": 17, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 3100, "tid": 201863462912, "ts": 1754737498039910, "dur": 43, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 3100, "tid": 201863462912, "ts": 1754737498039956, "dur": 1, "ph": "X", "name": "ProcessMessages 96", "args": {}}, {"pid": 3100, "tid": 201863462912, "ts": 1754737498039958, "dur": 34, "ph": "X", "name": "ReadAsync 96", "args": {}}, {"pid": 3100, "tid": 201863462912, "ts": 1754737498039997, "dur": 33, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 3100, "tid": 201863462912, "ts": 1754737498040032, "dur": 26, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3100, "tid": 201863462912, "ts": 1754737498040061, "dur": 43, "ph": "X", "name": "ReadAsync 52", "args": {}}, {"pid": 3100, "tid": 201863462912, "ts": 1754737498040108, "dur": 29, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3100, "tid": 201863462912, "ts": 1754737498040140, "dur": 119, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 3100, "tid": 201863462912, "ts": 1754737498040261, "dur": 32, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3100, "tid": 201863462912, "ts": 1754737498040296, "dur": 27, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 3100, "tid": 201863462912, "ts": 1754737498040324, "dur": 1, "ph": "X", "name": "ProcessMessages 128", "args": {}}, {"pid": 3100, "tid": 201863462912, "ts": 1754737498040326, "dur": 29, "ph": "X", "name": "ReadAsync 128", "args": {}}, {"pid": 3100, "tid": 201863462912, "ts": 1754737498040359, "dur": 56, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 3100, "tid": 201863462912, "ts": 1754737498040417, "dur": 24, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3100, "tid": 201863462912, "ts": 1754737498040443, "dur": 18, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 3100, "tid": 201863462912, "ts": 1754737498040464, "dur": 36, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 3100, "tid": 201863462912, "ts": 1754737498040501, "dur": 30, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3100, "tid": 201863462912, "ts": 1754737498040533, "dur": 24, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 3100, "tid": 201863462912, "ts": 1754737498040559, "dur": 57, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 3100, "tid": 201863462912, "ts": 1754737498040619, "dur": 22, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3100, "tid": 201863462912, "ts": 1754737498040644, "dur": 1, "ph": "X", "name": "ProcessMessages 36", "args": {}}, {"pid": 3100, "tid": 201863462912, "ts": 1754737498040646, "dur": 77, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 3100, "tid": 201863462912, "ts": 1754737498040725, "dur": 34, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3100, "tid": 201863462912, "ts": 1754737498040762, "dur": 78, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 3100, "tid": 201863462912, "ts": 1754737498040842, "dur": 28, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3100, "tid": 201863462912, "ts": 1754737498040873, "dur": 1, "ph": "X", "name": "ProcessMessages 68", "args": {}}, {"pid": 3100, "tid": 201863462912, "ts": 1754737498040875, "dur": 47, "ph": "X", "name": "ReadAsync 68", "args": {}}, {"pid": 3100, "tid": 201863462912, "ts": 1754737498040929, "dur": 38, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 3100, "tid": 201863462912, "ts": 1754737498040971, "dur": 110, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 3100, "tid": 201863462912, "ts": 1754737498041086, "dur": 52, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3100, "tid": 201863462912, "ts": 1754737498041142, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 3100, "tid": 201863462912, "ts": 1754737498041143, "dur": 43, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 3100, "tid": 201863462912, "ts": 1754737498041190, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 3100, "tid": 201863462912, "ts": 1754737498041192, "dur": 40, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 3100, "tid": 201863462912, "ts": 1754737498041235, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 3100, "tid": 201863462912, "ts": 1754737498041237, "dur": 131, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 3100, "tid": 201863462912, "ts": 1754737498041372, "dur": 36, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3100, "tid": 201863462912, "ts": 1754737498041412, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 3100, "tid": 201863462912, "ts": 1754737498041413, "dur": 41, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 3100, "tid": 201863462912, "ts": 1754737498041458, "dur": 3, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 3100, "tid": 201863462912, "ts": 1754737498041462, "dur": 52, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 3100, "tid": 201863462912, "ts": 1754737498041518, "dur": 33, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3100, "tid": 201863462912, "ts": 1754737498041553, "dur": 31, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 3100, "tid": 201863462912, "ts": 1754737498041589, "dur": 32, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 3100, "tid": 201863462912, "ts": 1754737498041626, "dur": 233, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 3100, "tid": 201863462912, "ts": 1754737498041864, "dur": 40, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3100, "tid": 201863462912, "ts": 1754737498041907, "dur": 24, "ph": "X", "name": "ReadAsync 68", "args": {}}, {"pid": 3100, "tid": 201863462912, "ts": 1754737498041934, "dur": 1, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 3100, "tid": 201863462912, "ts": 1754737498041936, "dur": 38, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 3100, "tid": 201863462912, "ts": 1754737498041975, "dur": 32, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 3100, "tid": 201863462912, "ts": 1754737498042010, "dur": 1, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 3100, "tid": 201863462912, "ts": 1754737498042012, "dur": 169, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 3100, "tid": 201863462912, "ts": 1754737498042185, "dur": 36, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3100, "tid": 201863462912, "ts": 1754737498042223, "dur": 22, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 3100, "tid": 201863462912, "ts": 1754737498042247, "dur": 51, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 3100, "tid": 201863462912, "ts": 1754737498042302, "dur": 22, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3100, "tid": 201863462912, "ts": 1754737498042325, "dur": 23, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 3100, "tid": 201863462912, "ts": 1754737498042350, "dur": 46, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 3100, "tid": 201863462912, "ts": 1754737498042400, "dur": 30, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3100, "tid": 201863462912, "ts": 1754737498042433, "dur": 1, "ph": "X", "name": "ProcessMessages 36", "args": {}}, {"pid": 3100, "tid": 201863462912, "ts": 1754737498042435, "dur": 190, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 3100, "tid": 201863462912, "ts": 1754737498042627, "dur": 24, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3100, "tid": 201863462912, "ts": 1754737498042653, "dur": 1, "ph": "X", "name": "ProcessMessages 52", "args": {}}, {"pid": 3100, "tid": 201863462912, "ts": 1754737498042655, "dur": 28, "ph": "X", "name": "ReadAsync 52", "args": {}}, {"pid": 3100, "tid": 201863462912, "ts": 1754737498042685, "dur": 24, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3100, "tid": 201863462912, "ts": 1754737498042712, "dur": 115, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 3100, "tid": 201863462912, "ts": 1754737498042832, "dur": 34, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3100, "tid": 201863462912, "ts": 1754737498042869, "dur": 26, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 3100, "tid": 201863462912, "ts": 1754737498042898, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 3100, "tid": 201863462912, "ts": 1754737498042899, "dur": 35, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 3100, "tid": 201863462912, "ts": 1754737498042937, "dur": 27, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 3100, "tid": 201863462912, "ts": 1754737498042967, "dur": 207, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 3100, "tid": 201863462912, "ts": 1754737498043177, "dur": 22, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3100, "tid": 201863462912, "ts": 1754737498043201, "dur": 20, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 3100, "tid": 201863462912, "ts": 1754737498043223, "dur": 281, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 3100, "tid": 201863462912, "ts": 1754737498043507, "dur": 24, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3100, "tid": 201863462912, "ts": 1754737498043534, "dur": 2, "ph": "X", "name": "ProcessMessages 8", "args": {}}, {"pid": 3100, "tid": 201863462912, "ts": 1754737498043537, "dur": 272325, "ph": "X", "name": "ReadAsync 8", "args": {}}, {"pid": 3100, "tid": 201863462912, "ts": 1754737498315872, "dur": 3, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 3100, "tid": 201863462912, "ts": 1754737498315877, "dur": 55, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3100, "tid": 201863462912, "ts": 1754737498315936, "dur": 27, "ph": "X", "name": "ProcessMessages 192", "args": {}}, {"pid": 3100, "tid": 201863462912, "ts": 1754737498315963, "dur": 3753, "ph": "X", "name": "ReadAsync 192", "args": {}}, {"pid": 3100, "tid": 201863462912, "ts": 1754737498319721, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 3100, "tid": 201863462912, "ts": 1754737498319723, "dur": 40, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3100, "tid": 201863462912, "ts": 1754737498319765, "dur": 1, "ph": "X", "name": "ProcessMessages 148", "args": {}}, {"pid": 3100, "tid": 201863462912, "ts": 1754737498319766, "dur": 30, "ph": "X", "name": "ReadAsync 148", "args": {}}, {"pid": 3100, "tid": 201863462912, "ts": 1754737498319800, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 3100, "tid": 201863462912, "ts": 1754737498319802, "dur": 41, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 3100, "tid": 201863462912, "ts": 1754737498319852, "dur": 33, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 3100, "tid": 201863462912, "ts": 1754737498319889, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 3100, "tid": 201863462912, "ts": 1754737498319891, "dur": 106, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 3100, "tid": 201863462912, "ts": 1754737498320000, "dur": 227, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3100, "tid": 201863462912, "ts": 1754737498320230, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 3100, "tid": 201863462912, "ts": 1754737498320232, "dur": 1004, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 3100, "tid": 201863462912, "ts": 1754737498321242, "dur": 37, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3100, "tid": 201863462912, "ts": 1754737498321281, "dur": 74, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 3100, "tid": 201863462912, "ts": 1754737498321362, "dur": 32, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3100, "tid": 201863462912, "ts": 1754737498321397, "dur": 2, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 3100, "tid": 201863462912, "ts": 1754737498321401, "dur": 34, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 3100, "tid": 201863462912, "ts": 1754737498321439, "dur": 27, "ph": "X", "name": "ReadAsync 28", "args": {}}, {"pid": 3100, "tid": 201863462912, "ts": 1754737498321468, "dur": 47, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 3100, "tid": 201863462912, "ts": 1754737498321518, "dur": 27, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3100, "tid": 201863462912, "ts": 1754737498321548, "dur": 27, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 3100, "tid": 201863462912, "ts": 1754737498321578, "dur": 61, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 3100, "tid": 201863462912, "ts": 1754737498321641, "dur": 25, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3100, "tid": 201863462912, "ts": 1754737498321671, "dur": 83, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 3100, "tid": 201863462912, "ts": 1754737498321758, "dur": 23, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3100, "tid": 201863462912, "ts": 1754737498321783, "dur": 280, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 3100, "tid": 201863462912, "ts": 1754737498322065, "dur": 24, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3100, "tid": 201863462912, "ts": 1754737498322094, "dur": 258, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 3100, "tid": 201863462912, "ts": 1754737498322355, "dur": 22, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3100, "tid": 201863462912, "ts": 1754737498322379, "dur": 452, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 3100, "tid": 201863462912, "ts": 1754737498322832, "dur": 21, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3100, "tid": 201863462912, "ts": 1754737498322856, "dur": 60, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 3100, "tid": 201863462912, "ts": 1754737498322919, "dur": 27, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3100, "tid": 201863462912, "ts": 1754737498322949, "dur": 27, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 3100, "tid": 201863462912, "ts": 1754737498322978, "dur": 30, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 3100, "tid": 201863462912, "ts": 1754737498323012, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 3100, "tid": 201863462912, "ts": 1754737498323013, "dur": 45, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 3100, "tid": 201863462912, "ts": 1754737498323062, "dur": 1, "ph": "X", "name": "ProcessMessages 28", "args": {}}, {"pid": 3100, "tid": 201863462912, "ts": 1754737498323064, "dur": 36, "ph": "X", "name": "ReadAsync 28", "args": {}}, {"pid": 3100, "tid": 201863462912, "ts": 1754737498323103, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 3100, "tid": 201863462912, "ts": 1754737498323105, "dur": 63, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 3100, "tid": 201863462912, "ts": 1754737498323173, "dur": 38, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3100, "tid": 201863462912, "ts": 1754737498323214, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 3100, "tid": 201863462912, "ts": 1754737498323215, "dur": 45, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 3100, "tid": 201863462912, "ts": 1754737498323266, "dur": 35, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3100, "tid": 201863462912, "ts": 1754737498323305, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 3100, "tid": 201863462912, "ts": 1754737498323306, "dur": 443, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 3100, "tid": 201863462912, "ts": 1754737498323754, "dur": 29, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3100, "tid": 201863462912, "ts": 1754737498323787, "dur": 29, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 3100, "tid": 201863462912, "ts": 1754737498323826, "dur": 30, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 3100, "tid": 201863462912, "ts": 1754737498323858, "dur": 1, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 3100, "tid": 201863462912, "ts": 1754737498323860, "dur": 23, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 3100, "tid": 201863462912, "ts": 1754737498323885, "dur": 23, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 3100, "tid": 201863462912, "ts": 1754737498323911, "dur": 21, "ph": "X", "name": "ReadAsync 60", "args": {}}, {"pid": 3100, "tid": 201863462912, "ts": 1754737498323935, "dur": 23, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 3100, "tid": 201863462912, "ts": 1754737498323961, "dur": 21, "ph": "X", "name": "ReadAsync 28", "args": {}}, {"pid": 3100, "tid": 201863462912, "ts": 1754737498323984, "dur": 22, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 3100, "tid": 201863462912, "ts": 1754737498324009, "dur": 22, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 3100, "tid": 201863462912, "ts": 1754737498324033, "dur": 24, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 3100, "tid": 201863462912, "ts": 1754737498324060, "dur": 21, "ph": "X", "name": "ReadAsync 28", "args": {}}, {"pid": 3100, "tid": 201863462912, "ts": 1754737498324084, "dur": 21, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 3100, "tid": 201863462912, "ts": 1754737498324108, "dur": 25, "ph": "X", "name": "ReadAsync 68", "args": {}}, {"pid": 3100, "tid": 201863462912, "ts": 1754737498324135, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 3100, "tid": 201863462912, "ts": 1754737498324136, "dur": 23, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 3100, "tid": 201863462912, "ts": 1754737498324162, "dur": 23, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 3100, "tid": 201863462912, "ts": 1754737498324187, "dur": 26, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 3100, "tid": 201863462912, "ts": 1754737498324217, "dur": 27, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 3100, "tid": 201863462912, "ts": 1754737498324247, "dur": 1, "ph": "X", "name": "ProcessMessages 44", "args": {}}, {"pid": 3100, "tid": 201863462912, "ts": 1754737498324249, "dur": 22, "ph": "X", "name": "ReadAsync 44", "args": {}}, {"pid": 3100, "tid": 201863462912, "ts": 1754737498324273, "dur": 21, "ph": "X", "name": "ReadAsync 84", "args": {}}, {"pid": 3100, "tid": 201863462912, "ts": 1754737498324297, "dur": 26, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 3100, "tid": 201863462912, "ts": 1754737498324326, "dur": 22, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 3100, "tid": 201863462912, "ts": 1754737498324350, "dur": 23, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 3100, "tid": 201863462912, "ts": 1754737498324376, "dur": 25, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 3100, "tid": 201863462912, "ts": 1754737498324403, "dur": 24, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 3100, "tid": 201863462912, "ts": 1754737498324431, "dur": 27, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 3100, "tid": 201863462912, "ts": 1754737498324460, "dur": 27, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 3100, "tid": 201863462912, "ts": 1754737498324490, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 3100, "tid": 201863462912, "ts": 1754737498324492, "dur": 30, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 3100, "tid": 201863462912, "ts": 1754737498324524, "dur": 21, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 3100, "tid": 201863462912, "ts": 1754737498324547, "dur": 28, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 3100, "tid": 201863462912, "ts": 1754737498324579, "dur": 27, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 3100, "tid": 201863462912, "ts": 1754737498324612, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 3100, "tid": 201863462912, "ts": 1754737498324614, "dur": 28, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 3100, "tid": 201863462912, "ts": 1754737498324644, "dur": 73, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 3100, "tid": 201863462912, "ts": 1754737498324721, "dur": 29, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3100, "tid": 201863462912, "ts": 1754737498324753, "dur": 213, "ph": "X", "name": "ReadAsync 68", "args": {}}, {"pid": 3100, "tid": 201863462912, "ts": 1754737498324970, "dur": 35, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3100, "tid": 201863462912, "ts": 1754737498325008, "dur": 32, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 3100, "tid": 201863462912, "ts": 1754737498325042, "dur": 27, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 3100, "tid": 201863462912, "ts": 1754737498325073, "dur": 1, "ph": "X", "name": "ProcessMessages 64", "args": {}}, {"pid": 3100, "tid": 201863462912, "ts": 1754737498325074, "dur": 41, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 3100, "tid": 201863462912, "ts": 1754737498325118, "dur": 1, "ph": "X", "name": "ProcessMessages 96", "args": {}}, {"pid": 3100, "tid": 201863462912, "ts": 1754737498325120, "dur": 32, "ph": "X", "name": "ReadAsync 96", "args": {}}, {"pid": 3100, "tid": 201863462912, "ts": 1754737498325156, "dur": 30, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 3100, "tid": 201863462912, "ts": 1754737498325189, "dur": 25, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 3100, "tid": 201863462912, "ts": 1754737498325217, "dur": 103, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 3100, "tid": 201863462912, "ts": 1754737498325321, "dur": 23, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3100, "tid": 201863462912, "ts": 1754737498325346, "dur": 23, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 3100, "tid": 201863462912, "ts": 1754737498325371, "dur": 69, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 3100, "tid": 201863462912, "ts": 1754737498325442, "dur": 27, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3100, "tid": 201863462912, "ts": 1754737498325470, "dur": 23, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 3100, "tid": 201863462912, "ts": 1754737498325495, "dur": 21, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 3100, "tid": 201863462912, "ts": 1754737498325518, "dur": 52197, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 3100, "tid": 201863462912, "ts": 1754737498377725, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 3100, "tid": 201863462912, "ts": 1754737498377729, "dur": 32, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3100, "tid": 201863462912, "ts": 1754737498377762, "dur": 176, "ph": "X", "name": "ProcessMessages 1516", "args": {}}, {"pid": 3100, "tid": 201863462912, "ts": 1754737498377939, "dur": 2774, "ph": "X", "name": "ReadAsync 1516", "args": {}}, {"pid": 3100, "tid": 201863462912, "ts": 1754737498380717, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 3100, "tid": 201863462912, "ts": 1754737498380719, "dur": 29, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3100, "tid": 201863462912, "ts": 1754737498380750, "dur": 6874, "ph": "X", "name": "ReadAsync 13", "args": {}}, {"pid": 3100, "tid": 24783, "ts": 1754737498388291, "dur": 1042, "ph": "X", "name": "ReadEntireBinlogFromIpcAsync", "args": {}}, {"pid": 3100, "tid": 197568495616, "ph": "M", "name": "thread_name", "args": {"name": "WaitForBuildProgramInputDataBeingWrittenAndSendDagReadyMessageAsync"}}, {"pid": 3100, "tid": 197568495616, "ts": 1754737497859380, "dur": 37861, "ph": "X", "name": "await writeBuildProgramInputDataTask", "args": {}}, {"pid": 3100, "tid": 197568495616, "ts": 1754737497897242, "dur": 107064, "ph": "X", "name": "WritePipe.WaitConnectionAsync", "args": {}}, {"pid": 3100, "tid": 197568495616, "ts": 1754737498004307, "dur": 39, "ph": "X", "name": "WriteDagReadyMessage", "args": {}}, {"pid": 3100, "tid": 24783, "ts": 1754737498389334, "dur": 6, "ph": "X", "name": "WaitForBuildProgramInputDataBeingWrittenAndSendDagReadyMessageAsync", "args": {}}, {"pid": 3100, "tid": 193273528320, "ph": "M", "name": "thread_name", "args": {"name": "BuildAsync"}}, {"pid": 3100, "tid": 193273528320, "ts": 1754737497742034, "dur": 645637, "ph": "X", "name": "RunBackend", "args": {}}, {"pid": 3100, "tid": 193273528320, "ts": 1754737497742123, "dur": 117218, "ph": "X", "name": "BackendProgram.Start", "args": {}}, {"pid": 3100, "tid": 193273528320, "ts": 1754737498387674, "dur": 45, "ph": "X", "name": "await WaitForAndApplyScriptUpdaters", "args": {}}, {"pid": 3100, "tid": 193273528320, "ts": 1754737498387687, "dur": 15, "ph": "X", "name": "await <PERSON><PERSON>tUp<PERSON><PERSON>", "args": {}}, {"pid": 3100, "tid": 193273528320, "ts": 1754737498387733, "dur": 1, "ph": "X", "name": "await taskToReadBuildProgramOutput", "args": {}}, {"pid": 3100, "tid": 24783, "ts": 1754737498389341, "dur": 6, "ph": "X", "name": "BuildAsync", "args": {}}, {"cat": "", "pid": 12345, "tid": 0, "ts": 0, "ph": "M", "name": "process_name", "args": {"name": "bee_backend"}}, {"pid": 12345, "tid": 0, "ts": 1754737498005874, "dur": 1383, "ph": "X", "name": "DriverInitData", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1754737498007270, "dur": 782, "ph": "X", "name": "RemoveStaleOutputs", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1754737498008162, "dur": 51, "ph": "X", "name": "<PERSON><PERSON>", "args": {"detail": "PrepareNodes"}}, {"pid": 12345, "tid": 0, "ts": 1754737498008213, "dur": 373, "ph": "X", "name": "BuildQueueInit", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1754737498009191, "dur": 71, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.AIModule.dll_DD29252AECEC5C10.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1754737498009334, "dur": 112, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.AssetBundleModule.dll_022E81689B98A39E.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1754737498009914, "dur": 60, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.JSONSerializeModule.dll_AB4BD236749C82D4.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1754737498010566, "dur": 63, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityConnectModule.dll_467A1CC760B32A9D.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1754737498010762, "dur": 116, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityWebRequestModule.dll_55F435D8463A793C.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1754737498011016, "dur": 98, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.VirtualTexturingModule.dll_38C1DD3B59418845.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1754737498012624, "dur": 324, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.PlasticSCM.Editor.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 0, "ts": 1754737498013586, "dur": 51, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.TextMeshPro.rsp2"}}, {"pid": 12345, "tid": 0, "ts": 1754737498014609, "dur": 174, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Universal.Config.Runtime.dll (+2 others)"}}, {"pid": 12345, "tid": 0, "ts": 1754737498014823, "dur": 68, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Universal.Config.Runtime.rsp"}}, {"pid": 12345, "tid": 0, "ts": 1754737498017406, "dur": 59, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Timeline.Editor.rsp"}}, {"pid": 12345, "tid": 0, "ts": 1754737498021398, "dur": 50, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.Mathematics.pdb"}}, {"pid": 12345, "tid": 0, "ts": 1754737498022350, "dur": 50, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.TextMeshPro.dll"}}, {"pid": 12345, "tid": 0, "ts": 1754737498025690, "dur": 197, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Collections.DocCodeSamples.dll (+2 others)"}}, {"pid": 12345, "tid": 0, "ts": 1754737498026499, "dur": 171, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.RenderPipelines.Universal.Config.Runtime.dll"}}, {"pid": 12345, "tid": 0, "ts": 1754737498031290, "dur": 57, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/9149840420361016890.rsp"}}, {"pid": 12345, "tid": 0, "ts": 1754737498008604, "dur": 23764, "ph": "X", "name": "EnqueueRequestedNodes", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1754737498032381, "dur": 348490, "ph": "X", "name": "WaitForBuildFinished", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1754737498380873, "dur": 102, "ph": "X", "name": "JoinBuildThread", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1754737498380975, "dur": 63, "ph": "X", "name": "ThreadStateDestroy", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1754737498382212, "dur": 58, "ph": "X", "name": "BuildQueueDestroyTail", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1754737498382291, "dur": 1108, "ph": "X", "name": "<PERSON><PERSON>", "args": {"detail": "Write AllBuiltNodes"}}, {"pid": 12345, "tid": 1, "ts": 1754737498008716, "dur": 23682, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1754737498032407, "dur": 3580, "ph": "X", "name": "CheckDagSignatures", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1754737498035988, "dur": 593, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1754737498036581, "dur": 619, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1754737498037200, "dur": 166, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1754737498037367, "dur": 485, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1754737498037852, "dur": 177, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1754737498038029, "dur": 219, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1754737498038248, "dur": 200, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1754737498038448, "dur": 205, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1754737498038653, "dur": 157, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1754737498038811, "dur": 202, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1754737498039013, "dur": 166, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1754737498039179, "dur": 196, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1754737498039375, "dur": 72, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1754737498039545, "dur": 64, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1754737498039609, "dur": 210, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1754737498039820, "dur": 463, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1754737498040284, "dur": 119, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.InputSystem.dll.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1754737498040462, "dur": 959, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.InputSystem.dll (+2 others)"}}, {"pid": 12345, "tid": 1, "ts": 1754737498041453, "dur": 58, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1754737498041519, "dur": 77, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Core.Runtime.dll.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1754737498041611, "dur": 439, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Core.Runtime.dll (+2 others)"}}, {"pid": 12345, "tid": 1, "ts": 1754737498042131, "dur": 64, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipeline.Universal.ShaderLibrary.dll.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1754737498042220, "dur": 200, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipeline.Universal.ShaderLibrary.dll (+2 others)"}}, {"pid": 12345, "tid": 1, "ts": 1754737498042469, "dur": 768, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1754737498043237, "dur": 1213, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1754737498044452, "dur": 63, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Assembly-CSharp-Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1754737498044538, "dur": 275193, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1754737498319733, "dur": 1519, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.RenderPipelines.Universal.Config.Runtime.dll (+pdb)"}}, {"pid": 12345, "tid": 1, "ts": 1754737498321537, "dur": 421, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\GameSoft\\Unity\\Editor\\2022.3.38f1c1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Linq.dll"}}, {"pid": 12345, "tid": 1, "ts": 1754737498321302, "dur": 1937, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.RenderPipelines.Core.Runtime.dll (+pdb)"}}, {"pid": 12345, "tid": 1, "ts": 1754737498323239, "dur": 401, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1754737498323655, "dur": 1456, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.VisualScripting.State.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 1, "ts": 1754737498325112, "dur": 312, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1754737498325898, "dur": 50, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1754737498326168, "dur": 128, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1754737498326319, "dur": 353, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1754737498326672, "dur": 358, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1754737498327056, "dur": 53858, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1754737498008744, "dur": 23667, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1754737498032435, "dur": 232, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1754737498033081, "dur": 104, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1754737498033185, "dur": 159, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/UnityEngine.TestRunner.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 2, "ts": 1754737498033468, "dur": 203, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Core.Editor.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 2, "ts": 1754737498033707, "dur": 60, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.ShaderGraph.Editor.rsp"}}, {"pid": 12345, "tid": 2, "ts": 1754737498033843, "dur": 70, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.ShaderGraph.ShaderGraphLibrary.rsp2"}}, {"pid": 12345, "tid": 2, "ts": 1754737498034001, "dur": 239, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/com.crashkonijn.goap.demos.complex.rsp2"}}, {"pid": 12345, "tid": 2, "ts": 1754737498034267, "dur": 129, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.InputSystem.TestFramework.rsp"}}, {"pid": 12345, "tid": 2, "ts": 1754737498034458, "dur": 92, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/4014436084419441659.rsp"}}, {"pid": 12345, "tid": 2, "ts": 1754737498034566, "dur": 123, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1754737498034700, "dur": 205, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1754737498034905, "dur": 218, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1754737498035123, "dur": 202, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1754737498035325, "dur": 251, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1754737498035576, "dur": 171, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1754737498035747, "dur": 180, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1754737498035927, "dur": 524, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1754737498036451, "dur": 458, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1754737498036909, "dur": 254, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1754737498037163, "dur": 155, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1754737498037318, "dur": 222, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1754737498037540, "dur": 179, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1754737498037719, "dur": 174, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1754737498037893, "dur": 175, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1754737498038068, "dur": 199, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1754737498038267, "dur": 188, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1754737498038455, "dur": 182, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1754737498038637, "dur": 171, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1754737498038809, "dur": 174, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1754737498038984, "dur": 174, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1754737498039158, "dur": 170, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1754737498039329, "dur": 220, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1754737498039550, "dur": 78, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1754737498039629, "dur": 187, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1754737498039845, "dur": 452, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1754737498040356, "dur": 50, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\GameSoft\\Unity\\Editor\\2022.3.38f1c1\\Editor\\Data\\Tools\\ScriptUpdater\\ApiUpdater.MovedFromExtractor.exe"}}, {"pid": 12345, "tid": 2, "ts": 1754737498040298, "dur": 109, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.PlasticSCM.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1754737498040415, "dur": 73, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.PlasticSCM.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1754737498040488, "dur": 900, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.PlasticSCM.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 2, "ts": 1754737498041389, "dur": 116, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1754737498041562, "dur": 67, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.CollabProxy.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1754737498041630, "dur": 240, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1754737498041895, "dur": 188, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.CollabProxy.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 2, "ts": 1754737498042129, "dur": 1122, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1754737498043251, "dur": 276478, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1754737498319731, "dur": 1534, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Tests.dll (+pdb)"}}, {"pid": 12345, "tid": 2, "ts": 1754737498321507, "dur": 62, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\GameSoft\\Unity\\Editor\\2022.3.38f1c1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.IO.IsolatedStorage.dll"}}, {"pid": 12345, "tid": 2, "ts": 1754737498321636, "dur": 114, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\GameSoft\\Unity\\Editor\\2022.3.38f1c1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Security.Principal.dll"}}, {"pid": 12345, "tid": 2, "ts": 1754737498321319, "dur": 1580, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Collections.dll (+pdb)"}}, {"pid": 12345, "tid": 2, "ts": 1754737498322899, "dur": 250, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1754737498323153, "dur": 1432, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Burst.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 2, "ts": 1754737498325372, "dur": 64, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1754737498325694, "dur": 60, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1754737498325838, "dur": 66, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1754737498325988, "dur": 59, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1754737498326220, "dur": 82, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1754737498326306, "dur": 254, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1754737498326587, "dur": 118, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1754737498326721, "dur": 54146, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1754737498008780, "dur": 23644, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1754737498032438, "dur": 229, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1754737498032753, "dur": 65, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.QuickSearchModule.dll_BE454E773931EEAC.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1754737498032893, "dur": 168, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.GameCenterModule.dll_09D119D0E0AA1ABF.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1754737498033064, "dur": 103, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\GameSoft\\Unity\\Editor\\2022.3.38f1c1\\Editor\\Data\\PlaybackEngines\\WindowsStandaloneSupport\\UnityEditor.WindowsStandalone.Extensions.dll"}}, {"pid": 12345, "tid": 3, "ts": 1754737498033063, "dur": 105, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.WindowsStandalone.Extensions.dll_35385077F0CEC424.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1754737498033182, "dur": 197, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.WindowsStandalone.Extensions.dll_35385077F0CEC424.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1754737498033476, "dur": 51, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Core.Editor.rsp"}}, {"pid": 12345, "tid": 3, "ts": 1754737498033584, "dur": 69, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Core.Editor.rsp"}}, {"pid": 12345, "tid": 3, "ts": 1754737498033743, "dur": 133, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Universal.Editor.rsp2"}}, {"pid": 12345, "tid": 3, "ts": 1754737498033996, "dur": 80, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/com.crashkonijn.goap.demos.complex.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 3, "ts": 1754737498034238, "dur": 133, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Collections.DocCodeSamples.rsp2"}}, {"pid": 12345, "tid": 3, "ts": 1754737498034511, "dur": 148, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1754737498034691, "dur": 194, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1754737498034886, "dur": 236, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1754737498035122, "dur": 178, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1754737498035301, "dur": 204, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1754737498035505, "dur": 237, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1754737498035742, "dur": 164, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1754737498035906, "dur": 556, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1754737498036462, "dur": 190, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1754737498036652, "dur": 250, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1754737498036902, "dur": 327, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1754737498037230, "dur": 197, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1754737498037427, "dur": 173, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1754737498037600, "dur": 164, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1754737498037765, "dur": 202, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1754737498037967, "dur": 198, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1754737498038165, "dur": 214, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1754737498038379, "dur": 325, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1754737498038704, "dur": 180, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1754737498038885, "dur": 368, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1754737498039254, "dur": 185, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1754737498039439, "dur": 50, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1754737498039570, "dur": 102, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1754737498039672, "dur": 197, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1754737498039869, "dur": 435, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1754737498040306, "dur": 79, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.VisualStudio.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1754737498040434, "dur": 103, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.ShaderGraph.ShaderGraphLibrary.dll.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1754737498040552, "dur": 326, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.ShaderGraph.ShaderGraphLibrary.dll (+2 others)"}}, {"pid": 12345, "tid": 3, "ts": 1754737498040878, "dur": 71, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1754737498040956, "dur": 120, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/com.crashkonijn.goap.demos.turn-based.dll.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1754737498041076, "dur": 193, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1754737498041318, "dur": 107, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1754737498041425, "dur": 78, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1754737498041522, "dur": 72, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.InputSystem.TestFramework.dll.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1754737498041619, "dur": 266, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.InputSystem.TestFramework.dll (+2 others)"}}, {"pid": 12345, "tid": 3, "ts": 1754737498041936, "dur": 189, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1754737498042126, "dur": 51, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Core.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1754737498042201, "dur": 411, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Core.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 3, "ts": 1754737498042660, "dur": 83, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.ShaderGraph.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1754737498042761, "dur": 1079, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.ShaderGraph.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 3, "ts": 1754737498043899, "dur": 61, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Universal.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1754737498043973, "dur": 400, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Universal.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 3, "ts": 1754737498044420, "dur": 60, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/com.unity.cinemachine.editor.dll.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1754737498044519, "dur": 204, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/com.unity.cinemachine.editor.dll (+2 others)"}}, {"pid": 12345, "tid": 3, "ts": 1754737498044747, "dur": 274996, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1754737498320003, "dur": 55, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\GameSoft\\Unity\\Editor\\2022.3.38f1c1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Diagnostics.Tracing.dll"}}, {"pid": 12345, "tid": 3, "ts": 1754737498319745, "dur": 1543, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/CrashKonijn.Agent.Core.dll (+pdb)"}}, {"pid": 12345, "tid": 3, "ts": 1754737498321289, "dur": 138, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1754737498321636, "dur": 112, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\GameSoft\\Unity\\Editor\\2022.3.38f1c1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.IO.Compression.dll"}}, {"pid": 12345, "tid": 3, "ts": 1754737498322830, "dur": 61, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\GameSoft\\Unity\\Editor\\2022.3.38f1c1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Reflection.Primitives.dll"}}, {"pid": 12345, "tid": 3, "ts": 1754737498321432, "dur": 1592, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/GlassSystem.dll (+pdb)"}}, {"pid": 12345, "tid": 3, "ts": 1754737498323025, "dur": 318, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1754737498323364, "dur": 1335, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Searcher.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 3, "ts": 1754737498325884, "dur": 183, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\GameSoft\\Unity\\Editor\\2022.3.38f1c1\\Editor\\Data\\Tools\\ilpp\\Unity.ILPP.Runner\\System.Threading.Tasks.Dataflow.dll"}}, {"pid": 12345, "tid": 3, "ts": 1754737498324739, "dur": 1884, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/CrashKonijn.Goap.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 3, "ts": 1754737498326668, "dur": 82, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1754737498326767, "dur": 54202, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1754737498008825, "dur": 23612, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1754737498032457, "dur": 244, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\GameSoft\\Unity\\Editor\\2022.3.38f1c1\\Editor\\Data\\Managed\\UnityEngine\\UnityEditor.UIElementsSamplesModule.dll"}}, {"pid": 12345, "tid": 4, "ts": 1754737498032445, "dur": 259, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.UIElementsSamplesModule.dll_623E7535F4BD3DF5.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1754737498032770, "dur": 95, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.SceneViewModule.dll_2C01C8125CB356D8.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1754737498033070, "dur": 82, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1754737498033211, "dur": 159, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/UnityEngine.UI.rsp"}}, {"pid": 12345, "tid": 4, "ts": 1754737498033405, "dur": 59, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1754737498033888, "dur": 80, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Cinemachine.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 4, "ts": 1754737498034033, "dur": 268, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/com.unity.cinemachine.editor.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 4, "ts": 1754737498034519, "dur": 156, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1754737498034685, "dur": 217, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1754737498034902, "dur": 173, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1754737498035075, "dur": 265, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1754737498035340, "dur": 287, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1754737498035627, "dur": 162, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1754737498035789, "dur": 169, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1754737498035958, "dur": 581, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1754737498036539, "dur": 162, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1754737498036701, "dur": 207, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1754737498036909, "dur": 188, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1754737498037097, "dur": 662, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1754737498037760, "dur": 202, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1754737498037963, "dur": 185, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1754737498038148, "dur": 598, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1754737498038746, "dur": 201, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1754737498038947, "dur": 219, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1754737498039166, "dur": 173, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1754737498039339, "dur": 198, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1754737498039626, "dur": 191, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1754737498039817, "dur": 472, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1754737498040290, "dur": 96, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.ShaderGraph.Utilities.dll.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1754737498040422, "dur": 296, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.ShaderGraph.Utilities.dll (+2 others)"}}, {"pid": 12345, "tid": 4, "ts": 1754737498040719, "dur": 166, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1754737498040925, "dur": 112, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/com.crashkonijn.goap.demos.simple.dll.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1754737498041037, "dur": 217, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1754737498041362, "dur": 55, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1754737498041418, "dur": 102, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1754737498041521, "dur": 78, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.InputSystem.ForUI.dll.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1754737498041627, "dur": 211, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.InputSystem.ForUI.dll (+2 others)"}}, {"pid": 12345, "tid": 4, "ts": 1754737498041929, "dur": 74, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.Collections.BurstCompatibilityGen.dll.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1754737498042016, "dur": 249, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Collections.BurstCompatibilityGen.dll (+2 others)"}}, {"pid": 12345, "tid": 4, "ts": 1754737498042298, "dur": 908, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1754737498043207, "dur": 271, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/com.crashkonijn.goap.demos.turn-based.dll (+2 others)"}}, {"pid": 12345, "tid": 4, "ts": 1754737498043480, "dur": 58, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1754737498043543, "dur": 276181, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1754737498319739, "dur": 1518, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/CrashKonijn.Goap.Resolver.dll (+pdb)"}}, {"pid": 12345, "tid": 4, "ts": 1754737498321301, "dur": 87, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/CrashKonijn.Goap.Resolver.dll (+pdb)"}}, {"pid": 12345, "tid": 4, "ts": 1754737498321391, "dur": 123, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\Bee\\artifacts\\1900b0aE.dag\\Unity.TextMeshPro.dll"}}, {"pid": 12345, "tid": 4, "ts": 1754737498321538, "dur": 319, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\GameSoft\\Unity\\Editor\\2022.3.38f1c1\\Editor\\Data\\Managed\\UnityEngine\\UnityEditor.UnityConnectModule.dll"}}, {"pid": 12345, "tid": 4, "ts": 1754737498323163, "dur": 65, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\GameSoft\\Unity\\Editor\\2022.3.38f1c1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Security.Cryptography.Csp.dll"}}, {"pid": 12345, "tid": 4, "ts": 1754737498321390, "dur": 1918, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.TextMeshPro.dll (+pdb)"}}, {"pid": 12345, "tid": 4, "ts": 1754737498323360, "dur": 1436, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Collections.BurstCompatibilityGen.dll (+pdb)"}}, {"pid": 12345, "tid": 4, "ts": 1754737498325809, "dur": 55, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\GameSoft\\Unity\\Editor\\2022.3.38f1c1\\Editor\\Data\\Tools\\ilpp\\Unity.ILPP.Runner\\System.Security.Claims.dll"}}, {"pid": 12345, "tid": 4, "ts": 1754737498324843, "dur": 1655, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/com.unity.cinemachine.editor.dll (+pdb)"}}, {"pid": 12345, "tid": 4, "ts": 1754737498326583, "dur": 71, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1754737498326677, "dur": 54196, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1754737498008856, "dur": 23593, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1754737498032467, "dur": 221, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\GameSoft\\Unity\\Editor\\2022.3.38f1c1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.UnityWebRequestWWWModule.dll"}}, {"pid": 12345, "tid": 5, "ts": 1754737498032458, "dur": 246, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityWebRequestWWWModule.dll_BA3DE9AF0772CF8F.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1754737498032795, "dur": 73, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\GameSoft\\Unity\\Editor\\2022.3.38f1c1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.AndroidJNIModule.dll"}}, {"pid": 12345, "tid": 5, "ts": 1754737498032794, "dur": 75, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.AndroidJNIModule.dll_DF83CFB61A6929CE.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1754737498032879, "dur": 164, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.AndroidJNIModule.dll_DF83CFB61A6929CE.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1754737498033064, "dur": 70, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\GameSoft\\Unity\\Editor\\2022.3.38f1c1\\Editor\\Data\\Tools\\ScriptUpdater\\ApiUpdater.MovedFromExtractor.exe"}}, {"pid": 12345, "tid": 5, "ts": 1754737498033044, "dur": 91, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityWebRequestAssetBundleModule.dll_20062661B6C0E97A.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1754737498033150, "dur": 57, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.visualscripting@1.9.4\\Editor\\VisualScripting.Core\\Dependencies\\DotNetZip\\Unity.VisualScripting.IonicZip.dll"}}, {"pid": 12345, "tid": 5, "ts": 1754737498033149, "dur": 59, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.VisualScripting.IonicZip.dll_840FAE5C8A141D5B.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1754737498033484, "dur": 125, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Core.Editor.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 5, "ts": 1754737498033625, "dur": 73, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Flow.Editor.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 5, "ts": 1754737498033777, "dur": 66, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Timeline.Editor.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 5, "ts": 1754737498033938, "dur": 70, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.ShaderGraph.ShaderGraphLibrary.rsp"}}, {"pid": 12345, "tid": 5, "ts": 1754737498034025, "dur": 367, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.ShaderGraph.ShaderGraphLibrary.rsp"}}, {"pid": 12345, "tid": 5, "ts": 1754737498034423, "dur": 107, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/8899139255040401798.rsp"}}, {"pid": 12345, "tid": 5, "ts": 1754737498034543, "dur": 125, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1754737498034713, "dur": 213, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1754737498034926, "dur": 189, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1754737498035115, "dur": 201, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1754737498035317, "dur": 260, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1754737498035577, "dur": 183, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1754737498035760, "dur": 164, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1754737498035924, "dur": 522, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1754737498036446, "dur": 165, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1754737498036611, "dur": 546, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1754737498037158, "dur": 146, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1754737498037304, "dur": 534, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1754737498037838, "dur": 202, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1754737498038040, "dur": 204, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1754737498038244, "dur": 184, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1754737498038428, "dur": 181, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1754737498038609, "dur": 187, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1754737498038796, "dur": 195, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1754737498038991, "dur": 217, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1754737498039209, "dur": 204, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1754737498039413, "dur": 317, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1754737498039730, "dur": 94, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1754737498039824, "dur": 471, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1754737498040296, "dur": 115, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.TextMeshPro.dll.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1754737498040429, "dur": 396, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.TextMeshPro.dll (+2 others)"}}, {"pid": 12345, "tid": 5, "ts": 1754737498040826, "dur": 64, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1754737498040915, "dur": 221, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/CrashKonijn.Goap.Runtime.dll.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1754737498041136, "dur": 74, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1754737498041217, "dur": 62, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1754737498041320, "dur": 96, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1754737498041417, "dur": 80, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.Burst.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1754737498041511, "dur": 317, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Burst.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 5, "ts": 1754737498041931, "dur": 200, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1754737498042131, "dur": 1077, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1754737498043229, "dur": 224, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/com.crashkonijn.goap.demos.complex.dll (+2 others)"}}, {"pid": 12345, "tid": 5, "ts": 1754737498043497, "dur": 50, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Flow.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1754737498043558, "dur": 320, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Flow.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 5, "ts": 1754737498043969, "dur": 213, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.State.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 5, "ts": 1754737498044267, "dur": 145, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Shared.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 5, "ts": 1754737498044446, "dur": 50, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Assembly-CSharp.dll.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1754737498044506, "dur": 276007, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1754737498321536, "dur": 645, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\GameSoft\\Unity\\Editor\\2022.3.38f1c1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.PhysicsModule.dll"}}, {"pid": 12345, "tid": 5, "ts": 1754737498320516, "dur": 2069, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/CrashKonijn.Goap.Core.dll (+pdb)"}}, {"pid": 12345, "tid": 5, "ts": 1754737498322590, "dur": 336, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1754737498323163, "dur": 212, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\GameSoft\\Unity\\Editor\\2022.3.38f1c1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.IO.FileSystem.Watcher.dll"}}, {"pid": 12345, "tid": 5, "ts": 1754737498324452, "dur": 53, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\GameSoft\\Unity\\Editor\\2022.3.38f1c1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Runtime.InteropServices.dll"}}, {"pid": 12345, "tid": 5, "ts": 1754737498322936, "dur": 1686, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/CrashKonijn.Agent.Runtime.dll (+pdb)"}}, {"pid": 12345, "tid": 5, "ts": 1754737498325438, "dur": 206, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1754737498325691, "dur": 64, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1754737498325885, "dur": 81, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\Bee\\artifacts\\1900b0aE.dag\\post-processed\\Unity.Searcher.Editor.dll"}}, {"pid": 12345, "tid": 5, "ts": 1754737498325885, "dur": 82, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.Searcher.Editor.dll"}}, {"pid": 12345, "tid": 5, "ts": 1754737498326020, "dur": 283, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1754737498326311, "dur": 303, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1754737498326676, "dur": 405, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1754737498327094, "dur": 53810, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1754737498008892, "dur": 23573, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1754737498032483, "dur": 248, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\GameSoft\\Unity\\Editor\\2022.3.38f1c1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.VehiclesModule.dll"}}, {"pid": 12345, "tid": 6, "ts": 1754737498032475, "dur": 260, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.VehiclesModule.dll_B4028357F84AC7DF.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1754737498032773, "dur": 53, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\GameSoft\\Unity\\Editor\\2022.3.38f1c1\\Editor\\Data\\Managed\\UnityEngine\\UnityEditor.UnityConnectModule.dll"}}, {"pid": 12345, "tid": 6, "ts": 1754737498032770, "dur": 58, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.UnityConnectModule.dll_591A98B8662B29F2.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1754737498032844, "dur": 237, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.UnityConnectModule.dll_591A98B8662B29F2.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1754737498033084, "dur": 72, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.collab-proxy@2.8.2\\Lib\\Editor\\Unity.Plastic.Newtonsoft.Json.dll"}}, {"pid": 12345, "tid": 6, "ts": 1754737498033083, "dur": 82, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.Plastic.Newtonsoft.Json.dll_CF17D4CA2A31CD8C.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1754737498033314, "dur": 102, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Mathematics.rsp2"}}, {"pid": 12345, "tid": 6, "ts": 1754737498033690, "dur": 86, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/CrashKonijn.Goap.Runtime.rsp2"}}, {"pid": 12345, "tid": 6, "ts": 1754737498033846, "dur": 85, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Shared.Editor.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 6, "ts": 1754737498033977, "dur": 139, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/URPWizard.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 6, "ts": 1754737498034205, "dur": 114, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.CollabProxy.Editor.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 6, "ts": 1754737498034498, "dur": 164, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1754737498034697, "dur": 204, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1754737498034901, "dur": 220, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1754737498035122, "dur": 169, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1754737498035291, "dur": 184, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1754737498035475, "dur": 181, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1754737498035656, "dur": 166, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1754737498035822, "dur": 220, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1754737498036042, "dur": 567, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1754737498036609, "dur": 164, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1754737498036774, "dur": 626, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1754737498037400, "dur": 172, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1754737498037573, "dur": 172, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1754737498037745, "dur": 179, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1754737498037924, "dur": 172, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1754737498038096, "dur": 205, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1754737498038301, "dur": 177, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1754737498038478, "dur": 179, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1754737498038657, "dur": 168, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1754737498038825, "dur": 202, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1754737498039027, "dur": 163, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1754737498039190, "dur": 177, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1754737498039367, "dur": 349, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1754737498039716, "dur": 117, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1754737498039833, "dur": 455, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1754737498040289, "dur": 66, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.Searcher.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1754737498040356, "dur": 58, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1754737498040472, "dur": 203, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\GameSoft\\Unity\\Editor\\2022.3.38f1c1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.DirectorModule.dll"}}, {"pid": 12345, "tid": 6, "ts": 1754737498040436, "dur": 654, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Searcher.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 6, "ts": 1754737498041090, "dur": 86, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1754737498041319, "dur": 100, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1754737498041420, "dur": 94, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1754737498041520, "dur": 78, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Core.dll.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1754737498041630, "dur": 757, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Core.dll (+2 others)"}}, {"pid": 12345, "tid": 6, "ts": 1754737498042435, "dur": 66, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Flow.dll.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1754737498042511, "dur": 874, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Flow.dll (+2 others)"}}, {"pid": 12345, "tid": 6, "ts": 1754737498043435, "dur": 53, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1754737498043551, "dur": 177, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.State.dll (+2 others)"}}, {"pid": 12345, "tid": 6, "ts": 1754737498043772, "dur": 275961, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1754737498319743, "dur": 1531, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.RenderPipeline.Universal.ShaderLibrary.dll (+pdb)"}}, {"pid": 12345, "tid": 6, "ts": 1754737498321634, "dur": 115, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\GameSoft\\Unity\\Editor\\2022.3.38f1c1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Threading.Overlapped.dll"}}, {"pid": 12345, "tid": 6, "ts": 1754737498321315, "dur": 1443, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.InputSystem.dll (+pdb)"}}, {"pid": 12345, "tid": 6, "ts": 1754737498322807, "dur": 1192, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/UnityEngine.UI.dll (+pdb)"}}, {"pid": 12345, "tid": 6, "ts": 1754737498323999, "dur": 417, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1754737498325340, "dur": 76, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\GameSoft\\Unity\\Editor\\2022.3.38f1c1\\Editor\\Data\\Tools\\ilpp\\Unity.ILPP.Runner\\Microsoft.AspNetCore.Razor.Runtime.dll"}}, {"pid": 12345, "tid": 6, "ts": 1754737498326280, "dur": 68, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\GameSoft\\Unity\\Editor\\2022.3.38f1c1\\Editor\\Data\\NetStandard\\Extensions\\2.0.0\\System.Runtime.InteropServices.WindowsRuntime.dll"}}, {"pid": 12345, "tid": 6, "ts": 1754737498324772, "dur": 1742, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/URPWizard.dll (+pdb)"}}, {"pid": 12345, "tid": 6, "ts": 1754737498326515, "dur": 90, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1754737498326671, "dur": 241, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1754737498326930, "dur": 54027, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1754737498008922, "dur": 23556, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1754737498032489, "dur": 221, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\GameSoft\\Unity\\Editor\\2022.3.38f1c1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.VFXModule.dll"}}, {"pid": 12345, "tid": 7, "ts": 1754737498032483, "dur": 228, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.VFXModule.dll_7FCD2258FCB2D3F4.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1754737498032777, "dur": 64, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.UIBuilderModule.dll_41DA9226DA4902FE.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1754737498032953, "dur": 95, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ParticleSystemModule.dll_33F6F62BD0C52DED.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1754737498033053, "dur": 102, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\GameSoft\\Unity\\Editor\\2022.3.38f1c1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.UnityWebRequestAudioModule.dll"}}, {"pid": 12345, "tid": 7, "ts": 1754737498033050, "dur": 113, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityWebRequestAudioModule.dll_180837F1985FCEBE.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1754737498033344, "dur": 107, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.Burst.Cecil.Mdb.dll_825BAFEC5BACF8AB.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1754737498033480, "dur": 107, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/CrashKonijn.Goap.Core.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 7, "ts": 1754737498033756, "dur": 132, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Timeline.Editor.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 7, "ts": 1754737498033982, "dur": 180, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/URPWizard.rsp2"}}, {"pid": 12345, "tid": 7, "ts": 1754737498034173, "dur": 167, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Assembly-CSharp.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 7, "ts": 1754737498034495, "dur": 144, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1754737498034671, "dur": 195, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1754737498034866, "dur": 169, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1754737498035035, "dur": 209, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1754737498035245, "dur": 184, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1754737498035429, "dur": 227, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1754737498035657, "dur": 173, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1754737498035831, "dur": 191, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1754737498036022, "dur": 495, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1754737498036518, "dur": 197, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1754737498036715, "dur": 712, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1754737498037427, "dur": 191, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1754737498037619, "dur": 413, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1754737498038032, "dur": 322, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1754737498038355, "dur": 271, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1754737498038626, "dur": 205, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1754737498038831, "dur": 183, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1754737498039014, "dur": 205, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1754737498039219, "dur": 158, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1754737498039416, "dur": 175, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1754737498039628, "dur": 192, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1754737498039821, "dur": 472, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1754737498040294, "dur": 95, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.Timeline.dll.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1754737498040941, "dur": 90, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.timeline@1.7.6\\Runtime\\Utilities\\FrameRate.cs"}}, {"pid": 12345, "tid": 7, "ts": 1754737498040438, "dur": 617, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Timeline.dll (+2 others)"}}, {"pid": 12345, "tid": 7, "ts": 1754737498041141, "dur": 212, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.Timeline.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1754737498041367, "dur": 551, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Timeline.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 7, "ts": 1754737498041918, "dur": 76, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1754737498042019, "dur": 114, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1754737498042133, "dur": 1072, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1754737498043205, "dur": 269, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/com.crashkonijn.docs.getting_started.dll (+2 others)"}}, {"pid": 12345, "tid": 7, "ts": 1754737498043507, "dur": 276227, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1754737498319740, "dur": 1498, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.VisualScripting.State.dll (+pdb)"}}, {"pid": 12345, "tid": 7, "ts": 1754737498321273, "dur": 59, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.VisualScripting.State.dll (+pdb)"}}, {"pid": 12345, "tid": 7, "ts": 1754737498321539, "dur": 55, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\GameSoft\\Unity\\Editor\\2022.3.38f1c1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Diagnostics.Contracts.dll"}}, {"pid": 12345, "tid": 7, "ts": 1754737498321637, "dur": 112, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\GameSoft\\Unity\\Editor\\2022.3.38f1c1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.IO.UnmanagedMemoryStream.dll"}}, {"pid": 12345, "tid": 7, "ts": 1754737498321335, "dur": 1634, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Cinemachine.dll (+pdb)"}}, {"pid": 12345, "tid": 7, "ts": 1754737498322970, "dur": 52, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1754737498323028, "dur": 1352, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.RenderPipelines.Universal.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 7, "ts": 1754737498324381, "dur": 109, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1754737498325521, "dur": 99, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\GameSoft\\Unity\\Editor\\2022.3.38f1c1\\Editor\\Data\\Tools\\ilpp\\Unity.ILPP.Runner\\msquic.dll"}}, {"pid": 12345, "tid": 7, "ts": 1754737498326029, "dur": 90, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\GameSoft\\Unity\\Editor\\2022.3.38f1c1\\Editor\\Data\\Tools\\ilpp\\Unity.ILPP.Runner\\Unity.ILPP.Runner.exe"}}, {"pid": 12345, "tid": 7, "ts": 1754737498324807, "dur": 1881, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Timeline.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 7, "ts": 1754737498326753, "dur": 54116, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1754737498008951, "dur": 23537, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1754737498032501, "dur": 214, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\GameSoft\\Unity\\Editor\\2022.3.38f1c1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.VideoModule.dll"}}, {"pid": 12345, "tid": 8, "ts": 1754737498032494, "dur": 223, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.VideoModule.dll_1DBF74AC9293BF07.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1754737498032772, "dur": 60, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\GameSoft\\Unity\\Editor\\2022.3.38f1c1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.AccessibilityModule.dll"}}, {"pid": 12345, "tid": 8, "ts": 1754737498032771, "dur": 62, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.AccessibilityModule.dll_4B3448FA7561224A.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1754737498032843, "dur": 169, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.AccessibilityModule.dll_4B3448FA7561224A.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1754737498033053, "dur": 69, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1754737498033182, "dur": 108, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.Plastic.Antlr3.Runtime.dll_493D590627D79599.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1754737498033294, "dur": 69, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.InputSystem.rsp"}}, {"pid": 12345, "tid": 8, "ts": 1754737498033377, "dur": 70, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.InputSystem.rsp"}}, {"pid": 12345, "tid": 8, "ts": 1754737498033458, "dur": 94, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/CrashKonijn.Goap.Core.rsp2"}}, {"pid": 12345, "tid": 8, "ts": 1754737498033648, "dur": 60, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Burst.Editor.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 8, "ts": 1754737498033805, "dur": 156, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/CrashKonijn.Goap.Editor.rsp2"}}, {"pid": 12345, "tid": 8, "ts": 1754737498034029, "dur": 173, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/com.crashkonijn.goap.demos.simple.rsp2"}}, {"pid": 12345, "tid": 8, "ts": 1754737498034268, "dur": 145, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Collections.BurstCompatibilityGen.rsp"}}, {"pid": 12345, "tid": 8, "ts": 1754737498034518, "dur": 145, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1754737498034671, "dur": 224, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1754737498034895, "dur": 179, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1754737498035074, "dur": 256, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1754737498035331, "dur": 252, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1754737498035584, "dur": 183, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1754737498035767, "dur": 172, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1754737498035939, "dur": 507, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1754737498036446, "dur": 168, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1754737498036614, "dur": 188, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1754737498036803, "dur": 173, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1754737498036976, "dur": 163, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1754737498037139, "dur": 168, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1754737498037307, "dur": 183, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1754737498037490, "dur": 176, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1754737498037666, "dur": 212, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1754737498037878, "dur": 177, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1754737498038055, "dur": 203, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1754737498038258, "dur": 193, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1754737498038451, "dur": 566, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1754737498039017, "dur": 212, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1754737498039230, "dur": 173, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1754737498039418, "dur": 285, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1754737498039703, "dur": 139, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1754737498039842, "dur": 520, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1754737498040366, "dur": 73, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/CrashKonijn.Goap.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1754737498040484, "dur": 280, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Core.ShaderLibrary.dll (+2 others)"}}, {"pid": 12345, "tid": 8, "ts": 1754737498040942, "dur": 86, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\GameSoft\\Unity\\Editor\\2022.3.38f1c1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Diagnostics.TextWriterTraceListener.dll"}}, {"pid": 12345, "tid": 8, "ts": 1754737498040796, "dur": 549, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/CrashKonijn.Agent.Runtime.dll (+2 others)"}}, {"pid": 12345, "tid": 8, "ts": 1754737498041424, "dur": 76, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1754737498041523, "dur": 338, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1754737498041861, "dur": 73, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1754737498041934, "dur": 198, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1754737498042132, "dur": 1094, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1754737498043226, "dur": 274, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1754737498043500, "dur": 276241, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1754737498319741, "dur": 1492, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/CrashKonijn.Goap.Runtime.dll (+pdb)"}}, {"pid": 12345, "tid": 8, "ts": 1754737498321538, "dur": 406, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\GameSoft\\Unity\\Editor\\2022.3.38f1c1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Reflection.DispatchProxy.dll"}}, {"pid": 12345, "tid": 8, "ts": 1754737498323121, "dur": 110, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\GameSoft\\Unity\\Editor\\2022.3.38f1c1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\System.Drawing.dll"}}, {"pid": 12345, "tid": 8, "ts": 1754737498321290, "dur": 2012, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/com.crashkonijn.goap.demos.turn-based.dll (+pdb)"}}, {"pid": 12345, "tid": 8, "ts": 1754737498324511, "dur": 85, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\GameSoft\\Unity\\Editor\\2022.3.38f1c1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Threading.Timer.dll"}}, {"pid": 12345, "tid": 8, "ts": 1754737498323340, "dur": 1455, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.TextMeshPro.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 8, "ts": 1754737498324796, "dur": 54, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1754737498325598, "dur": 127, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\GameSoft\\Unity\\Editor\\2022.3.38f1c1\\Editor\\Data\\Tools\\ilpp\\Unity.ILPP.Runner\\System.ComponentModel.dll"}}, {"pid": 12345, "tid": 8, "ts": 1754737498325889, "dur": 69, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\GameSoft\\Unity\\Editor\\2022.3.38f1c1\\Editor\\Data\\Tools\\ilpp\\Unity.ILPP.Runner\\System.Net.Mail.dll"}}, {"pid": 12345, "tid": 8, "ts": 1754737498326271, "dur": 53, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\GameSoft\\Unity\\Editor\\2022.3.38f1c1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.StreamingModule.dll"}}, {"pid": 12345, "tid": 8, "ts": 1754737498324857, "dur": 1859, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/UnityEditor.UI.dll (+pdb)"}}, {"pid": 12345, "tid": 8, "ts": 1754737498326755, "dur": 54111, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1754737498008977, "dur": 23522, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1754737498032517, "dur": 218, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\GameSoft\\Unity\\Editor\\2022.3.38f1c1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.VirtualTexturingModule.dll"}}, {"pid": 12345, "tid": 9, "ts": 1754737498032510, "dur": 228, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.VirtualTexturingModule.dll_38C1DD3B59418845.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1754737498032782, "dur": 197, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.UIElementsModule.dll_613AE6E2D149BAF8.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1754737498033097, "dur": 81, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.visualscripting@1.9.4\\Runtime\\VisualScripting.Flow\\Dependencies\\NCalc\\Unity.VisualScripting.Antlr3.Runtime.dll"}}, {"pid": 12345, "tid": 9, "ts": 1754737498033096, "dur": 83, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.VisualScripting.Antlr3.Runtime.dll_FBC62C826EB2B4B0.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1754737498033189, "dur": 62, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.VisualScripting.Antlr3.Runtime.dll_FBC62C826EB2B4B0.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1754737498033413, "dur": 62, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/UnityEngine.UI.dll.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1754737498033483, "dur": 138, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/UnityEngine.UI.dll.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1754737498034499, "dur": 152, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\GameSoft\\Unity\\Editor\\2022.3.38f1c1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Runtime.InteropServices.RuntimeInformation.dll"}}, {"pid": 12345, "tid": 9, "ts": 1754737498033623, "dur": 2446, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/UnityEngine.UI.dll (+2 others)"}}, {"pid": 12345, "tid": 9, "ts": 1754737498036070, "dur": 302, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1754737498036399, "dur": 267, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1754737498036666, "dur": 222, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1754737498036888, "dur": 170, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1754737498037058, "dur": 171, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1754737498037229, "dur": 193, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1754737498037423, "dur": 175, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1754737498037598, "dur": 178, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1754737498037777, "dur": 262, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1754737498038039, "dur": 599, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1754737498038638, "dur": 200, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1754737498038838, "dur": 186, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1754737498039024, "dur": 173, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1754737498039197, "dur": 174, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1754737498039371, "dur": 189, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1754737498039595, "dur": 55, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1754737498039650, "dur": 224, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1754737498039874, "dur": 492, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1754737498040367, "dur": 72, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.Rider.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1754737498040450, "dur": 60, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.Rider.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1754737498040511, "dur": 357, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Rider.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 9, "ts": 1754737498040920, "dur": 117, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.TextMeshPro.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1754737498041054, "dur": 445, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.TextMeshPro.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 9, "ts": 1754737498041548, "dur": 132, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1754737498041686, "dur": 169, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1754737498041942, "dur": 193, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1754737498042136, "dur": 1108, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1754737498043244, "dur": 276482, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1754737498319728, "dur": 1504, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.RenderPipelines.Core.ShaderLibrary.dll (+pdb)"}}, {"pid": 12345, "tid": 9, "ts": 1754737498321273, "dur": 62, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.RenderPipelines.Core.ShaderLibrary.dll (+pdb)"}}, {"pid": 12345, "tid": 9, "ts": 1754737498321538, "dur": 547, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\GameSoft\\Unity\\Editor\\2022.3.38f1c1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Diagnostics.StackTrace.dll"}}, {"pid": 12345, "tid": 9, "ts": 1754737498321337, "dur": 2057, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Burst.dll (+pdb)"}}, {"pid": 12345, "tid": 9, "ts": 1754737498323394, "dur": 246, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1754737498323640, "dur": 791, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Burst.dll (+pdb)"}}, {"pid": 12345, "tid": 9, "ts": 1754737498325808, "dur": 86, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\GameSoft\\Unity\\Editor\\2022.3.38f1c1\\Editor\\Data\\Tools\\ilpp\\Unity.ILPP.Runner\\System.Private.CoreLib.dll"}}, {"pid": 12345, "tid": 9, "ts": 1754737498324785, "dur": 1805, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Collections.DocCodeSamples.dll (+pdb)"}}, {"pid": 12345, "tid": 9, "ts": 1754737498326684, "dur": 54201, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1754737498009013, "dur": 23500, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1754737498032524, "dur": 234, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\GameSoft\\Unity\\Editor\\2022.3.38f1c1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.VRModule.dll"}}, {"pid": 12345, "tid": 10, "ts": 1754737498032518, "dur": 244, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.VRModule.dll_2B6EE9DDEAEDE734.mvfrm"}}, {"pid": 12345, "tid": 10, "ts": 1754737498032969, "dur": 119, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ProfilerModule.dll_6E85C30F177A7F51.mvfrm"}}, {"pid": 12345, "tid": 10, "ts": 1754737498033092, "dur": 117, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.collections@1.2.4\\Unity.Collections.LowLevel.ILSupport\\Unity.Collections.LowLevel.ILSupport.dll"}}, {"pid": 12345, "tid": 10, "ts": 1754737498033090, "dur": 120, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.Collections.LowLevel.ILSupport.dll_2E40CE80E145E521.mvfrm"}}, {"pid": 12345, "tid": 10, "ts": 1754737498033353, "dur": 211, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.CompilationPipeline.Common.dll_824A65D7BB344A6A.mvfrm"}}, {"pid": 12345, "tid": 10, "ts": 1754737498033582, "dur": 224, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Searcher.Editor.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 10, "ts": 1754737498033848, "dur": 90, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Core.ShaderLibrary.rsp2"}}, {"pid": 12345, "tid": 10, "ts": 1754737498034024, "dur": 149, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/com.crashkonijn.goap.demos.simple.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 10, "ts": 1754737498034183, "dur": 186, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Assembly-CSharp.rsp2"}}, {"pid": 12345, "tid": 10, "ts": 1754737498034397, "dur": 144, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/8654950501350094070.rsp"}}, {"pid": 12345, "tid": 10, "ts": 1754737498034554, "dur": 118, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1754737498034679, "dur": 223, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1754737498034902, "dur": 194, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1754737498035096, "dur": 198, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1754737498035294, "dur": 199, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1754737498035494, "dur": 218, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1754737498035712, "dur": 179, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1754737498035892, "dur": 649, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1754737498036542, "dur": 235, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1754737498036777, "dur": 205, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1754737498036983, "dur": 245, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1754737498037228, "dur": 303, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1754737498037531, "dur": 453, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1754737498037984, "dur": 296, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1754737498038280, "dur": 197, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1754737498038477, "dur": 245, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1754737498038722, "dur": 212, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1754737498038934, "dur": 613, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1754737498039547, "dur": 63, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1754737498039610, "dur": 209, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1754737498039819, "dur": 466, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1754737498040286, "dur": 60, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/CrashKonijn.Agent.Core.dll.mvfrm"}}, {"pid": 12345, "tid": 10, "ts": 1754737498040383, "dur": 364, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/CrashKonijn.Agent.Core.dll (+2 others)"}}, {"pid": 12345, "tid": 10, "ts": 1754737498040785, "dur": 500, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/CrashKonijn.Goap.Core.dll (+2 others)"}}, {"pid": 12345, "tid": 10, "ts": 1754737498041356, "dur": 62, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1754737498041418, "dur": 90, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1754737498041523, "dur": 333, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1754737498041880, "dur": 52, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1754737498041933, "dur": 193, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1754737498042126, "dur": 312, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1754737498042439, "dur": 207, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/CrashKonijn.Goap.Resolver.dll (+2 others)"}}, {"pid": 12345, "tid": 10, "ts": 1754737498042647, "dur": 70, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1754737498042734, "dur": 404, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/CrashKonijn.Goap.Runtime.dll (+2 others)"}}, {"pid": 12345, "tid": 10, "ts": 1754737498043208, "dur": 336, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/CrashKonijn.Goap.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 10, "ts": 1754737498043581, "dur": 186, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/com.crashkonijn.goap.demos.simple.dll (+2 others)"}}, {"pid": 12345, "tid": 10, "ts": 1754737498043800, "dur": 276030, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1754737498319834, "dur": 1427, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.RenderPipelines.Universal.Shaders.dll (+pdb)"}}, {"pid": 12345, "tid": 10, "ts": 1754737498321262, "dur": 57, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1754737498321634, "dur": 115, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\GameSoft\\Unity\\Editor\\2022.3.38f1c1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Runtime.InteropServices.RuntimeInformation.dll"}}, {"pid": 12345, "tid": 10, "ts": 1754737498322831, "dur": 182, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\GameSoft\\Unity\\Editor\\2022.3.38f1c1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\System.Data.dll"}}, {"pid": 12345, "tid": 10, "ts": 1754737498321330, "dur": 1723, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.VisualScripting.Core.dll (+pdb)"}}, {"pid": 12345, "tid": 10, "ts": 1754737498323104, "dur": 1384, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.PlasticSCM.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 10, "ts": 1754737498324488, "dur": 54, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1754737498326030, "dur": 116, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\GameSoft\\Unity\\Editor\\2022.3.38f1c1\\Editor\\Data\\Managed\\UnityEngine\\UnityEditor.EditorToolbarModule.dll"}}, {"pid": 12345, "tid": 10, "ts": 1754737498324891, "dur": 1753, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.VisualScripting.Shared.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 10, "ts": 1754737498326713, "dur": 54170, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1754737498009047, "dur": 23476, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1754737498032532, "dur": 223, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\GameSoft\\Unity\\Editor\\2022.3.38f1c1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.WindModule.dll"}}, {"pid": 12345, "tid": 11, "ts": 1754737498032528, "dur": 228, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.WindModule.dll_504B268BBB0FC6D0.mvfrm"}}, {"pid": 12345, "tid": 11, "ts": 1754737498032756, "dur": 60, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1754737498032894, "dur": 53, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.GIModule.dll_75656E38889B442A.mvfrm"}}, {"pid": 12345, "tid": 11, "ts": 1754737498033069, "dur": 81, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1754737498033157, "dur": 56, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.visualscripting@1.9.4\\Editor\\VisualScripting.Core\\EditorAssetResources\\Unity.VisualScripting.TextureAssets.dll"}}, {"pid": 12345, "tid": 11, "ts": 1754737498033156, "dur": 60, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.VisualScripting.TextureAssets.dll_E1772EF4FEB70289.mvfrm"}}, {"pid": 12345, "tid": 11, "ts": 1754737498033315, "dur": 121, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Core.Runtime.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 11, "ts": 1754737498033756, "dur": 80, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.State.Editor.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 11, "ts": 1754737498033854, "dur": 98, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.ShaderGraph.ShaderGraphLibrary.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 11, "ts": 1754737498034022, "dur": 114, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/PPv2URPConverters.rsp2"}}, {"pid": 12345, "tid": 11, "ts": 1754737498034238, "dur": 114, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Collections.DocCodeSamples.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 11, "ts": 1754737498034378, "dur": 77, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/10010842633742469623.rsp"}}, {"pid": 12345, "tid": 11, "ts": 1754737498034514, "dur": 146, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1754737498034660, "dur": 90, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/2240406767038398906.rsp"}}, {"pid": 12345, "tid": 11, "ts": 1754737498034751, "dur": 213, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1754737498034964, "dur": 292, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1754737498035256, "dur": 168, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1754737498035425, "dur": 233, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1754737498035658, "dur": 183, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1754737498035842, "dur": 679, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1754737498036521, "dur": 171, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1754737498036693, "dur": 213, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1754737498036906, "dur": 389, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1754737498037296, "dur": 191, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1754737498037487, "dur": 185, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1754737498037672, "dur": 206, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1754737498037879, "dur": 264, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1754737498038143, "dur": 173, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1754737498038316, "dur": 190, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1754737498038506, "dur": 204, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1754737498038710, "dur": 195, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1754737498038905, "dur": 187, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1754737498039092, "dur": 168, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1754737498039260, "dur": 170, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1754737498039430, "dur": 250, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1754737498039680, "dur": 180, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1754737498039861, "dur": 507, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1754737498040370, "dur": 69, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Core.ShaderLibrary.dll.mvfrm"}}, {"pid": 12345, "tid": 11, "ts": 1754737498040474, "dur": 474, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\GameSoft\\Unity\\Editor\\2022.3.38f1c1\\Editor\\Data\\Managed\\UnityEditor.Graphs.dll"}}, {"pid": 12345, "tid": 11, "ts": 1754737498041147, "dur": 53, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\GameSoft\\Unity\\Editor\\2022.3.38f1c1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Linq.dll"}}, {"pid": 12345, "tid": 11, "ts": 1754737498040473, "dur": 980, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.VisualStudio.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 11, "ts": 1754737498041534, "dur": 324, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1754737498041859, "dur": 78, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1754737498041937, "dur": 187, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1754737498042125, "dur": 65, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Universal.Config.Runtime.dll.mvfrm"}}, {"pid": 12345, "tid": 11, "ts": 1754737498042204, "dur": 200, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Universal.Config.Runtime.dll (+2 others)"}}, {"pid": 12345, "tid": 11, "ts": 1754737498042472, "dur": 54, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Universal.Runtime.dll.mvfrm"}}, {"pid": 12345, "tid": 11, "ts": 1754737498042537, "dur": 365, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Universal.Runtime.dll (+2 others)"}}, {"pid": 12345, "tid": 11, "ts": 1754737498042976, "dur": 62, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Cinemachine.dll.mvfrm"}}, {"pid": 12345, "tid": 11, "ts": 1754737498043094, "dur": 319, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Cinemachine.dll (+2 others)"}}, {"pid": 12345, "tid": 11, "ts": 1754737498043446, "dur": 276292, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1754737498319740, "dur": 1500, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.RenderPipelines.ShaderGraph.ShaderGraphLibrary.dll (+pdb)"}}, {"pid": 12345, "tid": 11, "ts": 1754737498321636, "dur": 113, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\GameSoft\\Unity\\Editor\\2022.3.38f1c1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.ComponentModel.Annotations.dll"}}, {"pid": 12345, "tid": 11, "ts": 1754737498321293, "dur": 1529, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.VisualScripting.Flow.dll (+pdb)"}}, {"pid": 12345, "tid": 11, "ts": 1754737498322823, "dur": 185, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1754737498323017, "dur": 1486, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.RenderPipelines.Core.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 11, "ts": 1754737498324504, "dur": 60, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1754737498325343, "dur": 92, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\GameSoft\\Unity\\Editor\\2022.3.38f1c1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Security.Cryptography.Encoding.dll"}}, {"pid": 12345, "tid": 11, "ts": 1754737498325624, "dur": 71, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\GameSoft\\Unity\\Editor\\2022.3.38f1c1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Runtime.dll"}}, {"pid": 12345, "tid": 11, "ts": 1754737498325109, "dur": 1767, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.VisualScripting.Flow.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 11, "ts": 1754737498326922, "dur": 53998, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1754737498009076, "dur": 23467, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1754737498032575, "dur": 247, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\GameSoft\\Unity\\Editor\\2022.3.38f1c1\\Editor\\Data\\Managed\\UnityEngine\\UnityEditor.CoreModule.dll"}}, {"pid": 12345, "tid": 12, "ts": 1754737498032559, "dur": 266, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.CoreModule.dll_B6DB781081D3DD3C.mvfrm"}}, {"pid": 12345, "tid": 12, "ts": 1754737498032952, "dur": 150, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.PerformanceReportingModule.dll_A00BE41311F50426.mvfrm"}}, {"pid": 12345, "tid": 12, "ts": 1754737498033106, "dur": 73, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.visualscripting@1.9.4\\Editor\\VisualScripting.Core\\Dependencies\\YamlDotNet\\Unity.VisualScripting.YamlDotNet.dll"}}, {"pid": 12345, "tid": 12, "ts": 1754737498033105, "dur": 75, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.VisualScripting.YamlDotNet.dll_4A5905A13DE9A491.mvfrm"}}, {"pid": 12345, "tid": 12, "ts": 1754737498033191, "dur": 120, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.VisualScripting.YamlDotNet.dll_4A5905A13DE9A491.mvfrm"}}, {"pid": 12345, "tid": 12, "ts": 1754737498033451, "dur": 76, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Universal.Config.Runtime.rsp2"}}, {"pid": 12345, "tid": 12, "ts": 1754737498033578, "dur": 63, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.State.rsp2"}}, {"pid": 12345, "tid": 12, "ts": 1754737498033840, "dur": 60, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Universal.Shaders.rsp2"}}, {"pid": 12345, "tid": 12, "ts": 1754737498033915, "dur": 190, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.InputSystem.ForUI.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 12, "ts": 1754737498034272, "dur": 144, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.CollabProxy.Editor.rsp"}}, {"pid": 12345, "tid": 12, "ts": 1754737498034497, "dur": 147, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1754737498034677, "dur": 204, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1754737498034881, "dur": 184, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1754737498035065, "dur": 205, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1754737498035270, "dur": 272, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1754737498035542, "dur": 483, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1754737498036086, "dur": 642, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.visualscripting@1.9.4\\Runtime\\VisualScripting.State\\StateTransition.cs"}}, {"pid": 12345, "tid": 12, "ts": 1754737498036026, "dur": 835, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1754737498036862, "dur": 165, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1754737498037044, "dur": 217, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1754737498037262, "dur": 190, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1754737498037453, "dur": 209, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1754737498037662, "dur": 201, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1754737498037863, "dur": 161, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1754737498038024, "dur": 194, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1754737498038218, "dur": 189, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1754737498038407, "dur": 193, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1754737498038600, "dur": 199, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1754737498038799, "dur": 180, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1754737498038979, "dur": 205, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1754737498039184, "dur": 186, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1754737498039370, "dur": 187, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1754737498039635, "dur": 183, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1754737498039818, "dur": 468, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1754737498040287, "dur": 65, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/CrashKonijn.Goap.Core.dll.mvfrm"}}, {"pid": 12345, "tid": 12, "ts": 1754737498040386, "dur": 76, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Universal.Shaders.dll.mvfrm"}}, {"pid": 12345, "tid": 12, "ts": 1754737498040490, "dur": 335, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Universal.Shaders.dll (+2 others)"}}, {"pid": 12345, "tid": 12, "ts": 1754737498040825, "dur": 123, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1754737498040952, "dur": 144, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/com.crashkonijn.goap.demos.complex.dll.mvfrm"}}, {"pid": 12345, "tid": 12, "ts": 1754737498041096, "dur": 60, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1754737498041229, "dur": 173, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Tests.dll.mvfrm"}}, {"pid": 12345, "tid": 12, "ts": 1754737498041417, "dur": 242, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Tests.dll (+2 others)"}}, {"pid": 12345, "tid": 12, "ts": 1754737498041705, "dur": 161, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1754737498041866, "dur": 67, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1754737498041933, "dur": 195, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1754737498042129, "dur": 1103, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1754737498043232, "dur": 690, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1754737498043923, "dur": 61, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.SettingsProvider.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 12, "ts": 1754737498044000, "dur": 180, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.SettingsProvider.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 12, "ts": 1754737498044219, "dur": 275517, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1754737498319742, "dur": 1488, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/com.crashkonijn.goap.demos.simple.dll (+pdb)"}}, {"pid": 12345, "tid": 12, "ts": 1754737498322981, "dur": 134, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.collab-proxy@2.8.2\\Lib\\Editor\\log4netPlastic.dll"}}, {"pid": 12345, "tid": 12, "ts": 1754737498321305, "dur": 1841, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/com.crashkonijn.docs.getting_started.dll (+pdb)"}}, {"pid": 12345, "tid": 12, "ts": 1754737498323147, "dur": 72, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1754737498323227, "dur": 1369, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.VisualScripting.Core.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 12, "ts": 1754737498325411, "dur": 117, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\Bee\\artifacts\\1900b0aE.dag\\post-processed\\com.crashkonijn.goap.demos.complex.dll"}}, {"pid": 12345, "tid": 12, "ts": 1754737498325410, "dur": 119, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "CopyFiles Library/ScriptAssemblies/com.crashkonijn.goap.demos.complex.dll"}}, {"pid": 12345, "tid": 12, "ts": 1754737498325529, "dur": 83, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1754737498325758, "dur": 63, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1754737498325844, "dur": 62, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\Bee\\artifacts\\1900b0aE.dag\\post-processed\\Unity.RenderPipelines.Universal.Editor.dll"}}, {"pid": 12345, "tid": 12, "ts": 1754737498325843, "dur": 63, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.RenderPipelines.Universal.Editor.dll"}}, {"pid": 12345, "tid": 12, "ts": 1754737498325994, "dur": 119, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1754737498326145, "dur": 73, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\Bee\\artifacts\\1900b0aE.dag\\post-processed\\Tests.pdb"}}, {"pid": 12345, "tid": 12, "ts": 1754737498326145, "dur": 73, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Tests.pdb"}}, {"pid": 12345, "tid": 12, "ts": 1754737498326219, "dur": 88, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1754737498326316, "dur": 324, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1754737498326641, "dur": 106, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1754737498326760, "dur": 54235, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1754737498009107, "dur": 23452, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1754737498032570, "dur": 188, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\GameSoft\\Unity\\Editor\\2022.3.38f1c1\\Editor\\Data\\Managed\\UnityEngine\\UnityEditor.DeviceSimulatorModule.dll"}}, {"pid": 12345, "tid": 13, "ts": 1754737498032564, "dur": 196, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.DeviceSimulatorModule.dll_D2B66E0C49661945.mvfrm"}}, {"pid": 12345, "tid": 13, "ts": 1754737498032988, "dur": 71, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.SpriteShapeModule.dll_1B8FAEC3513ADBA8.mvfrm"}}, {"pid": 12345, "tid": 13, "ts": 1754737498033063, "dur": 82, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\GameSoft\\Unity\\Editor\\2022.3.38f1c1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.UnityWebRequestTextureModule.dll"}}, {"pid": 12345, "tid": 13, "ts": 1754737498033061, "dur": 86, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityWebRequestTextureModule.dll_3AB3AEE512D0C853.mvfrm"}}, {"pid": 12345, "tid": 13, "ts": 1754737498033175, "dur": 229, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityWebRequestTextureModule.dll_3AB3AEE512D0C853.mvfrm"}}, {"pid": 12345, "tid": 13, "ts": 1754737498033416, "dur": 53, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/UnityEngine.TestRunner.dll.mvfrm"}}, {"pid": 12345, "tid": 13, "ts": 1754737498034493, "dur": 139, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\GameSoft\\Unity\\Editor\\2022.3.38f1c1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Runtime.InteropServices.dll"}}, {"pid": 12345, "tid": 13, "ts": 1754737498034951, "dur": 91, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\GameSoft\\Unity\\Editor\\2022.3.38f1c1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.ValueTuple.dll"}}, {"pid": 12345, "tid": 13, "ts": 1754737498035062, "dur": 93, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.ext.nunit@1.0.6\\net35\\unity-custom\\nunit.framework.dll"}}, {"pid": 12345, "tid": 13, "ts": 1754737498036081, "dur": 513, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.test-framework@1.1.33\\UnityEngine.TestRunner\\TestRunner\\SynchronousFilter.cs"}}, {"pid": 12345, "tid": 13, "ts": 1754737498033521, "dur": 3445, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/UnityEngine.TestRunner.dll (+2 others)"}}, {"pid": 12345, "tid": 13, "ts": 1754737498037022, "dur": 83, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/UnityEditor.TestRunner.dll.mvfrm"}}, {"pid": 12345, "tid": 13, "ts": 1754737498037348, "dur": 150, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\GameSoft\\Unity\\Editor\\2022.3.38f1c1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.ComponentModel.Annotations.dll"}}, {"pid": 12345, "tid": 13, "ts": 1754737498039394, "dur": 63, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.test-framework@1.1.33\\UnityEditor.TestRunner\\GUI\\Views\\EditModeTestListGUI.cs"}}, {"pid": 12345, "tid": 13, "ts": 1754737498037117, "dur": 2644, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/UnityEditor.TestRunner.dll (+2 others)"}}, {"pid": 12345, "tid": 13, "ts": 1754737498039955, "dur": 146, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\GameSoft\\Unity\\Editor\\2022.3.38f1c1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.ComponentModel.Primitives.dll"}}, {"pid": 12345, "tid": 13, "ts": 1754737498039888, "dur": 330, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/UnityEditor.UI.dll (+2 others)"}}, {"pid": 12345, "tid": 13, "ts": 1754737498040298, "dur": 88, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.Burst.dll.mvfrm"}}, {"pid": 12345, "tid": 13, "ts": 1754737498040418, "dur": 83, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.Burst.dll.mvfrm"}}, {"pid": 12345, "tid": 13, "ts": 1754737498040502, "dur": 392, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Burst.dll (+2 others)"}}, {"pid": 12345, "tid": 13, "ts": 1754737498040895, "dur": 478, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1754737498041418, "dur": 78, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.Burst.CodeGen.dll.mvfrm"}}, {"pid": 12345, "tid": 13, "ts": 1754737498041522, "dur": 278, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Burst.CodeGen.dll (+2 others)"}}, {"pid": 12345, "tid": 13, "ts": 1754737498041855, "dur": 416, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPP-Configuration Library/ilpp-configuration.nevergeneratedoutput"}}, {"pid": 12345, "tid": 13, "ts": 1754737498042313, "dur": 70, "ph": "X", "name": "StoreTimestampsOfNonGeneratedInputFiles", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1754737498043014, "dur": 274412, "ph": "X", "name": "ILPP-Configuration", "args": {"detail": "Library/ilpp-configuration.nevergeneratedoutput"}}, {"pid": 12345, "tid": 13, "ts": 1754737498319728, "dur": 1510, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/com.crashkonijn.goap.demos.complex.dll (+pdb)"}}, {"pid": 12345, "tid": 13, "ts": 1754737498321269, "dur": 81, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/com.crashkonijn.goap.demos.complex.dll (+pdb)"}}, {"pid": 12345, "tid": 13, "ts": 1754737498321636, "dur": 113, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\GameSoft\\Unity\\Editor\\2022.3.38f1c1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.ObjectModel.dll"}}, {"pid": 12345, "tid": 13, "ts": 1754737498321352, "dur": 1616, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Mathematics.dll (+pdb)"}}, {"pid": 12345, "tid": 13, "ts": 1754737498323010, "dur": 1363, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.VisualScripting.SettingsProvider.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 13, "ts": 1754737498326140, "dur": 70, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\GameSoft\\Unity\\Editor\\2022.3.38f1c1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Reflection.Emit.ILGeneration.dll"}}, {"pid": 12345, "tid": 13, "ts": 1754737498324738, "dur": 1636, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/PPv2URPConverters.dll (+pdb)"}}, {"pid": 12345, "tid": 13, "ts": 1754737498326375, "dur": 263, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1754737498326691, "dur": 54204, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1754737498009136, "dur": 23433, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1754737498032581, "dur": 242, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\GameSoft\\Unity\\Editor\\2022.3.38f1c1\\Editor\\Data\\Managed\\UnityEngine\\UnityEditor.DiagnosticsModule.dll"}}, {"pid": 12345, "tid": 14, "ts": 1754737498032575, "dur": 250, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.DiagnosticsModule.dll_3335CD69E05AF253.mvfrm"}}, {"pid": 12345, "tid": 14, "ts": 1754737498032835, "dur": 53, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.DiagnosticsModule.dll_3335CD69E05AF253.mvfrm"}}, {"pid": 12345, "tid": 14, "ts": 1754737498033065, "dur": 83, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\GameSoft\\Unity\\Editor\\2022.3.38f1c1\\Editor\\Data\\Managed\\UnityEditor.Graphs.dll"}}, {"pid": 12345, "tid": 14, "ts": 1754737498033064, "dur": 88, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.Graphs.dll_34C604C212D62B6E.mvfrm"}}, {"pid": 12345, "tid": 14, "ts": 1754737498033195, "dur": 50, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/UnityEngine.UI.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 14, "ts": 1754737498033345, "dur": 235, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.Burst.Cecil.Rocks.dll_8573C5AEBE7FAC0E.mvfrm"}}, {"pid": 12345, "tid": 14, "ts": 1754737498033684, "dur": 442, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.State.rsp"}}, {"pid": 12345, "tid": 14, "ts": 1754737498034458, "dur": 98, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/10571807241835812913.rsp"}}, {"pid": 12345, "tid": 14, "ts": 1754737498034572, "dur": 123, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1754737498034701, "dur": 206, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1754737498034907, "dur": 220, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1754737498035127, "dur": 227, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1754737498035354, "dur": 233, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1754737498035588, "dur": 193, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1754737498035781, "dur": 171, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1754737498035952, "dur": 545, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1754737498036498, "dur": 201, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1754737498036699, "dur": 233, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1754737498036932, "dur": 246, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1754737498037179, "dur": 211, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1754737498037390, "dur": 204, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1754737498037595, "dur": 643, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1754737498038238, "dur": 178, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1754737498038416, "dur": 169, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1754737498038585, "dur": 203, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1754737498038789, "dur": 188, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1754737498038977, "dur": 191, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1754737498039168, "dur": 174, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1754737498039342, "dur": 331, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1754737498039673, "dur": 177, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1754737498039851, "dur": 450, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1754737498040307, "dur": 92, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.VSCode.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 14, "ts": 1754737498040413, "dur": 55, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.VSCode.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 14, "ts": 1754737498040698, "dur": 133, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\GameSoft\\Unity\\Editor\\2022.3.38f1c1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Diagnostics.Tools.dll"}}, {"pid": 12345, "tid": 14, "ts": 1754737498040881, "dur": 79, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\GameSoft\\Unity\\Editor\\2022.3.38f1c1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Net.Sockets.dll"}}, {"pid": 12345, "tid": 14, "ts": 1754737498040469, "dur": 713, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.VSCode.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 14, "ts": 1754737498041182, "dur": 117, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1754737498041359, "dur": 56, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1754737498041416, "dur": 76, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.Collections.dll.mvfrm"}}, {"pid": 12345, "tid": 14, "ts": 1754737498041504, "dur": 346, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Collections.dll (+2 others)"}}, {"pid": 12345, "tid": 14, "ts": 1754737498041931, "dur": 51, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/CrashKonijn.Goap.Resolver.dll.mvfrm"}}, {"pid": 12345, "tid": 14, "ts": 1754737498041983, "dur": 449, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1754737498042437, "dur": 65, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Core.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 14, "ts": 1754737498042525, "dur": 871, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Core.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 14, "ts": 1754737498043396, "dur": 51, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1754737498043470, "dur": 276280, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1754737498319987, "dur": 53, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\GameSoft\\Unity\\Editor\\2022.3.38f1c1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.XRModule.dll"}}, {"pid": 12345, "tid": 14, "ts": 1754737498319752, "dur": 1507, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.InputSystem.ForUI.dll (+pdb)"}}, {"pid": 12345, "tid": 14, "ts": 1754737498321536, "dur": 53, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\GameSoft\\Unity\\Editor\\2022.3.38f1c1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Memory.dll"}}, {"pid": 12345, "tid": 14, "ts": 1754737498321633, "dur": 116, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\GameSoft\\Unity\\Editor\\2022.3.38f1c1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Security.Cryptography.Algorithms.dll"}}, {"pid": 12345, "tid": 14, "ts": 1754737498321950, "dur": 84, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\GameSoft\\Unity\\Editor\\2022.3.38f1c1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.ServiceModel.Primitives.dll"}}, {"pid": 12345, "tid": 14, "ts": 1754737498321307, "dur": 1760, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.InputSystem.TestFramework.dll (+pdb)"}}, {"pid": 12345, "tid": 14, "ts": 1754737498323107, "dur": 1429, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.CollabProxy.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 14, "ts": 1754737498325340, "dur": 188, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\GameSoft\\Unity\\Editor\\2022.3.38f1c1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.UnityAnalyticsModule.dll"}}, {"pid": 12345, "tid": 14, "ts": 1754737498325623, "dur": 63, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\GameSoft\\Unity\\Editor\\2022.3.38f1c1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.ComponentModel.dll"}}, {"pid": 12345, "tid": 14, "ts": 1754737498326030, "dur": 52, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\GameSoft\\Unity\\Editor\\2022.3.38f1c1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Collections.NonGeneric.dll"}}, {"pid": 12345, "tid": 14, "ts": 1754737498325255, "dur": 1745, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.ShaderGraph.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 14, "ts": 1754737498327037, "dur": 53898, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1754737498009173, "dur": 23408, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1754737498032590, "dur": 236, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\GameSoft\\Unity\\Editor\\2022.3.38f1c1\\Editor\\Data\\Managed\\UnityEngine\\UnityEditor.dll"}}, {"pid": 12345, "tid": 15, "ts": 1754737498032587, "dur": 240, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.dll_A3CA169B49A7C799.mvfrm"}}, {"pid": 12345, "tid": 15, "ts": 1754737498033053, "dur": 76, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\GameSoft\\Unity\\Editor\\2022.3.38f1c1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.UnityWebRequestModule.dll"}}, {"pid": 12345, "tid": 15, "ts": 1754737498033052, "dur": 79, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityWebRequestModule.dll_55F435D8463A793C.mvfrm"}}, {"pid": 12345, "tid": 15, "ts": 1754737498033257, "dur": 52, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Burst.rsp2"}}, {"pid": 12345, "tid": 15, "ts": 1754737498033351, "dur": 113, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.Burst.Cecil.Pdb.dll_99AE285BDA61FA2E.mvfrm"}}, {"pid": 12345, "tid": 15, "ts": 1754737498033465, "dur": 51, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Flow.rsp"}}, {"pid": 12345, "tid": 15, "ts": 1754737498033543, "dur": 134, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Universal.Runtime.rsp2"}}, {"pid": 12345, "tid": 15, "ts": 1754737498033716, "dur": 75, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Mathematics.Editor.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 15, "ts": 1754737498033993, "dur": 155, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Universal.Shaders.rsp"}}, {"pid": 12345, "tid": 15, "ts": 1754737498034202, "dur": 108, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Assembly-CSharp.rsp"}}, {"pid": 12345, "tid": 15, "ts": 1754737498034423, "dur": 95, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/97894721033061733.rsp"}}, {"pid": 12345, "tid": 15, "ts": 1754737498034534, "dur": 150, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1754737498034688, "dur": 217, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1754737498034905, "dur": 180, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1754737498035086, "dur": 206, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1754737498035292, "dur": 167, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1754737498035459, "dur": 300, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1754737498035759, "dur": 191, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1754737498035950, "dur": 902, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1754737498036852, "dur": 164, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1754737498037032, "dur": 195, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1754737498037227, "dur": 201, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1754737498037429, "dur": 173, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1754737498037602, "dur": 169, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1754737498037771, "dur": 228, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1754737498037999, "dur": 269, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1754737498038268, "dur": 175, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1754737498038443, "dur": 318, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1754737498038762, "dur": 224, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1754737498038987, "dur": 188, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1754737498039176, "dur": 205, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1754737498039437, "dur": 212, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1754737498039650, "dur": 165, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1754737498039831, "dur": 453, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1754737498040285, "dur": 61, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.Mathematics.dll.mvfrm"}}, {"pid": 12345, "tid": 15, "ts": 1754737498040347, "dur": 50, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1754737498040443, "dur": 403, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Mathematics.dll (+2 others)"}}, {"pid": 12345, "tid": 15, "ts": 1754737498040912, "dur": 121, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.Mathematics.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 15, "ts": 1754737498041051, "dur": 387, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Mathematics.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 15, "ts": 1754737498041439, "dur": 418, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1754737498041880, "dur": 50, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1754737498041930, "dur": 74, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.Collections.DocCodeSamples.dll.mvfrm"}}, {"pid": 12345, "tid": 15, "ts": 1754737498042034, "dur": 244, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Collections.DocCodeSamples.dll (+2 others)"}}, {"pid": 12345, "tid": 15, "ts": 1754737498042279, "dur": 134, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1754737498042422, "dur": 810, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1754737498043233, "dur": 1190, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1754737498044424, "dur": 64, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/PPv2URPConverters.dll.mvfrm"}}, {"pid": 12345, "tid": 15, "ts": 1754737498044516, "dur": 232, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/PPv2URPConverters.dll (+2 others)"}}, {"pid": 12345, "tid": 15, "ts": 1754737498044788, "dur": 244, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Assembly-CSharp.dll (+2 others)"}}, {"pid": 12345, "tid": 15, "ts": 1754737498045111, "dur": 334182, "ph": "X", "name": "Csc", "args": {"detail": "Library/Bee/artifacts/1900b0aE.dag/Assembly-CSharp.dll (+2 others)"}}, {"pid": 12345, "tid": 16, "ts": 1754737498009206, "dur": 23384, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1754737498032596, "dur": 224, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\GameSoft\\Unity\\Editor\\2022.3.38f1c1\\Editor\\Data\\Managed\\UnityEngine\\UnityEditor.EditorToolbarModule.dll"}}, {"pid": 12345, "tid": 16, "ts": 1754737498032591, "dur": 231, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.EditorToolbarModule.dll_F4EF6CA575FE5075.mvfrm"}}, {"pid": 12345, "tid": 16, "ts": 1754737498033013, "dur": 63, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.TerrainPhysicsModule.dll_34719EFEEA08287A.mvfrm"}}, {"pid": 12345, "tid": 16, "ts": 1754737498033101, "dur": 79, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1754737498033180, "dur": 301, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/UnityEngine.TestRunner.rsp"}}, {"pid": 12345, "tid": 16, "ts": 1754737498033617, "dur": 50, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Searcher.Editor.rsp"}}, {"pid": 12345, "tid": 16, "ts": 1754737498033819, "dur": 50, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Universal.Editor.rsp"}}, {"pid": 12345, "tid": 16, "ts": 1754737498033926, "dur": 73, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualStudio.Editor.rsp"}}, {"pid": 12345, "tid": 16, "ts": 1754737498034037, "dur": 292, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/com.unity.cinemachine.editor.rsp2"}}, {"pid": 12345, "tid": 16, "ts": 1754737498034419, "dur": 74, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/13457449390435613332.rsp"}}, {"pid": 12345, "tid": 16, "ts": 1754737498034518, "dur": 149, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1754737498034667, "dur": 74, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/4772145154433932943.rsp"}}, {"pid": 12345, "tid": 16, "ts": 1754737498034749, "dur": 210, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1754737498034959, "dur": 239, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1754737498035199, "dur": 198, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1754737498035397, "dur": 228, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1754737498035626, "dur": 166, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1754737498035792, "dur": 164, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1754737498035956, "dur": 607, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1754737498036564, "dur": 206, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1754737498036770, "dur": 182, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1754737498036952, "dur": 179, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1754737498037153, "dur": 663, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.shadergraph@14.0.11\\Editor\\Data\\Graphs\\GradientShaderProperty.cs"}}, {"pid": 12345, "tid": 16, "ts": 1754737498037131, "dur": 923, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1754737498038055, "dur": 237, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1754737498038292, "dur": 162, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1754737498038454, "dur": 181, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1754737498038635, "dur": 178, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1754737498038813, "dur": 294, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1754737498039108, "dur": 172, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1754737498039281, "dur": 224, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1754737498039505, "dur": 62, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1754737498039595, "dur": 50, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1754737498039645, "dur": 237, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1754737498039882, "dur": 410, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1754737498040293, "dur": 96, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/CrashKonijn.Agent.Runtime.dll.mvfrm"}}, {"pid": 12345, "tid": 16, "ts": 1754737498040390, "dur": 103, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1754737498040497, "dur": 73, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/com.crashkonijn.docs.getting_started.dll.mvfrm"}}, {"pid": 12345, "tid": 16, "ts": 1754737498040584, "dur": 70, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/GlassSystem.dll.mvfrm"}}, {"pid": 12345, "tid": 16, "ts": 1754737498040655, "dur": 313, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1754737498041021, "dur": 136, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\GameSoft\\Unity\\Editor\\2022.3.38f1c1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.JSONSerializeModule.dll"}}, {"pid": 12345, "tid": 16, "ts": 1754737498040973, "dur": 460, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/GlassSystem.dll (+2 others)"}}, {"pid": 12345, "tid": 16, "ts": 1754737498041540, "dur": 319, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1754737498041860, "dur": 72, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1754737498041932, "dur": 195, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1754737498042127, "dur": 894, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1754737498043022, "dur": 116, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/URPWizard.dll.mvfrm"}}, {"pid": 12345, "tid": 16, "ts": 1754737498043159, "dur": 254, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/URPWizard.dll (+2 others)"}}, {"pid": 12345, "tid": 16, "ts": 1754737498043413, "dur": 51, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1754737498043467, "dur": 276631, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1754737498320101, "dur": 1408, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.RenderPipelines.Universal.Runtime.dll (+pdb)"}}, {"pid": 12345, "tid": 16, "ts": 1754737498321509, "dur": 58, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1754737498323122, "dur": 527, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\GameSoft\\Unity\\Editor\\2022.3.38f1c1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Resources.ResourceManager.dll"}}, {"pid": 12345, "tid": 16, "ts": 1754737498321580, "dur": 2165, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Timeline.dll (+pdb)"}}, {"pid": 12345, "tid": 16, "ts": 1754737498323745, "dur": 185, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1754737498324512, "dur": 97, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\GameSoft\\Unity\\Editor\\2022.3.38f1c1\\Editor\\Data\\Tools\\ilpp\\Unity.ILPP.Runner\\Microsoft.AspNetCore.Http.Connections.Common.dll"}}, {"pid": 12345, "tid": 16, "ts": 1754737498323937, "dur": 1358, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.ShaderGraph.Utilities.dll (+pdb)"}}, {"pid": 12345, "tid": 16, "ts": 1754737498325837, "dur": 71, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\GameSoft\\Unity\\Editor\\2022.3.38f1c1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Threading.Tasks.Parallel.dll"}}, {"pid": 12345, "tid": 16, "ts": 1754737498325333, "dur": 1712, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Mathematics.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 16, "ts": 1754737498327090, "dur": 53838, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1754737498387341, "dur": 1522, "ph": "X", "name": "ProfilerWriteOutput"}, {"pid": 3100, "tid": 24783, "ts": 1754737498389369, "dur": 1745, "ph": "X", "name": "Wait for external events", "args": {"First to finish": "backend1.traceevents"}}, {"pid": 3100, "tid": 24783, "ts": 1754737498393332, "dur": 830, "ph": "X", "name": "backend1.traceevents", "args": {}}, {"pid": 3100, "tid": 24783, "ts": 1754737498388267, "dur": 5927, "ph": "X", "name": "Write chrome-trace events", "args": {}}, {}]}