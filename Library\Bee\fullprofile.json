{"Instructions Readme": "1) Open Chrome or Edge, 2) go to chrome://tracing, 3) click Load, 4) navigate to this file.", "processInfo": {}, "traceEvents": [{"pid": 3100, "tid": -1, "ph": "M", "name": "process_name", "args": {"name": "BeeDriver"}}, {"pid": 3100, "tid": -1, "ph": "M", "name": "process_sort_index", "args": {"sort_index": "-2"}}, {"pid": 3100, "tid": 24780, "ph": "M", "name": "thread_name", "args": {"name": "Thread Pool Worker"}}, {"pid": 3100, "tid": 24780, "ts": 1754735215673205, "dur": 490, "ph": "X", "name": "ChromeTraceHeader", "args": {}}, {"pid": 3100, "tid": 24780, "ts": 1754735215676049, "dur": 461, "ph": "X", "name": "Thread Pool Worker", "args": {}}, {"pid": 3100, "tid": 21474836480, "ph": "M", "name": "thread_name", "args": {"name": "ReadEntireBinlogFromIpcAsync"}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735214493223, "dur": 134712, "ph": "X", "name": "WaitForConnectionAsync", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735214627937, "dur": 1041098, "ph": "X", "name": "UpdateFromStreamAsync", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735214627953, "dur": 46, "ph": "X", "name": "ReadAsync 0", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735214628001, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735214628003, "dur": 103048, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735214731067, "dur": 8, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735214731079, "dur": 77, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735214731160, "dur": 7, "ph": "X", "name": "ProcessMessages 41", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735214731168, "dur": 2117, "ph": "X", "name": "ReadAsync 41", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735214733296, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735214733299, "dur": 73, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735214733377, "dur": 2, "ph": "X", "name": "ProcessMessages 728", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735214733381, "dur": 58, "ph": "X", "name": "ReadAsync 728", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735214733442, "dur": 1, "ph": "X", "name": "ProcessMessages 728", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735214733444, "dur": 39, "ph": "X", "name": "ReadAsync 728", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735214733486, "dur": 1, "ph": "X", "name": "ProcessMessages 530", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735214733489, "dur": 53, "ph": "X", "name": "ReadAsync 530", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735214733545, "dur": 1, "ph": "X", "name": "ProcessMessages 351", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735214733546, "dur": 38, "ph": "X", "name": "ReadAsync 351", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735214733587, "dur": 26, "ph": "X", "name": "ReadAsync 479", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735214733615, "dur": 1, "ph": "X", "name": "ProcessMessages 440", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735214733616, "dur": 23, "ph": "X", "name": "ReadAsync 440", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735214733641, "dur": 28, "ph": "X", "name": "ReadAsync 126", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735214733671, "dur": 1, "ph": "X", "name": "ProcessMessages 336", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735214733673, "dur": 26, "ph": "X", "name": "ReadAsync 336", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735214733701, "dur": 27, "ph": "X", "name": "ReadAsync 239", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735214733730, "dur": 26, "ph": "X", "name": "ReadAsync 489", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735214733758, "dur": 27, "ph": "X", "name": "ReadAsync 356", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735214733787, "dur": 17, "ph": "X", "name": "ReadAsync 422", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735214733806, "dur": 53, "ph": "X", "name": "ReadAsync 21", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735214733862, "dur": 27, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735214733890, "dur": 26, "ph": "X", "name": "ReadAsync 346", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735214733919, "dur": 25, "ph": "X", "name": "ReadAsync 399", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735214733946, "dur": 26, "ph": "X", "name": "ReadAsync 439", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735214733974, "dur": 25, "ph": "X", "name": "ReadAsync 443", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735214734001, "dur": 26, "ph": "X", "name": "ReadAsync 352", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735214734032, "dur": 29, "ph": "X", "name": "ReadAsync 409", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735214734063, "dur": 17, "ph": "X", "name": "ReadAsync 580", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735214734082, "dur": 26, "ph": "X", "name": "ReadAsync 138", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735214734110, "dur": 27, "ph": "X", "name": "ReadAsync 290", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735214734139, "dur": 27, "ph": "X", "name": "ReadAsync 489", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735214734168, "dur": 17, "ph": "X", "name": "ReadAsync 471", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735214734187, "dur": 29, "ph": "X", "name": "ReadAsync 139", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735214734219, "dur": 27, "ph": "X", "name": "ReadAsync 287", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735214734248, "dur": 26, "ph": "X", "name": "ReadAsync 594", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735214734276, "dur": 28, "ph": "X", "name": "ReadAsync 142", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735214734305, "dur": 31, "ph": "X", "name": "ReadAsync 254", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735214734338, "dur": 27, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735214734367, "dur": 26, "ph": "X", "name": "ReadAsync 339", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735214734395, "dur": 26, "ph": "X", "name": "ReadAsync 313", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735214734424, "dur": 29, "ph": "X", "name": "ReadAsync 341", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735214734455, "dur": 25, "ph": "X", "name": "ReadAsync 567", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735214734482, "dur": 29, "ph": "X", "name": "ReadAsync 363", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735214734513, "dur": 27, "ph": "X", "name": "ReadAsync 408", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735214734542, "dur": 25, "ph": "X", "name": "ReadAsync 497", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735214734569, "dur": 24, "ph": "X", "name": "ReadAsync 343", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735214734595, "dur": 25, "ph": "X", "name": "ReadAsync 468", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735214734623, "dur": 26, "ph": "X", "name": "ReadAsync 351", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735214734652, "dur": 27, "ph": "X", "name": "ReadAsync 477", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735214734680, "dur": 28, "ph": "X", "name": "ReadAsync 467", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735214734710, "dur": 26, "ph": "X", "name": "ReadAsync 454", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735214734738, "dur": 26, "ph": "X", "name": "ReadAsync 352", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735214734767, "dur": 29, "ph": "X", "name": "ReadAsync 89", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735214734798, "dur": 27, "ph": "X", "name": "ReadAsync 690", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735214734827, "dur": 26, "ph": "X", "name": "ReadAsync 454", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735214734855, "dur": 27, "ph": "X", "name": "ReadAsync 333", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735214734884, "dur": 25, "ph": "X", "name": "ReadAsync 496", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735214734912, "dur": 27, "ph": "X", "name": "ReadAsync 351", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735214734940, "dur": 1, "ph": "X", "name": "ProcessMessages 530", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735214734942, "dur": 25, "ph": "X", "name": "ReadAsync 530", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735214734969, "dur": 25, "ph": "X", "name": "ReadAsync 356", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735214734996, "dur": 26, "ph": "X", "name": "ReadAsync 494", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735214735024, "dur": 25, "ph": "X", "name": "ReadAsync 490", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735214735051, "dur": 27, "ph": "X", "name": "ReadAsync 364", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735214735080, "dur": 28, "ph": "X", "name": "ReadAsync 437", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735214735110, "dur": 22, "ph": "X", "name": "ReadAsync 457", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735214735134, "dur": 78, "ph": "X", "name": "ReadAsync 212", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735214735213, "dur": 25, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735214735239, "dur": 28, "ph": "X", "name": "ReadAsync 353", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735214735269, "dur": 25, "ph": "X", "name": "ReadAsync 434", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735214735296, "dur": 18, "ph": "X", "name": "ReadAsync 365", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735214735316, "dur": 29, "ph": "X", "name": "ReadAsync 465", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735214735346, "dur": 25, "ph": "X", "name": "ReadAsync 242", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735214735373, "dur": 28, "ph": "X", "name": "ReadAsync 217", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735214735402, "dur": 25, "ph": "X", "name": "ReadAsync 628", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735214735429, "dur": 26, "ph": "X", "name": "ReadAsync 391", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735214735456, "dur": 26, "ph": "X", "name": "ReadAsync 463", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735214735485, "dur": 25, "ph": "X", "name": "ReadAsync 527", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735214735512, "dur": 27, "ph": "X", "name": "ReadAsync 466", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735214735540, "dur": 25, "ph": "X", "name": "ReadAsync 338", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735214735567, "dur": 27, "ph": "X", "name": "ReadAsync 392", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735214735596, "dur": 17, "ph": "X", "name": "ReadAsync 504", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735214735614, "dur": 22, "ph": "X", "name": "ReadAsync 212", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735214735638, "dur": 27, "ph": "X", "name": "ReadAsync 315", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735214735667, "dur": 27, "ph": "X", "name": "ReadAsync 462", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735214735695, "dur": 28, "ph": "X", "name": "ReadAsync 482", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735214735725, "dur": 26, "ph": "X", "name": "ReadAsync 451", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735214735753, "dur": 26, "ph": "X", "name": "ReadAsync 500", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735214735781, "dur": 28, "ph": "X", "name": "ReadAsync 362", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735214735810, "dur": 27, "ph": "X", "name": "ReadAsync 581", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735214735839, "dur": 24, "ph": "X", "name": "ReadAsync 505", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735214735866, "dur": 28, "ph": "X", "name": "ReadAsync 318", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735214735895, "dur": 26, "ph": "X", "name": "ReadAsync 431", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735214735923, "dur": 27, "ph": "X", "name": "ReadAsync 272", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735214735953, "dur": 26, "ph": "X", "name": "ReadAsync 307", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735214735981, "dur": 24, "ph": "X", "name": "ReadAsync 315", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735214736006, "dur": 22, "ph": "X", "name": "ReadAsync 220", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735214736030, "dur": 26, "ph": "X", "name": "ReadAsync 74", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735214736058, "dur": 25, "ph": "X", "name": "ReadAsync 325", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735214736085, "dur": 26, "ph": "X", "name": "ReadAsync 391", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735214736113, "dur": 27, "ph": "X", "name": "ReadAsync 271", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735214736142, "dur": 26, "ph": "X", "name": "ReadAsync 426", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735214736170, "dur": 28, "ph": "X", "name": "ReadAsync 252", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735214736201, "dur": 26, "ph": "X", "name": "ReadAsync 377", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735214736229, "dur": 21, "ph": "X", "name": "ReadAsync 356", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735214736252, "dur": 24, "ph": "X", "name": "ReadAsync 127", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735214736279, "dur": 44, "ph": "X", "name": "ReadAsync 293", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735214736324, "dur": 27, "ph": "X", "name": "ReadAsync 309", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735214736353, "dur": 26, "ph": "X", "name": "ReadAsync 345", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735214736380, "dur": 24, "ph": "X", "name": "ReadAsync 234", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735214736406, "dur": 26, "ph": "X", "name": "ReadAsync 265", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735214736434, "dur": 20, "ph": "X", "name": "ReadAsync 324", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735214736456, "dur": 52, "ph": "X", "name": "ReadAsync 8", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735214736509, "dur": 28, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735214736539, "dur": 25, "ph": "X", "name": "ReadAsync 401", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735214736566, "dur": 29, "ph": "X", "name": "ReadAsync 229", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735214736596, "dur": 25, "ph": "X", "name": "ReadAsync 419", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735214736624, "dur": 25, "ph": "X", "name": "ReadAsync 238", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735214736651, "dur": 17, "ph": "X", "name": "ReadAsync 336", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735214736669, "dur": 1, "ph": "X", "name": "ProcessMessages 262", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735214736671, "dur": 29, "ph": "X", "name": "ReadAsync 262", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735214736701, "dur": 26, "ph": "X", "name": "ReadAsync 315", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735214736730, "dur": 25, "ph": "X", "name": "ReadAsync 477", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735214736757, "dur": 26, "ph": "X", "name": "ReadAsync 309", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735214736785, "dur": 2, "ph": "X", "name": "ProcessMessages 297", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735214736788, "dur": 27, "ph": "X", "name": "ReadAsync 297", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735214736817, "dur": 16, "ph": "X", "name": "ReadAsync 326", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735214736835, "dur": 31, "ph": "X", "name": "ReadAsync 169", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735214736868, "dur": 25, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735214736895, "dur": 27, "ph": "X", "name": "ReadAsync 241", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735214736924, "dur": 25, "ph": "X", "name": "ReadAsync 484", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735214736951, "dur": 26, "ph": "X", "name": "ReadAsync 417", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735214736979, "dur": 24, "ph": "X", "name": "ReadAsync 408", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735214737005, "dur": 27, "ph": "X", "name": "ReadAsync 350", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735214737034, "dur": 25, "ph": "X", "name": "ReadAsync 524", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735214737060, "dur": 25, "ph": "X", "name": "ReadAsync 358", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735214737088, "dur": 26, "ph": "X", "name": "ReadAsync 325", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735214737116, "dur": 26, "ph": "X", "name": "ReadAsync 282", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735214737144, "dur": 21, "ph": "X", "name": "ReadAsync 513", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735214737166, "dur": 25, "ph": "X", "name": "ReadAsync 81", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735214737193, "dur": 25, "ph": "X", "name": "ReadAsync 316", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735214737220, "dur": 27, "ph": "X", "name": "ReadAsync 303", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735214737249, "dur": 30, "ph": "X", "name": "ReadAsync 477", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735214737281, "dur": 26, "ph": "X", "name": "ReadAsync 253", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735214737308, "dur": 24, "ph": "X", "name": "ReadAsync 323", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735214737334, "dur": 26, "ph": "X", "name": "ReadAsync 291", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735214737362, "dur": 26, "ph": "X", "name": "ReadAsync 237", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735214737390, "dur": 31, "ph": "X", "name": "ReadAsync 382", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735214737423, "dur": 27, "ph": "X", "name": "ReadAsync 477", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735214737452, "dur": 25, "ph": "X", "name": "ReadAsync 248", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735214737480, "dur": 27, "ph": "X", "name": "ReadAsync 270", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735214737509, "dur": 21, "ph": "X", "name": "ReadAsync 341", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735214737531, "dur": 27, "ph": "X", "name": "ReadAsync 52", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735214737561, "dur": 20, "ph": "X", "name": "ReadAsync 386", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735214737582, "dur": 26, "ph": "X", "name": "ReadAsync 189", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735214737611, "dur": 27, "ph": "X", "name": "ReadAsync 341", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735214737639, "dur": 25, "ph": "X", "name": "ReadAsync 380", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735214737666, "dur": 25, "ph": "X", "name": "ReadAsync 275", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735214737694, "dur": 23, "ph": "X", "name": "ReadAsync 348", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735214737719, "dur": 28, "ph": "X", "name": "ReadAsync 315", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735214737749, "dur": 25, "ph": "X", "name": "ReadAsync 400", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735214737776, "dur": 27, "ph": "X", "name": "ReadAsync 272", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735214737805, "dur": 29, "ph": "X", "name": "ReadAsync 312", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735214737835, "dur": 26, "ph": "X", "name": "ReadAsync 315", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735214737864, "dur": 27, "ph": "X", "name": "ReadAsync 283", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735214737892, "dur": 27, "ph": "X", "name": "ReadAsync 434", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735214737921, "dur": 25, "ph": "X", "name": "ReadAsync 407", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735214737948, "dur": 27, "ph": "X", "name": "ReadAsync 265", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735214737977, "dur": 24, "ph": "X", "name": "ReadAsync 299", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735214738003, "dur": 25, "ph": "X", "name": "ReadAsync 287", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735214738029, "dur": 27, "ph": "X", "name": "ReadAsync 271", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735214738059, "dur": 27, "ph": "X", "name": "ReadAsync 493", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735214738088, "dur": 26, "ph": "X", "name": "ReadAsync 495", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735214738116, "dur": 26, "ph": "X", "name": "ReadAsync 313", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735214738144, "dur": 25, "ph": "X", "name": "ReadAsync 376", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735214738171, "dur": 21, "ph": "X", "name": "ReadAsync 465", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735214738194, "dur": 25, "ph": "X", "name": "ReadAsync 195", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735214738221, "dur": 27, "ph": "X", "name": "ReadAsync 363", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735214738250, "dur": 18, "ph": "X", "name": "ReadAsync 453", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735214738271, "dur": 18, "ph": "X", "name": "ReadAsync 194", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735214738290, "dur": 29, "ph": "X", "name": "ReadAsync 268", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735214738322, "dur": 27, "ph": "X", "name": "ReadAsync 271", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735214738351, "dur": 20, "ph": "X", "name": "ReadAsync 534", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735214738373, "dur": 26, "ph": "X", "name": "ReadAsync 78", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735214738401, "dur": 26, "ph": "X", "name": "ReadAsync 532", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735214738429, "dur": 27, "ph": "X", "name": "ReadAsync 547", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735214738458, "dur": 27, "ph": "X", "name": "ReadAsync 352", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735214738487, "dur": 26, "ph": "X", "name": "ReadAsync 385", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735214738516, "dur": 26, "ph": "X", "name": "ReadAsync 401", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735214738543, "dur": 26, "ph": "X", "name": "ReadAsync 490", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735214738571, "dur": 25, "ph": "X", "name": "ReadAsync 502", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735214738597, "dur": 26, "ph": "X", "name": "ReadAsync 329", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735214738626, "dur": 24, "ph": "X", "name": "ReadAsync 536", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735214738653, "dur": 27, "ph": "X", "name": "ReadAsync 352", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735214738681, "dur": 25, "ph": "X", "name": "ReadAsync 519", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735214738708, "dur": 17, "ph": "X", "name": "ReadAsync 213", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735214738727, "dur": 29, "ph": "X", "name": "ReadAsync 372", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735214738758, "dur": 28, "ph": "X", "name": "ReadAsync 359", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735214738788, "dur": 26, "ph": "X", "name": "ReadAsync 567", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735214738815, "dur": 25, "ph": "X", "name": "ReadAsync 296", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735214738843, "dur": 24, "ph": "X", "name": "ReadAsync 402", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735214738868, "dur": 24, "ph": "X", "name": "ReadAsync 319", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735214738894, "dur": 20, "ph": "X", "name": "ReadAsync 336", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735214738916, "dur": 25, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735214738943, "dur": 27, "ph": "X", "name": "ReadAsync 343", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735214738972, "dur": 25, "ph": "X", "name": "ReadAsync 458", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735214738998, "dur": 27, "ph": "X", "name": "ReadAsync 255", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735214739027, "dur": 26, "ph": "X", "name": "ReadAsync 344", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735214739054, "dur": 26, "ph": "X", "name": "ReadAsync 295", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735214739082, "dur": 25, "ph": "X", "name": "ReadAsync 362", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735214739109, "dur": 29, "ph": "X", "name": "ReadAsync 471", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735214739140, "dur": 27, "ph": "X", "name": "ReadAsync 391", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735214739170, "dur": 27, "ph": "X", "name": "ReadAsync 349", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735214739198, "dur": 73, "ph": "X", "name": "ReadAsync 443", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735214739278, "dur": 2, "ph": "X", "name": "ProcessMessages 318", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735214739281, "dur": 97, "ph": "X", "name": "ReadAsync 318", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735214739382, "dur": 2, "ph": "X", "name": "ProcessMessages 1562", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735214739384, "dur": 63, "ph": "X", "name": "ReadAsync 1562", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735214739452, "dur": 2, "ph": "X", "name": "ProcessMessages 610", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735214739455, "dur": 142, "ph": "X", "name": "ReadAsync 610", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735214739602, "dur": 44, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735214739648, "dur": 27, "ph": "X", "name": "ReadAsync 914", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735214739677, "dur": 27, "ph": "X", "name": "ReadAsync 352", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735214739705, "dur": 1, "ph": "X", "name": "ProcessMessages 392", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735214739707, "dur": 28, "ph": "X", "name": "ReadAsync 392", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735214739738, "dur": 23, "ph": "X", "name": "ReadAsync 471", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735214739763, "dur": 26, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735214739791, "dur": 27, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735214739819, "dur": 26, "ph": "X", "name": "ReadAsync 500", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735214739847, "dur": 26, "ph": "X", "name": "ReadAsync 404", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735214739875, "dur": 27, "ph": "X", "name": "ReadAsync 455", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735214739904, "dur": 26, "ph": "X", "name": "ReadAsync 386", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735214739932, "dur": 25, "ph": "X", "name": "ReadAsync 490", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735214739959, "dur": 25, "ph": "X", "name": "ReadAsync 200", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735214739985, "dur": 27, "ph": "X", "name": "ReadAsync 240", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735214740014, "dur": 26, "ph": "X", "name": "ReadAsync 386", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735214740042, "dur": 27, "ph": "X", "name": "ReadAsync 411", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735214740072, "dur": 25, "ph": "X", "name": "ReadAsync 323", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735214740099, "dur": 27, "ph": "X", "name": "ReadAsync 168", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735214740128, "dur": 21, "ph": "X", "name": "ReadAsync 386", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735214740151, "dur": 25, "ph": "X", "name": "ReadAsync 71", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735214740178, "dur": 38, "ph": "X", "name": "ReadAsync 256", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735214740218, "dur": 27, "ph": "X", "name": "ReadAsync 642", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735214740248, "dur": 32, "ph": "X", "name": "ReadAsync 354", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735214740282, "dur": 25, "ph": "X", "name": "ReadAsync 378", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735214740308, "dur": 28, "ph": "X", "name": "ReadAsync 467", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735214740338, "dur": 35, "ph": "X", "name": "ReadAsync 154", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735214740375, "dur": 25, "ph": "X", "name": "ReadAsync 534", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735214740402, "dur": 29, "ph": "X", "name": "ReadAsync 563", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735214740433, "dur": 27, "ph": "X", "name": "ReadAsync 286", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735214740462, "dur": 25, "ph": "X", "name": "ReadAsync 528", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735214740489, "dur": 33, "ph": "X", "name": "ReadAsync 72", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735214740524, "dur": 35, "ph": "X", "name": "ReadAsync 488", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735214740560, "dur": 35, "ph": "X", "name": "ReadAsync 506", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735214740597, "dur": 25, "ph": "X", "name": "ReadAsync 502", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735214740624, "dur": 30, "ph": "X", "name": "ReadAsync 500", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735214740656, "dur": 27, "ph": "X", "name": "ReadAsync 319", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735214740685, "dur": 29, "ph": "X", "name": "ReadAsync 632", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735214740716, "dur": 34, "ph": "X", "name": "ReadAsync 308", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735214740752, "dur": 34, "ph": "X", "name": "ReadAsync 549", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735214740788, "dur": 24, "ph": "X", "name": "ReadAsync 405", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735214740814, "dur": 26, "ph": "X", "name": "ReadAsync 289", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735214740842, "dur": 29, "ph": "X", "name": "ReadAsync 518", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735214740874, "dur": 27, "ph": "X", "name": "ReadAsync 288", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735214740903, "dur": 26, "ph": "X", "name": "ReadAsync 552", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735214740931, "dur": 25, "ph": "X", "name": "ReadAsync 306", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735214740958, "dur": 24, "ph": "X", "name": "ReadAsync 406", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735214740984, "dur": 28, "ph": "X", "name": "ReadAsync 355", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735214741015, "dur": 34, "ph": "X", "name": "ReadAsync 375", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735214741050, "dur": 26, "ph": "X", "name": "ReadAsync 619", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735214741079, "dur": 26, "ph": "X", "name": "ReadAsync 432", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735214741106, "dur": 25, "ph": "X", "name": "ReadAsync 380", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735214741133, "dur": 24, "ph": "X", "name": "ReadAsync 346", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735214741160, "dur": 25, "ph": "X", "name": "ReadAsync 311", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735214741188, "dur": 25, "ph": "X", "name": "ReadAsync 222", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735214741214, "dur": 29, "ph": "X", "name": "ReadAsync 429", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735214741245, "dur": 25, "ph": "X", "name": "ReadAsync 367", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735214741272, "dur": 25, "ph": "X", "name": "ReadAsync 337", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735214741299, "dur": 24, "ph": "X", "name": "ReadAsync 327", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735214741325, "dur": 34, "ph": "X", "name": "ReadAsync 255", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735214741360, "dur": 26, "ph": "X", "name": "ReadAsync 446", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735214741388, "dur": 1, "ph": "X", "name": "ProcessMessages 522", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735214741389, "dur": 33, "ph": "X", "name": "ReadAsync 522", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735214741424, "dur": 25, "ph": "X", "name": "ReadAsync 367", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735214741452, "dur": 24, "ph": "X", "name": "ReadAsync 459", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735214741478, "dur": 33, "ph": "X", "name": "ReadAsync 289", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735214741513, "dur": 27, "ph": "X", "name": "ReadAsync 517", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735214741542, "dur": 23, "ph": "X", "name": "ReadAsync 613", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735214741567, "dur": 31, "ph": "X", "name": "ReadAsync 342", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735214741600, "dur": 26, "ph": "X", "name": "ReadAsync 435", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735214741628, "dur": 25, "ph": "X", "name": "ReadAsync 475", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735214741654, "dur": 26, "ph": "X", "name": "ReadAsync 384", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735214741682, "dur": 28, "ph": "X", "name": "ReadAsync 486", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735214741712, "dur": 26, "ph": "X", "name": "ReadAsync 378", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735214741742, "dur": 32, "ph": "X", "name": "ReadAsync 455", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735214741776, "dur": 26, "ph": "X", "name": "ReadAsync 624", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735214741803, "dur": 26, "ph": "X", "name": "ReadAsync 493", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735214741831, "dur": 34, "ph": "X", "name": "ReadAsync 430", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735214741867, "dur": 34, "ph": "X", "name": "ReadAsync 613", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735214741903, "dur": 34, "ph": "X", "name": "ReadAsync 444", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735214741939, "dur": 26, "ph": "X", "name": "ReadAsync 603", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735214741968, "dur": 33, "ph": "X", "name": "ReadAsync 401", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735214742002, "dur": 35, "ph": "X", "name": "ReadAsync 472", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735214742039, "dur": 26, "ph": "X", "name": "ReadAsync 426", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735214742067, "dur": 25, "ph": "X", "name": "ReadAsync 412", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735214742094, "dur": 27, "ph": "X", "name": "ReadAsync 278", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735214742123, "dur": 21, "ph": "X", "name": "ReadAsync 345", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735214742145, "dur": 25, "ph": "X", "name": "ReadAsync 52", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735214742172, "dur": 76, "ph": "X", "name": "ReadAsync 413", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735214742251, "dur": 24, "ph": "X", "name": "ReadAsync 348", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735214742277, "dur": 24, "ph": "X", "name": "ReadAsync 386", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735214742303, "dur": 25, "ph": "X", "name": "ReadAsync 259", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735214742330, "dur": 17, "ph": "X", "name": "ReadAsync 375", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735214742348, "dur": 42, "ph": "X", "name": "ReadAsync 292", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735214742393, "dur": 30, "ph": "X", "name": "ReadAsync 437", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735214742425, "dur": 25, "ph": "X", "name": "ReadAsync 426", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735214742452, "dur": 44, "ph": "X", "name": "ReadAsync 324", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735214742498, "dur": 33, "ph": "X", "name": "ReadAsync 359", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735214742534, "dur": 26, "ph": "X", "name": "ReadAsync 744", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735214742561, "dur": 34, "ph": "X", "name": "ReadAsync 457", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735214742597, "dur": 27, "ph": "X", "name": "ReadAsync 439", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735214742627, "dur": 26, "ph": "X", "name": "ReadAsync 485", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735214742654, "dur": 17, "ph": "X", "name": "ReadAsync 450", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735214742673, "dur": 28, "ph": "X", "name": "ReadAsync 110", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735214742703, "dur": 26, "ph": "X", "name": "ReadAsync 355", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735214742730, "dur": 28, "ph": "X", "name": "ReadAsync 378", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735214742760, "dur": 24, "ph": "X", "name": "ReadAsync 510", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735214742786, "dur": 27, "ph": "X", "name": "ReadAsync 224", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735214742814, "dur": 25, "ph": "X", "name": "ReadAsync 412", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735214742841, "dur": 24, "ph": "X", "name": "ReadAsync 415", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735214742867, "dur": 29, "ph": "X", "name": "ReadAsync 412", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735214742898, "dur": 26, "ph": "X", "name": "ReadAsync 262", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735214742925, "dur": 1, "ph": "X", "name": "ProcessMessages 461", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735214742926, "dur": 25, "ph": "X", "name": "ReadAsync 461", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735214742953, "dur": 25, "ph": "X", "name": "ReadAsync 398", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735214742981, "dur": 25, "ph": "X", "name": "ReadAsync 347", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735214743007, "dur": 24, "ph": "X", "name": "ReadAsync 364", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735214743034, "dur": 31, "ph": "X", "name": "ReadAsync 315", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735214743066, "dur": 26, "ph": "X", "name": "ReadAsync 276", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735214743093, "dur": 2, "ph": "X", "name": "ProcessMessages 363", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735214743095, "dur": 28, "ph": "X", "name": "ReadAsync 363", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735214743125, "dur": 25, "ph": "X", "name": "ReadAsync 305", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735214743152, "dur": 26, "ph": "X", "name": "ReadAsync 264", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735214743181, "dur": 25, "ph": "X", "name": "ReadAsync 345", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735214743208, "dur": 34, "ph": "X", "name": "ReadAsync 237", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735214743244, "dur": 26, "ph": "X", "name": "ReadAsync 341", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735214743271, "dur": 25, "ph": "X", "name": "ReadAsync 366", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735214743298, "dur": 25, "ph": "X", "name": "ReadAsync 356", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735214743325, "dur": 26, "ph": "X", "name": "ReadAsync 367", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735214743354, "dur": 24, "ph": "X", "name": "ReadAsync 449", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735214743379, "dur": 25, "ph": "X", "name": "ReadAsync 311", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735214743406, "dur": 29, "ph": "X", "name": "ReadAsync 406", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735214743436, "dur": 17, "ph": "X", "name": "ReadAsync 156", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735214743455, "dur": 22, "ph": "X", "name": "ReadAsync 185", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735214743479, "dur": 27, "ph": "X", "name": "ReadAsync 215", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735214743508, "dur": 25, "ph": "X", "name": "ReadAsync 134", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735214743535, "dur": 25, "ph": "X", "name": "ReadAsync 240", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735214743562, "dur": 25, "ph": "X", "name": "ReadAsync 92", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735214743588, "dur": 24, "ph": "X", "name": "ReadAsync 271", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735214743614, "dur": 25, "ph": "X", "name": "ReadAsync 183", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735214743642, "dur": 27, "ph": "X", "name": "ReadAsync 385", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735214743671, "dur": 25, "ph": "X", "name": "ReadAsync 58", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735214743697, "dur": 27, "ph": "X", "name": "ReadAsync 151", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735214743726, "dur": 34, "ph": "X", "name": "ReadAsync 277", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735214743762, "dur": 25, "ph": "X", "name": "ReadAsync 368", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735214743789, "dur": 25, "ph": "X", "name": "ReadAsync 445", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735214743816, "dur": 34, "ph": "X", "name": "ReadAsync 382", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735214743852, "dur": 26, "ph": "X", "name": "ReadAsync 311", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735214743879, "dur": 30, "ph": "X", "name": "ReadAsync 430", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735214743911, "dur": 25, "ph": "X", "name": "ReadAsync 166", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735214743939, "dur": 24, "ph": "X", "name": "ReadAsync 237", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735214743964, "dur": 28, "ph": "X", "name": "ReadAsync 171", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735214743994, "dur": 29, "ph": "X", "name": "ReadAsync 248", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735214744026, "dur": 25, "ph": "X", "name": "ReadAsync 335", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735214744052, "dur": 25, "ph": "X", "name": "ReadAsync 485", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735214744079, "dur": 24, "ph": "X", "name": "ReadAsync 248", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735214744105, "dur": 25, "ph": "X", "name": "ReadAsync 270", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735214744133, "dur": 24, "ph": "X", "name": "ReadAsync 379", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735214744171, "dur": 23, "ph": "X", "name": "ReadAsync 232", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735214744196, "dur": 25, "ph": "X", "name": "ReadAsync 148", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735214744223, "dur": 25, "ph": "X", "name": "ReadAsync 287", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735214744250, "dur": 34, "ph": "X", "name": "ReadAsync 354", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735214744286, "dur": 27, "ph": "X", "name": "ReadAsync 331", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735214744316, "dur": 25, "ph": "X", "name": "ReadAsync 387", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735214744342, "dur": 24, "ph": "X", "name": "ReadAsync 132", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735214744368, "dur": 25, "ph": "X", "name": "ReadAsync 119", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735214744394, "dur": 25, "ph": "X", "name": "ReadAsync 251", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735214744421, "dur": 24, "ph": "X", "name": "ReadAsync 235", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735214744447, "dur": 34, "ph": "X", "name": "ReadAsync 237", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735214744483, "dur": 26, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735214744512, "dur": 30, "ph": "X", "name": "ReadAsync 217", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735214744544, "dur": 25, "ph": "X", "name": "ReadAsync 278", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735214744570, "dur": 20, "ph": "X", "name": "ReadAsync 377", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735214744592, "dur": 29, "ph": "X", "name": "ReadAsync 56", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735214744623, "dur": 26, "ph": "X", "name": "ReadAsync 183", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735214744651, "dur": 42, "ph": "X", "name": "ReadAsync 357", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735214744695, "dur": 27, "ph": "X", "name": "ReadAsync 317", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735214744725, "dur": 25, "ph": "X", "name": "ReadAsync 141", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735214744752, "dur": 25, "ph": "X", "name": "ReadAsync 167", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735214744779, "dur": 25, "ph": "X", "name": "ReadAsync 275", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735214744806, "dur": 16, "ph": "X", "name": "ReadAsync 426", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735214744824, "dur": 21, "ph": "X", "name": "ReadAsync 144", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735214744848, "dur": 30, "ph": "X", "name": "ReadAsync 157", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735214744879, "dur": 25, "ph": "X", "name": "ReadAsync 282", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735214744906, "dur": 24, "ph": "X", "name": "ReadAsync 352", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735214744932, "dur": 25, "ph": "X", "name": "ReadAsync 257", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735214744959, "dur": 34, "ph": "X", "name": "ReadAsync 179", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735214744996, "dur": 55, "ph": "X", "name": "ReadAsync 390", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735214745054, "dur": 1, "ph": "X", "name": "ProcessMessages 599", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735214745056, "dur": 45, "ph": "X", "name": "ReadAsync 599", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735214745103, "dur": 1, "ph": "X", "name": "ProcessMessages 616", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735214745105, "dur": 38, "ph": "X", "name": "ReadAsync 616", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735214745145, "dur": 57, "ph": "X", "name": "ReadAsync 539", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735214745206, "dur": 1, "ph": "X", "name": "ProcessMessages 120", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735214745207, "dur": 44, "ph": "X", "name": "ReadAsync 120", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735214745254, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735214745256, "dur": 47, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735214745307, "dur": 1, "ph": "X", "name": "ProcessMessages 326", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735214745310, "dur": 37, "ph": "X", "name": "ReadAsync 326", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735214745349, "dur": 26, "ph": "X", "name": "ReadAsync 430", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735214745376, "dur": 25, "ph": "X", "name": "ReadAsync 256", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735214745403, "dur": 26, "ph": "X", "name": "ReadAsync 237", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735214745431, "dur": 27, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735214745460, "dur": 25, "ph": "X", "name": "ReadAsync 369", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735214745488, "dur": 26, "ph": "X", "name": "ReadAsync 139", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735214745516, "dur": 53, "ph": "X", "name": "ReadAsync 450", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735214745571, "dur": 57, "ph": "X", "name": "ReadAsync 68", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735214745630, "dur": 16, "ph": "X", "name": "ReadAsync 1", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735214745648, "dur": 31, "ph": "X", "name": "ReadAsync 67", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735214745681, "dur": 33, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735214745716, "dur": 27, "ph": "X", "name": "ReadAsync 261", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735214745744, "dur": 25, "ph": "X", "name": "ReadAsync 223", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735214745772, "dur": 24, "ph": "X", "name": "ReadAsync 454", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735214745798, "dur": 23, "ph": "X", "name": "ReadAsync 134", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735214745824, "dur": 34, "ph": "X", "name": "ReadAsync 106", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735214745859, "dur": 25, "ph": "X", "name": "ReadAsync 316", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735214745886, "dur": 24, "ph": "X", "name": "ReadAsync 383", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735214745912, "dur": 20, "ph": "X", "name": "ReadAsync 237", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735214745934, "dur": 26, "ph": "X", "name": "ReadAsync 95", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735214745962, "dur": 26, "ph": "X", "name": "ReadAsync 276", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735214745990, "dur": 28, "ph": "X", "name": "ReadAsync 366", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735214746020, "dur": 17, "ph": "X", "name": "ReadAsync 195", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735214746038, "dur": 30, "ph": "X", "name": "ReadAsync 62", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735214746070, "dur": 27, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735214746099, "dur": 20, "ph": "X", "name": "ReadAsync 424", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735214746122, "dur": 24, "ph": "X", "name": "ReadAsync 21", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735214746147, "dur": 26, "ph": "X", "name": "ReadAsync 233", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735214746175, "dur": 29, "ph": "X", "name": "ReadAsync 330", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735214746206, "dur": 34, "ph": "X", "name": "ReadAsync 456", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735214746242, "dur": 35, "ph": "X", "name": "ReadAsync 447", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735214746279, "dur": 27, "ph": "X", "name": "ReadAsync 385", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735214746307, "dur": 20, "ph": "X", "name": "ReadAsync 467", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735214746329, "dur": 28, "ph": "X", "name": "ReadAsync 59", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735214746359, "dur": 26, "ph": "X", "name": "ReadAsync 183", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735214746388, "dur": 33, "ph": "X", "name": "ReadAsync 414", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735214746423, "dur": 26, "ph": "X", "name": "ReadAsync 341", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735214746450, "dur": 1, "ph": "X", "name": "ProcessMessages 436", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735214746451, "dur": 26, "ph": "X", "name": "ReadAsync 436", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735214746480, "dur": 25, "ph": "X", "name": "ReadAsync 319", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735214746506, "dur": 34, "ph": "X", "name": "ReadAsync 264", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735214746542, "dur": 25, "ph": "X", "name": "ReadAsync 503", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735214746569, "dur": 24, "ph": "X", "name": "ReadAsync 91", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735214746595, "dur": 20, "ph": "X", "name": "ReadAsync 228", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735214746617, "dur": 27, "ph": "X", "name": "ReadAsync 40", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735214746646, "dur": 25, "ph": "X", "name": "ReadAsync 291", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735214746673, "dur": 33, "ph": "X", "name": "ReadAsync 289", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735214746708, "dur": 26, "ph": "X", "name": "ReadAsync 270", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735214746736, "dur": 16, "ph": "X", "name": "ReadAsync 531", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735214746755, "dur": 29, "ph": "X", "name": "ReadAsync 67", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735214746786, "dur": 25, "ph": "X", "name": "ReadAsync 164", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735214746813, "dur": 25, "ph": "X", "name": "ReadAsync 342", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735214746840, "dur": 34, "ph": "X", "name": "ReadAsync 358", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735214746876, "dur": 25, "ph": "X", "name": "ReadAsync 510", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735214746903, "dur": 25, "ph": "X", "name": "ReadAsync 353", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735214746929, "dur": 26, "ph": "X", "name": "ReadAsync 379", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735214746957, "dur": 24, "ph": "X", "name": "ReadAsync 414", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735214746983, "dur": 20, "ph": "X", "name": "ReadAsync 253", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735214747005, "dur": 28, "ph": "X", "name": "ReadAsync 56", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735214747034, "dur": 26, "ph": "X", "name": "ReadAsync 221", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735214747062, "dur": 25, "ph": "X", "name": "ReadAsync 363", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735214747089, "dur": 25, "ph": "X", "name": "ReadAsync 383", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735214747117, "dur": 25, "ph": "X", "name": "ReadAsync 66", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735214747144, "dur": 34, "ph": "X", "name": "ReadAsync 322", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735214747180, "dur": 25, "ph": "X", "name": "ReadAsync 431", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735214747207, "dur": 25, "ph": "X", "name": "ReadAsync 295", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735214747233, "dur": 30, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735214747265, "dur": 27, "ph": "X", "name": "ReadAsync 271", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735214747294, "dur": 24, "ph": "X", "name": "ReadAsync 529", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735214747320, "dur": 29, "ph": "X", "name": "ReadAsync 361", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735214747351, "dur": 25, "ph": "X", "name": "ReadAsync 311", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735214747378, "dur": 29, "ph": "X", "name": "ReadAsync 491", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735214747410, "dur": 25, "ph": "X", "name": "ReadAsync 398", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735214747436, "dur": 24, "ph": "X", "name": "ReadAsync 410", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735214747462, "dur": 25, "ph": "X", "name": "ReadAsync 240", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735214747489, "dur": 33, "ph": "X", "name": "ReadAsync 195", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735214747523, "dur": 34, "ph": "X", "name": "ReadAsync 350", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735214747560, "dur": 20, "ph": "X", "name": "ReadAsync 497", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735214747581, "dur": 24, "ph": "X", "name": "ReadAsync 102", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735214747607, "dur": 27, "ph": "X", "name": "ReadAsync 311", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735214747637, "dur": 24, "ph": "X", "name": "ReadAsync 470", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735214747663, "dur": 42, "ph": "X", "name": "ReadAsync 310", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735214747707, "dur": 31, "ph": "X", "name": "ReadAsync 107", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735214747739, "dur": 34, "ph": "X", "name": "ReadAsync 407", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735214747776, "dur": 20, "ph": "X", "name": "ReadAsync 527", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735214747798, "dur": 28, "ph": "X", "name": "ReadAsync 68", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735214747827, "dur": 26, "ph": "X", "name": "ReadAsync 176", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735214747855, "dur": 25, "ph": "X", "name": "ReadAsync 418", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735214747881, "dur": 25, "ph": "X", "name": "ReadAsync 325", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735214747908, "dur": 25, "ph": "X", "name": "ReadAsync 138", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735214747935, "dur": 25, "ph": "X", "name": "ReadAsync 277", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735214747962, "dur": 25, "ph": "X", "name": "ReadAsync 440", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735214747989, "dur": 20, "ph": "X", "name": "ReadAsync 345", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735214748011, "dur": 35, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735214748047, "dur": 27, "ph": "X", "name": "ReadAsync 260", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735214748077, "dur": 25, "ph": "X", "name": "ReadAsync 471", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735214748103, "dur": 26, "ph": "X", "name": "ReadAsync 322", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735214748132, "dur": 28, "ph": "X", "name": "ReadAsync 429", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735214748163, "dur": 34, "ph": "X", "name": "ReadAsync 223", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735214748199, "dur": 34, "ph": "X", "name": "ReadAsync 449", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735214748235, "dur": 25, "ph": "X", "name": "ReadAsync 479", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735214748262, "dur": 24, "ph": "X", "name": "ReadAsync 245", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735214748288, "dur": 25, "ph": "X", "name": "ReadAsync 187", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735214748315, "dur": 34, "ph": "X", "name": "ReadAsync 357", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735214748350, "dur": 35, "ph": "X", "name": "ReadAsync 515", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735214748387, "dur": 25, "ph": "X", "name": "ReadAsync 623", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735214748414, "dur": 30, "ph": "X", "name": "ReadAsync 596", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735214748446, "dur": 25, "ph": "X", "name": "ReadAsync 366", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735214748473, "dur": 24, "ph": "X", "name": "ReadAsync 345", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735214748499, "dur": 25, "ph": "X", "name": "ReadAsync 240", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735214748525, "dur": 38, "ph": "X", "name": "ReadAsync 267", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735214748565, "dur": 2, "ph": "X", "name": "ProcessMessages 543", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735214748568, "dur": 32, "ph": "X", "name": "ReadAsync 543", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735214748602, "dur": 26, "ph": "X", "name": "ReadAsync 611", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735214748630, "dur": 26, "ph": "X", "name": "ReadAsync 432", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735214748657, "dur": 45, "ph": "X", "name": "ReadAsync 334", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735214748708, "dur": 2, "ph": "X", "name": "ProcessMessages 392", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735214748712, "dur": 63, "ph": "X", "name": "ReadAsync 392", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735214748782, "dur": 2, "ph": "X", "name": "ProcessMessages 513", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735214748786, "dur": 53, "ph": "X", "name": "ReadAsync 513", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735214748840, "dur": 1, "ph": "X", "name": "ProcessMessages 899", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735214748842, "dur": 82, "ph": "X", "name": "ReadAsync 899", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735214748929, "dur": 2, "ph": "X", "name": "ProcessMessages 84", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735214748932, "dur": 55, "ph": "X", "name": "ReadAsync 84", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735214748989, "dur": 1, "ph": "X", "name": "ProcessMessages 240", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735214748991, "dur": 45, "ph": "X", "name": "ReadAsync 240", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735214749039, "dur": 53, "ph": "X", "name": "ReadAsync 127", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735214749093, "dur": 1, "ph": "X", "name": "ProcessMessages 373", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735214749096, "dur": 47, "ph": "X", "name": "ReadAsync 373", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735214749147, "dur": 71, "ph": "X", "name": "ReadAsync 212", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735214749220, "dur": 1, "ph": "X", "name": "ProcessMessages 477", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735214749221, "dur": 62, "ph": "X", "name": "ReadAsync 477", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735214749289, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735214749291, "dur": 60, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735214749353, "dur": 1, "ph": "X", "name": "ProcessMessages 545", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735214749356, "dur": 41, "ph": "X", "name": "ReadAsync 545", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735214749400, "dur": 1, "ph": "X", "name": "ProcessMessages 441", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735214749402, "dur": 42, "ph": "X", "name": "ReadAsync 441", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735214749447, "dur": 1, "ph": "X", "name": "ProcessMessages 240", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735214749449, "dur": 37, "ph": "X", "name": "ReadAsync 240", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735214749490, "dur": 42, "ph": "X", "name": "ReadAsync 40", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735214749533, "dur": 1, "ph": "X", "name": "ProcessMessages 308", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735214749534, "dur": 36, "ph": "X", "name": "ReadAsync 308", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735214749573, "dur": 51, "ph": "X", "name": "ReadAsync 488", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735214749629, "dur": 2, "ph": "X", "name": "ProcessMessages 351", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735214749632, "dur": 57, "ph": "X", "name": "ReadAsync 351", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735214749692, "dur": 3, "ph": "X", "name": "ProcessMessages 452", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735214749696, "dur": 43, "ph": "X", "name": "ReadAsync 452", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735214749741, "dur": 1, "ph": "X", "name": "ProcessMessages 433", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735214749742, "dur": 34, "ph": "X", "name": "ReadAsync 433", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735214749781, "dur": 1, "ph": "X", "name": "ProcessMessages 237", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735214749783, "dur": 30, "ph": "X", "name": "ReadAsync 237", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735214749814, "dur": 28, "ph": "X", "name": "ReadAsync 355", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735214749845, "dur": 21, "ph": "X", "name": "ReadAsync 469", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735214749868, "dur": 26, "ph": "X", "name": "ReadAsync 355", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735214749895, "dur": 28, "ph": "X", "name": "ReadAsync 417", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735214749926, "dur": 1, "ph": "X", "name": "ProcessMessages 347", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735214749927, "dur": 37, "ph": "X", "name": "ReadAsync 347", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735214749967, "dur": 28, "ph": "X", "name": "ReadAsync 592", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735214749996, "dur": 24, "ph": "X", "name": "ReadAsync 184", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735214750022, "dur": 25, "ph": "X", "name": "ReadAsync 134", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735214750048, "dur": 26, "ph": "X", "name": "ReadAsync 171", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735214750077, "dur": 26, "ph": "X", "name": "ReadAsync 350", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735214750105, "dur": 25, "ph": "X", "name": "ReadAsync 408", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735214750132, "dur": 23, "ph": "X", "name": "ReadAsync 240", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735214750157, "dur": 28, "ph": "X", "name": "ReadAsync 205", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735214750187, "dur": 35, "ph": "X", "name": "ReadAsync 324", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735214750223, "dur": 25, "ph": "X", "name": "ReadAsync 354", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735214750249, "dur": 1, "ph": "X", "name": "ProcessMessages 237", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735214750251, "dur": 29, "ph": "X", "name": "ReadAsync 237", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735214750282, "dur": 34, "ph": "X", "name": "ReadAsync 222", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735214750318, "dur": 28, "ph": "X", "name": "ReadAsync 426", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735214750349, "dur": 29, "ph": "X", "name": "ReadAsync 349", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735214750379, "dur": 22, "ph": "X", "name": "ReadAsync 136", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735214750402, "dur": 19, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735214750423, "dur": 20, "ph": "X", "name": "ReadAsync 257", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735214750444, "dur": 26, "ph": "X", "name": "ReadAsync 251", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735214750474, "dur": 1, "ph": "X", "name": "ProcessMessages 363", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735214750475, "dur": 30, "ph": "X", "name": "ReadAsync 363", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735214750506, "dur": 24, "ph": "X", "name": "ReadAsync 237", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735214750534, "dur": 28, "ph": "X", "name": "ReadAsync 243", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735214750565, "dur": 25, "ph": "X", "name": "ReadAsync 321", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735214750592, "dur": 22, "ph": "X", "name": "ReadAsync 205", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735214750616, "dur": 25, "ph": "X", "name": "ReadAsync 250", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735214750644, "dur": 27, "ph": "X", "name": "ReadAsync 304", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735214750672, "dur": 21, "ph": "X", "name": "ReadAsync 389", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735214750695, "dur": 25, "ph": "X", "name": "ReadAsync 104", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735214750721, "dur": 22, "ph": "X", "name": "ReadAsync 334", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735214750745, "dur": 24, "ph": "X", "name": "ReadAsync 144", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735214750772, "dur": 21, "ph": "X", "name": "ReadAsync 237", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735214750795, "dur": 27, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735214750823, "dur": 102, "ph": "X", "name": "ReadAsync 292", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735214750930, "dur": 51, "ph": "X", "name": "ReadAsync 1", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735214750985, "dur": 42, "ph": "X", "name": "ReadAsync 489", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735214751029, "dur": 39, "ph": "X", "name": "ReadAsync 553", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735214751072, "dur": 1, "ph": "X", "name": "ProcessMessages 196", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735214751074, "dur": 46, "ph": "X", "name": "ReadAsync 196", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735214751123, "dur": 1, "ph": "X", "name": "ProcessMessages 410", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735214751124, "dur": 33, "ph": "X", "name": "ReadAsync 410", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735214751160, "dur": 1, "ph": "X", "name": "ProcessMessages 349", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735214751163, "dur": 39, "ph": "X", "name": "ReadAsync 349", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735214751204, "dur": 29, "ph": "X", "name": "ReadAsync 245", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735214751235, "dur": 26, "ph": "X", "name": "ReadAsync 240", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735214751263, "dur": 25, "ph": "X", "name": "ReadAsync 183", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735214751290, "dur": 28, "ph": "X", "name": "ReadAsync 253", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735214751320, "dur": 24, "ph": "X", "name": "ReadAsync 350", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735214751346, "dur": 26, "ph": "X", "name": "ReadAsync 223", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735214751373, "dur": 1, "ph": "X", "name": "ProcessMessages 375", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735214751375, "dur": 24, "ph": "X", "name": "ReadAsync 375", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735214751400, "dur": 34, "ph": "X", "name": "ReadAsync 119", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735214751436, "dur": 26, "ph": "X", "name": "ReadAsync 318", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735214751464, "dur": 24, "ph": "X", "name": "ReadAsync 350", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735214751491, "dur": 24, "ph": "X", "name": "ReadAsync 234", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735214751518, "dur": 25, "ph": "X", "name": "ReadAsync 68", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735214751545, "dur": 29, "ph": "X", "name": "ReadAsync 189", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735214751576, "dur": 29, "ph": "X", "name": "ReadAsync 332", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735214751606, "dur": 20, "ph": "X", "name": "ReadAsync 576", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735214751629, "dur": 20, "ph": "X", "name": "ReadAsync 21", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735214751651, "dur": 32, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735214751685, "dur": 26, "ph": "X", "name": "ReadAsync 485", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735214751712, "dur": 25, "ph": "X", "name": "ReadAsync 509", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735214751739, "dur": 25, "ph": "X", "name": "ReadAsync 237", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735214751766, "dur": 27, "ph": "X", "name": "ReadAsync 227", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735214751794, "dur": 1, "ph": "X", "name": "ProcessMessages 394", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735214751796, "dur": 27, "ph": "X", "name": "ReadAsync 394", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735214751825, "dur": 25, "ph": "X", "name": "ReadAsync 439", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735214751852, "dur": 24, "ph": "X", "name": "ReadAsync 138", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735214751878, "dur": 24, "ph": "X", "name": "ReadAsync 272", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735214751904, "dur": 30, "ph": "X", "name": "ReadAsync 422", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735214751936, "dur": 26, "ph": "X", "name": "ReadAsync 472", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735214751965, "dur": 25, "ph": "X", "name": "ReadAsync 543", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735214751992, "dur": 25, "ph": "X", "name": "ReadAsync 379", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735214752020, "dur": 33, "ph": "X", "name": "ReadAsync 310", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735214752055, "dur": 24, "ph": "X", "name": "ReadAsync 458", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735214752081, "dur": 30, "ph": "X", "name": "ReadAsync 187", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735214752113, "dur": 24, "ph": "X", "name": "ReadAsync 134", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735214752139, "dur": 25, "ph": "X", "name": "ReadAsync 257", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735214752166, "dur": 26, "ph": "X", "name": "ReadAsync 293", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735214752194, "dur": 30, "ph": "X", "name": "ReadAsync 503", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735214752226, "dur": 25, "ph": "X", "name": "ReadAsync 224", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735214752253, "dur": 33, "ph": "X", "name": "ReadAsync 286", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735214752288, "dur": 25, "ph": "X", "name": "ReadAsync 419", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735214752315, "dur": 25, "ph": "X", "name": "ReadAsync 388", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735214752342, "dur": 24, "ph": "X", "name": "ReadAsync 445", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735214752368, "dur": 24, "ph": "X", "name": "ReadAsync 255", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735214752394, "dur": 26, "ph": "X", "name": "ReadAsync 67", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735214752422, "dur": 33, "ph": "X", "name": "ReadAsync 272", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735214752457, "dur": 35, "ph": "X", "name": "ReadAsync 426", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735214752493, "dur": 26, "ph": "X", "name": "ReadAsync 602", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735214752521, "dur": 34, "ph": "X", "name": "ReadAsync 512", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735214752558, "dur": 25, "ph": "X", "name": "ReadAsync 394", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735214752584, "dur": 28, "ph": "X", "name": "ReadAsync 428", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735214752614, "dur": 25, "ph": "X", "name": "ReadAsync 331", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735214752641, "dur": 16, "ph": "X", "name": "ReadAsync 237", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735214752658, "dur": 28, "ph": "X", "name": "ReadAsync 196", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735214752689, "dur": 25, "ph": "X", "name": "ReadAsync 242", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735214752715, "dur": 30, "ph": "X", "name": "ReadAsync 376", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735214752747, "dur": 25, "ph": "X", "name": "ReadAsync 270", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735214752774, "dur": 30, "ph": "X", "name": "ReadAsync 347", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735214752806, "dur": 34, "ph": "X", "name": "ReadAsync 425", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735214752842, "dur": 26, "ph": "X", "name": "ReadAsync 274", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735214752869, "dur": 1, "ph": "X", "name": "ProcessMessages 356", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735214752870, "dur": 24, "ph": "X", "name": "ReadAsync 356", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735214752896, "dur": 24, "ph": "X", "name": "ReadAsync 225", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735214752922, "dur": 25, "ph": "X", "name": "ReadAsync 167", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735214752949, "dur": 28, "ph": "X", "name": "ReadAsync 329", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735214752979, "dur": 16, "ph": "X", "name": "ReadAsync 490", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735214752997, "dur": 21, "ph": "X", "name": "ReadAsync 134", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735214753019, "dur": 35, "ph": "X", "name": "ReadAsync 192", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735214753058, "dur": 28, "ph": "X", "name": "ReadAsync 286", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735214753088, "dur": 27, "ph": "X", "name": "ReadAsync 447", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735214753116, "dur": 21, "ph": "X", "name": "ReadAsync 607", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735214753139, "dur": 21, "ph": "X", "name": "ReadAsync 101", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735214753161, "dur": 28, "ph": "X", "name": "ReadAsync 122", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735214753191, "dur": 34, "ph": "X", "name": "ReadAsync 378", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735214753228, "dur": 34, "ph": "X", "name": "ReadAsync 428", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735214753264, "dur": 24, "ph": "X", "name": "ReadAsync 478", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735214753290, "dur": 25, "ph": "X", "name": "ReadAsync 66", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735214753317, "dur": 25, "ph": "X", "name": "ReadAsync 375", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735214753345, "dur": 25, "ph": "X", "name": "ReadAsync 446", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735214753372, "dur": 24, "ph": "X", "name": "ReadAsync 407", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735214753398, "dur": 30, "ph": "X", "name": "ReadAsync 451", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735214753431, "dur": 26, "ph": "X", "name": "ReadAsync 368", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735214753459, "dur": 24, "ph": "X", "name": "ReadAsync 426", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735214753484, "dur": 24, "ph": "X", "name": "ReadAsync 264", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735214753510, "dur": 25, "ph": "X", "name": "ReadAsync 237", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735214753538, "dur": 26, "ph": "X", "name": "ReadAsync 234", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735214753565, "dur": 25, "ph": "X", "name": "ReadAsync 348", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735214753592, "dur": 29, "ph": "X", "name": "ReadAsync 398", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735214753622, "dur": 35, "ph": "X", "name": "ReadAsync 331", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735214753659, "dur": 35, "ph": "X", "name": "ReadAsync 408", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735214753696, "dur": 25, "ph": "X", "name": "ReadAsync 414", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735214753723, "dur": 16, "ph": "X", "name": "ReadAsync 359", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735214753741, "dur": 21, "ph": "X", "name": "ReadAsync 146", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735214753763, "dur": 26, "ph": "X", "name": "ReadAsync 158", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735214753791, "dur": 34, "ph": "X", "name": "ReadAsync 272", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735214753827, "dur": 26, "ph": "X", "name": "ReadAsync 409", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735214753854, "dur": 1, "ph": "X", "name": "ProcessMessages 315", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735214753856, "dur": 24, "ph": "X", "name": "ReadAsync 315", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735214753881, "dur": 24, "ph": "X", "name": "ReadAsync 276", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735214753908, "dur": 34, "ph": "X", "name": "ReadAsync 256", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735214753943, "dur": 25, "ph": "X", "name": "ReadAsync 376", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735214753970, "dur": 25, "ph": "X", "name": "ReadAsync 471", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735214753997, "dur": 25, "ph": "X", "name": "ReadAsync 237", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735214754025, "dur": 29, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735214754056, "dur": 93, "ph": "X", "name": "ReadAsync 224", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735214754150, "dur": 16, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735214754169, "dur": 31, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735214754202, "dur": 47, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735214754251, "dur": 28, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735214754281, "dur": 22, "ph": "X", "name": "ReadAsync 128", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735214754307, "dur": 19, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735214754327, "dur": 27, "ph": "X", "name": "ReadAsync 92", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735214754356, "dur": 25, "ph": "X", "name": "ReadAsync 52", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735214754383, "dur": 25, "ph": "X", "name": "ReadAsync 92", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735214754411, "dur": 36, "ph": "X", "name": "ReadAsync 84", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735214754453, "dur": 2, "ph": "X", "name": "ProcessMessages 80", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735214754457, "dur": 59, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735214754517, "dur": 1, "ph": "X", "name": "ProcessMessages 220", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735214754519, "dur": 33, "ph": "X", "name": "ReadAsync 220", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735214754554, "dur": 26, "ph": "X", "name": "ReadAsync 132", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735214754582, "dur": 17, "ph": "X", "name": "ReadAsync 96", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735214754601, "dur": 54, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735214754661, "dur": 35, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735214754699, "dur": 25, "ph": "X", "name": "ReadAsync 188", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735214754726, "dur": 24, "ph": "X", "name": "ReadAsync 96", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735214754752, "dur": 24, "ph": "X", "name": "ReadAsync 128", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735214754778, "dur": 25, "ph": "X", "name": "ReadAsync 112", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735214754805, "dur": 26, "ph": "X", "name": "ReadAsync 112", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735214754834, "dur": 22, "ph": "X", "name": "ReadAsync 132", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735214754859, "dur": 1, "ph": "X", "name": "ProcessMessages 140", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735214754861, "dur": 30, "ph": "X", "name": "ReadAsync 140", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735214754894, "dur": 24, "ph": "X", "name": "ReadAsync 144", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735214754920, "dur": 1, "ph": "X", "name": "ProcessMessages 144", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735214754921, "dur": 33, "ph": "X", "name": "ReadAsync 144", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735214754957, "dur": 27, "ph": "X", "name": "ReadAsync 132", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735214754987, "dur": 25, "ph": "X", "name": "ReadAsync 172", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735214755015, "dur": 24, "ph": "X", "name": "ReadAsync 144", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735214755042, "dur": 1, "ph": "X", "name": "ProcessMessages 132", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735214755044, "dur": 33, "ph": "X", "name": "ReadAsync 132", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735214755079, "dur": 24, "ph": "X", "name": "ReadAsync 176", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735214755105, "dur": 17, "ph": "X", "name": "ReadAsync 124", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735214755124, "dur": 23, "ph": "X", "name": "ReadAsync 96", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735214755149, "dur": 27, "ph": "X", "name": "ReadAsync 96", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735214755179, "dur": 1, "ph": "X", "name": "ProcessMessages 100", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735214755180, "dur": 27, "ph": "X", "name": "ReadAsync 100", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735214755210, "dur": 24, "ph": "X", "name": "ReadAsync 156", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735214755237, "dur": 26, "ph": "X", "name": "ReadAsync 132", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735214755265, "dur": 1, "ph": "X", "name": "ProcessMessages 144", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735214755267, "dur": 26, "ph": "X", "name": "ReadAsync 144", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735214755295, "dur": 24, "ph": "X", "name": "ReadAsync 144", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735214755322, "dur": 26, "ph": "X", "name": "ReadAsync 140", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735214755349, "dur": 25, "ph": "X", "name": "ReadAsync 96", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735214755375, "dur": 1, "ph": "X", "name": "ProcessMessages 128", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735214755378, "dur": 20, "ph": "X", "name": "ReadAsync 128", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735214755400, "dur": 26, "ph": "X", "name": "ReadAsync 132", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735214755428, "dur": 25, "ph": "X", "name": "ReadAsync 96", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735214755455, "dur": 26, "ph": "X", "name": "ReadAsync 128", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735214755483, "dur": 22, "ph": "X", "name": "ReadAsync 96", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735214755507, "dur": 26, "ph": "X", "name": "ReadAsync 128", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735214755534, "dur": 28, "ph": "X", "name": "ReadAsync 128", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735214755565, "dur": 1, "ph": "X", "name": "ProcessMessages 124", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735214755567, "dur": 33, "ph": "X", "name": "ReadAsync 124", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735214755602, "dur": 1, "ph": "X", "name": "ProcessMessages 180", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735214755603, "dur": 19, "ph": "X", "name": "ReadAsync 180", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735214755624, "dur": 21, "ph": "X", "name": "ReadAsync 144", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735214755648, "dur": 1, "ph": "X", "name": "ProcessMessages 96", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735214755650, "dur": 31, "ph": "X", "name": "ReadAsync 96", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735214755683, "dur": 36, "ph": "X", "name": "ReadAsync 144", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735214755722, "dur": 50, "ph": "X", "name": "ReadAsync 144", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735214755774, "dur": 1, "ph": "X", "name": "ProcessMessages 192", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735214755776, "dur": 117, "ph": "X", "name": "ReadAsync 192", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735214755895, "dur": 111, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735214756008, "dur": 212, "ph": "X", "name": "ReadAsync 84", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735214756223, "dur": 64, "ph": "X", "name": "ReadAsync 128", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735214756291, "dur": 1179, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735214757475, "dur": 92, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735214757569, "dur": 2688, "ph": "X", "name": "ProcessMessages 46", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735214760260, "dur": 51, "ph": "X", "name": "ReadAsync 46", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735214760315, "dur": 1, "ph": "X", "name": "ProcessMessages 112", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735214760317, "dur": 1816, "ph": "X", "name": "ReadAsync 112", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735214762137, "dur": 30, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735214762169, "dur": 25, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735214762197, "dur": 57, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735214762256, "dur": 21, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735214762279, "dur": 167, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735214762450, "dur": 24, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735214762476, "dur": 874, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735214763353, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735214763355, "dur": 121, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735214763479, "dur": 2, "ph": "X", "name": "ProcessMessages 356", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735214763482, "dur": 383, "ph": "X", "name": "ReadAsync 356", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735214763869, "dur": 1, "ph": "X", "name": "ProcessMessages 96", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735214763871, "dur": 65, "ph": "X", "name": "ReadAsync 96", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735214763939, "dur": 2, "ph": "X", "name": "ProcessMessages 352", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735214763942, "dur": 30, "ph": "X", "name": "ReadAsync 352", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735214763974, "dur": 170, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735214764147, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735214764149, "dur": 46, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735214764197, "dur": 1, "ph": "X", "name": "ProcessMessages 160", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735214764199, "dur": 66, "ph": "X", "name": "ReadAsync 160", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735214764268, "dur": 1, "ph": "X", "name": "ProcessMessages 64", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735214764271, "dur": 143, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735214764417, "dur": 1, "ph": "X", "name": "ProcessMessages 96", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735214764419, "dur": 40, "ph": "X", "name": "ReadAsync 96", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735214764462, "dur": 1, "ph": "X", "name": "ProcessMessages 160", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735214764465, "dur": 50, "ph": "X", "name": "ReadAsync 160", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735214764518, "dur": 1, "ph": "X", "name": "ProcessMessages 80", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735214764520, "dur": 42, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735214764568, "dur": 44, "ph": "X", "name": "ReadAsync 28", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735214764614, "dur": 33, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735214764652, "dur": 47, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735214764701, "dur": 58, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735214764763, "dur": 31, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735214764795, "dur": 84, "ph": "X", "name": "ReadAsync 84", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735214764881, "dur": 25, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735214764909, "dur": 24, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735214764935, "dur": 75, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735214765013, "dur": 25, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735214765038, "dur": 2, "ph": "X", "name": "ProcessMessages 24", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735214765042, "dur": 22, "ph": "X", "name": "ReadAsync 24", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735214765065, "dur": 21, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735214765088, "dur": 21, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735214765111, "dur": 55, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735214765168, "dur": 24, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735214765195, "dur": 41, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735214765238, "dur": 22, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735214765263, "dur": 1, "ph": "X", "name": "ProcessMessages 36", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735214765265, "dur": 101, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735214765369, "dur": 34, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735214765405, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735214765407, "dur": 93, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735214765503, "dur": 33, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735214765540, "dur": 1, "ph": "X", "name": "ProcessMessages 36", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735214765541, "dur": 31, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735214765577, "dur": 31, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735214765614, "dur": 28, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735214765644, "dur": 23, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735214765669, "dur": 88, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735214765758, "dur": 25, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735214765786, "dur": 43, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735214765831, "dur": 25, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735214765858, "dur": 31, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735214765892, "dur": 46, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735214765940, "dur": 30, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735214765973, "dur": 25, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735214766000, "dur": 149, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735214766151, "dur": 21, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735214766174, "dur": 42, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735214766219, "dur": 24, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735214766245, "dur": 17, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735214766264, "dur": 22, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735214766288, "dur": 33, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735214766322, "dur": 21, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735214766345, "dur": 66, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735214766413, "dur": 21, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735214766436, "dur": 17, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735214766455, "dur": 38, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735214766495, "dur": 22, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735214766519, "dur": 142, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735214766662, "dur": 17, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735214766681, "dur": 82, "ph": "X", "name": "ReadAsync 52", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735214766765, "dur": 21, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735214766788, "dur": 21, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735214766811, "dur": 140, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735214766952, "dur": 21, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735214766975, "dur": 21, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735214766998, "dur": 552, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735214767551, "dur": 25, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735214767577, "dur": 1, "ph": "X", "name": "ProcessMessages 8", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735214767579, "dur": 273387, "ph": "X", "name": "ReadAsync 8", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735215040977, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735215040981, "dur": 45, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735215041028, "dur": 24, "ph": "X", "name": "ProcessMessages 192", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735215041053, "dur": 3437, "ph": "X", "name": "ReadAsync 192", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735215044499, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735215044502, "dur": 189, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735215044695, "dur": 2, "ph": "X", "name": "ProcessMessages 164", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735215044698, "dur": 2585, "ph": "X", "name": "ReadAsync 164", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735215047287, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735215047289, "dur": 108, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735215047402, "dur": 2, "ph": "X", "name": "ProcessMessages 304", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735215047404, "dur": 61, "ph": "X", "name": "ReadAsync 304", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735215047470, "dur": 1282, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735215048756, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735215048758, "dur": 143, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735215048904, "dur": 2, "ph": "X", "name": "ProcessMessages 512", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735215048908, "dur": 46, "ph": "X", "name": "ReadAsync 512", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735215048960, "dur": 2, "ph": "X", "name": "ProcessMessages 428", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735215048963, "dur": 191, "ph": "X", "name": "ReadAsync 428", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735215049159, "dur": 9, "ph": "X", "name": "ProcessMessages 84", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735215049173, "dur": 49, "ph": "X", "name": "ReadAsync 84", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735215049225, "dur": 3, "ph": "X", "name": "ProcessMessages 768", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735215049229, "dur": 34, "ph": "X", "name": "ReadAsync 768", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735215049266, "dur": 29, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735215049297, "dur": 32, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735215049332, "dur": 51, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735215049386, "dur": 28, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735215049417, "dur": 24, "ph": "X", "name": "ReadAsync 52", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735215049442, "dur": 24, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735215049468, "dur": 37, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735215049508, "dur": 18, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735215049528, "dur": 369, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735215049899, "dur": 32, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735215049933, "dur": 22, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735215049957, "dur": 52205, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735215102173, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735215102177, "dur": 36, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735215102214, "dur": 4068, "ph": "X", "name": "ProcessMessages 817", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735215106287, "dur": 1353, "ph": "X", "name": "ReadAsync 817", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735215107646, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735215107649, "dur": 68, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735215107718, "dur": 3, "ph": "X", "name": "ProcessMessages 8", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735215107722, "dur": 28404, "ph": "X", "name": "ReadAsync 8", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735215136139, "dur": 3, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735215136144, "dur": 85, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735215136232, "dur": 4, "ph": "X", "name": "ProcessMessages 8", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735215136237, "dur": 250514, "ph": "X", "name": "ReadAsync 8", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735215386766, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735215386770, "dur": 71, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735215386846, "dur": 34, "ph": "X", "name": "ProcessMessages 416", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735215386881, "dur": 5623, "ph": "X", "name": "ReadAsync 416", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735215392511, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735215392514, "dur": 75, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735215392595, "dur": 5, "ph": "X", "name": "ProcessMessages 8", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735215392602, "dur": 1588, "ph": "X", "name": "ReadAsync 8", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735215394194, "dur": 4, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735215394199, "dur": 34, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735215394236, "dur": 28, "ph": "X", "name": "ProcessMessages 34", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735215394265, "dur": 4096, "ph": "X", "name": "ReadAsync 34", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735215398366, "dur": 31, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735215398402, "dur": 285, "ph": "X", "name": "ProcessMessages 353", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735215398689, "dur": 17487, "ph": "X", "name": "ReadAsync 353", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735215416187, "dur": 3, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735215416192, "dur": 51, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735215416245, "dur": 3, "ph": "X", "name": "ProcessMessages 8", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735215416248, "dur": 6528, "ph": "X", "name": "ReadAsync 8", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735215422786, "dur": 3, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735215422793, "dur": 52, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735215422848, "dur": 2, "ph": "X", "name": "ProcessMessages 8", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735215422851, "dur": 630, "ph": "X", "name": "ReadAsync 8", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735215423488, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735215423491, "dur": 42, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735215423536, "dur": 27, "ph": "X", "name": "ProcessMessages 34", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735215423565, "dur": 219402, "ph": "X", "name": "ReadAsync 34", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735215642980, "dur": 3, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735215642986, "dur": 83, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735215643072, "dur": 41, "ph": "X", "name": "ProcessMessages 422", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735215643114, "dur": 5163, "ph": "X", "name": "ReadAsync 422", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735215648286, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735215648293, "dur": 66, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735215648362, "dur": 4, "ph": "X", "name": "ProcessMessages 8", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735215648367, "dur": 691, "ph": "X", "name": "ReadAsync 8", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735215649062, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735215649064, "dur": 52, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735215649119, "dur": 26, "ph": "X", "name": "ProcessMessages 34", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735215649146, "dur": 11047, "ph": "X", "name": "ReadAsync 34", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735215660201, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735215660204, "dur": 36, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735215660242, "dur": 2, "ph": "X", "name": "ProcessMessages 8", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735215660244, "dur": 703, "ph": "X", "name": "ReadAsync 8", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735215660955, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735215660958, "dur": 60, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735215661023, "dur": 22, "ph": "X", "name": "ProcessMessages 50", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735215661047, "dur": 489, "ph": "X", "name": "ReadAsync 50", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735215661540, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735215661545, "dur": 38, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735215661586, "dur": 1, "ph": "X", "name": "ProcessMessages 13", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735215661588, "dur": 7442, "ph": "X", "name": "ReadAsync 13", "args": {}}, {"pid": 3100, "tid": 24780, "ts": 1754735215676513, "dur": 848, "ph": "X", "name": "ReadEntireBinlogFromIpcAsync", "args": {}}, {"pid": 3100, "tid": 17179869184, "ph": "M", "name": "thread_name", "args": {"name": "WaitForBuildProgramInputDataBeingWrittenAndSendDagReadyMessageAsync"}}, {"pid": 3100, "tid": 17179869184, "ts": 1754735214493189, "dur": 7, "ph": "X", "name": "await writeBuildProgramInputDataTask", "args": {}}, {"pid": 3100, "tid": 17179869184, "ts": 1754735214493196, "dur": 134722, "ph": "X", "name": "WritePipe.WaitConnectionAsync", "args": {}}, {"pid": 3100, "tid": 17179869184, "ts": 1754735214627919, "dur": 35, "ph": "X", "name": "WriteDagReadyMessage", "args": {}}, {"pid": 3100, "tid": 24780, "ts": 1754735215677363, "dur": 3, "ph": "X", "name": "WaitForBuildProgramInputDataBeingWrittenAndSendDagReadyMessageAsync", "args": {}}, {"pid": 3100, "tid": 1, "ph": "M", "name": "thread_name", "args": {"name": ""}}, {"pid": 3100, "tid": 1, "ts": 1754735213547398, "dur": 3388, "ph": "X", "name": "<Add>b__0", "args": {}}, {"pid": 3100, "tid": 1, "ts": 1754735213550790, "dur": 26701, "ph": "X", "name": "<Add>b__0", "args": {}}, {"pid": 3100, "tid": 1, "ts": 1754735213577500, "dur": 90084, "ph": "X", "name": "WriteJson", "args": {}}, {"pid": 3100, "tid": 24780, "ts": 1754735215677367, "dur": 2, "ph": "X", "name": "", "args": {}}, {"pid": 3100, "tid": 12884901888, "ph": "M", "name": "thread_name", "args": {"name": "ReadEntireBinlogFromIpcAsync"}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735213546282, "dur": 153738, "ph": "X", "name": "WaitForConnectionAsync", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735213700022, "dur": 33196, "ph": "X", "name": "UpdateFromStreamAsync", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735213700974, "dur": 917, "ph": "X", "name": "ReadAsync 0", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735213701897, "dur": 899, "ph": "X", "name": "ProcessMessages 57", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735213702800, "dur": 289, "ph": "X", "name": "ReadAsync 57", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735213703091, "dur": 277, "ph": "X", "name": "ProcessMessages 9028", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735213703370, "dur": 100, "ph": "X", "name": "ReadAsync 9028", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735213703474, "dur": 3, "ph": "X", "name": "ProcessMessages 7297", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735213703478, "dur": 39, "ph": "X", "name": "ReadAsync 7297", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735213703520, "dur": 38, "ph": "X", "name": "ReadAsync 557", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735213703562, "dur": 1, "ph": "X", "name": "ProcessMessages 482", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735213703565, "dur": 49, "ph": "X", "name": "ReadAsync 482", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735213703618, "dur": 1, "ph": "X", "name": "ProcessMessages 670", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735213703620, "dur": 35, "ph": "X", "name": "ReadAsync 670", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735213703658, "dur": 34, "ph": "X", "name": "ReadAsync 599", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735213703695, "dur": 1, "ph": "X", "name": "ProcessMessages 428", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735213703697, "dur": 42, "ph": "X", "name": "ReadAsync 428", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735213703742, "dur": 1, "ph": "X", "name": "ProcessMessages 695", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735213703745, "dur": 36, "ph": "X", "name": "ReadAsync 695", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735213703783, "dur": 23, "ph": "X", "name": "ReadAsync 829", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735213703810, "dur": 31, "ph": "X", "name": "ReadAsync 277", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735213703843, "dur": 38, "ph": "X", "name": "ReadAsync 577", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735213703884, "dur": 1, "ph": "X", "name": "ProcessMessages 338", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735213703887, "dur": 44, "ph": "X", "name": "ReadAsync 338", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735213703936, "dur": 1, "ph": "X", "name": "ProcessMessages 464", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735213703938, "dur": 47, "ph": "X", "name": "ReadAsync 464", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735213703988, "dur": 1, "ph": "X", "name": "ProcessMessages 305", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735213703990, "dur": 47, "ph": "X", "name": "ReadAsync 305", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735213704041, "dur": 1, "ph": "X", "name": "ProcessMessages 553", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735213704044, "dur": 57, "ph": "X", "name": "ReadAsync 553", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735213704104, "dur": 1, "ph": "X", "name": "ProcessMessages 830", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735213704106, "dur": 46, "ph": "X", "name": "ReadAsync 830", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735213704155, "dur": 1, "ph": "X", "name": "ProcessMessages 705", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735213704156, "dur": 37, "ph": "X", "name": "ReadAsync 705", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735213704196, "dur": 33, "ph": "X", "name": "ReadAsync 747", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735213704232, "dur": 44, "ph": "X", "name": "ReadAsync 518", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735213704279, "dur": 1, "ph": "X", "name": "ProcessMessages 739", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735213704281, "dur": 45, "ph": "X", "name": "ReadAsync 739", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735213704329, "dur": 1, "ph": "X", "name": "ProcessMessages 688", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735213704330, "dur": 39, "ph": "X", "name": "ReadAsync 688", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735213704372, "dur": 31, "ph": "X", "name": "ReadAsync 673", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735213704405, "dur": 32, "ph": "X", "name": "ReadAsync 459", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735213704439, "dur": 30, "ph": "X", "name": "ReadAsync 314", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735213704472, "dur": 28, "ph": "X", "name": "ReadAsync 459", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735213704502, "dur": 31, "ph": "X", "name": "ReadAsync 470", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735213704538, "dur": 1, "ph": "X", "name": "ProcessMessages 216", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735213704540, "dur": 42, "ph": "X", "name": "ReadAsync 216", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735213704584, "dur": 1, "ph": "X", "name": "ProcessMessages 717", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735213704586, "dur": 37, "ph": "X", "name": "ReadAsync 717", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735213704625, "dur": 38, "ph": "X", "name": "ReadAsync 687", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735213704666, "dur": 1, "ph": "X", "name": "ProcessMessages 366", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735213704669, "dur": 43, "ph": "X", "name": "ReadAsync 366", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735213704714, "dur": 34, "ph": "X", "name": "ReadAsync 729", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735213704750, "dur": 28, "ph": "X", "name": "ReadAsync 315", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735213704780, "dur": 24, "ph": "X", "name": "ReadAsync 572", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735213704806, "dur": 25, "ph": "X", "name": "ReadAsync 370", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735213704833, "dur": 26, "ph": "X", "name": "ReadAsync 191", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735213704861, "dur": 38, "ph": "X", "name": "ReadAsync 286", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735213704903, "dur": 37, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735213704943, "dur": 33, "ph": "X", "name": "ReadAsync 639", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735213704978, "dur": 28, "ph": "X", "name": "ReadAsync 315", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735213705008, "dur": 27, "ph": "X", "name": "ReadAsync 349", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735213705036, "dur": 24, "ph": "X", "name": "ReadAsync 350", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735213705063, "dur": 24, "ph": "X", "name": "ReadAsync 145", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735213705089, "dur": 24, "ph": "X", "name": "ReadAsync 310", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735213705115, "dur": 24, "ph": "X", "name": "ReadAsync 286", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735213705141, "dur": 26, "ph": "X", "name": "ReadAsync 226", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735213705170, "dur": 25, "ph": "X", "name": "ReadAsync 270", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735213705197, "dur": 24, "ph": "X", "name": "ReadAsync 327", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735213705223, "dur": 25, "ph": "X", "name": "ReadAsync 107", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735213705250, "dur": 25, "ph": "X", "name": "ReadAsync 295", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735213705276, "dur": 25, "ph": "X", "name": "ReadAsync 132", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735213705303, "dur": 39, "ph": "X", "name": "ReadAsync 386", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735213705345, "dur": 1, "ph": "X", "name": "ProcessMessages 154", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735213705347, "dur": 91, "ph": "X", "name": "ReadAsync 154", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735213705442, "dur": 42, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735213705487, "dur": 31, "ph": "X", "name": "ReadAsync 578", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735213705520, "dur": 35, "ph": "X", "name": "ReadAsync 397", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735213705557, "dur": 31, "ph": "X", "name": "ReadAsync 349", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735213705591, "dur": 28, "ph": "X", "name": "ReadAsync 416", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735213705622, "dur": 28, "ph": "X", "name": "ReadAsync 145", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735213705651, "dur": 27, "ph": "X", "name": "ReadAsync 128", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735213705680, "dur": 22, "ph": "X", "name": "ReadAsync 413", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735213705705, "dur": 25, "ph": "X", "name": "ReadAsync 250", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735213705733, "dur": 25, "ph": "X", "name": "ReadAsync 311", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735213705760, "dur": 26, "ph": "X", "name": "ReadAsync 252", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735213705787, "dur": 25, "ph": "X", "name": "ReadAsync 270", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735213705813, "dur": 30, "ph": "X", "name": "ReadAsync 269", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735213705845, "dur": 25, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735213705872, "dur": 24, "ph": "X", "name": "ReadAsync 241", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735213705898, "dur": 26, "ph": "X", "name": "ReadAsync 138", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735213705926, "dur": 25, "ph": "X", "name": "ReadAsync 449", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735213705953, "dur": 26, "ph": "X", "name": "ReadAsync 204", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735213705981, "dur": 24, "ph": "X", "name": "ReadAsync 439", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735213706009, "dur": 24, "ph": "X", "name": "ReadAsync 323", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735213706035, "dur": 24, "ph": "X", "name": "ReadAsync 436", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735213706061, "dur": 24, "ph": "X", "name": "ReadAsync 363", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735213706087, "dur": 26, "ph": "X", "name": "ReadAsync 387", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735213706115, "dur": 25, "ph": "X", "name": "ReadAsync 343", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735213706142, "dur": 24, "ph": "X", "name": "ReadAsync 351", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735213706169, "dur": 27, "ph": "X", "name": "ReadAsync 309", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735213706197, "dur": 26, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735213706225, "dur": 24, "ph": "X", "name": "ReadAsync 412", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735213706252, "dur": 25, "ph": "X", "name": "ReadAsync 195", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735213706278, "dur": 25, "ph": "X", "name": "ReadAsync 226", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735213706305, "dur": 27, "ph": "X", "name": "ReadAsync 316", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735213706334, "dur": 26, "ph": "X", "name": "ReadAsync 360", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735213706362, "dur": 17, "ph": "X", "name": "ReadAsync 286", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735213706380, "dur": 22, "ph": "X", "name": "ReadAsync 156", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735213706403, "dur": 25, "ph": "X", "name": "ReadAsync 228", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735213706430, "dur": 25, "ph": "X", "name": "ReadAsync 288", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735213706457, "dur": 26, "ph": "X", "name": "ReadAsync 396", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735213706485, "dur": 28, "ph": "X", "name": "ReadAsync 351", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735213706514, "dur": 24, "ph": "X", "name": "ReadAsync 356", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735213706540, "dur": 21, "ph": "X", "name": "ReadAsync 275", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735213706563, "dur": 24, "ph": "X", "name": "ReadAsync 69", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735213706589, "dur": 24, "ph": "X", "name": "ReadAsync 233", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735213706618, "dur": 25, "ph": "X", "name": "ReadAsync 365", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735213706645, "dur": 25, "ph": "X", "name": "ReadAsync 322", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735213706672, "dur": 25, "ph": "X", "name": "ReadAsync 343", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735213706699, "dur": 25, "ph": "X", "name": "ReadAsync 254", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735213706726, "dur": 24, "ph": "X", "name": "ReadAsync 381", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735213706751, "dur": 26, "ph": "X", "name": "ReadAsync 65", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735213706779, "dur": 25, "ph": "X", "name": "ReadAsync 422", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735213706805, "dur": 25, "ph": "X", "name": "ReadAsync 294", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735213706833, "dur": 27, "ph": "X", "name": "ReadAsync 349", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735213706862, "dur": 26, "ph": "X", "name": "ReadAsync 272", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735213706889, "dur": 24, "ph": "X", "name": "ReadAsync 405", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735213706916, "dur": 24, "ph": "X", "name": "ReadAsync 155", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735213706942, "dur": 27, "ph": "X", "name": "ReadAsync 324", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735213706970, "dur": 25, "ph": "X", "name": "ReadAsync 425", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735213706997, "dur": 25, "ph": "X", "name": "ReadAsync 271", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735213707023, "dur": 24, "ph": "X", "name": "ReadAsync 325", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735213707050, "dur": 26, "ph": "X", "name": "ReadAsync 270", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735213707078, "dur": 20, "ph": "X", "name": "ReadAsync 340", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735213707100, "dur": 25, "ph": "X", "name": "ReadAsync 8", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735213707127, "dur": 25, "ph": "X", "name": "ReadAsync 375", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735213707154, "dur": 25, "ph": "X", "name": "ReadAsync 407", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735213707181, "dur": 27, "ph": "X", "name": "ReadAsync 518", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735213707210, "dur": 24, "ph": "X", "name": "ReadAsync 309", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735213707237, "dur": 25, "ph": "X", "name": "ReadAsync 317", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735213707264, "dur": 23, "ph": "X", "name": "ReadAsync 411", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735213707289, "dur": 25, "ph": "X", "name": "ReadAsync 131", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735213707317, "dur": 25, "ph": "X", "name": "ReadAsync 425", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735213707344, "dur": 26, "ph": "X", "name": "ReadAsync 360", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735213707371, "dur": 25, "ph": "X", "name": "ReadAsync 288", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735213707398, "dur": 40, "ph": "X", "name": "ReadAsync 190", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735213707439, "dur": 24, "ph": "X", "name": "ReadAsync 469", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735213707465, "dur": 16, "ph": "X", "name": "ReadAsync 298", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735213707483, "dur": 22, "ph": "X", "name": "ReadAsync 158", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735213707507, "dur": 25, "ph": "X", "name": "ReadAsync 375", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735213707533, "dur": 27, "ph": "X", "name": "ReadAsync 432", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735213707562, "dur": 25, "ph": "X", "name": "ReadAsync 526", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735213707589, "dur": 24, "ph": "X", "name": "ReadAsync 268", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735213707615, "dur": 25, "ph": "X", "name": "ReadAsync 341", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735213707641, "dur": 21, "ph": "X", "name": "ReadAsync 436", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735213707663, "dur": 25, "ph": "X", "name": "ReadAsync 21", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735213707690, "dur": 24, "ph": "X", "name": "ReadAsync 423", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735213707716, "dur": 25, "ph": "X", "name": "ReadAsync 399", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735213707744, "dur": 24, "ph": "X", "name": "ReadAsync 522", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735213707769, "dur": 25, "ph": "X", "name": "ReadAsync 325", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735213707797, "dur": 26, "ph": "X", "name": "ReadAsync 468", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735213707825, "dur": 17, "ph": "X", "name": "ReadAsync 456", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735213707844, "dur": 18, "ph": "X", "name": "ReadAsync 52", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735213707863, "dur": 22, "ph": "X", "name": "ReadAsync 368", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735213707887, "dur": 25, "ph": "X", "name": "ReadAsync 319", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735213707914, "dur": 26, "ph": "X", "name": "ReadAsync 393", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735213707943, "dur": 25, "ph": "X", "name": "ReadAsync 460", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735213707970, "dur": 24, "ph": "X", "name": "ReadAsync 318", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735213707996, "dur": 20, "ph": "X", "name": "ReadAsync 337", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735213708018, "dur": 24, "ph": "X", "name": "ReadAsync 76", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735213708044, "dur": 29, "ph": "X", "name": "ReadAsync 336", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735213708075, "dur": 25, "ph": "X", "name": "ReadAsync 535", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735213708103, "dur": 24, "ph": "X", "name": "ReadAsync 321", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735213708129, "dur": 24, "ph": "X", "name": "ReadAsync 256", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735213708155, "dur": 24, "ph": "X", "name": "ReadAsync 360", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735213708181, "dur": 24, "ph": "X", "name": "ReadAsync 243", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735213708207, "dur": 25, "ph": "X", "name": "ReadAsync 354", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735213708234, "dur": 24, "ph": "X", "name": "ReadAsync 465", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735213708260, "dur": 25, "ph": "X", "name": "ReadAsync 316", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735213708287, "dur": 24, "ph": "X", "name": "ReadAsync 360", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735213708314, "dur": 24, "ph": "X", "name": "ReadAsync 304", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735213708340, "dur": 24, "ph": "X", "name": "ReadAsync 384", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735213708366, "dur": 25, "ph": "X", "name": "ReadAsync 264", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735213708393, "dur": 25, "ph": "X", "name": "ReadAsync 332", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735213708420, "dur": 24, "ph": "X", "name": "ReadAsync 463", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735213708446, "dur": 25, "ph": "X", "name": "ReadAsync 290", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735213708473, "dur": 25, "ph": "X", "name": "ReadAsync 352", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735213708500, "dur": 20, "ph": "X", "name": "ReadAsync 523", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735213708522, "dur": 140, "ph": "X", "name": "ReadAsync 91", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735213708664, "dur": 24, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735213708691, "dur": 24, "ph": "X", "name": "ReadAsync 448", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735213708717, "dur": 26, "ph": "X", "name": "ReadAsync 361", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735213708745, "dur": 24, "ph": "X", "name": "ReadAsync 436", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735213708770, "dur": 1, "ph": "X", "name": "ProcessMessages 271", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735213708772, "dur": 26, "ph": "X", "name": "ReadAsync 271", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735213708801, "dur": 24, "ph": "X", "name": "ReadAsync 375", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735213708827, "dur": 51, "ph": "X", "name": "ReadAsync 258", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735213708879, "dur": 25, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735213708905, "dur": 29, "ph": "X", "name": "ReadAsync 422", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735213708936, "dur": 24, "ph": "X", "name": "ReadAsync 600", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735213708962, "dur": 27, "ph": "X", "name": "ReadAsync 327", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735213708992, "dur": 28, "ph": "X", "name": "ReadAsync 396", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735213709021, "dur": 24, "ph": "X", "name": "ReadAsync 594", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735213709048, "dur": 24, "ph": "X", "name": "ReadAsync 96", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735213709073, "dur": 25, "ph": "X", "name": "ReadAsync 306", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735213709100, "dur": 27, "ph": "X", "name": "ReadAsync 320", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735213709129, "dur": 26, "ph": "X", "name": "ReadAsync 411", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735213709157, "dur": 25, "ph": "X", "name": "ReadAsync 317", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735213709184, "dur": 25, "ph": "X", "name": "ReadAsync 283", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735213709210, "dur": 20, "ph": "X", "name": "ReadAsync 348", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735213709232, "dur": 25, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735213709259, "dur": 25, "ph": "X", "name": "ReadAsync 373", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735213709286, "dur": 24, "ph": "X", "name": "ReadAsync 416", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735213709313, "dur": 37, "ph": "X", "name": "ReadAsync 380", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735213709352, "dur": 25, "ph": "X", "name": "ReadAsync 336", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735213709379, "dur": 24, "ph": "X", "name": "ReadAsync 425", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735213709405, "dur": 24, "ph": "X", "name": "ReadAsync 175", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735213709432, "dur": 28, "ph": "X", "name": "ReadAsync 337", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735213709462, "dur": 24, "ph": "X", "name": "ReadAsync 459", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735213709488, "dur": 26, "ph": "X", "name": "ReadAsync 390", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735213709516, "dur": 24, "ph": "X", "name": "ReadAsync 311", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735213709543, "dur": 24, "ph": "X", "name": "ReadAsync 307", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735213709569, "dur": 31, "ph": "X", "name": "ReadAsync 313", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735213709601, "dur": 25, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735213709628, "dur": 28, "ph": "X", "name": "ReadAsync 404", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735213709658, "dur": 24, "ph": "X", "name": "ReadAsync 568", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735213709684, "dur": 26, "ph": "X", "name": "ReadAsync 305", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735213709712, "dur": 33, "ph": "X", "name": "ReadAsync 352", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735213709748, "dur": 1, "ph": "X", "name": "ProcessMessages 355", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735213709750, "dur": 42, "ph": "X", "name": "ReadAsync 355", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735213709796, "dur": 1, "ph": "X", "name": "ProcessMessages 319", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735213709798, "dur": 50, "ph": "X", "name": "ReadAsync 319", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735213709851, "dur": 1, "ph": "X", "name": "ProcessMessages 633", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735213709853, "dur": 35, "ph": "X", "name": "ReadAsync 633", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735213709890, "dur": 25, "ph": "X", "name": "ReadAsync 293", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735213709917, "dur": 40, "ph": "X", "name": "ReadAsync 209", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735213709960, "dur": 44, "ph": "X", "name": "ReadAsync 156", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735213710008, "dur": 27, "ph": "X", "name": "ReadAsync 391", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735213710036, "dur": 25, "ph": "X", "name": "ReadAsync 179", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735213710064, "dur": 25, "ph": "X", "name": "ReadAsync 142", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735213710090, "dur": 64, "ph": "X", "name": "ReadAsync 180", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735213710156, "dur": 25, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735213710183, "dur": 21, "ph": "X", "name": "ReadAsync 263", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735213710206, "dur": 24, "ph": "X", "name": "ReadAsync 112", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735213710232, "dur": 24, "ph": "X", "name": "ReadAsync 230", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735213710258, "dur": 25, "ph": "X", "name": "ReadAsync 193", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735213710285, "dur": 34, "ph": "X", "name": "ReadAsync 300", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735213710324, "dur": 30, "ph": "X", "name": "ReadAsync 239", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735213710355, "dur": 1, "ph": "X", "name": "ProcessMessages 316", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735213710357, "dur": 23, "ph": "X", "name": "ReadAsync 316", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735213710382, "dur": 25, "ph": "X", "name": "ReadAsync 76", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735213710409, "dur": 21, "ph": "X", "name": "ReadAsync 256", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735213710432, "dur": 25, "ph": "X", "name": "ReadAsync 226", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735213710458, "dur": 1, "ph": "X", "name": "ProcessMessages 194", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735213710459, "dur": 15, "ph": "X", "name": "ReadAsync 194", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735213710476, "dur": 89, "ph": "X", "name": "ReadAsync 8", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735213710568, "dur": 24, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735213710593, "dur": 24, "ph": "X", "name": "ReadAsync 255", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735213710619, "dur": 24, "ph": "X", "name": "ReadAsync 152", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735213710645, "dur": 121, "ph": "X", "name": "ReadAsync 256", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735213710768, "dur": 25, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735213710795, "dur": 24, "ph": "X", "name": "ReadAsync 390", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735213710821, "dur": 26, "ph": "X", "name": "ReadAsync 313", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735213710848, "dur": 24, "ph": "X", "name": "ReadAsync 339", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735213710875, "dur": 116, "ph": "X", "name": "ReadAsync 294", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735213710993, "dur": 23, "ph": "X", "name": "ReadAsync 240", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735213711018, "dur": 27, "ph": "X", "name": "ReadAsync 82", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735213711048, "dur": 25, "ph": "X", "name": "ReadAsync 441", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735213711074, "dur": 25, "ph": "X", "name": "ReadAsync 340", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735213711101, "dur": 24, "ph": "X", "name": "ReadAsync 373", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735213711127, "dur": 27, "ph": "X", "name": "ReadAsync 295", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735213711156, "dur": 24, "ph": "X", "name": "ReadAsync 311", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735213711183, "dur": 24, "ph": "X", "name": "ReadAsync 132", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735213711208, "dur": 24, "ph": "X", "name": "ReadAsync 159", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735213711235, "dur": 25, "ph": "X", "name": "ReadAsync 334", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735213711261, "dur": 26, "ph": "X", "name": "ReadAsync 371", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735213711289, "dur": 24, "ph": "X", "name": "ReadAsync 464", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735213711315, "dur": 24, "ph": "X", "name": "ReadAsync 110", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735213711341, "dur": 21, "ph": "X", "name": "ReadAsync 439", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735213711364, "dur": 25, "ph": "X", "name": "ReadAsync 88", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735213711392, "dur": 24, "ph": "X", "name": "ReadAsync 289", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735213711417, "dur": 25, "ph": "X", "name": "ReadAsync 279", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735213711445, "dur": 24, "ph": "X", "name": "ReadAsync 436", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735213711471, "dur": 24, "ph": "X", "name": "ReadAsync 447", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735213711497, "dur": 24, "ph": "X", "name": "ReadAsync 427", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735213711523, "dur": 26, "ph": "X", "name": "ReadAsync 435", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735213711551, "dur": 24, "ph": "X", "name": "ReadAsync 362", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735213711577, "dur": 24, "ph": "X", "name": "ReadAsync 83", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735213711603, "dur": 26, "ph": "X", "name": "ReadAsync 429", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735213711630, "dur": 25, "ph": "X", "name": "ReadAsync 590", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735213711657, "dur": 22, "ph": "X", "name": "ReadAsync 295", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735213711680, "dur": 24, "ph": "X", "name": "ReadAsync 294", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735213711706, "dur": 24, "ph": "X", "name": "ReadAsync 271", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735213711732, "dur": 25, "ph": "X", "name": "ReadAsync 360", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735213711759, "dur": 16, "ph": "X", "name": "ReadAsync 473", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735213711777, "dur": 22, "ph": "X", "name": "ReadAsync 21", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735213711801, "dur": 1, "ph": "X", "name": "ProcessMessages 369", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735213711803, "dur": 24, "ph": "X", "name": "ReadAsync 369", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735213711830, "dur": 58, "ph": "X", "name": "ReadAsync 396", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735213711890, "dur": 26, "ph": "X", "name": "ReadAsync 910", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735213711917, "dur": 24, "ph": "X", "name": "ReadAsync 414", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735213711944, "dur": 24, "ph": "X", "name": "ReadAsync 358", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735213711970, "dur": 25, "ph": "X", "name": "ReadAsync 240", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735213711996, "dur": 25, "ph": "X", "name": "ReadAsync 386", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735213712023, "dur": 16, "ph": "X", "name": "ReadAsync 415", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735213712041, "dur": 22, "ph": "X", "name": "ReadAsync 133", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735213712065, "dur": 25, "ph": "X", "name": "ReadAsync 195", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735213712091, "dur": 24, "ph": "X", "name": "ReadAsync 334", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735213712117, "dur": 24, "ph": "X", "name": "ReadAsync 282", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735213712143, "dur": 24, "ph": "X", "name": "ReadAsync 252", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735213712170, "dur": 24, "ph": "X", "name": "ReadAsync 416", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735213712196, "dur": 24, "ph": "X", "name": "ReadAsync 435", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735213712221, "dur": 25, "ph": "X", "name": "ReadAsync 351", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735213712248, "dur": 24, "ph": "X", "name": "ReadAsync 371", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735213712274, "dur": 24, "ph": "X", "name": "ReadAsync 292", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735213712300, "dur": 35, "ph": "X", "name": "ReadAsync 310", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735213712337, "dur": 37, "ph": "X", "name": "ReadAsync 363", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735213712376, "dur": 24, "ph": "X", "name": "ReadAsync 514", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735213712403, "dur": 26, "ph": "X", "name": "ReadAsync 359", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735213712431, "dur": 33, "ph": "X", "name": "ReadAsync 394", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735213712466, "dur": 24, "ph": "X", "name": "ReadAsync 67", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735213712492, "dur": 1, "ph": "X", "name": "ProcessMessages 289", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735213712494, "dur": 23, "ph": "X", "name": "ReadAsync 289", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735213712519, "dur": 25, "ph": "X", "name": "ReadAsync 526", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735213712546, "dur": 24, "ph": "X", "name": "ReadAsync 514", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735213712572, "dur": 24, "ph": "X", "name": "ReadAsync 431", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735213712598, "dur": 25, "ph": "X", "name": "ReadAsync 358", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735213712625, "dur": 20, "ph": "X", "name": "ReadAsync 440", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735213712647, "dur": 24, "ph": "X", "name": "ReadAsync 65", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735213712673, "dur": 24, "ph": "X", "name": "ReadAsync 457", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735213712699, "dur": 24, "ph": "X", "name": "ReadAsync 497", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735213712725, "dur": 24, "ph": "X", "name": "ReadAsync 314", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735213712751, "dur": 24, "ph": "X", "name": "ReadAsync 312", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735213712777, "dur": 24, "ph": "X", "name": "ReadAsync 410", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735213712803, "dur": 24, "ph": "X", "name": "ReadAsync 325", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735213712829, "dur": 26, "ph": "X", "name": "ReadAsync 348", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735213712857, "dur": 24, "ph": "X", "name": "ReadAsync 460", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735213712883, "dur": 24, "ph": "X", "name": "ReadAsync 305", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735213712909, "dur": 24, "ph": "X", "name": "ReadAsync 356", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735213712935, "dur": 24, "ph": "X", "name": "ReadAsync 375", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735213712960, "dur": 24, "ph": "X", "name": "ReadAsync 295", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735213712987, "dur": 24, "ph": "X", "name": "ReadAsync 215", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735213713013, "dur": 25, "ph": "X", "name": "ReadAsync 269", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735213713040, "dur": 24, "ph": "X", "name": "ReadAsync 390", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735213713066, "dur": 25, "ph": "X", "name": "ReadAsync 229", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735213713093, "dur": 26, "ph": "X", "name": "ReadAsync 289", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735213713121, "dur": 20, "ph": "X", "name": "ReadAsync 387", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735213713143, "dur": 21, "ph": "X", "name": "ReadAsync 5", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735213713166, "dur": 24, "ph": "X", "name": "ReadAsync 62", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735213713192, "dur": 24, "ph": "X", "name": "ReadAsync 178", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735213713218, "dur": 24, "ph": "X", "name": "ReadAsync 170", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735213713243, "dur": 24, "ph": "X", "name": "ReadAsync 142", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735213713269, "dur": 24, "ph": "X", "name": "ReadAsync 160", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735213713295, "dur": 24, "ph": "X", "name": "ReadAsync 169", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735213713321, "dur": 24, "ph": "X", "name": "ReadAsync 208", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735213713347, "dur": 16, "ph": "X", "name": "ReadAsync 196", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735213713365, "dur": 17, "ph": "X", "name": "ReadAsync 229", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735213713384, "dur": 21, "ph": "X", "name": "ReadAsync 109", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735213713407, "dur": 24, "ph": "X", "name": "ReadAsync 120", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735213713433, "dur": 16, "ph": "X", "name": "ReadAsync 151", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735213713451, "dur": 17, "ph": "X", "name": "ReadAsync 165", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735213713471, "dur": 24, "ph": "X", "name": "ReadAsync 129", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735213713496, "dur": 20, "ph": "X", "name": "ReadAsync 122", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735213713519, "dur": 40, "ph": "X", "name": "ReadAsync 109", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735213713560, "dur": 24, "ph": "X", "name": "ReadAsync 114", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735213713586, "dur": 20, "ph": "X", "name": "ReadAsync 213", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735213713608, "dur": 21, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735213713630, "dur": 24, "ph": "X", "name": "ReadAsync 40", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735213713656, "dur": 24, "ph": "X", "name": "ReadAsync 187", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735213713683, "dur": 23, "ph": "X", "name": "ReadAsync 161", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735213713708, "dur": 35, "ph": "X", "name": "ReadAsync 138", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735213713745, "dur": 25, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735213713771, "dur": 35, "ph": "X", "name": "ReadAsync 228", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735213713808, "dur": 24, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735213713834, "dur": 27, "ph": "X", "name": "ReadAsync 244", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735213713863, "dur": 16, "ph": "X", "name": "ReadAsync 379", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735213713881, "dur": 22, "ph": "X", "name": "ReadAsync 114", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735213713905, "dur": 101, "ph": "X", "name": "ReadAsync 240", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735213714008, "dur": 25, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735213714034, "dur": 27, "ph": "X", "name": "ReadAsync 240", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735213714063, "dur": 24, "ph": "X", "name": "ReadAsync 428", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735213714089, "dur": 26, "ph": "X", "name": "ReadAsync 308", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735213714117, "dur": 24, "ph": "X", "name": "ReadAsync 364", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735213714143, "dur": 24, "ph": "X", "name": "ReadAsync 262", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735213714169, "dur": 26, "ph": "X", "name": "ReadAsync 308", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735213714197, "dur": 24, "ph": "X", "name": "ReadAsync 362", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735213714223, "dur": 24, "ph": "X", "name": "ReadAsync 246", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735213714249, "dur": 61, "ph": "X", "name": "ReadAsync 237", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735213714311, "dur": 24, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735213714337, "dur": 24, "ph": "X", "name": "ReadAsync 224", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735213714363, "dur": 24, "ph": "X", "name": "ReadAsync 203", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735213714389, "dur": 25, "ph": "X", "name": "ReadAsync 355", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735213714417, "dur": 24, "ph": "X", "name": "ReadAsync 350", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735213714443, "dur": 24, "ph": "X", "name": "ReadAsync 264", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735213714469, "dur": 24, "ph": "X", "name": "ReadAsync 304", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735213714495, "dur": 24, "ph": "X", "name": "ReadAsync 326", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735213714521, "dur": 20, "ph": "X", "name": "ReadAsync 241", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735213714549, "dur": 22, "ph": "X", "name": "ReadAsync 89", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735213714573, "dur": 49, "ph": "X", "name": "ReadAsync 148", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735213714623, "dur": 24, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735213714650, "dur": 24, "ph": "X", "name": "ReadAsync 270", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735213714676, "dur": 24, "ph": "X", "name": "ReadAsync 304", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735213714702, "dur": 24, "ph": "X", "name": "ReadAsync 316", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735213714728, "dur": 42, "ph": "X", "name": "ReadAsync 307", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735213714772, "dur": 24, "ph": "X", "name": "ReadAsync 347", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735213714798, "dur": 24, "ph": "X", "name": "ReadAsync 158", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735213714824, "dur": 25, "ph": "X", "name": "ReadAsync 334", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735213714851, "dur": 25, "ph": "X", "name": "ReadAsync 151", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735213714877, "dur": 49, "ph": "X", "name": "ReadAsync 134", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735213714929, "dur": 24, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735213714955, "dur": 26, "ph": "X", "name": "ReadAsync 258", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735213714983, "dur": 26, "ph": "X", "name": "ReadAsync 333", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735213715010, "dur": 20, "ph": "X", "name": "ReadAsync 280", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735213715032, "dur": 54, "ph": "X", "name": "ReadAsync 57", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735213715088, "dur": 24, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735213715114, "dur": 24, "ph": "X", "name": "ReadAsync 281", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735213715140, "dur": 25, "ph": "X", "name": "ReadAsync 349", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735213715168, "dur": 23, "ph": "X", "name": "ReadAsync 303", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735213715193, "dur": 48, "ph": "X", "name": "ReadAsync 53", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735213715242, "dur": 25, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735213715269, "dur": 24, "ph": "X", "name": "ReadAsync 304", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735213715296, "dur": 24, "ph": "X", "name": "ReadAsync 348", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735213715322, "dur": 31, "ph": "X", "name": "ReadAsync 210", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735213715357, "dur": 42, "ph": "X", "name": "ReadAsync 138", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735213715401, "dur": 27, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735213715430, "dur": 25, "ph": "X", "name": "ReadAsync 310", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735213715457, "dur": 25, "ph": "X", "name": "ReadAsync 182", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735213715483, "dur": 24, "ph": "X", "name": "ReadAsync 410", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735213715509, "dur": 46, "ph": "X", "name": "ReadAsync 134", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735213715557, "dur": 25, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735213715583, "dur": 48, "ph": "X", "name": "ReadAsync 340", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735213715638, "dur": 2, "ph": "X", "name": "ProcessMessages 440", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735213715642, "dur": 72, "ph": "X", "name": "ReadAsync 440", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735213715717, "dur": 57, "ph": "X", "name": "ReadAsync 232", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735213715779, "dur": 1, "ph": "X", "name": "ProcessMessages 144", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735213715782, "dur": 52, "ph": "X", "name": "ReadAsync 144", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735213715839, "dur": 46, "ph": "X", "name": "ReadAsync 202", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735213715888, "dur": 1, "ph": "X", "name": "ProcessMessages 701", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735213715890, "dur": 27, "ph": "X", "name": "ReadAsync 701", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735213715919, "dur": 27, "ph": "X", "name": "ReadAsync 372", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735213715948, "dur": 54, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735213716004, "dur": 26, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735213716032, "dur": 26, "ph": "X", "name": "ReadAsync 358", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735213716060, "dur": 24, "ph": "X", "name": "ReadAsync 509", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735213716086, "dur": 25, "ph": "X", "name": "ReadAsync 133", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735213716113, "dur": 69, "ph": "X", "name": "ReadAsync 237", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735213716184, "dur": 24, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735213716210, "dur": 24, "ph": "X", "name": "ReadAsync 369", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735213716237, "dur": 24, "ph": "X", "name": "ReadAsync 139", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735213716263, "dur": 24, "ph": "X", "name": "ReadAsync 441", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735213716289, "dur": 54, "ph": "X", "name": "ReadAsync 145", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735213716345, "dur": 24, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735213716371, "dur": 24, "ph": "X", "name": "ReadAsync 322", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735213716397, "dur": 24, "ph": "X", "name": "ReadAsync 190", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735213716424, "dur": 24, "ph": "X", "name": "ReadAsync 426", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735213716450, "dur": 45, "ph": "X", "name": "ReadAsync 134", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735213716496, "dur": 25, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735213716523, "dur": 25, "ph": "X", "name": "ReadAsync 334", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735213716550, "dur": 24, "ph": "X", "name": "ReadAsync 353", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735213716576, "dur": 60, "ph": "X", "name": "ReadAsync 343", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735213716638, "dur": 24, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735213716664, "dur": 26, "ph": "X", "name": "ReadAsync 297", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735213716691, "dur": 24, "ph": "X", "name": "ReadAsync 341", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735213716717, "dur": 24, "ph": "X", "name": "ReadAsync 147", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735213716743, "dur": 73, "ph": "X", "name": "ReadAsync 197", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735213716817, "dur": 24, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735213716844, "dur": 24, "ph": "X", "name": "ReadAsync 298", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735213716870, "dur": 24, "ph": "X", "name": "ReadAsync 147", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735213716896, "dur": 25, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735213716922, "dur": 25, "ph": "X", "name": "ReadAsync 383", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735213716949, "dur": 24, "ph": "X", "name": "ReadAsync 421", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735213716976, "dur": 26, "ph": "X", "name": "ReadAsync 235", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735213717003, "dur": 24, "ph": "X", "name": "ReadAsync 420", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735213717030, "dur": 25, "ph": "X", "name": "ReadAsync 183", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735213717057, "dur": 16, "ph": "X", "name": "ReadAsync 415", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735213717075, "dur": 22, "ph": "X", "name": "ReadAsync 89", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735213717098, "dur": 67, "ph": "X", "name": "ReadAsync 219", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735213717167, "dur": 26, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735213717195, "dur": 25, "ph": "X", "name": "ReadAsync 332", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735213717222, "dur": 24, "ph": "X", "name": "ReadAsync 364", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735213717249, "dur": 25, "ph": "X", "name": "ReadAsync 307", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735213717276, "dur": 24, "ph": "X", "name": "ReadAsync 413", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735213717303, "dur": 24, "ph": "X", "name": "ReadAsync 378", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735213717329, "dur": 21, "ph": "X", "name": "ReadAsync 151", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735213717351, "dur": 25, "ph": "X", "name": "ReadAsync 116", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735213717378, "dur": 24, "ph": "X", "name": "ReadAsync 392", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735213717404, "dur": 24, "ph": "X", "name": "ReadAsync 114", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735213717430, "dur": 51, "ph": "X", "name": "ReadAsync 200", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735213717483, "dur": 24, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735213717509, "dur": 25, "ph": "X", "name": "ReadAsync 313", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735213717536, "dur": 24, "ph": "X", "name": "ReadAsync 301", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735213717562, "dur": 25, "ph": "X", "name": "ReadAsync 224", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735213717589, "dur": 28, "ph": "X", "name": "ReadAsync 377", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735213717619, "dur": 60, "ph": "X", "name": "ReadAsync 261", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735213717680, "dur": 24, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735213717707, "dur": 24, "ph": "X", "name": "ReadAsync 309", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735213717733, "dur": 27, "ph": "X", "name": "ReadAsync 381", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735213717761, "dur": 24, "ph": "X", "name": "ReadAsync 420", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735213717787, "dur": 24, "ph": "X", "name": "ReadAsync 322", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735213717813, "dur": 20, "ph": "X", "name": "ReadAsync 283", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735213717835, "dur": 24, "ph": "X", "name": "ReadAsync 113", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735213717861, "dur": 26, "ph": "X", "name": "ReadAsync 270", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735213717889, "dur": 20, "ph": "X", "name": "ReadAsync 411", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735213717911, "dur": 24, "ph": "X", "name": "ReadAsync 71", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735213717937, "dur": 58, "ph": "X", "name": "ReadAsync 237", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735213717997, "dur": 24, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735213718023, "dur": 25, "ph": "X", "name": "ReadAsync 320", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735213718050, "dur": 26, "ph": "X", "name": "ReadAsync 353", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735213718077, "dur": 24, "ph": "X", "name": "ReadAsync 340", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735213718103, "dur": 37, "ph": "X", "name": "ReadAsync 8", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735213718142, "dur": 24, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735213718168, "dur": 24, "ph": "X", "name": "ReadAsync 306", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735213718194, "dur": 26, "ph": "X", "name": "ReadAsync 376", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735213718223, "dur": 20, "ph": "X", "name": "ReadAsync 346", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735213718244, "dur": 52, "ph": "X", "name": "ReadAsync 8", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735213718298, "dur": 24, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735213718324, "dur": 25, "ph": "X", "name": "ReadAsync 271", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735213718351, "dur": 24, "ph": "X", "name": "ReadAsync 358", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735213718377, "dur": 24, "ph": "X", "name": "ReadAsync 527", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735213718403, "dur": 24, "ph": "X", "name": "ReadAsync 310", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735213718429, "dur": 25, "ph": "X", "name": "ReadAsync 369", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735213718456, "dur": 24, "ph": "X", "name": "ReadAsync 414", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735213718482, "dur": 20, "ph": "X", "name": "ReadAsync 433", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735213718504, "dur": 26, "ph": "X", "name": "ReadAsync 101", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735213718532, "dur": 52, "ph": "X", "name": "ReadAsync 228", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735213718586, "dur": 24, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735213718612, "dur": 25, "ph": "X", "name": "ReadAsync 349", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735213718640, "dur": 24, "ph": "X", "name": "ReadAsync 376", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735213718666, "dur": 21, "ph": "X", "name": "ReadAsync 295", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735213718688, "dur": 47, "ph": "X", "name": "ReadAsync 68", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735213718737, "dur": 24, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735213718764, "dur": 24, "ph": "X", "name": "ReadAsync 337", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735213718790, "dur": 24, "ph": "X", "name": "ReadAsync 421", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735213718816, "dur": 23, "ph": "X", "name": "ReadAsync 357", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735213718841, "dur": 37, "ph": "X", "name": "ReadAsync 8", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735213718880, "dur": 24, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735213718906, "dur": 24, "ph": "X", "name": "ReadAsync 239", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735213718932, "dur": 24, "ph": "X", "name": "ReadAsync 242", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735213718958, "dur": 25, "ph": "X", "name": "ReadAsync 388", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735213718985, "dur": 49, "ph": "X", "name": "ReadAsync 228", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735213719036, "dur": 24, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735213719062, "dur": 24, "ph": "X", "name": "ReadAsync 325", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735213719088, "dur": 28, "ph": "X", "name": "ReadAsync 373", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735213719119, "dur": 54, "ph": "X", "name": "ReadAsync 347", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735213719175, "dur": 24, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735213719202, "dur": 24, "ph": "X", "name": "ReadAsync 321", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735213719227, "dur": 24, "ph": "X", "name": "ReadAsync 443", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735213719253, "dur": 58, "ph": "X", "name": "ReadAsync 306", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735213719313, "dur": 25, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735213719340, "dur": 24, "ph": "X", "name": "ReadAsync 339", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735213719366, "dur": 24, "ph": "X", "name": "ReadAsync 385", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735213719391, "dur": 1, "ph": "X", "name": "ProcessMessages 441", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735213719393, "dur": 24, "ph": "X", "name": "ReadAsync 441", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735213719419, "dur": 24, "ph": "X", "name": "ReadAsync 325", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735213719445, "dur": 26, "ph": "X", "name": "ReadAsync 380", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735213719473, "dur": 24, "ph": "X", "name": "ReadAsync 388", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735213719498, "dur": 20, "ph": "X", "name": "ReadAsync 299", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735213719521, "dur": 24, "ph": "X", "name": "ReadAsync 72", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735213719546, "dur": 60, "ph": "X", "name": "ReadAsync 237", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735213719608, "dur": 23, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735213719634, "dur": 25, "ph": "X", "name": "ReadAsync 345", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735213719661, "dur": 24, "ph": "X", "name": "ReadAsync 436", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735213719687, "dur": 24, "ph": "X", "name": "ReadAsync 267", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735213719713, "dur": 24, "ph": "X", "name": "ReadAsync 526", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735213719739, "dur": 24, "ph": "X", "name": "ReadAsync 403", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735213719765, "dur": 25, "ph": "X", "name": "ReadAsync 289", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735213719791, "dur": 24, "ph": "X", "name": "ReadAsync 374", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735213719817, "dur": 24, "ph": "X", "name": "ReadAsync 337", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735213719843, "dur": 16, "ph": "X", "name": "ReadAsync 187", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735213719861, "dur": 48, "ph": "X", "name": "ReadAsync 53", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735213719911, "dur": 25, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735213719938, "dur": 26, "ph": "X", "name": "ReadAsync 380", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735213719966, "dur": 24, "ph": "X", "name": "ReadAsync 414", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735213719992, "dur": 24, "ph": "X", "name": "ReadAsync 338", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735213720018, "dur": 24, "ph": "X", "name": "ReadAsync 403", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735213720044, "dur": 26, "ph": "X", "name": "ReadAsync 371", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735213720072, "dur": 24, "ph": "X", "name": "ReadAsync 415", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735213720097, "dur": 1, "ph": "X", "name": "ProcessMessages 332", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735213720099, "dur": 23, "ph": "X", "name": "ReadAsync 332", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735213720124, "dur": 24, "ph": "X", "name": "ReadAsync 190", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735213720150, "dur": 49, "ph": "X", "name": "ReadAsync 237", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735213720200, "dur": 24, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735213720226, "dur": 24, "ph": "X", "name": "ReadAsync 371", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735213720253, "dur": 25, "ph": "X", "name": "ReadAsync 457", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735213720279, "dur": 21, "ph": "X", "name": "ReadAsync 247", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735213720302, "dur": 91, "ph": "X", "name": "ReadAsync 136", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735213720412, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735213720414, "dur": 48, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735213720465, "dur": 1, "ph": "X", "name": "ProcessMessages 1177", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735213720468, "dur": 95, "ph": "X", "name": "ReadAsync 1177", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735213720569, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735213720572, "dur": 103, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735213720679, "dur": 2, "ph": "X", "name": "ProcessMessages 986", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735213720683, "dur": 59, "ph": "X", "name": "ReadAsync 986", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735213720745, "dur": 1, "ph": "X", "name": "ProcessMessages 240", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735213720747, "dur": 37, "ph": "X", "name": "ReadAsync 240", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735213720788, "dur": 27, "ph": "X", "name": "ReadAsync 125", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735213720817, "dur": 35, "ph": "X", "name": "ReadAsync 452", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735213720855, "dur": 25, "ph": "X", "name": "ReadAsync 479", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735213720882, "dur": 79, "ph": "X", "name": "ReadAsync 139", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735213720964, "dur": 33, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735213721000, "dur": 28, "ph": "X", "name": "ReadAsync 484", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735213721030, "dur": 33, "ph": "X", "name": "ReadAsync 33", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735213721065, "dur": 1, "ph": "X", "name": "ProcessMessages 529", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735213721067, "dur": 39, "ph": "X", "name": "ReadAsync 529", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735213721109, "dur": 1, "ph": "X", "name": "ProcessMessages 56", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735213721111, "dur": 43, "ph": "X", "name": "ReadAsync 56", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735213721159, "dur": 39, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735213721201, "dur": 29, "ph": "X", "name": "ReadAsync 714", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735213721233, "dur": 1, "ph": "X", "name": "ProcessMessages 387", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735213721235, "dur": 39, "ph": "X", "name": "ReadAsync 387", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735213721276, "dur": 25, "ph": "X", "name": "ReadAsync 577", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735213721303, "dur": 36, "ph": "X", "name": "ReadAsync 326", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735213721343, "dur": 1, "ph": "X", "name": "ProcessMessages 430", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735213721345, "dur": 45, "ph": "X", "name": "ReadAsync 430", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735213721393, "dur": 1, "ph": "X", "name": "ProcessMessages 340", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735213721396, "dur": 33, "ph": "X", "name": "ReadAsync 340", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735213721431, "dur": 43, "ph": "X", "name": "ReadAsync 67", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735213721478, "dur": 52, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735213721533, "dur": 1, "ph": "X", "name": "ProcessMessages 654", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735213721535, "dur": 51, "ph": "X", "name": "ReadAsync 654", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735213721588, "dur": 1, "ph": "X", "name": "ProcessMessages 440", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735213721590, "dur": 46, "ph": "X", "name": "ReadAsync 440", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735213721640, "dur": 52, "ph": "X", "name": "ReadAsync 63", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735213721696, "dur": 111, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735213721810, "dur": 1, "ph": "X", "name": "ProcessMessages 749", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735213721811, "dur": 88, "ph": "X", "name": "ReadAsync 749", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735213721903, "dur": 1, "ph": "X", "name": "ProcessMessages 359", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735213721905, "dur": 65, "ph": "X", "name": "ReadAsync 359", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735213721971, "dur": 27, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735213722001, "dur": 33, "ph": "X", "name": "ReadAsync 342", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735213722037, "dur": 1, "ph": "X", "name": "ProcessMessages 531", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735213722039, "dur": 37, "ph": "X", "name": "ReadAsync 531", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735213722080, "dur": 1, "ph": "X", "name": "ProcessMessages 248", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735213722082, "dur": 35, "ph": "X", "name": "ReadAsync 248", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735213722119, "dur": 28, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735213722149, "dur": 31, "ph": "X", "name": "ReadAsync 363", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735213722181, "dur": 27, "ph": "X", "name": "ReadAsync 500", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735213722212, "dur": 88, "ph": "X", "name": "ReadAsync 189", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735213722303, "dur": 1, "ph": "X", "name": "ProcessMessages 56", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735213722305, "dur": 44, "ph": "X", "name": "ReadAsync 56", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735213722354, "dur": 38, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735213722395, "dur": 1, "ph": "X", "name": "ProcessMessages 482", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735213722397, "dur": 35, "ph": "X", "name": "ReadAsync 482", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735213722434, "dur": 1, "ph": "X", "name": "ProcessMessages 275", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735213722436, "dur": 35, "ph": "X", "name": "ReadAsync 275", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735213722473, "dur": 33, "ph": "X", "name": "ReadAsync 554", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735213722509, "dur": 1, "ph": "X", "name": "ProcessMessages 395", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735213722510, "dur": 40, "ph": "X", "name": "ReadAsync 395", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735213722554, "dur": 1, "ph": "X", "name": "ProcessMessages 556", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735213722556, "dur": 36, "ph": "X", "name": "ReadAsync 556", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735213722594, "dur": 26, "ph": "X", "name": "ReadAsync 196", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735213722622, "dur": 60, "ph": "X", "name": "ReadAsync 61", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735213722684, "dur": 26, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735213722712, "dur": 25, "ph": "X", "name": "ReadAsync 288", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735213722738, "dur": 27, "ph": "X", "name": "ReadAsync 186", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735213722767, "dur": 25, "ph": "X", "name": "ReadAsync 424", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735213722794, "dur": 25, "ph": "X", "name": "ReadAsync 334", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735213722820, "dur": 1, "ph": "X", "name": "ProcessMessages 391", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735213722822, "dur": 25, "ph": "X", "name": "ReadAsync 391", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735213722849, "dur": 24, "ph": "X", "name": "ReadAsync 358", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735213722875, "dur": 25, "ph": "X", "name": "ReadAsync 265", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735213722901, "dur": 24, "ph": "X", "name": "ReadAsync 289", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735213722927, "dur": 71, "ph": "X", "name": "ReadAsync 240", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735213723000, "dur": 26, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735213723028, "dur": 24, "ph": "X", "name": "ReadAsync 314", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735213723054, "dur": 25, "ph": "X", "name": "ReadAsync 321", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735213723081, "dur": 27, "ph": "X", "name": "ReadAsync 342", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735213723110, "dur": 24, "ph": "X", "name": "ReadAsync 394", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735213723136, "dur": 24, "ph": "X", "name": "ReadAsync 120", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735213723162, "dur": 25, "ph": "X", "name": "ReadAsync 264", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735213723189, "dur": 24, "ph": "X", "name": "ReadAsync 322", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735213723216, "dur": 23, "ph": "X", "name": "ReadAsync 144", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735213723241, "dur": 67, "ph": "X", "name": "ReadAsync 240", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735213723309, "dur": 25, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735213723336, "dur": 27, "ph": "X", "name": "ReadAsync 229", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735213723364, "dur": 24, "ph": "X", "name": "ReadAsync 499", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735213723390, "dur": 24, "ph": "X", "name": "ReadAsync 237", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735213723416, "dur": 56, "ph": "X", "name": "ReadAsync 141", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735213723474, "dur": 25, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735213723501, "dur": 24, "ph": "X", "name": "ReadAsync 390", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735213723527, "dur": 24, "ph": "X", "name": "ReadAsync 187", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735213723553, "dur": 16, "ph": "X", "name": "ReadAsync 515", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735213723571, "dur": 54, "ph": "X", "name": "ReadAsync 139", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735213723626, "dur": 25, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735213723652, "dur": 25, "ph": "X", "name": "ReadAsync 400", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735213723679, "dur": 1, "ph": "X", "name": "ProcessMessages 421", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735213723680, "dur": 27, "ph": "X", "name": "ReadAsync 421", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735213723709, "dur": 20, "ph": "X", "name": "ReadAsync 304", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735213723731, "dur": 54, "ph": "X", "name": "ReadAsync 61", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735213723787, "dur": 24, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735213723814, "dur": 23, "ph": "X", "name": "ReadAsync 272", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735213723839, "dur": 25, "ph": "X", "name": "ReadAsync 406", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735213723867, "dur": 26, "ph": "X", "name": "ReadAsync 470", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735213723895, "dur": 25, "ph": "X", "name": "ReadAsync 489", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735213723921, "dur": 24, "ph": "X", "name": "ReadAsync 297", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735213723948, "dur": 26, "ph": "X", "name": "ReadAsync 322", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735213723975, "dur": 21, "ph": "X", "name": "ReadAsync 479", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735213723998, "dur": 24, "ph": "X", "name": "ReadAsync 193", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735213724024, "dur": 55, "ph": "X", "name": "ReadAsync 237", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735213724081, "dur": 24, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735213724107, "dur": 25, "ph": "X", "name": "ReadAsync 373", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735213724134, "dur": 39, "ph": "X", "name": "ReadAsync 421", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735213724176, "dur": 27, "ph": "X", "name": "ReadAsync 351", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735213724205, "dur": 24, "ph": "X", "name": "ReadAsync 485", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735213724231, "dur": 24, "ph": "X", "name": "ReadAsync 292", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735213724257, "dur": 25, "ph": "X", "name": "ReadAsync 347", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735213724283, "dur": 21, "ph": "X", "name": "ReadAsync 524", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735213724305, "dur": 24, "ph": "X", "name": "ReadAsync 92", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735213724332, "dur": 59, "ph": "X", "name": "ReadAsync 240", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735213724392, "dur": 24, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735213724418, "dur": 26, "ph": "X", "name": "ReadAsync 370", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735213724446, "dur": 24, "ph": "X", "name": "ReadAsync 429", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735213724472, "dur": 25, "ph": "X", "name": "ReadAsync 260", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735213724499, "dur": 25, "ph": "X", "name": "ReadAsync 516", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735213724526, "dur": 24, "ph": "X", "name": "ReadAsync 321", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735213724552, "dur": 24, "ph": "X", "name": "ReadAsync 379", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735213724578, "dur": 25, "ph": "X", "name": "ReadAsync 478", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735213724605, "dur": 24, "ph": "X", "name": "ReadAsync 200", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735213724631, "dur": 55, "ph": "X", "name": "ReadAsync 237", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735213724687, "dur": 26, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735213724715, "dur": 25, "ph": "X", "name": "ReadAsync 359", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735213724742, "dur": 24, "ph": "X", "name": "ReadAsync 349", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735213724768, "dur": 24, "ph": "X", "name": "ReadAsync 220", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735213724794, "dur": 24, "ph": "X", "name": "ReadAsync 411", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735213724820, "dur": 24, "ph": "X", "name": "ReadAsync 262", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735213724846, "dur": 26, "ph": "X", "name": "ReadAsync 303", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735213724874, "dur": 24, "ph": "X", "name": "ReadAsync 402", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735213724899, "dur": 24, "ph": "X", "name": "ReadAsync 156", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735213724925, "dur": 59, "ph": "X", "name": "ReadAsync 237", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735213724986, "dur": 25, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735213725013, "dur": 24, "ph": "X", "name": "ReadAsync 317", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735213725038, "dur": 25, "ph": "X", "name": "ReadAsync 310", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735213725066, "dur": 20, "ph": "X", "name": "ReadAsync 424", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735213725088, "dur": 42, "ph": "X", "name": "ReadAsync 57", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735213725132, "dur": 25, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735213725159, "dur": 24, "ph": "X", "name": "ReadAsync 303", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735213725185, "dur": 26, "ph": "X", "name": "ReadAsync 370", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735213725212, "dur": 24, "ph": "X", "name": "ReadAsync 464", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735213725238, "dur": 27, "ph": "X", "name": "ReadAsync 394", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735213725267, "dur": 46, "ph": "X", "name": "ReadAsync 206", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735213725316, "dur": 24, "ph": "X", "name": "ReadAsync 445", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735213725342, "dur": 24, "ph": "X", "name": "ReadAsync 347", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735213725367, "dur": 1, "ph": "X", "name": "ProcessMessages 327", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735213725369, "dur": 24, "ph": "X", "name": "ReadAsync 327", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735213725394, "dur": 20, "ph": "X", "name": "ReadAsync 229", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735213725416, "dur": 43, "ph": "X", "name": "ReadAsync 8", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735213725460, "dur": 25, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735213725488, "dur": 26, "ph": "X", "name": "ReadAsync 363", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735213725516, "dur": 23, "ph": "X", "name": "ReadAsync 393", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735213725540, "dur": 21, "ph": "X", "name": "ReadAsync 100", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735213725563, "dur": 24, "ph": "X", "name": "ReadAsync 137", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735213725589, "dur": 25, "ph": "X", "name": "ReadAsync 215", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735213725616, "dur": 24, "ph": "X", "name": "ReadAsync 251", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735213725643, "dur": 23, "ph": "X", "name": "ReadAsync 202", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735213725668, "dur": 24, "ph": "X", "name": "ReadAsync 163", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735213725693, "dur": 24, "ph": "X", "name": "ReadAsync 210", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735213725719, "dur": 24, "ph": "X", "name": "ReadAsync 189", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735213725745, "dur": 24, "ph": "X", "name": "ReadAsync 198", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735213725771, "dur": 20, "ph": "X", "name": "ReadAsync 229", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735213725793, "dur": 51, "ph": "X", "name": "ReadAsync 75", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735213725846, "dur": 24, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735213725872, "dur": 20, "ph": "X", "name": "ReadAsync 168", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735213725895, "dur": 115, "ph": "X", "name": "ReadAsync 57", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735213726012, "dur": 24, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735213726037, "dur": 24, "ph": "X", "name": "ReadAsync 176", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735213726063, "dur": 24, "ph": "X", "name": "ReadAsync 159", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735213726089, "dur": 24, "ph": "X", "name": "ReadAsync 122", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735213726115, "dur": 34, "ph": "X", "name": "ReadAsync 223", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735213726150, "dur": 24, "ph": "X", "name": "ReadAsync 97", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735213726176, "dur": 24, "ph": "X", "name": "ReadAsync 187", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735213726202, "dur": 24, "ph": "X", "name": "ReadAsync 148", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735213726228, "dur": 24, "ph": "X", "name": "ReadAsync 182", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735213726254, "dur": 24, "ph": "X", "name": "ReadAsync 172", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735213726280, "dur": 34, "ph": "X", "name": "ReadAsync 136", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735213726316, "dur": 24, "ph": "X", "name": "ReadAsync 179", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735213726342, "dur": 24, "ph": "X", "name": "ReadAsync 163", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735213726368, "dur": 24, "ph": "X", "name": "ReadAsync 169", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735213726394, "dur": 20, "ph": "X", "name": "ReadAsync 205", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735213726415, "dur": 16, "ph": "X", "name": "ReadAsync 68", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735213726433, "dur": 23, "ph": "X", "name": "ReadAsync 94", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735213726458, "dur": 17, "ph": "X", "name": "ReadAsync 89", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735213726477, "dur": 99, "ph": "X", "name": "ReadAsync 57", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735213726578, "dur": 24, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735213726604, "dur": 16, "ph": "X", "name": "ReadAsync 150", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735213726621, "dur": 21, "ph": "X", "name": "ReadAsync 153", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735213726644, "dur": 17, "ph": "X", "name": "ReadAsync 115", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735213726663, "dur": 17, "ph": "X", "name": "ReadAsync 151", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735213726682, "dur": 25, "ph": "X", "name": "ReadAsync 210", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735213726709, "dur": 24, "ph": "X", "name": "ReadAsync 302", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735213726736, "dur": 24, "ph": "X", "name": "ReadAsync 337", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735213726762, "dur": 24, "ph": "X", "name": "ReadAsync 362", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735213726788, "dur": 26, "ph": "X", "name": "ReadAsync 270", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735213726817, "dur": 23, "ph": "X", "name": "ReadAsync 401", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735213726842, "dur": 24, "ph": "X", "name": "ReadAsync 70", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735213726867, "dur": 20, "ph": "X", "name": "ReadAsync 175", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735213726890, "dur": 58, "ph": "X", "name": "ReadAsync 62", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735213726950, "dur": 24, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735213726976, "dur": 96, "ph": "X", "name": "ReadAsync 224", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735213727073, "dur": 20, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735213727094, "dur": 448, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735213727546, "dur": 79, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735213727628, "dur": 4, "ph": "X", "name": "ProcessMessages 1168", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735213727633, "dur": 52, "ph": "X", "name": "ReadAsync 1168", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735213727689, "dur": 243, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735213727937, "dur": 29, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735213727968, "dur": 201, "ph": "X", "name": "ProcessMessages 13", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735213728169, "dur": 4995, "ph": "X", "name": "ReadAsync 13", "args": {}}, {"pid": 3100, "tid": 24780, "ts": 1754735215677371, "dur": 602, "ph": "X", "name": "ReadEntireBinlogFromIpcAsync", "args": {}}, {"pid": 3100, "tid": 8589934592, "ph": "M", "name": "thread_name", "args": {"name": "WaitForBuildProgramInputDataBeingWrittenAndSendDagReadyMessageAsync"}}, {"pid": 3100, "tid": 8589934592, "ts": 1754735213544512, "dur": 123181, "ph": "X", "name": "await writeBuildProgramInputDataTask", "args": {}}, {"pid": 3100, "tid": 8589934592, "ts": 1754735213667696, "dur": 32317, "ph": "X", "name": "WritePipe.WaitConnectionAsync", "args": {}}, {"pid": 3100, "tid": 8589934592, "ts": 1754735213700015, "dur": 1616, "ph": "X", "name": "WriteDagReadyMessage", "args": {}}, {"pid": 3100, "tid": 24780, "ts": 1754735215677973, "dur": 9, "ph": "X", "name": "WaitForBuildProgramInputDataBeingWrittenAndSendDagReadyMessageAsync", "args": {}}, {"pid": 3100, "tid": 4294967296, "ph": "M", "name": "thread_name", "args": {"name": "BuildAsync"}}, {"pid": 3100, "tid": 4294967296, "ts": 1754735213416524, "dur": 317621, "ph": "X", "name": "RunBackend", "args": {}}, {"pid": 3100, "tid": 4294967296, "ts": 1754735213419055, "dur": 122322, "ph": "X", "name": "BackendProgram.Start", "args": {}}, {"pid": 3100, "tid": 4294967296, "ts": 1754735213734280, "dur": 642709, "ph": "X", "name": "await ExecuteBuildProgram", "args": {}}, {"pid": 3100, "tid": 4294967296, "ts": 1754735214377225, "dur": 1291854, "ph": "X", "name": "RunBackend", "args": {}}, {"pid": 3100, "tid": 4294967296, "ts": 1754735214377396, "dur": 115752, "ph": "X", "name": "BackendProgram.Start", "args": {}}, {"pid": 3100, "tid": 4294967296, "ts": 1754735215669092, "dur": 2797, "ph": "X", "name": "await WaitForAndApplyScriptUpdaters", "args": {}}, {"pid": 3100, "tid": 4294967296, "ts": 1754735215671191, "dur": 27, "ph": "X", "name": "await <PERSON><PERSON>tUp<PERSON><PERSON>", "args": {}}, {"pid": 3100, "tid": 4294967296, "ts": 1754735215671893, "dur": 8, "ph": "X", "name": "await taskToReadBuildProgramOutput", "args": {}}, {"pid": 3100, "tid": 24780, "ts": 1754735215677983, "dur": 5, "ph": "X", "name": "BuildAsync", "args": {}}, {"cat": "", "pid": 12345, "tid": 0, "ts": 0, "ph": "M", "name": "process_name", "args": {"name": "bee_backend"}}, {"pid": 12345, "tid": 0, "ts": 1754735214629515, "dur": 104316, "ph": "X", "name": "DriverInitData", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1754735214733840, "dur": 288, "ph": "X", "name": "RemoveStaleOutputs", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1754735214734246, "dur": 80, "ph": "X", "name": "<PERSON><PERSON>", "args": {"detail": "PrepareNodes"}}, {"pid": 12345, "tid": 0, "ts": 1754735214734327, "dur": 513, "ph": "X", "name": "BuildQueueInit", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1754735214740929, "dur": 53, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Core.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1754735214743797, "dur": 74, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.TextMeshPro.Editor.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 0, "ts": 1754735214746735, "dur": 53, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/18218310762646611085.rsp"}}, {"pid": 12345, "tid": 0, "ts": 1754735214747116, "dur": 137, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/15394042617203071315.rsp"}}, {"pid": 12345, "tid": 0, "ts": 1754735214750655, "dur": 59, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.RenderPipelines.Core.Editor.dll"}}, {"pid": 12345, "tid": 0, "ts": 1754735214750907, "dur": 50, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.RenderPipelines.Universal.Config.Runtime.dll"}}, {"pid": 12345, "tid": 0, "ts": 1754735214752424, "dur": 123, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "CopyFiles Library/ScriptAssemblies/CrashKonijn.Goap.Editor.dll"}}, {"pid": 12345, "tid": 0, "ts": 1754735214734861, "dur": 20793, "ph": "X", "name": "EnqueueRequestedNodes", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1754735214755668, "dur": 906921, "ph": "X", "name": "WaitForBuildFinished", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1754735215662591, "dur": 220, "ph": "X", "name": "JoinBuildThread", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1754735215662847, "dur": 63, "ph": "X", "name": "JoinBuildThread", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1754735215663074, "dur": 51, "ph": "X", "name": "BuildQueueDestroyTail", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1754735215663147, "dur": 1084, "ph": "X", "name": "<PERSON><PERSON>", "args": {"detail": "Write AllBuiltNodes"}}, {"pid": 12345, "tid": 1, "ts": 1754735214735108, "dur": 20578, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1754735214755873, "dur": 52, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1754735214755952, "dur": 87, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ContentLoadModule.dll_9B44DAA96A1B4B8B.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1754735214756119, "dur": 135, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.TextCoreTextEngineModule.dll_EF7BA45317B31323.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1754735214756449, "dur": 108, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Mathematics.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 1, "ts": 1754735214756651, "dur": 117, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Flow.Editor.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 1, "ts": 1754735214756813, "dur": 59, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VSCode.Editor.rsp2"}}, {"pid": 12345, "tid": 1, "ts": 1754735214756880, "dur": 52, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Core.ShaderLibrary.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 1, "ts": 1754735214756989, "dur": 97, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Universal.Shaders.rsp"}}, {"pid": 12345, "tid": 1, "ts": 1754735214757113, "dur": 78, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/com.crashkonijn.goap.demos.turn-based.rsp"}}, {"pid": 12345, "tid": 1, "ts": 1754735214757249, "dur": 93, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/8654950501350094070.rsp"}}, {"pid": 12345, "tid": 1, "ts": 1754735214757362, "dur": 162, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1754735214757528, "dur": 216, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1754735214757744, "dur": 305, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1754735214758049, "dur": 159, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1754735214758208, "dur": 994, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1754735214759203, "dur": 322, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1754735214759525, "dur": 222, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1754735214759747, "dur": 756, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1754735214760503, "dur": 314, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1754735214760817, "dur": 257, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1754735214761074, "dur": 257, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1754735214761332, "dur": 281, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1754735214761614, "dur": 253, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1754735214761867, "dur": 147, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1754735214762014, "dur": 249, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1754735214762263, "dur": 335, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1754735214762598, "dur": 330, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1754735214762929, "dur": 147, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1754735214763076, "dur": 246, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1754735214763355, "dur": 227, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1754735214763606, "dur": 168, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1754735214763774, "dur": 339, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1754735214764113, "dur": 393, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.InputSystem.dll.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1754735214764535, "dur": 716, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.InputSystem.dll (+2 others)"}}, {"pid": 12345, "tid": 1, "ts": 1754735214765303, "dur": 80, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Core.Runtime.dll.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1754735214765384, "dur": 181, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1754735214765569, "dur": 579, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Core.Runtime.dll (+2 others)"}}, {"pid": 12345, "tid": 1, "ts": 1754735214766149, "dur": 111, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1754735214766294, "dur": 72, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipeline.Universal.ShaderLibrary.dll.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1754735214766381, "dur": 182, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipeline.Universal.ShaderLibrary.dll (+2 others)"}}, {"pid": 12345, "tid": 1, "ts": 1754735214766564, "dur": 71, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1754735214766655, "dur": 224, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1754735214766879, "dur": 1441, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1754735214768320, "dur": 276227, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1754735215044549, "dur": 1495, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Tests.dll (+pdb)"}}, {"pid": 12345, "tid": 1, "ts": 1754735215046091, "dur": 1308, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.InputSystem.dll (+pdb)"}}, {"pid": 12345, "tid": 1, "ts": 1754735215047427, "dur": 1348, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.RenderPipelines.Core.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 1, "ts": 1754735215049076, "dur": 1449, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\GameSoft\\Unity\\Editor\\2022.3.38f1c1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Runtime.Numerics.dll"}}, {"pid": 12345, "tid": 1, "ts": 1754735215048799, "dur": 2672, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/CrashKonijn.Goap.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 1, "ts": 1754735215051533, "dur": 611084, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1754735214735179, "dur": 20535, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1754735214755722, "dur": 50, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.UIElementsSamplesModule.dll_623E7535F4BD3DF5.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1754735214755942, "dur": 58, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.AndroidJNIModule.dll_DF83CFB61A6929CE.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1754735214756163, "dur": 138, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityConnectModule.dll_467A1CC760B32A9D.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1754735214756331, "dur": 115, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/UnityEngine.UI.rsp"}}, {"pid": 12345, "tid": 2, "ts": 1754735214756513, "dur": 109, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/CrashKonijn.Agent.Core.rsp"}}, {"pid": 12345, "tid": 2, "ts": 1754735214756757, "dur": 73, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.State.Editor.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 2, "ts": 1754735214757092, "dur": 73, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/com.crashkonijn.goap.demos.complex.rsp"}}, {"pid": 12345, "tid": 2, "ts": 1754735214757367, "dur": 163, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1754735214757534, "dur": 190, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1754735214757724, "dur": 175, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1754735214757899, "dur": 186, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1754735214758086, "dur": 774, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1754735214758861, "dur": 562, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1754735214759423, "dur": 163, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1754735214759587, "dur": 154, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1754735214759741, "dur": 602, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1754735214760343, "dur": 215, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1754735214760559, "dur": 289, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1754735214760849, "dur": 282, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1754735214761131, "dur": 330, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1754735214761461, "dur": 296, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1754735214761757, "dur": 204, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1754735214761962, "dur": 137, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1754735214762099, "dur": 250, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1754735214762349, "dur": 188, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1754735214762537, "dur": 225, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1754735214762762, "dur": 196, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1754735214762958, "dur": 163, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1754735214763122, "dur": 283, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1754735214763405, "dur": 132, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1754735214763591, "dur": 176, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1754735214763767, "dur": 347, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1754735214764153, "dur": 336, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1754735214764497, "dur": 388, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Mathematics.dll (+2 others)"}}, {"pid": 12345, "tid": 2, "ts": 1754735214764885, "dur": 82, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1754735214764993, "dur": 71, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.Mathematics.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1754735214765070, "dur": 441, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.Mathematics.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1754735214765512, "dur": 419, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Mathematics.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 2, "ts": 1754735214765993, "dur": 125, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1754735214766132, "dur": 171, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1754735214766303, "dur": 576, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1754735214766879, "dur": 1458, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1754735214768337, "dur": 276234, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1754735215044577, "dur": 1474, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.InputSystem.ForUI.dll (+pdb)"}}, {"pid": 12345, "tid": 2, "ts": 1754735215046084, "dur": 202, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.InputSystem.ForUI.dll (+pdb)"}}, {"pid": 12345, "tid": 2, "ts": 1754735215047482, "dur": 71, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\GameSoft\\Unity\\Editor\\2022.3.38f1c1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Reflection.dll"}}, {"pid": 12345, "tid": 2, "ts": 1754735215046288, "dur": 1377, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.TextMeshPro.dll (+pdb)"}}, {"pid": 12345, "tid": 2, "ts": 1754735215047666, "dur": 279, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1754735215049078, "dur": 116, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\GameSoft\\Unity\\Editor\\2022.3.38f1c1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netfx\\System.Runtime.Serialization.dll"}}, {"pid": 12345, "tid": 2, "ts": 1754735215047952, "dur": 1592, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Burst.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 2, "ts": 1754735215050218, "dur": 57, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\GameSoft\\Unity\\Editor\\2022.3.38f1c1\\Editor\\Data\\Tools\\ilpp\\Unity.ILPP.Runner\\Microsoft.Extensions.Configuration.Json.dll"}}, {"pid": 12345, "tid": 2, "ts": 1754735215049583, "dur": 1417, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.VisualScripting.Flow.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 2, "ts": 1754735215051039, "dur": 611580, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1754735214735108, "dur": 20570, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1754735214755685, "dur": 2599, "ph": "X", "name": "CheckDagSignatures", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1754735214758285, "dur": 561, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1754735214758847, "dur": 295, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1754735214759142, "dur": 425, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1754735214759568, "dur": 598, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1754735214760167, "dur": 350, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1754735214760518, "dur": 220, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1754735214760738, "dur": 220, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1754735214760958, "dur": 314, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1754735214761272, "dur": 195, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1754735214761467, "dur": 317, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1754735214761785, "dur": 222, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1754735214762007, "dur": 214, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1754735214762222, "dur": 242, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1754735214762464, "dur": 207, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1754735214762671, "dur": 186, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1754735214762857, "dur": 164, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1754735214763021, "dur": 354, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1754735214763375, "dur": 223, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1754735214763598, "dur": 165, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1754735214763788, "dur": 334, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1754735214764127, "dur": 385, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/CrashKonijn.Agent.Runtime.dll.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1754735214764530, "dur": 141, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.ShaderGraph.ShaderGraphLibrary.dll.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1754735214764671, "dur": 63, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1754735214764739, "dur": 274, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.ShaderGraph.ShaderGraphLibrary.dll (+2 others)"}}, {"pid": 12345, "tid": 3, "ts": 1754735214765048, "dur": 62, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.ShaderGraph.ShaderGraphLibrary.dll (+2 others)"}}, {"pid": 12345, "tid": 3, "ts": 1754735214765112, "dur": 86, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/com.crashkonijn.goap.demos.simple.dll.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1754735214765221, "dur": 80, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/com.crashkonijn.goap.demos.turn-based.dll.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1754735214765313, "dur": 85, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Core.dll.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1754735214765409, "dur": 941, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Core.dll (+2 others)"}}, {"pid": 12345, "tid": 3, "ts": 1754735214766408, "dur": 69, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Flow.dll.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1754735214766492, "dur": 615, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Flow.dll (+2 others)"}}, {"pid": 12345, "tid": 3, "ts": 1754735214767148, "dur": 70, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.State.dll.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1754735214767232, "dur": 196, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.State.dll (+2 others)"}}, {"pid": 12345, "tid": 3, "ts": 1754735214767472, "dur": 844, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1754735214768316, "dur": 74, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Assembly-CSharp.dll.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1754735214768407, "dur": 276158, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1754735215044566, "dur": 1485, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/CrashKonijn.Goap.Runtime.dll (+pdb)"}}, {"pid": 12345, "tid": 3, "ts": 1754735215046116, "dur": 2194, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Burst.dll (+pdb)"}}, {"pid": 12345, "tid": 3, "ts": 1754735215048310, "dur": 203, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1754735215048867, "dur": 77, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\GameSoft\\Unity\\Editor\\2022.3.38f1c1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Reflection.Extensions.dll"}}, {"pid": 12345, "tid": 3, "ts": 1754735215048521, "dur": 1450, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.VisualScripting.State.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 3, "ts": 1754735215050048, "dur": 95, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1754735215050261, "dur": 72, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.RenderPipeline.Universal.ShaderLibrary.dll"}}, {"pid": 12345, "tid": 3, "ts": 1754735215050582, "dur": 60, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1754735215050794, "dur": 343091, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1754735215393888, "dur": 141, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\Bee\\artifacts\\1900b0aE.dag\\post-processed\\Assembly-CSharp.pdb"}}, {"pid": 12345, "tid": 3, "ts": 1754735215393887, "dur": 144, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Assembly-CSharp.pdb"}}, {"pid": 12345, "tid": 3, "ts": 1754735215394067, "dur": 1744, "ph": "X", "name": "CopyFiles", "args": {"detail": "Library/ScriptAssemblies/Assembly-CSharp.pdb"}}, {"pid": 12345, "tid": 3, "ts": 1754735215395814, "dur": 266792, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1754735214735152, "dur": 20549, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1754735214755873, "dur": 77, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1754735214756178, "dur": 95, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\GameSoft\\Unity\\Editor\\2022.3.38f1c1\\Editor\\Data\\PlaybackEngines\\WindowsStandaloneSupport\\UnityEditor.WindowsStandalone.Extensions.dll"}}, {"pid": 12345, "tid": 4, "ts": 1754735214756176, "dur": 101, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.WindowsStandalone.Extensions.dll_35385077F0CEC424.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1754735214756302, "dur": 275, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/UnityEditor.TestRunner.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 4, "ts": 1754735214756589, "dur": 83, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.ShaderGraph.Utilities.rsp2"}}, {"pid": 12345, "tid": 4, "ts": 1754735214756980, "dur": 159, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/URPWizard.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 4, "ts": 1754735214757346, "dur": 173, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1754735214757525, "dur": 201, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1754735214757726, "dur": 167, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1754735214757893, "dur": 171, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1754735214758065, "dur": 204, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1754735214758270, "dur": 454, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1754735214758724, "dur": 707, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1754735214759431, "dur": 224, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1754735214759655, "dur": 670, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1754735214760326, "dur": 433, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1754735214760777, "dur": 267, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1754735214761045, "dur": 211, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1754735214761257, "dur": 364, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1754735214761621, "dur": 348, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1754735214761970, "dur": 156, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1754735214762126, "dur": 275, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1754735214762401, "dur": 263, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1754735214762665, "dur": 252, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1754735214762917, "dur": 180, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1754735214763097, "dur": 326, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1754735214763589, "dur": 177, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1754735214763766, "dur": 351, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1754735214764118, "dur": 396, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.Searcher.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1754735214764530, "dur": 564, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.Searcher.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1754735214765095, "dur": 359, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Searcher.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 4, "ts": 1754735214765510, "dur": 252, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1754735214765763, "dur": 51, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1754735214765863, "dur": 135, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1754735214765998, "dur": 150, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1754735214766148, "dur": 152, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1754735214766300, "dur": 575, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1754735214766875, "dur": 211, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/com.crashkonijn.docs.getting_started.dll (+2 others)"}}, {"pid": 12345, "tid": 4, "ts": 1754735214767120, "dur": 1195, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1754735214768315, "dur": 63, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/PPv2URPConverters.dll.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1754735214768390, "dur": 160, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/PPv2URPConverters.dll (+2 others)"}}, {"pid": 12345, "tid": 4, "ts": 1754735214768568, "dur": 276117, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1754735215044685, "dur": 1422, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/CrashKonijn.Goap.Core.dll (+pdb)"}}, {"pid": 12345, "tid": 4, "ts": 1754735215046277, "dur": 86, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\GameSoft\\Unity\\Editor\\2022.3.38f1c1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Collections.Specialized.dll"}}, {"pid": 12345, "tid": 4, "ts": 1754735215046714, "dur": 116, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\GameSoft\\Unity\\Editor\\2022.3.38f1c1\\Editor\\Data\\Tools\\ilpp\\Unity.ILPP.Runner\\api-ms-win-core-debug-l1-1-0.dll"}}, {"pid": 12345, "tid": 4, "ts": 1754735215046135, "dur": 1581, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Mathematics.dll (+pdb)"}}, {"pid": 12345, "tid": 4, "ts": 1754735215047717, "dur": 401, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1754735215048123, "dur": 1429, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Collections.BurstCompatibilityGen.dll (+pdb)"}}, {"pid": 12345, "tid": 4, "ts": 1754735215049592, "dur": 1379, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.ShaderGraph.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 4, "ts": 1754735215051010, "dur": 611591, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1754735214735217, "dur": 20513, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1754735214755746, "dur": 59, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityWebRequestWWWModule.dll_BA3DE9AF0772CF8F.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1754735214755869, "dur": 70, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\GameSoft\\Unity\\Editor\\2022.3.38f1c1\\Editor\\Data\\Managed\\UnityEngine\\UnityEditor.UnityConnectModule.dll"}}, {"pid": 12345, "tid": 5, "ts": 1754735214755867, "dur": 74, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.UnityConnectModule.dll_591A98B8662B29F2.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1754735214756014, "dur": 63, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1754735214756109, "dur": 59, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.SubsystemsModule.dll_5DAAF2C2D061984C.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1754735214756170, "dur": 96, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\GameSoft\\Unity\\Editor\\2022.3.38f1c1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.UnityWebRequestAudioModule.dll"}}, {"pid": 12345, "tid": 5, "ts": 1754735214756169, "dur": 99, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityWebRequestAudioModule.dll_180837F1985FCEBE.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1754735214756277, "dur": 56, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityWebRequestAudioModule.dll_180837F1985FCEBE.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1754735214756380, "dur": 83, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Burst.CodeGen.rsp2"}}, {"pid": 12345, "tid": 5, "ts": 1754735214756715, "dur": 57, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/CrashKonijn.Agent.Runtime.rsp"}}, {"pid": 12345, "tid": 5, "ts": 1754735214756989, "dur": 54, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Core.ShaderLibrary.rsp"}}, {"pid": 12345, "tid": 5, "ts": 1754735214757117, "dur": 91, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.InputSystem.TestFramework.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 5, "ts": 1754735214757356, "dur": 160, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1754735214757521, "dur": 258, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1754735214757780, "dur": 175, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1754735214757955, "dur": 170, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1754735214758126, "dur": 269, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1754735214758396, "dur": 1166, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1754735214759562, "dur": 221, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1754735214759783, "dur": 678, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1754735214760461, "dur": 468, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1754735214760929, "dur": 147, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1754735214761077, "dur": 385, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1754735214761462, "dur": 303, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1754735214761765, "dur": 324, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1754735214762089, "dur": 279, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1754735214762368, "dur": 256, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1754735214762624, "dur": 311, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1754735214762935, "dur": 203, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1754735214763138, "dur": 182, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1754735214763321, "dur": 137, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1754735214763501, "dur": 105, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1754735214763607, "dur": 170, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1754735214763777, "dur": 382, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1754735214764164, "dur": 911, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.VSCode.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1754735214765100, "dur": 326, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.VSCode.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 5, "ts": 1754735214765426, "dur": 358, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1754735214765854, "dur": 148, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1754735214766002, "dur": 155, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1754735214766158, "dur": 141, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1754735214766299, "dur": 578, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1754735214766878, "dur": 318, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1754735214767196, "dur": 64, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/URPWizard.dll.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1754735214767275, "dur": 252, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/URPWizard.dll (+2 others)"}}, {"pid": 12345, "tid": 5, "ts": 1754735214767554, "dur": 771, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1754735214768325, "dur": 276231, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1754735215044571, "dur": 1453, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.VisualScripting.State.dll (+pdb)"}}, {"pid": 12345, "tid": 5, "ts": 1754735215046064, "dur": 1289, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/com.crashkonijn.goap.demos.turn-based.dll (+pdb)"}}, {"pid": 12345, "tid": 5, "ts": 1754735215047406, "dur": 1368, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.VisualScripting.SettingsProvider.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 5, "ts": 1754735215048866, "dur": 53, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\GameSoft\\Unity\\Editor\\2022.3.38f1c1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.InputModule.dll"}}, {"pid": 12345, "tid": 5, "ts": 1754735215048812, "dur": 1365, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/URPWizard.dll (+pdb)"}}, {"pid": 12345, "tid": 5, "ts": 1754735215050272, "dur": 223, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.VisualScripting.Core.dll"}}, {"pid": 12345, "tid": 5, "ts": 1754735215050554, "dur": 93, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.VisualScripting.State.Editor.pdb"}}, {"pid": 12345, "tid": 5, "ts": 1754735215050664, "dur": 64, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.InputSystem.TestFramework.pdb"}}, {"pid": 12345, "tid": 5, "ts": 1754735215050777, "dur": 747, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1754735215051537, "dur": 611076, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1754735214735262, "dur": 20494, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1754735214755771, "dur": 66, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.VehiclesModule.dll_B4028357F84AC7DF.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1754735214755881, "dur": 50, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\GameSoft\\Unity\\Editor\\2022.3.38f1c1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.AIModule.dll"}}, {"pid": 12345, "tid": 6, "ts": 1754735214755879, "dur": 54, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.AIModule.dll_DD29252AECEC5C10.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1754735214755989, "dur": 57, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.dll_D66E5D73A81B547E.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1754735214756072, "dur": 86, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.PropertiesModule.dll_370C590F53957EFB.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1754735214756168, "dur": 74, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1754735214756345, "dur": 59, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.VisualScripting.TextureAssets.dll_E1772EF4FEB70289.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1754735214756494, "dur": 57, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Core.Editor.rsp2"}}, {"pid": 12345, "tid": 6, "ts": 1754735214756577, "dur": 61, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Universal.Runtime.rsp2"}}, {"pid": 12345, "tid": 6, "ts": 1754735214756651, "dur": 56, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.State.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 6, "ts": 1754735214756841, "dur": 142, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Timeline.Editor.rsp"}}, {"pid": 12345, "tid": 6, "ts": 1754735214756991, "dur": 160, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/GlassSystem.rsp2"}}, {"pid": 12345, "tid": 6, "ts": 1754735214757366, "dur": 165, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1754735214757557, "dur": 303, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1754735214757860, "dur": 229, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1754735214758089, "dur": 271, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1754735214758360, "dur": 596, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1754735214758957, "dur": 573, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1754735214759531, "dur": 231, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1754735214759762, "dur": 616, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1754735214760378, "dur": 243, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1754735214760621, "dur": 286, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1754735214760908, "dur": 226, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1754735214761134, "dur": 282, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1754735214761416, "dur": 197, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1754735214761614, "dur": 367, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1754735214761981, "dur": 161, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1754735214762142, "dur": 279, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1754735214762421, "dur": 335, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1754735214762756, "dur": 172, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1754735214762928, "dur": 150, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1754735214763078, "dur": 212, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1754735214763290, "dur": 213, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1754735214763595, "dur": 170, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1754735214763781, "dur": 368, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1754735214764154, "dur": 446, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.PlasticSCM.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1754735214764601, "dur": 211, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1754735214764829, "dur": 183, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\GameSoft\\Unity\\Editor\\2022.3.38f1c1\\Editor\\Data\\Managed\\UnityEngine\\UnityEditor.EditorToolbarModule.dll"}}, {"pid": 12345, "tid": 6, "ts": 1754735214764822, "dur": 954, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.PlasticSCM.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 6, "ts": 1754735214765777, "dur": 178, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1754735214765989, "dur": 65, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.CollabProxy.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1754735214766069, "dur": 266, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.CollabProxy.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 6, "ts": 1754735214766388, "dur": 489, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1754735214766877, "dur": 307, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/com.crashkonijn.goap.demos.complex.dll (+2 others)"}}, {"pid": 12345, "tid": 6, "ts": 1754735214767184, "dur": 54, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1754735214767241, "dur": 1082, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1754735214768323, "dur": 276222, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1754735215044546, "dur": 1494, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.RenderPipelines.Core.ShaderLibrary.dll (+pdb)"}}, {"pid": 12345, "tid": 6, "ts": 1754735215046094, "dur": 1251, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Collections.dll (+pdb)"}}, {"pid": 12345, "tid": 6, "ts": 1754735215047392, "dur": 1302, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/CrashKonijn.Agent.Runtime.dll (+pdb)"}}, {"pid": 12345, "tid": 6, "ts": 1754735215048694, "dur": 244, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1754735215048938, "dur": 127, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/CrashKonijn.Agent.Runtime.dll (+pdb)"}}, {"pid": 12345, "tid": 6, "ts": 1754735215049067, "dur": 1345, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Timeline.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 6, "ts": 1754735215050413, "dur": 61, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1754735215050498, "dur": 69, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\Bee\\artifacts\\1900b0aE.dag\\post-processed\\Unity.TextMeshPro.Editor.pdb"}}, {"pid": 12345, "tid": 6, "ts": 1754735215050498, "dur": 70, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.TextMeshPro.Editor.pdb"}}, {"pid": 12345, "tid": 6, "ts": 1754735215050688, "dur": 135, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "CopyFiles Library/ScriptAssemblies/GlassSystem.pdb"}}, {"pid": 12345, "tid": 6, "ts": 1754735215050825, "dur": 611767, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1754735214735297, "dur": 20484, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1754735214755795, "dur": 69, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.VirtualTexturingModule.dll_38C1DD3B59418845.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1754735214755865, "dur": 74, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1754735214756177, "dur": 86, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\GameSoft\\Unity\\Editor\\2022.3.38f1c1\\Editor\\Data\\Tools\\ScriptUpdater\\ApiUpdater.MovedFromExtractor.exe"}}, {"pid": 12345, "tid": 7, "ts": 1754735214756162, "dur": 102, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityWebRequestAssetBundleModule.dll_20062661B6C0E97A.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1754735214756304, "dur": 59, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/UnityEditor.TestRunner.rsp2"}}, {"pid": 12345, "tid": 7, "ts": 1754735214756399, "dur": 77, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Core.Runtime.rsp2"}}, {"pid": 12345, "tid": 7, "ts": 1754735214756582, "dur": 105, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Collections.rsp"}}, {"pid": 12345, "tid": 7, "ts": 1754735214756775, "dur": 65, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/CrashKonijn.Goap.Runtime.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 7, "ts": 1754735214757034, "dur": 60, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/com.unity.cinemachine.editor.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 7, "ts": 1754735214757350, "dur": 159, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1754735214757550, "dur": 208, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1754735214757758, "dur": 160, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1754735214757919, "dur": 150, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1754735214758069, "dur": 380, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1754735214758449, "dur": 989, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1754735214759438, "dur": 236, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1754735214759675, "dur": 671, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1754735214760346, "dur": 199, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1754735214760545, "dur": 329, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1754735214760874, "dur": 208, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1754735214761082, "dur": 347, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1754735214761429, "dur": 258, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1754735214761687, "dur": 261, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1754735214761949, "dur": 171, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1754735214762120, "dur": 236, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1754735214762357, "dur": 308, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1754735214762666, "dur": 428, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1754735214763094, "dur": 227, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1754735214763358, "dur": 301, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1754735214763659, "dur": 123, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1754735214763783, "dur": 356, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1754735214764146, "dur": 430, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.TextMeshPro.dll.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1754735214764604, "dur": 333, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.TextMeshPro.dll (+2 others)"}}, {"pid": 12345, "tid": 7, "ts": 1754735214764937, "dur": 65, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1754735214765002, "dur": 86, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.TextMeshPro.dll (+2 others)"}}, {"pid": 12345, "tid": 7, "ts": 1754735214765109, "dur": 83, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.TextMeshPro.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1754735214765202, "dur": 376, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.TextMeshPro.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 7, "ts": 1754735214765618, "dur": 69, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.Burst.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1754735214765700, "dur": 315, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Burst.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 7, "ts": 1754735214766072, "dur": 58, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1754735214766164, "dur": 131, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1754735214766299, "dur": 76, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Universal.Config.Runtime.dll.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1754735214766400, "dur": 262, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Universal.Config.Runtime.dll (+2 others)"}}, {"pid": 12345, "tid": 7, "ts": 1754735214766707, "dur": 62, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Universal.Runtime.dll.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1754735214766783, "dur": 362, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Universal.Runtime.dll (+2 others)"}}, {"pid": 12345, "tid": 7, "ts": 1754735214767193, "dur": 54, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Cinemachine.dll.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1754735214767269, "dur": 317, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Cinemachine.dll (+2 others)"}}, {"pid": 12345, "tid": 7, "ts": 1754735214767605, "dur": 731, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1754735214768336, "dur": 276239, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1754735215044575, "dur": 1612, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.RenderPipelines.Universal.Shaders.dll (+pdb)"}}, {"pid": 12345, "tid": 7, "ts": 1754735215046187, "dur": 120, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1754735215046312, "dur": 1321, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/GlassSystem.dll (+pdb)"}}, {"pid": 12345, "tid": 7, "ts": 1754735215047674, "dur": 1388, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.PlasticSCM.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 7, "ts": 1754735215049062, "dur": 193, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1754735215049262, "dur": 1369, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/UnityEditor.UI.dll (+pdb)"}}, {"pid": 12345, "tid": 7, "ts": 1754735215050780, "dur": 54363, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1754735215105146, "dur": 30480, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\Bee\\artifacts\\1900b0aE.dag\\Assembly-CSharp.dll"}}, {"pid": 12345, "tid": 7, "ts": 1754735215105146, "dur": 31500, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Assembly-CSharp.dll (+pdb)"}}, {"pid": 12345, "tid": 7, "ts": 1754735215137518, "dur": 124, "ph": "X", "name": "StoreTimestampsOfNonGeneratedInputFiles", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1754735215137689, "dur": 250664, "ph": "X", "name": "ILPostProcess", "args": {"detail": "Library/Bee/artifacts/1900b0aE.dag/post-processed/Assembly-CSharp.dll (+pdb)"}}, {"pid": 12345, "tid": 7, "ts": 1754735215393878, "dur": 30414, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\Bee\\artifacts\\1900b0aE.dag\\post-processed\\Assembly-CSharp.dll"}}, {"pid": 12345, "tid": 7, "ts": 1754735215393876, "dur": 30418, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Assembly-CSharp.dll"}}, {"pid": 12345, "tid": 7, "ts": 1754735215424333, "dur": 761, "ph": "X", "name": "CopyFiles", "args": {"detail": "Library/ScriptAssemblies/Assembly-CSharp.dll"}}, {"pid": 12345, "tid": 7, "ts": 1754735215425097, "dur": 237535, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1754735214735330, "dur": 20466, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1754735214756093, "dur": 152, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.StreamingModule.dll_706C4E83917515AE.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1754735214756281, "dur": 141, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.Plastic.Newtonsoft.Json.dll_CF17D4CA2A31CD8C.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1754735214756466, "dur": 70, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/CrashKonijn.Agent.Core.rsp2"}}, {"pid": 12345, "tid": 8, "ts": 1754735214756620, "dur": 121, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/CrashKonijn.Goap.Core.rsp"}}, {"pid": 12345, "tid": 8, "ts": 1754735214756853, "dur": 220, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Universal.Shaders.rsp2"}}, {"pid": 12345, "tid": 8, "ts": 1754735214757146, "dur": 59, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Assembly-CSharp-Editor.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 8, "ts": 1754735214757352, "dur": 160, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1754735214757518, "dur": 204, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1754735214757723, "dur": 158, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1754735214757881, "dur": 282, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1754735214758164, "dur": 575, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1754735214758740, "dur": 363, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1754735214759103, "dur": 207, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1754735214759311, "dur": 331, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1754735214759643, "dur": 596, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1754735214760240, "dur": 443, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1754735214760684, "dur": 325, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1754735214761009, "dur": 288, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1754735214761298, "dur": 244, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1754735214761543, "dur": 306, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1754735214761849, "dur": 171, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1754735214762020, "dur": 342, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1754735214762362, "dur": 289, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1754735214762651, "dur": 252, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1754735214762903, "dur": 153, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1754735214763056, "dur": 289, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1754735214763345, "dur": 142, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1754735214763584, "dur": 183, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1754735214763767, "dur": 354, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1754735214764122, "dur": 408, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.ShaderGraph.Utilities.dll.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1754735214764557, "dur": 316, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.ShaderGraph.Utilities.dll (+2 others)"}}, {"pid": 12345, "tid": 8, "ts": 1754735214764874, "dur": 60, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1754735214764962, "dur": 307, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/CrashKonijn.Agent.Runtime.dll (+2 others)"}}, {"pid": 12345, "tid": 8, "ts": 1754735214765270, "dur": 159, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1754735214765435, "dur": 291, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Tests.dll (+2 others)"}}, {"pid": 12345, "tid": 8, "ts": 1754735214765821, "dur": 51, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1754735214765873, "dur": 200, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1754735214766073, "dur": 101, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1754735214766174, "dur": 135, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1754735214766309, "dur": 577, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1754735214766886, "dur": 1433, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1754735214768319, "dur": 276230, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1754735215044551, "dur": 1481, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.RenderPipelines.Universal.Config.Runtime.dll (+pdb)"}}, {"pid": 12345, "tid": 8, "ts": 1754735215046349, "dur": 55, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\GameSoft\\Unity\\Editor\\2022.3.38f1c1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Runtime.Serialization.Xml.dll"}}, {"pid": 12345, "tid": 8, "ts": 1754735215046075, "dur": 1988, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.RenderPipelines.Core.Runtime.dll (+pdb)"}}, {"pid": 12345, "tid": 8, "ts": 1754735215048866, "dur": 221, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\GameSoft\\Unity\\Editor\\2022.3.38f1c1\\Editor\\Data\\Tools\\ilpp\\Unity.ILPP.Runner\\System.Diagnostics.EventLog.dll"}}, {"pid": 12345, "tid": 8, "ts": 1754735215048118, "dur": 1569, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.TextMeshPro.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 8, "ts": 1754735215050492, "dur": 86, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\GameSoft\\Unity\\Editor\\2022.3.38f1c1\\Editor\\Data\\Tools\\ilpp\\Unity.ILPP.Runner\\System.IO.IsolatedStorage.dll"}}, {"pid": 12345, "tid": 8, "ts": 1754735215049725, "dur": 1360, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Mathematics.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 8, "ts": 1754735215051136, "dur": 611462, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1754735214735369, "dur": 20444, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1754735214755973, "dur": 248, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.CrashReportingModule.dll_E0E4ACDF62B3A25F.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1754735214756229, "dur": 59, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1754735214756288, "dur": 61, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/UnityEngine.TestRunner.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 9, "ts": 1754735214757354, "dur": 159, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\GameSoft\\Unity\\Editor\\2022.3.38f1c1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Runtime.Serialization.Formatters.dll"}}, {"pid": 12345, "tid": 9, "ts": 1754735214757820, "dur": 66, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\GameSoft\\Unity\\Editor\\2022.3.38f1c1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Xml.XmlSerializer.dll"}}, {"pid": 12345, "tid": 9, "ts": 1754735214758165, "dur": 141, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.test-framework@1.1.33\\UnityEngine.TestRunner\\NUnitExtensions\\BaseDelegator.cs"}}, {"pid": 12345, "tid": 9, "ts": 1754735214758307, "dur": 104, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.test-framework@1.1.33\\UnityEngine.TestRunner\\NUnitExtensions\\Commands\\BeforeAfterTestCommandBase.cs"}}, {"pid": 12345, "tid": 9, "ts": 1754735214758412, "dur": 239, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.test-framework@1.1.33\\UnityEngine.TestRunner\\NUnitExtensions\\Commands\\BeforeAfterTestCommandState.cs"}}, {"pid": 12345, "tid": 9, "ts": 1754735214758757, "dur": 53, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.test-framework@1.1.33\\UnityEngine.TestRunner\\NUnitExtensions\\Commands\\OuterUnityTestActionCommand.cs"}}, {"pid": 12345, "tid": 9, "ts": 1754735214759075, "dur": 255, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.test-framework@1.1.33\\UnityEngine.TestRunner\\NUnitExtensions\\Runner\\PlaymodeWorkItemFactory.cs"}}, {"pid": 12345, "tid": 9, "ts": 1754735214759463, "dur": 100, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.test-framework@1.1.33\\UnityEngine.TestRunner\\TestRunner\\Callbacks\\RemoteTestResultSender.cs"}}, {"pid": 12345, "tid": 9, "ts": 1754735214759610, "dur": 64, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.test-framework@1.1.33\\UnityEngine.TestRunner\\TestRunner\\PlaymodeTestsControllerSettings.cs"}}, {"pid": 12345, "tid": 9, "ts": 1754735214759795, "dur": 328, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.test-framework@1.1.33\\UnityEngine.TestRunner\\TestRunner\\TestListenerWrapper.cs"}}, {"pid": 12345, "tid": 9, "ts": 1754735214760459, "dur": 65, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.test-framework@1.1.33\\UnityEngine.TestRunner\\Utils\\TestRunCallbackAttribute.cs"}}, {"pid": 12345, "tid": 9, "ts": 1754735214760570, "dur": 58, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.test-framework@1.1.33\\UnityEngine.TestRunner\\Utils\\Vector3EqualityComparer.cs"}}, {"pid": 12345, "tid": 9, "ts": 1754735214760637, "dur": 80, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.test-framework@1.1.33\\UnityEngine.TestRunner\\Utils\\Vector4EqualityComparer.cs"}}, {"pid": 12345, "tid": 9, "ts": 1754735214756414, "dur": 4309, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/UnityEngine.TestRunner.dll (+2 others)"}}, {"pid": 12345, "tid": 9, "ts": 1754735214760770, "dur": 82, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/UnityEditor.TestRunner.dll.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1754735214761248, "dur": 101, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\GameSoft\\Unity\\Editor\\2022.3.38f1c1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Diagnostics.Tools.dll"}}, {"pid": 12345, "tid": 9, "ts": 1754735214761350, "dur": 77, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\GameSoft\\Unity\\Editor\\2022.3.38f1c1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Diagnostics.TraceSource.dll"}}, {"pid": 12345, "tid": 9, "ts": 1754735214762553, "dur": 82, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.test-framework@1.1.33\\UnityEditor.TestRunner\\Api\\CallbacksDelegator.cs"}}, {"pid": 12345, "tid": 9, "ts": 1754735214762830, "dur": 80, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.test-framework@1.1.33\\UnityEditor.TestRunner\\Api\\RunState.cs"}}, {"pid": 12345, "tid": 9, "ts": 1754735214762911, "dur": 139, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.test-framework@1.1.33\\UnityEditor.TestRunner\\Api\\TestAdaptor.cs"}}, {"pid": 12345, "tid": 9, "ts": 1754735214763068, "dur": 60, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.test-framework@1.1.33\\UnityEditor.TestRunner\\Api\\TestResultAdaptor.cs"}}, {"pid": 12345, "tid": 9, "ts": 1754735214760866, "dur": 2849, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/UnityEditor.TestRunner.dll (+2 others)"}}, {"pid": 12345, "tid": 9, "ts": 1754735214763852, "dur": 191, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/UnityEditor.UI.dll (+2 others)"}}, {"pid": 12345, "tid": 9, "ts": 1754735214764116, "dur": 464, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.Burst.dll.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1754735214764627, "dur": 311, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Burst.dll (+2 others)"}}, {"pid": 12345, "tid": 9, "ts": 1754735214764939, "dur": 77, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1754735214765016, "dur": 518, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Burst.dll (+2 others)"}}, {"pid": 12345, "tid": 9, "ts": 1754735214765600, "dur": 81, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.Burst.CodeGen.dll.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1754735214765713, "dur": 315, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Burst.CodeGen.dll (+2 others)"}}, {"pid": 12345, "tid": 9, "ts": 1754735214766029, "dur": 65, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1754735214766132, "dur": 366, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPP-Configuration Library/ilpp-configuration.nevergeneratedoutput"}}, {"pid": 12345, "tid": 9, "ts": 1754735214766530, "dur": 84, "ph": "X", "name": "StoreTimestampsOfNonGeneratedInputFiles", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1754735214766629, "dur": 275911, "ph": "X", "name": "ILPP-Configuration", "args": {"detail": "Library/ilpp-configuration.nevergeneratedoutput"}}, {"pid": 12345, "tid": 9, "ts": 1754735215044561, "dur": 1470, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/com.crashkonijn.goap.demos.complex.dll (+pdb)"}}, {"pid": 12345, "tid": 9, "ts": 1754735215046069, "dur": 1277, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.VisualScripting.Flow.dll (+pdb)"}}, {"pid": 12345, "tid": 9, "ts": 1754735215047388, "dur": 1162, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/UnityEngine.UI.dll (+pdb)"}}, {"pid": 12345, "tid": 9, "ts": 1754735215048550, "dur": 262, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1754735215048866, "dur": 52, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\GameSoft\\Unity\\Editor\\2022.3.38f1c1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.IMGUIModule.dll"}}, {"pid": 12345, "tid": 9, "ts": 1754735215048817, "dur": 1455, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Collections.DocCodeSamples.dll (+pdb)"}}, {"pid": 12345, "tid": 9, "ts": 1754735215050324, "dur": 83, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1754735215050801, "dur": 598922, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1754735215649725, "dur": 12036, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\Bee\\artifacts\\1900b0aE.dag\\post-processed\\Assembly-CSharp-Editor.dll"}}, {"pid": 12345, "tid": 9, "ts": 1754735215649724, "dur": 12039, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Assembly-CSharp-Editor.dll"}}, {"pid": 12345, "tid": 9, "ts": 1754735215661781, "dur": 758, "ph": "X", "name": "CopyFiles", "args": {"detail": "Library/ScriptAssemblies/Assembly-CSharp-Editor.dll"}}, {"pid": 12345, "tid": 10, "ts": 1754735214735423, "dur": 20402, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1754735214756010, "dur": 64, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1754735214756074, "dur": 248, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ImageConversionModule.dll_44552E2E3B45DEBD.mvfrm"}}, {"pid": 12345, "tid": 10, "ts": 1754735214756450, "dur": 93, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.Burst.Cecil.dll_778247FFEF05A9EE.mvfrm"}}, {"pid": 12345, "tid": 10, "ts": 1754735214756593, "dur": 59, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/CrashKonijn.Goap.Core.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 10, "ts": 1754735214756756, "dur": 244, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.State.Editor.rsp2"}}, {"pid": 12345, "tid": 10, "ts": 1754735214757013, "dur": 66, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/com.crashkonijn.goap.demos.complex.rsp2"}}, {"pid": 12345, "tid": 10, "ts": 1754735214757126, "dur": 57, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.CollabProxy.Editor.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 10, "ts": 1754735214757375, "dur": 156, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1754735214757540, "dur": 245, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1754735214757786, "dur": 181, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1754735214757967, "dur": 189, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1754735214758156, "dur": 476, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1754735214758632, "dur": 422, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1754735214759055, "dur": 424, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1754735214759480, "dur": 673, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1754735214760153, "dur": 262, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1754735214760415, "dur": 240, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1754735214760655, "dur": 269, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1754735214760925, "dur": 247, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1754735214761173, "dur": 197, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1754735214761371, "dur": 264, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1754735214761635, "dur": 206, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1754735214761841, "dur": 434, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1754735214762275, "dur": 269, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1754735214762544, "dur": 137, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1754735214762681, "dur": 190, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1754735214762872, "dur": 173, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1754735214763045, "dur": 267, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1754735214763312, "dur": 281, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1754735214763593, "dur": 176, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1754735214763769, "dur": 346, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1754735214764116, "dur": 392, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/CrashKonijn.Goap.Core.dll.mvfrm"}}, {"pid": 12345, "tid": 10, "ts": 1754735214764526, "dur": 88, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Universal.Shaders.dll.mvfrm"}}, {"pid": 12345, "tid": 10, "ts": 1754735214764630, "dur": 306, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Universal.Shaders.dll (+2 others)"}}, {"pid": 12345, "tid": 10, "ts": 1754735214764993, "dur": 82, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1754735214765086, "dur": 76, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/CrashKonijn.Goap.Runtime.dll.mvfrm"}}, {"pid": 12345, "tid": 10, "ts": 1754735214765163, "dur": 125, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1754735214765296, "dur": 180, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1754735214765479, "dur": 137, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1754735214765617, "dur": 73, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.Collections.dll.mvfrm"}}, {"pid": 12345, "tid": 10, "ts": 1754735214765702, "dur": 351, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Collections.dll (+2 others)"}}, {"pid": 12345, "tid": 10, "ts": 1754735214766124, "dur": 75, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/CrashKonijn.Goap.Resolver.dll.mvfrm"}}, {"pid": 12345, "tid": 10, "ts": 1754735214766223, "dur": 248, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/CrashKonijn.Goap.Resolver.dll (+2 others)"}}, {"pid": 12345, "tid": 10, "ts": 1754735214766534, "dur": 298, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/CrashKonijn.Goap.Runtime.dll (+2 others)"}}, {"pid": 12345, "tid": 10, "ts": 1754735214766882, "dur": 342, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/CrashKonijn.Goap.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 10, "ts": 1754735214767272, "dur": 266, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/com.crashkonijn.goap.demos.simple.dll (+2 others)"}}, {"pid": 12345, "tid": 10, "ts": 1754735214767567, "dur": 771, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1754735214768339, "dur": 276224, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1754735215044564, "dur": 1484, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.RenderPipelines.ShaderGraph.ShaderGraphLibrary.dll (+pdb)"}}, {"pid": 12345, "tid": 10, "ts": 1754735215046049, "dur": 2279, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1754735215048866, "dur": 70, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\GameSoft\\Unity\\Editor\\2022.3.38f1c1\\Editor\\Data\\Tools\\ilpp\\Unity.ILPP.Runner\\api-ms-win-crt-utility-l1-1-0.dll"}}, {"pid": 12345, "tid": 10, "ts": 1754735215048333, "dur": 1464, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Searcher.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 10, "ts": 1754735215049798, "dur": 538, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1754735215050458, "dur": 66, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1754735215050550, "dur": 80, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.Burst.Editor.dll"}}, {"pid": 12345, "tid": 10, "ts": 1754735215050769, "dur": 236, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1754735215051017, "dur": 611568, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1754735214735486, "dur": 20356, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1754735214756091, "dur": 113, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.PerformanceReportingModule.dll_A00BE41311F50426.mvfrm"}}, {"pid": 12345, "tid": 11, "ts": 1754735214756207, "dur": 66, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1754735214756301, "dur": 171, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.VisualScripting.YamlDotNet.dll_4A5905A13DE9A491.mvfrm"}}, {"pid": 12345, "tid": 11, "ts": 1754735214756478, "dur": 53, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/UnityEngine.UI.dll.mvfrm"}}, {"pid": 12345, "tid": 11, "ts": 1754735214757346, "dur": 169, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\GameSoft\\Unity\\Editor\\2022.3.38f1c1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Runtime.Numerics.dll"}}, {"pid": 12345, "tid": 11, "ts": 1754735214757546, "dur": 125, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\GameSoft\\Unity\\Editor\\2022.3.38f1c1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Security.Claims.dll"}}, {"pid": 12345, "tid": 11, "ts": 1754735214757874, "dur": 59, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.collab-proxy@2.8.2\\Lib\\Editor\\log4netPlastic.dll"}}, {"pid": 12345, "tid": 11, "ts": 1754735214758574, "dur": 59, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.ugui@1.0.0\\Runtime\\UI\\Core\\Layout\\CanvasScaler.cs"}}, {"pid": 12345, "tid": 11, "ts": 1754735214758987, "dur": 109, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.ugui@1.0.0\\Runtime\\UI\\Core\\Text.cs"}}, {"pid": 12345, "tid": 11, "ts": 1754735214756550, "dur": 2695, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/UnityEngine.UI.dll (+2 others)"}}, {"pid": 12345, "tid": 11, "ts": 1754735214759307, "dur": 394, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1754735214759701, "dur": 489, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1754735214760190, "dur": 356, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1754735214760546, "dur": 346, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1754735214760892, "dur": 277, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1754735214761169, "dur": 257, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1754735214761426, "dur": 184, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1754735214761610, "dur": 136, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1754735214761746, "dur": 429, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1754735214762175, "dur": 231, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1754735214762407, "dur": 335, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1754735214762742, "dur": 161, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1754735214762903, "dur": 196, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1754735214763099, "dur": 192, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1754735214763291, "dur": 311, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1754735214763602, "dur": 173, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1754735214763775, "dur": 410, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1754735214764189, "dur": 462, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/CrashKonijn.Goap.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 11, "ts": 1754735214764665, "dur": 61, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/com.crashkonijn.docs.getting_started.dll.mvfrm"}}, {"pid": 12345, "tid": 11, "ts": 1754735214764742, "dur": 62, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/GlassSystem.dll.mvfrm"}}, {"pid": 12345, "tid": 11, "ts": 1754735214764815, "dur": 284, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/GlassSystem.dll (+2 others)"}}, {"pid": 12345, "tid": 11, "ts": 1754735214765100, "dur": 78, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1754735214765182, "dur": 77, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/com.crashkonijn.goap.demos.complex.dll.mvfrm"}}, {"pid": 12345, "tid": 11, "ts": 1754735214765259, "dur": 53, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1754735214765316, "dur": 93, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.InputSystem.ForUI.dll.mvfrm"}}, {"pid": 12345, "tid": 11, "ts": 1754735214765422, "dur": 334, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.InputSystem.ForUI.dll (+2 others)"}}, {"pid": 12345, "tid": 11, "ts": 1754735214765857, "dur": 131, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1754735214765988, "dur": 129, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1754735214766134, "dur": 171, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1754735214766305, "dur": 573, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1754735214766878, "dur": 906, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1754735214767784, "dur": 52, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.SettingsProvider.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 11, "ts": 1754735214767845, "dur": 160, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.SettingsProvider.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 11, "ts": 1754735214768040, "dur": 290, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1754735214768330, "dur": 276239, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1754735215044571, "dur": 1477, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/CrashKonijn.Agent.Core.dll (+pdb)"}}, {"pid": 12345, "tid": 11, "ts": 1754735215046099, "dur": 1463, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.VisualScripting.Core.dll (+pdb)"}}, {"pid": 12345, "tid": 11, "ts": 1754735215047563, "dur": 233, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1754735215047800, "dur": 1417, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.CollabProxy.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 11, "ts": 1754735215049267, "dur": 1446, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.VisualScripting.Shared.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 11, "ts": 1754735215050765, "dur": 126, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1754735215050919, "dur": 611668, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1754735214735515, "dur": 20342, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1754735214755873, "dur": 70, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\GameSoft\\Unity\\Editor\\2022.3.38f1c1\\Editor\\Data\\Managed\\UnityEngine\\UnityEditor.UIBuilderModule.dll"}}, {"pid": 12345, "tid": 12, "ts": 1754735214755865, "dur": 82, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.UIBuilderModule.dll_41DA9226DA4902FE.mvfrm"}}, {"pid": 12345, "tid": 12, "ts": 1754735214756176, "dur": 86, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\GameSoft\\Unity\\Editor\\2022.3.38f1c1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.UnityWebRequestTextureModule.dll"}}, {"pid": 12345, "tid": 12, "ts": 1754735214756175, "dur": 89, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityWebRequestTextureModule.dll_3AB3AEE512D0C853.mvfrm"}}, {"pid": 12345, "tid": 12, "ts": 1754735214756529, "dur": 61, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Core.Editor.rsp"}}, {"pid": 12345, "tid": 12, "ts": 1754735214756611, "dur": 143, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.State.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 12, "ts": 1754735214757751, "dur": 511, "ph": "X", "name": "WriteText", "args": {"detail": "Library/Bee/artifacts/1900b0aE.dag/Assembly-CSharp.rsp"}}, {"pid": 12345, "tid": 12, "ts": 1754735214758262, "dur": 558, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1754735214758820, "dur": 516, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1754735214759336, "dur": 418, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1754735214759754, "dur": 690, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1754735214760444, "dur": 153, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1754735214760598, "dur": 178, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1754735214760788, "dur": 213, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1754735214761002, "dur": 211, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1754735214761213, "dur": 613, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1754735214761826, "dur": 198, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1754735214762024, "dur": 281, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1754735214762306, "dur": 256, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1754735214762563, "dur": 126, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1754735214762689, "dur": 194, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1754735214762883, "dur": 186, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1754735214763069, "dur": 206, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1754735214763275, "dur": 284, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1754735214763559, "dur": 51, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1754735214763610, "dur": 171, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1754735214763781, "dur": 387, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1754735214764173, "dur": 1206, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.VisualStudio.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 12, "ts": 1754735214765397, "dur": 404, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.VisualStudio.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 12, "ts": 1754735214765801, "dur": 162, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1754735214765999, "dur": 130, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1754735214766159, "dur": 137, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1754735214766297, "dur": 52, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Core.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 12, "ts": 1754735214766373, "dur": 451, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Core.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 12, "ts": 1754735214766883, "dur": 69, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.ShaderGraph.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 12, "ts": 1754735214766970, "dur": 882, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.ShaderGraph.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 12, "ts": 1754735214767938, "dur": 327, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Universal.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 12, "ts": 1754735214768381, "dur": 194, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/com.unity.cinemachine.editor.dll (+2 others)"}}, {"pid": 12345, "tid": 12, "ts": 1754735214768752, "dur": 132, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\Bee\\artifacts\\1900b0aE.dag\\Assembly-CSharp.rsp"}}, {"pid": 12345, "tid": 12, "ts": 1754735214768594, "dur": 290, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Assembly-CSharp.dll (+2 others)"}}, {"pid": 12345, "tid": 12, "ts": 1754735214769170, "dur": 334569, "ph": "X", "name": "Csc", "args": {"detail": "Library/Bee/artifacts/1900b0aE.dag/Assembly-CSharp.dll (+2 others)"}}, {"pid": 12345, "tid": 12, "ts": 1754735215105388, "dur": 3540, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\Bee\\artifacts\\1900b0aE.dag\\Assembly-CSharp.ref.dll"}}, {"pid": 12345, "tid": 12, "ts": 1754735215105138, "dur": 3850, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Assembly-CSharp-Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 12, "ts": 1754735215109166, "dur": 54, "ph": "X", "name": "StoreTimestampsOfNonGeneratedInputFiles", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1754735215109233, "dur": 290747, "ph": "X", "name": "Csc", "args": {"detail": "Library/Bee/artifacts/1900b0aE.dag/Assembly-CSharp-Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 12, "ts": 1754735215401415, "dur": 14237, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\Bee\\artifacts\\1900b0aE.dag\\Assembly-CSharp-Editor.dll"}}, {"pid": 12345, "tid": 12, "ts": 1754735215401414, "dur": 15250, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Assembly-CSharp-Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 12, "ts": 1754735215417550, "dur": 164, "ph": "X", "name": "StoreTimestampsOfNonGeneratedInputFiles", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1754735215417734, "dur": 226784, "ph": "X", "name": "ILPostProcess", "args": {"detail": "Library/Bee/artifacts/1900b0aE.dag/post-processed/Assembly-CSharp-Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 12, "ts": 1754735215649717, "dur": 90, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\Bee\\artifacts\\1900b0aE.dag\\post-processed\\Assembly-CSharp-Editor.pdb"}}, {"pid": 12345, "tid": 12, "ts": 1754735215649716, "dur": 92, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Assembly-CSharp-Editor.pdb"}}, {"pid": 12345, "tid": 12, "ts": 1754735215649826, "dur": 848, "ph": "X", "name": "CopyFiles", "args": {"detail": "Library/ScriptAssemblies/Assembly-CSharp-Editor.pdb"}}, {"pid": 12345, "tid": 12, "ts": 1754735215650679, "dur": 11920, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1754735214735553, "dur": 20321, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1754735214756046, "dur": 149, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.JSONSerializeModule.dll_AB4BD236749C82D4.mvfrm"}}, {"pid": 12345, "tid": 13, "ts": 1754735214756197, "dur": 80, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\GameSoft\\Unity\\Editor\\2022.3.38f1c1\\Editor\\Data\\Managed\\UnityEditor.Graphs.dll"}}, {"pid": 12345, "tid": 13, "ts": 1754735214756196, "dur": 82, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.Graphs.dll_34C604C212D62B6E.mvfrm"}}, {"pid": 12345, "tid": 13, "ts": 1754735214756523, "dur": 51, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Flow.rsp"}}, {"pid": 12345, "tid": 13, "ts": 1754735214756719, "dur": 61, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Flow.Editor.rsp"}}, {"pid": 12345, "tid": 13, "ts": 1754735214756810, "dur": 94, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VSCode.Editor.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 13, "ts": 1754735214757386, "dur": 146, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1754735214757541, "dur": 308, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1754735214757849, "dur": 229, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1754735214758079, "dur": 334, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1754735214758422, "dur": 582, "ph": "X", "name": "File", "args": {"detail": "D:\\GameSoft\\Unity\\Editor\\2022.3.38f1c1\\Editor\\Data\\Tools\\ilpp\\Unity.ILPP.Runner\\Grpc.Net.ClientFactory.dll"}}, {"pid": 12345, "tid": 13, "ts": 1754735214758413, "dur": 1051, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1754735214759464, "dur": 715, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1754735214760179, "dur": 263, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1754735214760442, "dur": 261, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1754735214760703, "dur": 230, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1754735214760933, "dur": 204, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1754735214761138, "dur": 380, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1754735214761518, "dur": 162, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1754735214761680, "dur": 205, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1754735214761886, "dur": 144, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1754735214762030, "dur": 182, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1754735214762212, "dur": 260, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1754735214762472, "dur": 262, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1754735214762734, "dur": 162, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1754735214762896, "dur": 156, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1754735214763052, "dur": 219, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1754735214763271, "dur": 337, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1754735214763609, "dur": 167, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1754735214763776, "dur": 425, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1754735214764202, "dur": 962, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Core.ShaderLibrary.dll.mvfrm"}}, {"pid": 12345, "tid": 13, "ts": 1754735214765176, "dur": 294, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Core.ShaderLibrary.dll (+2 others)"}}, {"pid": 12345, "tid": 13, "ts": 1754735214765471, "dur": 240, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1754735214765754, "dur": 72, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1754735214765826, "dur": 156, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1754735214765982, "dur": 171, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1754735214766153, "dur": 145, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1754735214766298, "dur": 117, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1754735214766415, "dur": 83, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Core.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 13, "ts": 1754735214766525, "dur": 812, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Core.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 13, "ts": 1754735214767446, "dur": 299, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Flow.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 13, "ts": 1754735214767830, "dur": 205, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.State.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 13, "ts": 1754735214768111, "dur": 147, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Shared.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 13, "ts": 1754735214768328, "dur": 276232, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1754735215044561, "dur": 1736, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/com.crashkonijn.goap.demos.simple.dll (+pdb)"}}, {"pid": 12345, "tid": 13, "ts": 1754735215046871, "dur": 189, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\GameSoft\\Unity\\Editor\\2022.3.38f1c1\\Editor\\Data\\Tools\\ilpp\\Unity.ILPP.Runner\\api-ms-win-core-errorhandling-l1-1-0.dll"}}, {"pid": 12345, "tid": 13, "ts": 1754735215046347, "dur": 1997, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Timeline.dll (+pdb)"}}, {"pid": 12345, "tid": 13, "ts": 1754735215048345, "dur": 344, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1754735215048866, "dur": 130, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\GameSoft\\Unity\\Editor\\2022.3.38f1c1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Globalization.Extensions.dll"}}, {"pid": 12345, "tid": 13, "ts": 1754735215048693, "dur": 1539, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/PPv2URPConverters.dll (+pdb)"}}, {"pid": 12345, "tid": 13, "ts": 1754735215050480, "dur": 89, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1754735215050691, "dur": 86, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1754735215050777, "dur": 57, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.InputSystem.pdb"}}, {"pid": 12345, "tid": 13, "ts": 1754735215050834, "dur": 611762, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1754735214735591, "dur": 20299, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1754735214755904, "dur": 51, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\GameSoft\\Unity\\Editor\\2022.3.38f1c1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.AnimationModule.dll"}}, {"pid": 12345, "tid": 14, "ts": 1754735214755895, "dur": 62, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.AnimationModule.dll_BBD7613341ED5E70.mvfrm"}}, {"pid": 12345, "tid": 14, "ts": 1754735214755968, "dur": 55, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.AnimationModule.dll_BBD7613341ED5E70.mvfrm"}}, {"pid": 12345, "tid": 14, "ts": 1754735214756112, "dur": 102, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.TerrainModule.dll_20F5B884F62AB761.mvfrm"}}, {"pid": 12345, "tid": 14, "ts": 1754735214756218, "dur": 65, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1754735214756283, "dur": 150, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/UnityEngine.UI.rsp2"}}, {"pid": 12345, "tid": 14, "ts": 1754735214756553, "dur": 65, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Searcher.Editor.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 14, "ts": 1754735214756832, "dur": 111, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Cinemachine.rsp2"}}, {"pid": 12345, "tid": 14, "ts": 1754735214757014, "dur": 120, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/PPv2URPConverters.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 14, "ts": 1754735214757230, "dur": 88, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/17346584914308636752.rsp"}}, {"pid": 12345, "tid": 14, "ts": 1754735214757391, "dur": 147, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1754735214757544, "dur": 240, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1754735214757785, "dur": 171, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1754735214757956, "dur": 177, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1754735214758133, "dur": 525, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1754735214758658, "dur": 187, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1754735214758845, "dur": 544, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1754735214759389, "dur": 752, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1754735214760141, "dur": 328, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1754735214760469, "dur": 203, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1754735214760673, "dur": 295, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1754735214760968, "dur": 266, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1754735214761234, "dur": 309, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1754735214761543, "dur": 310, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1754735214761853, "dur": 225, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1754735214762078, "dur": 311, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1754735214762389, "dur": 184, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1754735214762573, "dur": 233, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1754735214762806, "dur": 163, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1754735214762970, "dur": 195, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1754735214763165, "dur": 311, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1754735214763476, "dur": 50, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1754735214763606, "dur": 171, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1754735214763777, "dur": 416, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1754735214764197, "dur": 752, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.Rider.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 14, "ts": 1754735214764950, "dur": 119, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1754735214765070, "dur": 65, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.Rider.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 14, "ts": 1754735214765136, "dur": 351, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Rider.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 14, "ts": 1754735214765487, "dur": 344, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1754735214765891, "dur": 92, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1754735214765983, "dur": 132, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1754735214766116, "dur": 73, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.Collections.DocCodeSamples.dll.mvfrm"}}, {"pid": 12345, "tid": 14, "ts": 1754735214766205, "dur": 215, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Collections.DocCodeSamples.dll (+2 others)"}}, {"pid": 12345, "tid": 14, "ts": 1754735214766421, "dur": 83, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1754735214766507, "dur": 378, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1754735214766885, "dur": 1436, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1754735214768321, "dur": 276231, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1754735215044553, "dur": 1490, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.RenderPipeline.Universal.ShaderLibrary.dll (+pdb)"}}, {"pid": 12345, "tid": 14, "ts": 1754735215046993, "dur": 74, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\GameSoft\\Unity\\Editor\\2022.3.38f1c1\\Editor\\Data\\Tools\\ilpp\\Unity.ILPP.Runner\\Microsoft.DiaSymReader.Native.amd64.dll"}}, {"pid": 12345, "tid": 14, "ts": 1754735215047573, "dur": 78, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\GameSoft\\Unity\\Editor\\2022.3.38f1c1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Runtime.Serialization.Xml.dll"}}, {"pid": 12345, "tid": 14, "ts": 1754735215047662, "dur": 463, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\GameSoft\\Unity\\Editor\\2022.3.38f1c1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Security.Principal.dll"}}, {"pid": 12345, "tid": 14, "ts": 1754735215046082, "dur": 2292, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.InputSystem.TestFramework.dll (+pdb)"}}, {"pid": 12345, "tid": 14, "ts": 1754735215048375, "dur": 147, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1754735215048866, "dur": 80, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\GameSoft\\Unity\\Editor\\2022.3.38f1c1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Linq.Queryable.dll"}}, {"pid": 12345, "tid": 14, "ts": 1754735215048527, "dur": 1545, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.ShaderGraph.Utilities.dll (+pdb)"}}, {"pid": 12345, "tid": 14, "ts": 1754735215050106, "dur": 123, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1754735215050265, "dur": 101, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "CopyFiles Library/ScriptAssemblies/CrashKonijn.Goap.Resolver.dll"}}, {"pid": 12345, "tid": 14, "ts": 1754735215050559, "dur": 151, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.Burst.Editor.pdb"}}, {"pid": 12345, "tid": 14, "ts": 1754735215050774, "dur": 355, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1754735215051140, "dur": 611485, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1754735214735628, "dur": 20274, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1754735214755915, "dur": 58, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ARModule.dll_969BEA2E4A09BAD9.mvfrm"}}, {"pid": 12345, "tid": 15, "ts": 1754735214756174, "dur": 91, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\GameSoft\\Unity\\Editor\\2022.3.38f1c1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.UnityWebRequestModule.dll"}}, {"pid": 12345, "tid": 15, "ts": 1754735214756173, "dur": 93, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityWebRequestModule.dll_55F435D8463A793C.mvfrm"}}, {"pid": 12345, "tid": 15, "ts": 1754735214756299, "dur": 79, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.Plastic.Antlr3.Runtime.dll_493D590627D79599.mvfrm"}}, {"pid": 12345, "tid": 15, "ts": 1754735214756725, "dur": 55, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.ShaderGraph.Editor.rsp"}}, {"pid": 12345, "tid": 15, "ts": 1754735214756808, "dur": 112, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Timeline.Editor.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 15, "ts": 1754735214757047, "dur": 68, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/URPWizard.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 15, "ts": 1754735214757191, "dur": 72, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.InputSystem.TestFramework.rsp"}}, {"pid": 12345, "tid": 15, "ts": 1754735214757349, "dur": 158, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1754735214757547, "dur": 250, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1754735214757797, "dur": 478, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1754735214758275, "dur": 806, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1754735214759081, "dur": 191, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1754735214759272, "dur": 149, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1754735214759422, "dur": 765, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1754735214760187, "dur": 390, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1754735214760577, "dur": 355, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1754735214760932, "dur": 190, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1754735214761122, "dur": 222, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1754735214761345, "dur": 183, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1754735214761528, "dur": 251, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1754735214761779, "dur": 242, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1754735214762022, "dur": 264, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1754735214762287, "dur": 160, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1754735214762447, "dur": 314, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1754735214762762, "dur": 153, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1754735214762915, "dur": 169, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1754735214763084, "dur": 290, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1754735214763374, "dur": 237, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1754735214763611, "dur": 172, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1754735214763783, "dur": 347, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1754735214764136, "dur": 378, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.Timeline.dll.mvfrm"}}, {"pid": 12345, "tid": 15, "ts": 1754735214764565, "dur": 373, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Timeline.dll (+2 others)"}}, {"pid": 12345, "tid": 15, "ts": 1754735214764985, "dur": 75, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.Timeline.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 15, "ts": 1754735214765070, "dur": 78, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.Timeline.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 15, "ts": 1754735214765149, "dur": 720, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Timeline.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 15, "ts": 1754735214765918, "dur": 63, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1754735214766006, "dur": 108, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1754735214766115, "dur": 71, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.Collections.BurstCompatibilityGen.dll.mvfrm"}}, {"pid": 12345, "tid": 15, "ts": 1754735214766210, "dur": 270, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Collections.BurstCompatibilityGen.dll (+2 others)"}}, {"pid": 12345, "tid": 15, "ts": 1754735214766513, "dur": 373, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1754735214766886, "dur": 1441, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1754735214768327, "dur": 276216, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1754735215044545, "dur": 1497, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/CrashKonijn.Goap.Resolver.dll (+pdb)"}}, {"pid": 12345, "tid": 15, "ts": 1754735215046078, "dur": 1482, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/com.crashkonijn.docs.getting_started.dll (+pdb)"}}, {"pid": 12345, "tid": 15, "ts": 1754735215047604, "dur": 1380, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.RenderPipelines.Universal.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 15, "ts": 1754735215049006, "dur": 82, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.RenderPipelines.Universal.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 15, "ts": 1754735215049090, "dur": 1386, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/com.unity.cinemachine.editor.dll (+pdb)"}}, {"pid": 12345, "tid": 15, "ts": 1754735215050476, "dur": 52, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1754735215050557, "dur": 144, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "CopyFiles Library/ScriptAssemblies/com.unity.cinemachine.editor.pdb"}}, {"pid": 12345, "tid": 15, "ts": 1754735215050774, "dur": 258, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1754735215051059, "dur": 611556, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1754735214735669, "dur": 20245, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1754735214755989, "dur": 119, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.GIModule.dll_75656E38889B442A.mvfrm"}}, {"pid": 12345, "tid": 16, "ts": 1754735214756148, "dur": 118, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UIModule.dll_D89CC358493361BC.mvfrm"}}, {"pid": 12345, "tid": 16, "ts": 1754735214756399, "dur": 167, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Core.Runtime.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 16, "ts": 1754735214756682, "dur": 53, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Mathematics.Editor.rsp2"}}, {"pid": 12345, "tid": 16, "ts": 1754735214756748, "dur": 104, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.TextMeshPro.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 16, "ts": 1754735214757115, "dur": 79, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Tests.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 16, "ts": 1754735214757363, "dur": 168, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1754735214757564, "dur": 182, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1754735214757747, "dur": 212, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1754735214757959, "dur": 349, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1754735214758308, "dur": 938, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1754735214759247, "dur": 204, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1754735214759451, "dur": 786, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1754735214760237, "dur": 370, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1754735214760608, "dur": 208, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1754735214760816, "dur": 171, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1754735214760987, "dur": 232, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1754735214761219, "dur": 373, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1754735214761592, "dur": 435, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1754735214762027, "dur": 280, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1754735214762307, "dur": 281, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1754735214762588, "dur": 252, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1754735214762841, "dur": 191, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1754735214763032, "dur": 275, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1754735214763308, "dur": 369, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1754735214763678, "dur": 90, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1754735214763768, "dur": 347, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1754735214764115, "dur": 394, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/CrashKonijn.Agent.Core.dll.mvfrm"}}, {"pid": 12345, "tid": 16, "ts": 1754735214764541, "dur": 308, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/CrashKonijn.Agent.Core.dll (+2 others)"}}, {"pid": 12345, "tid": 16, "ts": 1754735214764893, "dur": 374, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/CrashKonijn.Goap.Core.dll (+2 others)"}}, {"pid": 12345, "tid": 16, "ts": 1754735214765296, "dur": 80, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Tests.dll.mvfrm"}}, {"pid": 12345, "tid": 16, "ts": 1754735214765400, "dur": 87, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.InputSystem.TestFramework.dll.mvfrm"}}, {"pid": 12345, "tid": 16, "ts": 1754735214765499, "dur": 294, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.InputSystem.TestFramework.dll (+2 others)"}}, {"pid": 12345, "tid": 16, "ts": 1754735214765866, "dur": 212, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1754735214766078, "dur": 53, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1754735214766131, "dur": 175, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1754735214766306, "dur": 570, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1754735214766876, "dur": 194, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/com.crashkonijn.goap.demos.turn-based.dll (+2 others)"}}, {"pid": 12345, "tid": 16, "ts": 1754735214767113, "dur": 1204, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1754735214768318, "dur": 61, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Assembly-CSharp-Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 16, "ts": 1754735214768404, "dur": 276174, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1754735215044581, "dur": 1458, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.RenderPipelines.Universal.Runtime.dll (+pdb)"}}, {"pid": 12345, "tid": 16, "ts": 1754735215047134, "dur": 217, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\GameSoft\\Unity\\Editor\\2022.3.38f1c1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.PhysicsModule.dll"}}, {"pid": 12345, "tid": 16, "ts": 1754735215047435, "dur": 132, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\GameSoft\\Unity\\Editor\\2022.3.38f1c1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netfx\\System.Numerics.dll"}}, {"pid": 12345, "tid": 16, "ts": 1754735215046108, "dur": 1846, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Cinemachine.dll (+pdb)"}}, {"pid": 12345, "tid": 16, "ts": 1754735215049076, "dur": 1455, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\GameSoft\\Unity\\Editor\\2022.3.38f1c1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netfx\\System.Windows.dll"}}, {"pid": 12345, "tid": 16, "ts": 1754735215047981, "dur": 2884, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.VisualScripting.Core.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 16, "ts": 1754735215050898, "dur": 611693, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1754735215668279, "dur": 1730, "ph": "X", "name": "ProfilerWriteOutput"}, {"pid": 35942, "tid": -1, "ph": "M", "name": "process_name", "args": {"name": "netcorerun.dll"}}, {"pid": 35942, "tid": -1, "ph": "M", "name": "process_sort_index", "args": {"sort_index": "-1"}}, {"pid": 35942, "tid": 1, "ph": "M", "name": "thread_name", "args": {"name": ""}}, {"pid": 35942, "tid": 1, "ts": 1754735214054066, "dur": 303658, "ph": "X", "name": "BuildProgram", "args": {}}, {"pid": 35942, "tid": 1, "ts": 1754735214054787, "dur": 47351, "ph": "X", "name": "BuildProgramContextConstructor", "args": {}}, {"pid": 35942, "tid": 1, "ts": 1754735214304625, "dur": 3496, "ph": "X", "name": "OutputData.Write", "args": {}}, {"pid": 35942, "tid": 1, "ts": 1754735214308125, "dur": 49584, "ph": "X", "name": "Backend.Write", "args": {}}, {"pid": 35942, "tid": 1, "ts": 1754735214309557, "dur": 30996, "ph": "X", "name": "JsonToString", "args": {}}, {"pid": 35942, "tid": 1, "ts": 1754735214362590, "dur": 891, "ph": "X", "name": "", "args": {}}, {"pid": 35942, "tid": 1, "ts": 1754735214362100, "dur": 1564, "ph": "X", "name": "Write chrome-trace events", "args": {}}, {"cat": "", "pid": 12345, "tid": 0, "ts": 0, "ph": "M", "name": "process_name", "args": {"name": "bee_backend"}}, {"pid": 12345, "tid": 0, "ts": 1754735213699691, "dur": 53, "ph": "X", "name": "IPC_Client_InitializeAndConnectToParent", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1754735213699783, "dur": 2443, "ph": "X", "name": "DriverInitData", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1754735213702238, "dur": 831, "ph": "X", "name": "RemoveStaleOutputs", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1754735213703194, "dur": 54, "ph": "X", "name": "<PERSON><PERSON>", "args": {"detail": "PrepareNodes"}}, {"pid": 12345, "tid": 0, "ts": 1754735213703249, "dur": 364, "ph": "X", "name": "BuildQueueInit", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1754735213704251, "dur": 101, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ClusterRendererModule.dll_D863307DB92126C9.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1754735213704816, "dur": 86, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ProfilerModule.dll_6E85C30F177A7F51.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1754735213711401, "dur": 63, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.State.dll.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1754735213712032, "dur": 164, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.State.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 0, "ts": 1754735213712292, "dur": 126, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.State.Editor.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 0, "ts": 1754735213714530, "dur": 52, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.VSCode.Editor.pdb"}}, {"pid": 12345, "tid": 0, "ts": 1754735213714951, "dur": 59, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Burst.CodeGen.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 0, "ts": 1754735213717019, "dur": 55, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.RenderPipelines.Core.ShaderLibrary.dll (+pdb)"}}, {"pid": 12345, "tid": 0, "ts": 1754735213717081, "dur": 60, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/9870516708743257357.rsp"}}, {"pid": 12345, "tid": 0, "ts": 1754735213723136, "dur": 99, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.ShaderGraph.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 0, "ts": 1754735213727510, "dur": 63, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Assembly-CSharp.dll (+pdb)"}}, {"pid": 12345, "tid": 0, "ts": 1754735213727699, "dur": 51, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Assembly-CSharp.rsp2"}}, {"pid": 12345, "tid": 0, "ts": 1754735213703632, "dur": 24757, "ph": "X", "name": "EnqueueRequestedNodes", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1754735213728402, "dur": 552, "ph": "X", "name": "WaitForBuildFinished", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1754735213728956, "dur": 231, "ph": "X", "name": "JoinBuildThread", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1754735213729283, "dur": 54, "ph": "X", "name": "BuildQueueDestroyTail", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1754735213729346, "dur": 1133, "ph": "X", "name": "<PERSON><PERSON>", "args": {"detail": "Write AllBuiltNodes"}}, {"pid": 12345, "tid": 1, "ts": 1754735213703957, "dur": 24500, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1754735213728465, "dur": 67, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.UIElementsSamplesModule.dll_623E7535F4BD3DF5.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1754735213728612, "dur": 347, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1754735213703941, "dur": 24490, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1754735213728624, "dur": 59, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\GameSoft\\Unity\\Editor\\2022.3.38f1c1\\Editor\\Data\\Managed\\UnityEngine\\UnityEditor.UIElementsModule.dll"}}, {"pid": 12345, "tid": 2, "ts": 1754735213728622, "dur": 62, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.UIElementsModule.dll_613AE6E2D149BAF8.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1754735213728695, "dur": 177, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.UIElementsModule.dll_613AE6E2D149BAF8.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1754735213703947, "dur": 24498, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1754735213728631, "dur": 55, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1754735213728760, "dur": 61, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ClusterRendererModule.dll_D863307DB92126C9.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1754735213703935, "dur": 24480, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1754735213728423, "dur": 520, "ph": "X", "name": "CheckDagSignatures", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1754735213704086, "dur": 24470, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1754735213728620, "dur": 57, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.UIBuilderModule.dll_41DA9226DA4902FE.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1754735213728850, "dur": 113, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.SharedInternalsModule.dll_C8666D03B273DA0D.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1754735213703994, "dur": 24510, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1754735213728760, "dur": 220, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.DSPGraphModule.dll_4190EB41F081C965.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1754735213703983, "dur": 24496, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1754735213728515, "dur": 55, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\GameSoft\\Unity\\Editor\\2022.3.38f1c1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.UnityWebRequestWWWModule.dll"}}, {"pid": 12345, "tid": 7, "ts": 1754735213728498, "dur": 75, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityWebRequestWWWModule.dll_BA3DE9AF0772CF8F.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1754735213728614, "dur": 62, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.TextCoreFontEngineModule.dll_2933F28E75319B3A.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1754735213728779, "dur": 143, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.dll_D66E5D73A81B547E.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1754735213728925, "dur": 50, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\GameSoft\\Unity\\Editor\\2022.3.38f1c1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.UnityAnalyticsCommonModule.dll"}}, {"pid": 12345, "tid": 7, "ts": 1754735213728924, "dur": 53, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityAnalyticsCommonModule.dll_3B4AA1572BDDA284.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1754735213704029, "dur": 24496, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1754735213728539, "dur": 67, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.VirtualTexturingModule.dll_38C1DD3B59418845.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1754735213728642, "dur": 108, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\GameSoft\\Unity\\Editor\\2022.3.38f1c1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.AIModule.dll"}}, {"pid": 12345, "tid": 8, "ts": 1754735213728640, "dur": 113, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.AIModule.dll_DD29252AECEC5C10.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1754735213728893, "dur": 91, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.SubsystemsModule.dll_5DAAF2C2D061984C.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1754735213704057, "dur": 24482, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1754735213728591, "dur": 109, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1754735213704116, "dur": 24452, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1754735213728628, "dur": 52, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\GameSoft\\Unity\\Editor\\2022.3.38f1c1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.InputLegacyModule.dll"}}, {"pid": 12345, "tid": 10, "ts": 1754735213728627, "dur": 55, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.InputLegacyModule.dll_93739C87338A598F.mvfrm"}}, {"pid": 12345, "tid": 10, "ts": 1754735213728801, "dur": 81, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\GameSoft\\Unity\\Editor\\2022.3.38f1c1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.Physics2DModule.dll"}}, {"pid": 12345, "tid": 10, "ts": 1754735213728799, "dur": 86, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.Physics2DModule.dll_087D5123E2EA5605.mvfrm"}}, {"pid": 12345, "tid": 10, "ts": 1754735213728886, "dur": 125, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1754735213704133, "dur": 24452, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1754735213728644, "dur": 82, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\GameSoft\\Unity\\Editor\\2022.3.38f1c1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.AndroidJNIModule.dll"}}, {"pid": 12345, "tid": 11, "ts": 1754735213728643, "dur": 85, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.AndroidJNIModule.dll_DF83CFB61A6929CE.mvfrm"}}, {"pid": 12345, "tid": 11, "ts": 1754735213728778, "dur": 102, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.GIModule.dll_75656E38889B442A.mvfrm"}}, {"pid": 12345, "tid": 12, "ts": 1754735213704178, "dur": 24433, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1754735213728630, "dur": 73, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\GameSoft\\Unity\\Editor\\2022.3.38f1c1\\Editor\\Data\\Managed\\UnityEngine\\UnityEditor.SceneViewModule.dll"}}, {"pid": 12345, "tid": 12, "ts": 1754735213728623, "dur": 82, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.SceneViewModule.dll_2C01C8125CB356D8.mvfrm"}}, {"pid": 12345, "tid": 12, "ts": 1754735213728841, "dur": 142, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1754735213704219, "dur": 24409, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1754735213728642, "dur": 63, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\GameSoft\\Unity\\Editor\\2022.3.38f1c1\\Editor\\Data\\Managed\\UnityEngine\\UnityEditor.UnityConnectModule.dll"}}, {"pid": 12345, "tid": 13, "ts": 1754735213728635, "dur": 73, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.UnityConnectModule.dll_591A98B8662B29F2.mvfrm"}}, {"pid": 12345, "tid": 13, "ts": 1754735213728901, "dur": 50, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\GameSoft\\Unity\\Editor\\2022.3.38f1c1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.TextRenderingModule.dll"}}, {"pid": 12345, "tid": 13, "ts": 1754735213728899, "dur": 53, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.TextRenderingModule.dll_EE72F1C640402F2B.mvfrm"}}, {"pid": 12345, "tid": 13, "ts": 1754735213728996, "dur": 55, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.TextRenderingModule.dll_EE72F1C640402F2B.mvfrm"}}, {"pid": 12345, "tid": 14, "ts": 1754735213704264, "dur": 24382, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1754735213728682, "dur": 66, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\GameSoft\\Unity\\Editor\\2022.3.38f1c1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.AnimationModule.dll"}}, {"pid": 12345, "tid": 14, "ts": 1754735213728666, "dur": 86, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.AnimationModule.dll_BBD7613341ED5E70.mvfrm"}}, {"pid": 12345, "tid": 14, "ts": 1754735213728774, "dur": 67, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.AnimationModule.dll_BBD7613341ED5E70.mvfrm"}}, {"pid": 12345, "tid": 15, "ts": 1754735213704301, "dur": 24367, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1754735213704342, "dur": 24338, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1754735213728831, "dur": 106, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.PerformanceReportingModule.dll_A00BE41311F50426.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1754735213733912, "dur": 321, "ph": "X", "name": "ProfilerWriteOutput"}, {"pid": 3100, "tid": 24780, "ts": 1754735215678236, "dur": 1227, "ph": "X", "name": "Wait for external events", "args": {"First to finish": "backend2.traceevents"}}, {"pid": 3100, "tid": 24780, "ts": 1754735215680432, "dur": 34, "ph": "X", "name": "Wait for external events", "args": {"First to finish": "buildprogram0.traceevents"}}, {"pid": 3100, "tid": 24780, "ts": 1754735215680675, "dur": 23, "ph": "X", "name": "Wait for external events", "args": {"First to finish": "backend1.traceevents"}}, {"pid": 3100, "tid": 24780, "ts": 1754735215679546, "dur": 883, "ph": "X", "name": "backend2.traceevents", "args": {}}, {"pid": 3100, "tid": 24780, "ts": 1754735215680540, "dur": 134, "ph": "X", "name": "buildprogram0.traceevents", "args": {}}, {"pid": 3100, "tid": 24780, "ts": 1754735215680764, "dur": 157, "ph": "X", "name": "backend1.traceevents", "args": {}}, {"pid": 3100, "tid": 24780, "ts": 1754735215675002, "dur": 6535, "ph": "X", "name": "Write chrome-trace events", "args": {}}, {}]}