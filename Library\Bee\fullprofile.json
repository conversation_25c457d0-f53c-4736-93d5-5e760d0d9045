{"Instructions Readme": "1) Open Chrome or Edge, 2) go to chrome://tracing, 3) click Load, 4) navigate to this file.", "processInfo": {}, "traceEvents": [{"pid": 3100, "tid": -1, "ph": "M", "name": "process_name", "args": {"name": "BeeDriver"}}, {"pid": 3100, "tid": -1, "ph": "M", "name": "process_sort_index", "args": {"sort_index": "-2"}}, {"pid": 3100, "tid": 24787, "ph": "M", "name": "thread_name", "args": {"name": "Thread Pool Worker"}}, {"pid": 3100, "tid": 24787, "ts": 1754735496214298, "dur": 481, "ph": "X", "name": "ChromeTraceHeader", "args": {}}, {"pid": 3100, "tid": 24787, "ts": 1754735496216578, "dur": 418, "ph": "X", "name": "Thread Pool Worker", "args": {}}, {"pid": 3100, "tid": 21474836480, "ph": "M", "name": "thread_name", "args": {"name": "ReadEntireBinlogFromIpcAsync"}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735495569865, "dur": 137235, "ph": "X", "name": "WaitForConnectionAsync", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735495707102, "dur": 503041, "ph": "X", "name": "UpdateFromStreamAsync", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735495707114, "dur": 34, "ph": "X", "name": "ReadAsync 0", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735495707150, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735495707152, "dur": 97402, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735495804566, "dur": 3, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735495804570, "dur": 57, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735495804632, "dur": 5, "ph": "X", "name": "ProcessMessages 41", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735495804638, "dur": 2074, "ph": "X", "name": "ReadAsync 41", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735495806720, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735495806723, "dur": 65, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735495806793, "dur": 1, "ph": "X", "name": "ProcessMessages 736", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735495806795, "dur": 50, "ph": "X", "name": "ReadAsync 736", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735495806849, "dur": 1, "ph": "X", "name": "ProcessMessages 708", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735495806851, "dur": 40, "ph": "X", "name": "ReadAsync 708", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735495806895, "dur": 48, "ph": "X", "name": "ReadAsync 648", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735495806946, "dur": 36, "ph": "X", "name": "ReadAsync 342", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735495806985, "dur": 1, "ph": "X", "name": "ProcessMessages 386", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735495806986, "dur": 30, "ph": "X", "name": "ReadAsync 386", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735495807019, "dur": 1, "ph": "X", "name": "ProcessMessages 650", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735495807021, "dur": 43, "ph": "X", "name": "ReadAsync 650", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735495807067, "dur": 1, "ph": "X", "name": "ProcessMessages 350", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735495807069, "dur": 37, "ph": "X", "name": "ReadAsync 350", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735495807110, "dur": 29, "ph": "X", "name": "ReadAsync 738", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735495807142, "dur": 1, "ph": "X", "name": "ProcessMessages 470", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735495807144, "dur": 37, "ph": "X", "name": "ReadAsync 470", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735495807184, "dur": 38, "ph": "X", "name": "ReadAsync 217", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735495807225, "dur": 24, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735495807251, "dur": 25, "ph": "X", "name": "ReadAsync 484", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735495807277, "dur": 29, "ph": "X", "name": "ReadAsync 473", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735495807309, "dur": 1, "ph": "X", "name": "ProcessMessages 403", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735495807311, "dur": 34, "ph": "X", "name": "ReadAsync 403", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735495807347, "dur": 24, "ph": "X", "name": "ReadAsync 614", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735495807374, "dur": 31, "ph": "X", "name": "ReadAsync 219", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735495807407, "dur": 28, "ph": "X", "name": "ReadAsync 334", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735495807437, "dur": 47, "ph": "X", "name": "ReadAsync 519", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735495807486, "dur": 1, "ph": "X", "name": "ProcessMessages 342", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735495807488, "dur": 31, "ph": "X", "name": "ReadAsync 342", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735495807521, "dur": 35, "ph": "X", "name": "ReadAsync 493", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735495807559, "dur": 1, "ph": "X", "name": "ProcessMessages 474", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735495807562, "dur": 43, "ph": "X", "name": "ReadAsync 474", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735495807607, "dur": 1, "ph": "X", "name": "ProcessMessages 682", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735495807609, "dur": 36, "ph": "X", "name": "ReadAsync 682", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735495807647, "dur": 29, "ph": "X", "name": "ReadAsync 441", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735495807679, "dur": 1, "ph": "X", "name": "ProcessMessages 294", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735495807680, "dur": 36, "ph": "X", "name": "ReadAsync 294", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735495807718, "dur": 33, "ph": "X", "name": "ReadAsync 132", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735495807753, "dur": 22, "ph": "X", "name": "ReadAsync 544", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735495807777, "dur": 22, "ph": "X", "name": "ReadAsync 321", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735495807801, "dur": 22, "ph": "X", "name": "ReadAsync 335", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735495807824, "dur": 22, "ph": "X", "name": "ReadAsync 382", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735495807848, "dur": 21, "ph": "X", "name": "ReadAsync 443", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735495807870, "dur": 160, "ph": "X", "name": "ReadAsync 336", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735495808034, "dur": 36, "ph": "X", "name": "ReadAsync 219", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735495808073, "dur": 1, "ph": "X", "name": "ProcessMessages 676", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735495808075, "dur": 29, "ph": "X", "name": "ReadAsync 676", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735495808106, "dur": 21, "ph": "X", "name": "ReadAsync 403", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735495808128, "dur": 22, "ph": "X", "name": "ReadAsync 452", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735495808152, "dur": 26, "ph": "X", "name": "ReadAsync 363", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735495808180, "dur": 25, "ph": "X", "name": "ReadAsync 350", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735495808207, "dur": 25, "ph": "X", "name": "ReadAsync 443", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735495808233, "dur": 21, "ph": "X", "name": "ReadAsync 422", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735495808256, "dur": 22, "ph": "X", "name": "ReadAsync 180", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735495808280, "dur": 21, "ph": "X", "name": "ReadAsync 419", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735495808302, "dur": 22, "ph": "X", "name": "ReadAsync 356", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735495808326, "dur": 23, "ph": "X", "name": "ReadAsync 458", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735495808351, "dur": 19, "ph": "X", "name": "ReadAsync 352", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735495808371, "dur": 21, "ph": "X", "name": "ReadAsync 345", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735495808394, "dur": 39, "ph": "X", "name": "ReadAsync 341", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735495808435, "dur": 32, "ph": "X", "name": "ReadAsync 232", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735495808469, "dur": 30, "ph": "X", "name": "ReadAsync 447", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735495808502, "dur": 32, "ph": "X", "name": "ReadAsync 469", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735495808535, "dur": 33, "ph": "X", "name": "ReadAsync 498", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735495808570, "dur": 34, "ph": "X", "name": "ReadAsync 561", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735495808607, "dur": 1, "ph": "X", "name": "ProcessMessages 483", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735495808608, "dur": 29, "ph": "X", "name": "ReadAsync 483", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735495808639, "dur": 28, "ph": "X", "name": "ReadAsync 543", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735495808669, "dur": 23, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735495808693, "dur": 41, "ph": "X", "name": "ReadAsync 433", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735495808738, "dur": 27, "ph": "X", "name": "ReadAsync 472", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735495808767, "dur": 22, "ph": "X", "name": "ReadAsync 363", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735495808791, "dur": 24, "ph": "X", "name": "ReadAsync 10", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735495808818, "dur": 22, "ph": "X", "name": "ReadAsync 444", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735495808843, "dur": 1, "ph": "X", "name": "ProcessMessages 386", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735495808844, "dur": 37, "ph": "X", "name": "ReadAsync 386", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735495808883, "dur": 25, "ph": "X", "name": "ReadAsync 610", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735495808910, "dur": 28, "ph": "X", "name": "ReadAsync 371", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735495808939, "dur": 1, "ph": "X", "name": "ProcessMessages 448", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735495808941, "dur": 22, "ph": "X", "name": "ReadAsync 448", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735495808966, "dur": 20, "ph": "X", "name": "ReadAsync 442", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735495808988, "dur": 22, "ph": "X", "name": "ReadAsync 369", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735495809011, "dur": 21, "ph": "X", "name": "ReadAsync 420", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735495809033, "dur": 24, "ph": "X", "name": "ReadAsync 327", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735495809058, "dur": 18, "ph": "X", "name": "ReadAsync 340", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735495809077, "dur": 22, "ph": "X", "name": "ReadAsync 229", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735495809101, "dur": 20, "ph": "X", "name": "ReadAsync 321", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735495809122, "dur": 20, "ph": "X", "name": "ReadAsync 235", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735495809144, "dur": 20, "ph": "X", "name": "ReadAsync 328", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735495809166, "dur": 23, "ph": "X", "name": "ReadAsync 365", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735495809190, "dur": 21, "ph": "X", "name": "ReadAsync 438", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735495809213, "dur": 22, "ph": "X", "name": "ReadAsync 356", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735495809236, "dur": 21, "ph": "X", "name": "ReadAsync 413", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735495809258, "dur": 20, "ph": "X", "name": "ReadAsync 440", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735495809280, "dur": 21, "ph": "X", "name": "ReadAsync 365", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735495809303, "dur": 18, "ph": "X", "name": "ReadAsync 482", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735495809322, "dur": 22, "ph": "X", "name": "ReadAsync 350", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735495809346, "dur": 21, "ph": "X", "name": "ReadAsync 212", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735495809369, "dur": 21, "ph": "X", "name": "ReadAsync 339", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735495809391, "dur": 20, "ph": "X", "name": "ReadAsync 367", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735495809412, "dur": 20, "ph": "X", "name": "ReadAsync 177", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735495809434, "dur": 20, "ph": "X", "name": "ReadAsync 268", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735495809455, "dur": 23, "ph": "X", "name": "ReadAsync 186", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735495809481, "dur": 21, "ph": "X", "name": "ReadAsync 286", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735495809503, "dur": 31, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735495809538, "dur": 42, "ph": "X", "name": "ReadAsync 321", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735495809584, "dur": 30, "ph": "X", "name": "ReadAsync 633", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735495809616, "dur": 29, "ph": "X", "name": "ReadAsync 442", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735495809648, "dur": 41, "ph": "X", "name": "ReadAsync 340", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735495809690, "dur": 33, "ph": "X", "name": "ReadAsync 492", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735495809726, "dur": 21, "ph": "X", "name": "ReadAsync 289", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735495809749, "dur": 20, "ph": "X", "name": "ReadAsync 475", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735495809771, "dur": 20, "ph": "X", "name": "ReadAsync 292", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735495809793, "dur": 20, "ph": "X", "name": "ReadAsync 330", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735495809814, "dur": 22, "ph": "X", "name": "ReadAsync 137", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735495809838, "dur": 17, "ph": "X", "name": "ReadAsync 290", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735495809857, "dur": 41, "ph": "X", "name": "ReadAsync 250", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735495809902, "dur": 30, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735495809934, "dur": 21, "ph": "X", "name": "ReadAsync 542", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735495809956, "dur": 25, "ph": "X", "name": "ReadAsync 254", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735495809984, "dur": 1, "ph": "X", "name": "ProcessMessages 257", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735495809986, "dur": 27, "ph": "X", "name": "ReadAsync 257", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735495810014, "dur": 25, "ph": "X", "name": "ReadAsync 400", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735495810042, "dur": 24, "ph": "X", "name": "ReadAsync 277", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735495810068, "dur": 32, "ph": "X", "name": "ReadAsync 273", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735495810101, "dur": 21, "ph": "X", "name": "ReadAsync 413", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735495810124, "dur": 29, "ph": "X", "name": "ReadAsync 261", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735495810155, "dur": 20, "ph": "X", "name": "ReadAsync 304", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735495810177, "dur": 21, "ph": "X", "name": "ReadAsync 302", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735495810199, "dur": 22, "ph": "X", "name": "ReadAsync 238", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735495810223, "dur": 26, "ph": "X", "name": "ReadAsync 257", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735495810251, "dur": 20, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735495810273, "dur": 19, "ph": "X", "name": "ReadAsync 241", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735495810293, "dur": 21, "ph": "X", "name": "ReadAsync 349", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735495810316, "dur": 22, "ph": "X", "name": "ReadAsync 300", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735495810339, "dur": 20, "ph": "X", "name": "ReadAsync 324", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735495810361, "dur": 20, "ph": "X", "name": "ReadAsync 344", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735495810382, "dur": 20, "ph": "X", "name": "ReadAsync 342", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735495810404, "dur": 19, "ph": "X", "name": "ReadAsync 330", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735495810425, "dur": 29, "ph": "X", "name": "ReadAsync 319", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735495810455, "dur": 22, "ph": "X", "name": "ReadAsync 359", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735495810479, "dur": 21, "ph": "X", "name": "ReadAsync 419", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735495810501, "dur": 22, "ph": "X", "name": "ReadAsync 293", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735495810526, "dur": 29, "ph": "X", "name": "ReadAsync 293", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735495810556, "dur": 32, "ph": "X", "name": "ReadAsync 114", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735495810590, "dur": 1, "ph": "X", "name": "ProcessMessages 554", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735495810592, "dur": 26, "ph": "X", "name": "ReadAsync 554", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735495810619, "dur": 27, "ph": "X", "name": "ReadAsync 146", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735495810649, "dur": 23, "ph": "X", "name": "ReadAsync 286", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735495810673, "dur": 22, "ph": "X", "name": "ReadAsync 355", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735495810697, "dur": 21, "ph": "X", "name": "ReadAsync 307", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735495810719, "dur": 19, "ph": "X", "name": "ReadAsync 271", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735495810739, "dur": 22, "ph": "X", "name": "ReadAsync 232", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735495810763, "dur": 23, "ph": "X", "name": "ReadAsync 279", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735495810787, "dur": 21, "ph": "X", "name": "ReadAsync 388", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735495810810, "dur": 22, "ph": "X", "name": "ReadAsync 262", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735495810833, "dur": 21, "ph": "X", "name": "ReadAsync 350", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735495810855, "dur": 30, "ph": "X", "name": "ReadAsync 296", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735495810889, "dur": 34, "ph": "X", "name": "ReadAsync 156", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735495810925, "dur": 24, "ph": "X", "name": "ReadAsync 598", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735495810952, "dur": 43, "ph": "X", "name": "ReadAsync 21", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735495810998, "dur": 1, "ph": "X", "name": "ProcessMessages 482", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735495811000, "dur": 38, "ph": "X", "name": "ReadAsync 482", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735495811040, "dur": 27, "ph": "X", "name": "ReadAsync 304", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735495811071, "dur": 28, "ph": "X", "name": "ReadAsync 287", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735495811102, "dur": 31, "ph": "X", "name": "ReadAsync 271", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735495811134, "dur": 19, "ph": "X", "name": "ReadAsync 501", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735495811155, "dur": 22, "ph": "X", "name": "ReadAsync 214", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735495811179, "dur": 21, "ph": "X", "name": "ReadAsync 272", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735495811201, "dur": 27, "ph": "X", "name": "ReadAsync 239", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735495811230, "dur": 23, "ph": "X", "name": "ReadAsync 240", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735495811254, "dur": 21, "ph": "X", "name": "ReadAsync 370", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735495811276, "dur": 23, "ph": "X", "name": "ReadAsync 105", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735495811300, "dur": 22, "ph": "X", "name": "ReadAsync 380", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735495811324, "dur": 21, "ph": "X", "name": "ReadAsync 320", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735495811346, "dur": 23, "ph": "X", "name": "ReadAsync 176", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735495811371, "dur": 23, "ph": "X", "name": "ReadAsync 253", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735495811395, "dur": 22, "ph": "X", "name": "ReadAsync 343", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735495811418, "dur": 21, "ph": "X", "name": "ReadAsync 292", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735495811441, "dur": 24, "ph": "X", "name": "ReadAsync 167", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735495811467, "dur": 21, "ph": "X", "name": "ReadAsync 481", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735495811489, "dur": 30, "ph": "X", "name": "ReadAsync 382", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735495811521, "dur": 1, "ph": "X", "name": "ProcessMessages 332", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735495811522, "dur": 36, "ph": "X", "name": "ReadAsync 332", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735495811562, "dur": 32, "ph": "X", "name": "ReadAsync 598", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735495811596, "dur": 25, "ph": "X", "name": "ReadAsync 210", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735495811623, "dur": 33, "ph": "X", "name": "ReadAsync 362", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735495811658, "dur": 1, "ph": "X", "name": "ProcessMessages 355", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735495811660, "dur": 35, "ph": "X", "name": "ReadAsync 355", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735495811697, "dur": 22, "ph": "X", "name": "ReadAsync 615", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735495811720, "dur": 29, "ph": "X", "name": "ReadAsync 308", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735495811753, "dur": 28, "ph": "X", "name": "ReadAsync 351", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735495811783, "dur": 22, "ph": "X", "name": "ReadAsync 492", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735495811807, "dur": 22, "ph": "X", "name": "ReadAsync 292", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735495811831, "dur": 23, "ph": "X", "name": "ReadAsync 496", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735495811856, "dur": 21, "ph": "X", "name": "ReadAsync 394", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735495811878, "dur": 22, "ph": "X", "name": "ReadAsync 342", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735495811902, "dur": 13, "ph": "X", "name": "ReadAsync 446", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735495811917, "dur": 301, "ph": "X", "name": "ReadAsync 135", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735495812222, "dur": 54, "ph": "X", "name": "ReadAsync 1", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735495812279, "dur": 1, "ph": "X", "name": "ProcessMessages 841", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735495812280, "dur": 44, "ph": "X", "name": "ReadAsync 841", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735495812328, "dur": 1, "ph": "X", "name": "ProcessMessages 581", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735495812330, "dur": 42, "ph": "X", "name": "ReadAsync 581", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735495812373, "dur": 35, "ph": "X", "name": "ReadAsync 803", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735495812412, "dur": 40, "ph": "X", "name": "ReadAsync 363", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735495812454, "dur": 32, "ph": "X", "name": "ReadAsync 601", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735495812488, "dur": 29, "ph": "X", "name": "ReadAsync 531", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735495812520, "dur": 1, "ph": "X", "name": "ProcessMessages 424", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735495812522, "dur": 52, "ph": "X", "name": "ReadAsync 424", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735495812578, "dur": 43, "ph": "X", "name": "ReadAsync 28", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735495812623, "dur": 1, "ph": "X", "name": "ProcessMessages 541", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735495812625, "dur": 130, "ph": "X", "name": "ReadAsync 541", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735495812759, "dur": 26, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735495812786, "dur": 26, "ph": "X", "name": "ReadAsync 642", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735495812816, "dur": 23, "ph": "X", "name": "ReadAsync 235", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735495812841, "dur": 33, "ph": "X", "name": "ReadAsync 366", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735495812876, "dur": 1, "ph": "X", "name": "ProcessMessages 275", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735495812878, "dur": 68, "ph": "X", "name": "ReadAsync 275", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735495812948, "dur": 29, "ph": "X", "name": "ReadAsync 317", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735495812980, "dur": 1, "ph": "X", "name": "ProcessMessages 256", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735495812981, "dur": 37, "ph": "X", "name": "ReadAsync 256", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735495813021, "dur": 1, "ph": "X", "name": "ProcessMessages 523", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735495813023, "dur": 29, "ph": "X", "name": "ReadAsync 523", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735495813054, "dur": 28, "ph": "X", "name": "ReadAsync 215", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735495813087, "dur": 29, "ph": "X", "name": "ReadAsync 425", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735495813118, "dur": 21, "ph": "X", "name": "ReadAsync 579", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735495813140, "dur": 25, "ph": "X", "name": "ReadAsync 268", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735495813168, "dur": 25, "ph": "X", "name": "ReadAsync 382", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735495813195, "dur": 1, "ph": "X", "name": "ProcessMessages 373", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735495813197, "dur": 35, "ph": "X", "name": "ReadAsync 373", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735495813234, "dur": 26, "ph": "X", "name": "ReadAsync 384", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735495813263, "dur": 28, "ph": "X", "name": "ReadAsync 222", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735495813293, "dur": 23, "ph": "X", "name": "ReadAsync 494", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735495813317, "dur": 26, "ph": "X", "name": "ReadAsync 214", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735495813346, "dur": 24, "ph": "X", "name": "ReadAsync 359", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735495813372, "dur": 17, "ph": "X", "name": "ReadAsync 474", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735495813391, "dur": 115, "ph": "X", "name": "ReadAsync 188", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735495813507, "dur": 36, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735495813546, "dur": 33, "ph": "X", "name": "ReadAsync 379", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735495813581, "dur": 1, "ph": "X", "name": "ProcessMessages 659", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735495813583, "dur": 43, "ph": "X", "name": "ReadAsync 659", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735495813628, "dur": 27, "ph": "X", "name": "ReadAsync 344", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735495813657, "dur": 1, "ph": "X", "name": "ProcessMessages 275", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735495813658, "dur": 30, "ph": "X", "name": "ReadAsync 275", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735495813692, "dur": 32, "ph": "X", "name": "ReadAsync 492", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735495813726, "dur": 22, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735495813749, "dur": 21, "ph": "X", "name": "ReadAsync 500", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735495813772, "dur": 27, "ph": "X", "name": "ReadAsync 409", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735495813803, "dur": 39, "ph": "X", "name": "ReadAsync 444", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735495813844, "dur": 25, "ph": "X", "name": "ReadAsync 530", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735495813872, "dur": 23, "ph": "X", "name": "ReadAsync 465", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735495813898, "dur": 27, "ph": "X", "name": "ReadAsync 99", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735495813927, "dur": 1, "ph": "X", "name": "ProcessMessages 505", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735495813929, "dur": 29, "ph": "X", "name": "ReadAsync 505", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735495813960, "dur": 20, "ph": "X", "name": "ReadAsync 515", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735495813983, "dur": 23, "ph": "X", "name": "ReadAsync 142", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735495814008, "dur": 23, "ph": "X", "name": "ReadAsync 287", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735495814033, "dur": 20, "ph": "X", "name": "ReadAsync 367", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735495814054, "dur": 20, "ph": "X", "name": "ReadAsync 157", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735495814075, "dur": 23, "ph": "X", "name": "ReadAsync 129", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735495814100, "dur": 21, "ph": "X", "name": "ReadAsync 329", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735495814123, "dur": 20, "ph": "X", "name": "ReadAsync 440", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735495814145, "dur": 36, "ph": "X", "name": "ReadAsync 288", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735495814182, "dur": 21, "ph": "X", "name": "ReadAsync 311", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735495814205, "dur": 21, "ph": "X", "name": "ReadAsync 308", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735495814227, "dur": 21, "ph": "X", "name": "ReadAsync 312", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735495814249, "dur": 21, "ph": "X", "name": "ReadAsync 247", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735495814272, "dur": 21, "ph": "X", "name": "ReadAsync 367", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735495814295, "dur": 22, "ph": "X", "name": "ReadAsync 356", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735495814318, "dur": 20, "ph": "X", "name": "ReadAsync 282", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735495814340, "dur": 22, "ph": "X", "name": "ReadAsync 379", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735495814363, "dur": 21, "ph": "X", "name": "ReadAsync 312", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735495814386, "dur": 20, "ph": "X", "name": "ReadAsync 174", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735495814407, "dur": 20, "ph": "X", "name": "ReadAsync 270", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735495814429, "dur": 21, "ph": "X", "name": "ReadAsync 399", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735495814451, "dur": 20, "ph": "X", "name": "ReadAsync 390", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735495814472, "dur": 21, "ph": "X", "name": "ReadAsync 310", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735495814495, "dur": 21, "ph": "X", "name": "ReadAsync 304", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735495814517, "dur": 28, "ph": "X", "name": "ReadAsync 449", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735495814548, "dur": 1, "ph": "X", "name": "ProcessMessages 193", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735495814550, "dur": 42, "ph": "X", "name": "ReadAsync 193", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735495814595, "dur": 1, "ph": "X", "name": "ProcessMessages 689", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735495814597, "dur": 36, "ph": "X", "name": "ReadAsync 689", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735495814635, "dur": 1, "ph": "X", "name": "ProcessMessages 805", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735495814636, "dur": 31, "ph": "X", "name": "ReadAsync 805", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735495814670, "dur": 27, "ph": "X", "name": "ReadAsync 509", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735495814698, "dur": 33, "ph": "X", "name": "ReadAsync 180", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735495814732, "dur": 30, "ph": "X", "name": "ReadAsync 611", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735495814764, "dur": 1, "ph": "X", "name": "ProcessMessages 519", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735495814766, "dur": 27, "ph": "X", "name": "ReadAsync 519", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735495814794, "dur": 26, "ph": "X", "name": "ReadAsync 611", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735495814822, "dur": 24, "ph": "X", "name": "ReadAsync 394", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735495814848, "dur": 21, "ph": "X", "name": "ReadAsync 438", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735495814871, "dur": 22, "ph": "X", "name": "ReadAsync 443", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735495814895, "dur": 41, "ph": "X", "name": "ReadAsync 402", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735495814938, "dur": 1, "ph": "X", "name": "ProcessMessages 369", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735495814940, "dur": 32, "ph": "X", "name": "ReadAsync 369", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735495814974, "dur": 26, "ph": "X", "name": "ReadAsync 697", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735495815002, "dur": 20, "ph": "X", "name": "ReadAsync 278", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735495815023, "dur": 26, "ph": "X", "name": "ReadAsync 531", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735495815051, "dur": 22, "ph": "X", "name": "ReadAsync 116", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735495815074, "dur": 19, "ph": "X", "name": "ReadAsync 498", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735495815095, "dur": 23, "ph": "X", "name": "ReadAsync 264", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735495815120, "dur": 23, "ph": "X", "name": "ReadAsync 282", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735495815145, "dur": 19, "ph": "X", "name": "ReadAsync 360", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735495815165, "dur": 21, "ph": "X", "name": "ReadAsync 342", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735495815188, "dur": 21, "ph": "X", "name": "ReadAsync 319", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735495815211, "dur": 23, "ph": "X", "name": "ReadAsync 425", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735495815236, "dur": 20, "ph": "X", "name": "ReadAsync 344", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735495815257, "dur": 22, "ph": "X", "name": "ReadAsync 298", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735495815281, "dur": 20, "ph": "X", "name": "ReadAsync 287", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735495815303, "dur": 20, "ph": "X", "name": "ReadAsync 225", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735495815324, "dur": 21, "ph": "X", "name": "ReadAsync 376", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735495815347, "dur": 21, "ph": "X", "name": "ReadAsync 407", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735495815369, "dur": 21, "ph": "X", "name": "ReadAsync 449", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735495815392, "dur": 21, "ph": "X", "name": "ReadAsync 317", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735495815414, "dur": 21, "ph": "X", "name": "ReadAsync 415", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735495815436, "dur": 27, "ph": "X", "name": "ReadAsync 352", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735495815467, "dur": 30, "ph": "X", "name": "ReadAsync 311", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735495815498, "dur": 27, "ph": "X", "name": "ReadAsync 726", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735495815528, "dur": 31, "ph": "X", "name": "ReadAsync 488", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735495815562, "dur": 1, "ph": "X", "name": "ProcessMessages 358", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735495815564, "dur": 43, "ph": "X", "name": "ReadAsync 358", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735495815609, "dur": 1, "ph": "X", "name": "ProcessMessages 763", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735495815611, "dur": 36, "ph": "X", "name": "ReadAsync 763", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735495815649, "dur": 31, "ph": "X", "name": "ReadAsync 717", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735495815682, "dur": 1, "ph": "X", "name": "ProcessMessages 422", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735495815684, "dur": 35, "ph": "X", "name": "ReadAsync 422", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735495815721, "dur": 23, "ph": "X", "name": "ReadAsync 713", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735495815746, "dur": 24, "ph": "X", "name": "ReadAsync 594", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735495815772, "dur": 24, "ph": "X", "name": "ReadAsync 325", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735495815798, "dur": 22, "ph": "X", "name": "ReadAsync 376", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735495815822, "dur": 22, "ph": "X", "name": "ReadAsync 325", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735495815845, "dur": 22, "ph": "X", "name": "ReadAsync 244", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735495815867, "dur": 1, "ph": "X", "name": "ProcessMessages 354", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735495815869, "dur": 22, "ph": "X", "name": "ReadAsync 354", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735495815893, "dur": 26, "ph": "X", "name": "ReadAsync 300", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735495815921, "dur": 26, "ph": "X", "name": "ReadAsync 157", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735495815949, "dur": 28, "ph": "X", "name": "ReadAsync 546", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735495815979, "dur": 21, "ph": "X", "name": "ReadAsync 436", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735495816001, "dur": 29, "ph": "X", "name": "ReadAsync 462", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735495816033, "dur": 1, "ph": "X", "name": "ProcessMessages 296", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735495816035, "dur": 31, "ph": "X", "name": "ReadAsync 296", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735495816067, "dur": 24, "ph": "X", "name": "ReadAsync 506", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735495816092, "dur": 19, "ph": "X", "name": "ReadAsync 387", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735495816114, "dur": 1, "ph": "X", "name": "ProcessMessages 342", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735495816115, "dur": 29, "ph": "X", "name": "ReadAsync 342", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735495816147, "dur": 22, "ph": "X", "name": "ReadAsync 329", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735495816171, "dur": 21, "ph": "X", "name": "ReadAsync 359", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735495816193, "dur": 16, "ph": "X", "name": "ReadAsync 302", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735495816211, "dur": 23, "ph": "X", "name": "ReadAsync 308", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735495816235, "dur": 24, "ph": "X", "name": "ReadAsync 299", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735495816261, "dur": 18, "ph": "X", "name": "ReadAsync 639", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735495816280, "dur": 23, "ph": "X", "name": "ReadAsync 350", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735495816305, "dur": 20, "ph": "X", "name": "ReadAsync 333", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735495816326, "dur": 18, "ph": "X", "name": "ReadAsync 358", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735495816346, "dur": 22, "ph": "X", "name": "ReadAsync 431", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735495816369, "dur": 21, "ph": "X", "name": "ReadAsync 284", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735495816392, "dur": 23, "ph": "X", "name": "ReadAsync 416", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735495816416, "dur": 22, "ph": "X", "name": "ReadAsync 400", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735495816440, "dur": 26, "ph": "X", "name": "ReadAsync 295", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735495816467, "dur": 22, "ph": "X", "name": "ReadAsync 318", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735495816491, "dur": 22, "ph": "X", "name": "ReadAsync 573", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735495816514, "dur": 24, "ph": "X", "name": "ReadAsync 105", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735495816540, "dur": 35, "ph": "X", "name": "ReadAsync 450", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735495816579, "dur": 29, "ph": "X", "name": "ReadAsync 517", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735495816610, "dur": 25, "ph": "X", "name": "ReadAsync 534", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735495816637, "dur": 19, "ph": "X", "name": "ReadAsync 331", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735495816657, "dur": 22, "ph": "X", "name": "ReadAsync 335", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735495816681, "dur": 22, "ph": "X", "name": "ReadAsync 240", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735495816705, "dur": 19, "ph": "X", "name": "ReadAsync 293", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735495816725, "dur": 18, "ph": "X", "name": "ReadAsync 307", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735495816745, "dur": 20, "ph": "X", "name": "ReadAsync 160", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735495816767, "dur": 21, "ph": "X", "name": "ReadAsync 108", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735495816789, "dur": 22, "ph": "X", "name": "ReadAsync 244", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735495816812, "dur": 22, "ph": "X", "name": "ReadAsync 320", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735495816836, "dur": 21, "ph": "X", "name": "ReadAsync 242", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735495816859, "dur": 37, "ph": "X", "name": "ReadAsync 319", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735495816898, "dur": 1, "ph": "X", "name": "ProcessMessages 255", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735495816899, "dur": 34, "ph": "X", "name": "ReadAsync 255", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735495816936, "dur": 1, "ph": "X", "name": "ProcessMessages 688", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735495816938, "dur": 36, "ph": "X", "name": "ReadAsync 688", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735495816978, "dur": 29, "ph": "X", "name": "ReadAsync 494", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735495817008, "dur": 28, "ph": "X", "name": "ReadAsync 483", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735495817037, "dur": 2, "ph": "X", "name": "ProcessMessages 290", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735495817039, "dur": 25, "ph": "X", "name": "ReadAsync 290", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735495817067, "dur": 23, "ph": "X", "name": "ReadAsync 356", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735495817091, "dur": 21, "ph": "X", "name": "ReadAsync 237", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735495817114, "dur": 18, "ph": "X", "name": "ReadAsync 240", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735495817132, "dur": 21, "ph": "X", "name": "ReadAsync 151", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735495817155, "dur": 21, "ph": "X", "name": "ReadAsync 216", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735495817178, "dur": 22, "ph": "X", "name": "ReadAsync 329", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735495817202, "dur": 23, "ph": "X", "name": "ReadAsync 293", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735495817226, "dur": 21, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735495817248, "dur": 23, "ph": "X", "name": "ReadAsync 212", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735495817272, "dur": 21, "ph": "X", "name": "ReadAsync 350", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735495817295, "dur": 21, "ph": "X", "name": "ReadAsync 333", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735495817317, "dur": 23, "ph": "X", "name": "ReadAsync 339", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735495817343, "dur": 21, "ph": "X", "name": "ReadAsync 377", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735495817365, "dur": 21, "ph": "X", "name": "ReadAsync 259", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735495817388, "dur": 18, "ph": "X", "name": "ReadAsync 341", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735495817407, "dur": 20, "ph": "X", "name": "ReadAsync 299", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735495817429, "dur": 16, "ph": "X", "name": "ReadAsync 179", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735495817447, "dur": 21, "ph": "X", "name": "ReadAsync 66", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735495817469, "dur": 18, "ph": "X", "name": "ReadAsync 176", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735495817488, "dur": 21, "ph": "X", "name": "ReadAsync 210", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735495817510, "dur": 17, "ph": "X", "name": "ReadAsync 259", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735495817529, "dur": 28, "ph": "X", "name": "ReadAsync 284", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735495817559, "dur": 35, "ph": "X", "name": "ReadAsync 300", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735495817597, "dur": 1, "ph": "X", "name": "ProcessMessages 302", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735495817598, "dur": 52, "ph": "X", "name": "ReadAsync 302", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735495817653, "dur": 1, "ph": "X", "name": "ProcessMessages 748", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735495817655, "dur": 37, "ph": "X", "name": "ReadAsync 748", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735495817711, "dur": 44, "ph": "X", "name": "ReadAsync 89", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735495817757, "dur": 1, "ph": "X", "name": "ProcessMessages 639", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735495817759, "dur": 38, "ph": "X", "name": "ReadAsync 639", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735495817799, "dur": 30, "ph": "X", "name": "ReadAsync 282", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735495817831, "dur": 29, "ph": "X", "name": "ReadAsync 352", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735495817861, "dur": 21, "ph": "X", "name": "ReadAsync 277", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735495817884, "dur": 26, "ph": "X", "name": "ReadAsync 208", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735495817913, "dur": 1, "ph": "X", "name": "ProcessMessages 251", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735495817915, "dur": 47, "ph": "X", "name": "ReadAsync 251", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735495817965, "dur": 1, "ph": "X", "name": "ProcessMessages 334", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735495817966, "dur": 31, "ph": "X", "name": "ReadAsync 334", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735495818000, "dur": 28, "ph": "X", "name": "ReadAsync 138", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735495818033, "dur": 36, "ph": "X", "name": "ReadAsync 177", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735495818071, "dur": 42, "ph": "X", "name": "ReadAsync 426", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735495818115, "dur": 1, "ph": "X", "name": "ProcessMessages 337", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735495818117, "dur": 33, "ph": "X", "name": "ReadAsync 337", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735495818152, "dur": 36, "ph": "X", "name": "ReadAsync 203", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735495818191, "dur": 1, "ph": "X", "name": "ProcessMessages 503", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735495818193, "dur": 41, "ph": "X", "name": "ReadAsync 503", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735495818238, "dur": 48, "ph": "X", "name": "ReadAsync 292", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735495818291, "dur": 1, "ph": "X", "name": "ProcessMessages 392", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735495818294, "dur": 49, "ph": "X", "name": "ReadAsync 392", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735495818346, "dur": 1, "ph": "X", "name": "ProcessMessages 620", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735495818348, "dur": 39, "ph": "X", "name": "ReadAsync 620", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735495818392, "dur": 39, "ph": "X", "name": "ReadAsync 213", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735495818432, "dur": 1, "ph": "X", "name": "ProcessMessages 477", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735495818434, "dur": 65, "ph": "X", "name": "ReadAsync 477", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735495818502, "dur": 1, "ph": "X", "name": "ProcessMessages 337", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735495818504, "dur": 32, "ph": "X", "name": "ReadAsync 337", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735495818540, "dur": 35, "ph": "X", "name": "ReadAsync 61", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735495818577, "dur": 1, "ph": "X", "name": "ProcessMessages 489", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735495818578, "dur": 27, "ph": "X", "name": "ReadAsync 489", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735495818607, "dur": 25, "ph": "X", "name": "ReadAsync 577", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735495818635, "dur": 24, "ph": "X", "name": "ReadAsync 62", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735495818661, "dur": 23, "ph": "X", "name": "ReadAsync 421", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735495818686, "dur": 31, "ph": "X", "name": "ReadAsync 492", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735495818719, "dur": 1, "ph": "X", "name": "ProcessMessages 361", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735495818721, "dur": 35, "ph": "X", "name": "ReadAsync 361", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735495818759, "dur": 30, "ph": "X", "name": "ReadAsync 326", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735495818791, "dur": 70, "ph": "X", "name": "ReadAsync 574", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735495818866, "dur": 46, "ph": "X", "name": "ReadAsync 350", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735495818915, "dur": 1, "ph": "X", "name": "ProcessMessages 830", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735495818916, "dur": 36, "ph": "X", "name": "ReadAsync 830", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735495818955, "dur": 1, "ph": "X", "name": "ProcessMessages 296", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735495818957, "dur": 27, "ph": "X", "name": "ReadAsync 296", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735495818986, "dur": 23, "ph": "X", "name": "ReadAsync 225", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735495819011, "dur": 19, "ph": "X", "name": "ReadAsync 299", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735495819032, "dur": 20, "ph": "X", "name": "ReadAsync 202", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735495819055, "dur": 21, "ph": "X", "name": "ReadAsync 301", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735495819078, "dur": 20, "ph": "X", "name": "ReadAsync 57", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735495819100, "dur": 55, "ph": "X", "name": "ReadAsync 243", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735495819156, "dur": 21, "ph": "X", "name": "ReadAsync 229", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735495819179, "dur": 21, "ph": "X", "name": "ReadAsync 570", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735495819202, "dur": 18, "ph": "X", "name": "ReadAsync 201", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735495819221, "dur": 14, "ph": "X", "name": "ReadAsync 232", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735495819237, "dur": 17, "ph": "X", "name": "ReadAsync 324", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735495819256, "dur": 28, "ph": "X", "name": "ReadAsync 185", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735495819288, "dur": 32, "ph": "X", "name": "ReadAsync 52", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735495819321, "dur": 24, "ph": "X", "name": "ReadAsync 437", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735495819347, "dur": 23, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735495819372, "dur": 22, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735495819396, "dur": 21, "ph": "X", "name": "ReadAsync 343", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735495819419, "dur": 21, "ph": "X", "name": "ReadAsync 327", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735495819441, "dur": 22, "ph": "X", "name": "ReadAsync 325", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735495819465, "dur": 21, "ph": "X", "name": "ReadAsync 277", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735495819487, "dur": 30, "ph": "X", "name": "ReadAsync 296", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735495819521, "dur": 39, "ph": "X", "name": "ReadAsync 283", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735495819564, "dur": 36, "ph": "X", "name": "ReadAsync 454", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735495819604, "dur": 38, "ph": "X", "name": "ReadAsync 60", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735495819644, "dur": 23, "ph": "X", "name": "ReadAsync 457", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735495819668, "dur": 22, "ph": "X", "name": "ReadAsync 347", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735495819692, "dur": 23, "ph": "X", "name": "ReadAsync 331", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735495819718, "dur": 23, "ph": "X", "name": "ReadAsync 417", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735495819743, "dur": 20, "ph": "X", "name": "ReadAsync 345", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735495819764, "dur": 21, "ph": "X", "name": "ReadAsync 100", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735495819787, "dur": 21, "ph": "X", "name": "ReadAsync 303", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735495819810, "dur": 30, "ph": "X", "name": "ReadAsync 239", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735495819841, "dur": 26, "ph": "X", "name": "ReadAsync 240", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735495819869, "dur": 22, "ph": "X", "name": "ReadAsync 164", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735495819893, "dur": 22, "ph": "X", "name": "ReadAsync 308", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735495819916, "dur": 27, "ph": "X", "name": "ReadAsync 307", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735495819945, "dur": 23, "ph": "X", "name": "ReadAsync 351", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735495819969, "dur": 34, "ph": "X", "name": "ReadAsync 358", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735495820007, "dur": 42, "ph": "X", "name": "ReadAsync 44", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735495820051, "dur": 25, "ph": "X", "name": "ReadAsync 654", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735495820078, "dur": 23, "ph": "X", "name": "ReadAsync 328", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735495820103, "dur": 22, "ph": "X", "name": "ReadAsync 439", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735495820126, "dur": 19, "ph": "X", "name": "ReadAsync 262", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735495820146, "dur": 21, "ph": "X", "name": "ReadAsync 245", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735495820169, "dur": 23, "ph": "X", "name": "ReadAsync 244", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735495820193, "dur": 21, "ph": "X", "name": "ReadAsync 309", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735495820215, "dur": 20, "ph": "X", "name": "ReadAsync 242", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735495820237, "dur": 18, "ph": "X", "name": "ReadAsync 62", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735495820257, "dur": 22, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735495820280, "dur": 17, "ph": "X", "name": "ReadAsync 242", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735495820299, "dur": 21, "ph": "X", "name": "ReadAsync 324", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735495820321, "dur": 20, "ph": "X", "name": "ReadAsync 258", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735495820343, "dur": 20, "ph": "X", "name": "ReadAsync 197", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735495820364, "dur": 21, "ph": "X", "name": "ReadAsync 44", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735495820386, "dur": 22, "ph": "X", "name": "ReadAsync 278", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735495820410, "dur": 22, "ph": "X", "name": "ReadAsync 367", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735495820434, "dur": 18, "ph": "X", "name": "ReadAsync 302", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735495820453, "dur": 20, "ph": "X", "name": "ReadAsync 57", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735495820474, "dur": 23, "ph": "X", "name": "ReadAsync 168", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735495820499, "dur": 29, "ph": "X", "name": "ReadAsync 261", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735495820532, "dur": 1, "ph": "X", "name": "ProcessMessages 441", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735495820533, "dur": 34, "ph": "X", "name": "ReadAsync 441", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735495820570, "dur": 1, "ph": "X", "name": "ProcessMessages 503", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735495820572, "dur": 31, "ph": "X", "name": "ReadAsync 503", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735495820606, "dur": 1, "ph": "X", "name": "ProcessMessages 413", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735495820608, "dur": 43, "ph": "X", "name": "ReadAsync 413", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735495820653, "dur": 1, "ph": "X", "name": "ProcessMessages 485", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735495820655, "dur": 24, "ph": "X", "name": "ReadAsync 485", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735495820680, "dur": 20, "ph": "X", "name": "ReadAsync 492", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735495820703, "dur": 19, "ph": "X", "name": "ReadAsync 198", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735495820723, "dur": 19, "ph": "X", "name": "ReadAsync 62", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735495820743, "dur": 21, "ph": "X", "name": "ReadAsync 109", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735495820767, "dur": 33, "ph": "X", "name": "ReadAsync 257", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735495820801, "dur": 22, "ph": "X", "name": "ReadAsync 375", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735495820825, "dur": 1, "ph": "X", "name": "ProcessMessages 301", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735495820827, "dur": 30, "ph": "X", "name": "ReadAsync 301", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735495820860, "dur": 33, "ph": "X", "name": "ReadAsync 155", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735495820896, "dur": 29, "ph": "X", "name": "ReadAsync 397", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735495820929, "dur": 32, "ph": "X", "name": "ReadAsync 331", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735495820963, "dur": 33, "ph": "X", "name": "ReadAsync 258", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735495820998, "dur": 1, "ph": "X", "name": "ProcessMessages 52", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735495821000, "dur": 31, "ph": "X", "name": "ReadAsync 52", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735495821033, "dur": 25, "ph": "X", "name": "ReadAsync 272", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735495821059, "dur": 24, "ph": "X", "name": "ReadAsync 282", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735495821086, "dur": 145, "ph": "X", "name": "ReadAsync 359", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735495821233, "dur": 1, "ph": "X", "name": "ProcessMessages 196", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735495821235, "dur": 36, "ph": "X", "name": "ReadAsync 196", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735495821272, "dur": 1, "ph": "X", "name": "ProcessMessages 1668", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735495821274, "dur": 21, "ph": "X", "name": "ReadAsync 1668", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735495821297, "dur": 21, "ph": "X", "name": "ReadAsync 271", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735495821320, "dur": 29, "ph": "X", "name": "ReadAsync 200", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735495821350, "dur": 20, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735495821372, "dur": 22, "ph": "X", "name": "ReadAsync 280", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735495821395, "dur": 26, "ph": "X", "name": "ReadAsync 324", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735495821423, "dur": 20, "ph": "X", "name": "ReadAsync 433", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735495821444, "dur": 17, "ph": "X", "name": "ReadAsync 371", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735495821463, "dur": 20, "ph": "X", "name": "ReadAsync 82", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735495821486, "dur": 23, "ph": "X", "name": "ReadAsync 265", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735495821512, "dur": 24, "ph": "X", "name": "ReadAsync 388", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735495821539, "dur": 27, "ph": "X", "name": "ReadAsync 413", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735495821568, "dur": 29, "ph": "X", "name": "ReadAsync 244", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735495821600, "dur": 37, "ph": "X", "name": "ReadAsync 66", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735495821642, "dur": 32, "ph": "X", "name": "ReadAsync 460", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735495821675, "dur": 1, "ph": "X", "name": "ProcessMessages 604", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735495821677, "dur": 22, "ph": "X", "name": "ReadAsync 604", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735495821701, "dur": 23, "ph": "X", "name": "ReadAsync 618", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735495821726, "dur": 33, "ph": "X", "name": "ReadAsync 347", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735495821760, "dur": 21, "ph": "X", "name": "ReadAsync 524", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735495821782, "dur": 23, "ph": "X", "name": "ReadAsync 436", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735495821807, "dur": 19, "ph": "X", "name": "ReadAsync 183", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735495821828, "dur": 27, "ph": "X", "name": "ReadAsync 57", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735495821857, "dur": 27, "ph": "X", "name": "ReadAsync 287", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735495821887, "dur": 1, "ph": "X", "name": "ProcessMessages 310", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735495821888, "dur": 31, "ph": "X", "name": "ReadAsync 310", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735495821921, "dur": 21, "ph": "X", "name": "ReadAsync 689", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735495821943, "dur": 27, "ph": "X", "name": "ReadAsync 466", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735495821973, "dur": 30, "ph": "X", "name": "ReadAsync 288", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735495822006, "dur": 1, "ph": "X", "name": "ProcessMessages 499", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735495822007, "dur": 36, "ph": "X", "name": "ReadAsync 499", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735495822045, "dur": 18, "ph": "X", "name": "ReadAsync 496", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735495822065, "dur": 28, "ph": "X", "name": "ReadAsync 57", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735495822095, "dur": 22, "ph": "X", "name": "ReadAsync 278", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735495822119, "dur": 34, "ph": "X", "name": "ReadAsync 437", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735495822156, "dur": 25, "ph": "X", "name": "ReadAsync 308", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735495822183, "dur": 23, "ph": "X", "name": "ReadAsync 200", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735495822210, "dur": 22, "ph": "X", "name": "ReadAsync 342", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735495822233, "dur": 26, "ph": "X", "name": "ReadAsync 148", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735495822261, "dur": 20, "ph": "X", "name": "ReadAsync 390", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735495822282, "dur": 21, "ph": "X", "name": "ReadAsync 309", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735495822305, "dur": 20, "ph": "X", "name": "ReadAsync 107", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735495822326, "dur": 20, "ph": "X", "name": "ReadAsync 376", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735495822348, "dur": 21, "ph": "X", "name": "ReadAsync 370", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735495822371, "dur": 19, "ph": "X", "name": "ReadAsync 323", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735495822392, "dur": 20, "ph": "X", "name": "ReadAsync 62", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735495822413, "dur": 21, "ph": "X", "name": "ReadAsync 308", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735495822436, "dur": 21, "ph": "X", "name": "ReadAsync 281", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735495822458, "dur": 15, "ph": "X", "name": "ReadAsync 463", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735495822475, "dur": 21, "ph": "X", "name": "ReadAsync 143", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735495822498, "dur": 23, "ph": "X", "name": "ReadAsync 116", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735495822525, "dur": 23, "ph": "X", "name": "ReadAsync 338", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735495822550, "dur": 43, "ph": "X", "name": "ReadAsync 358", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735495822596, "dur": 1, "ph": "X", "name": "ProcessMessages 302", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735495822598, "dur": 41, "ph": "X", "name": "ReadAsync 302", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735495822641, "dur": 1, "ph": "X", "name": "ProcessMessages 470", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735495822643, "dur": 41, "ph": "X", "name": "ReadAsync 470", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735495822685, "dur": 25, "ph": "X", "name": "ReadAsync 677", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735495822713, "dur": 22, "ph": "X", "name": "ReadAsync 537", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735495822737, "dur": 20, "ph": "X", "name": "ReadAsync 332", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735495822759, "dur": 20, "ph": "X", "name": "ReadAsync 348", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735495822780, "dur": 21, "ph": "X", "name": "ReadAsync 252", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735495822802, "dur": 20, "ph": "X", "name": "ReadAsync 237", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735495822824, "dur": 20, "ph": "X", "name": "ReadAsync 103", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735495822845, "dur": 22, "ph": "X", "name": "ReadAsync 340", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735495822869, "dur": 19, "ph": "X", "name": "ReadAsync 353", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735495822890, "dur": 31, "ph": "X", "name": "ReadAsync 237", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735495822929, "dur": 1, "ph": "X", "name": "ProcessMessages 136", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735495822931, "dur": 54, "ph": "X", "name": "ReadAsync 136", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735495822987, "dur": 1, "ph": "X", "name": "ProcessMessages 770", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735495822989, "dur": 32, "ph": "X", "name": "ReadAsync 770", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735495823023, "dur": 1, "ph": "X", "name": "ProcessMessages 350", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735495823024, "dur": 31, "ph": "X", "name": "ReadAsync 350", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735495823058, "dur": 24, "ph": "X", "name": "ReadAsync 40", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735495823084, "dur": 22, "ph": "X", "name": "ReadAsync 405", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735495823107, "dur": 23, "ph": "X", "name": "ReadAsync 380", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735495823132, "dur": 28, "ph": "X", "name": "ReadAsync 308", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735495823163, "dur": 31, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735495823196, "dur": 21, "ph": "X", "name": "ReadAsync 444", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735495823219, "dur": 23, "ph": "X", "name": "ReadAsync 195", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735495823243, "dur": 18, "ph": "X", "name": "ReadAsync 408", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735495823263, "dur": 17, "ph": "X", "name": "ReadAsync 61", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735495823282, "dur": 20, "ph": "X", "name": "ReadAsync 40", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735495823305, "dur": 20, "ph": "X", "name": "ReadAsync 255", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735495823326, "dur": 19, "ph": "X", "name": "ReadAsync 273", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735495823347, "dur": 23, "ph": "X", "name": "ReadAsync 201", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735495823372, "dur": 21, "ph": "X", "name": "ReadAsync 271", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735495823394, "dur": 20, "ph": "X", "name": "ReadAsync 327", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735495823416, "dur": 20, "ph": "X", "name": "ReadAsync 279", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735495823438, "dur": 19, "ph": "X", "name": "ReadAsync 318", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735495823459, "dur": 32, "ph": "X", "name": "ReadAsync 267", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735495823493, "dur": 1, "ph": "X", "name": "ProcessMessages 107", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735495823495, "dur": 46, "ph": "X", "name": "ReadAsync 107", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735495823544, "dur": 1, "ph": "X", "name": "ProcessMessages 283", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735495823546, "dur": 36, "ph": "X", "name": "ReadAsync 283", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735495823584, "dur": 31, "ph": "X", "name": "ReadAsync 642", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735495823618, "dur": 1, "ph": "X", "name": "ProcessMessages 381", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735495823619, "dur": 43, "ph": "X", "name": "ReadAsync 381", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735495823665, "dur": 26, "ph": "X", "name": "ReadAsync 686", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735495823692, "dur": 23, "ph": "X", "name": "ReadAsync 271", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735495823716, "dur": 21, "ph": "X", "name": "ReadAsync 310", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735495823738, "dur": 22, "ph": "X", "name": "ReadAsync 262", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735495823761, "dur": 19, "ph": "X", "name": "ReadAsync 145", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735495823782, "dur": 27, "ph": "X", "name": "ReadAsync 179", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735495823812, "dur": 1, "ph": "X", "name": "ProcessMessages 237", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735495823813, "dur": 45, "ph": "X", "name": "ReadAsync 237", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735495823861, "dur": 1, "ph": "X", "name": "ProcessMessages 555", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735495823863, "dur": 44, "ph": "X", "name": "ReadAsync 555", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735495823910, "dur": 1, "ph": "X", "name": "ProcessMessages 511", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735495823912, "dur": 40, "ph": "X", "name": "ReadAsync 511", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735495823955, "dur": 1, "ph": "X", "name": "ProcessMessages 483", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735495823956, "dur": 31, "ph": "X", "name": "ReadAsync 483", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735495823991, "dur": 22, "ph": "X", "name": "ReadAsync 268", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735495824015, "dur": 24, "ph": "X", "name": "ReadAsync 240", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735495824041, "dur": 31, "ph": "X", "name": "ReadAsync 87", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735495824075, "dur": 1, "ph": "X", "name": "ProcessMessages 227", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735495824077, "dur": 51, "ph": "X", "name": "ReadAsync 227", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735495824131, "dur": 1, "ph": "X", "name": "ProcessMessages 668", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735495824133, "dur": 38, "ph": "X", "name": "ReadAsync 668", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735495824175, "dur": 32, "ph": "X", "name": "ReadAsync 136", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735495824209, "dur": 52, "ph": "X", "name": "ReadAsync 213", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735495824264, "dur": 41, "ph": "X", "name": "ReadAsync 50", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735495824307, "dur": 34, "ph": "X", "name": "ReadAsync 600", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735495824344, "dur": 1, "ph": "X", "name": "ProcessMessages 324", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735495824346, "dur": 117, "ph": "X", "name": "ReadAsync 324", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735495824467, "dur": 34, "ph": "X", "name": "ReadAsync 56", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735495824502, "dur": 1, "ph": "X", "name": "ProcessMessages 1470", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735495824504, "dur": 28, "ph": "X", "name": "ReadAsync 1470", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735495824535, "dur": 1, "ph": "X", "name": "ProcessMessages 278", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735495824536, "dur": 49, "ph": "X", "name": "ReadAsync 278", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735495824588, "dur": 1, "ph": "X", "name": "ProcessMessages 741", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735495824590, "dur": 38, "ph": "X", "name": "ReadAsync 741", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735495824631, "dur": 1, "ph": "X", "name": "ProcessMessages 502", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735495824632, "dur": 38, "ph": "X", "name": "ReadAsync 502", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735495824672, "dur": 30, "ph": "X", "name": "ReadAsync 391", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735495824706, "dur": 45, "ph": "X", "name": "ReadAsync 84", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735495824753, "dur": 30, "ph": "X", "name": "ReadAsync 583", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735495824786, "dur": 21, "ph": "X", "name": "ReadAsync 265", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735495824808, "dur": 34, "ph": "X", "name": "ReadAsync 61", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735495824844, "dur": 17, "ph": "X", "name": "ReadAsync 258", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735495824863, "dur": 18, "ph": "X", "name": "ReadAsync 292", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735495824882, "dur": 21, "ph": "X", "name": "ReadAsync 377", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735495824905, "dur": 15, "ph": "X", "name": "ReadAsync 351", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735495824922, "dur": 18, "ph": "X", "name": "ReadAsync 372", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735495824941, "dur": 20, "ph": "X", "name": "ReadAsync 284", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735495824962, "dur": 21, "ph": "X", "name": "ReadAsync 282", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735495824985, "dur": 18, "ph": "X", "name": "ReadAsync 359", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735495825005, "dur": 20, "ph": "X", "name": "ReadAsync 322", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735495825026, "dur": 19, "ph": "X", "name": "ReadAsync 183", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735495825046, "dur": 32, "ph": "X", "name": "ReadAsync 57", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735495825081, "dur": 37, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735495825121, "dur": 23, "ph": "X", "name": "ReadAsync 471", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735495825146, "dur": 22, "ph": "X", "name": "ReadAsync 443", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735495825169, "dur": 22, "ph": "X", "name": "ReadAsync 395", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735495825194, "dur": 22, "ph": "X", "name": "ReadAsync 361", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735495825217, "dur": 24, "ph": "X", "name": "ReadAsync 427", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735495825242, "dur": 22, "ph": "X", "name": "ReadAsync 304", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735495825265, "dur": 18, "ph": "X", "name": "ReadAsync 357", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735495825284, "dur": 20, "ph": "X", "name": "ReadAsync 207", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735495825308, "dur": 1, "ph": "X", "name": "ProcessMessages 225", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735495825309, "dur": 38, "ph": "X", "name": "ReadAsync 225", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735495825351, "dur": 32, "ph": "X", "name": "ReadAsync 40", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735495825386, "dur": 1, "ph": "X", "name": "ProcessMessages 679", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735495825387, "dur": 32, "ph": "X", "name": "ReadAsync 679", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735495825422, "dur": 25, "ph": "X", "name": "ReadAsync 531", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735495825448, "dur": 20, "ph": "X", "name": "ReadAsync 181", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735495825470, "dur": 23, "ph": "X", "name": "ReadAsync 181", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735495825494, "dur": 23, "ph": "X", "name": "ReadAsync 244", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735495825519, "dur": 25, "ph": "X", "name": "ReadAsync 265", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735495825548, "dur": 1, "ph": "X", "name": "ProcessMessages 288", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735495825549, "dur": 28, "ph": "X", "name": "ReadAsync 288", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735495825581, "dur": 30, "ph": "X", "name": "ReadAsync 302", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735495825612, "dur": 26, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735495825640, "dur": 22, "ph": "X", "name": "ReadAsync 209", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735495825663, "dur": 22, "ph": "X", "name": "ReadAsync 413", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735495825687, "dur": 18, "ph": "X", "name": "ReadAsync 352", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735495825705, "dur": 1, "ph": "X", "name": "ProcessMessages 134", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735495825706, "dur": 21, "ph": "X", "name": "ReadAsync 134", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735495825730, "dur": 45, "ph": "X", "name": "ReadAsync 118", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735495825776, "dur": 20, "ph": "X", "name": "ReadAsync 406", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735495825798, "dur": 20, "ph": "X", "name": "ReadAsync 380", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735495825821, "dur": 23, "ph": "X", "name": "ReadAsync 322", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735495825846, "dur": 34, "ph": "X", "name": "ReadAsync 407", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735495825883, "dur": 29, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735495825914, "dur": 21, "ph": "X", "name": "ReadAsync 498", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735495825937, "dur": 30, "ph": "X", "name": "ReadAsync 193", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735495825969, "dur": 27, "ph": "X", "name": "ReadAsync 345", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735495825998, "dur": 30, "ph": "X", "name": "ReadAsync 290", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735495826030, "dur": 1, "ph": "X", "name": "ProcessMessages 134", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735495826032, "dur": 27, "ph": "X", "name": "ReadAsync 134", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735495826061, "dur": 32, "ph": "X", "name": "ReadAsync 375", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735495826096, "dur": 38, "ph": "X", "name": "ReadAsync 140", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735495826137, "dur": 24, "ph": "X", "name": "ReadAsync 692", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735495826163, "dur": 23, "ph": "X", "name": "ReadAsync 476", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735495826187, "dur": 26, "ph": "X", "name": "ReadAsync 364", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735495826216, "dur": 1, "ph": "X", "name": "ProcessMessages 386", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735495826217, "dur": 41, "ph": "X", "name": "ReadAsync 386", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735495826263, "dur": 19, "ph": "X", "name": "ReadAsync 304", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735495826284, "dur": 21, "ph": "X", "name": "ReadAsync 237", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735495826307, "dur": 22, "ph": "X", "name": "ReadAsync 107", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735495826330, "dur": 24, "ph": "X", "name": "ReadAsync 309", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735495826357, "dur": 1, "ph": "X", "name": "ProcessMessages 326", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735495826358, "dur": 30, "ph": "X", "name": "ReadAsync 326", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735495826392, "dur": 30, "ph": "X", "name": "ReadAsync 470", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735495826423, "dur": 19, "ph": "X", "name": "ReadAsync 394", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735495826443, "dur": 26, "ph": "X", "name": "ReadAsync 188", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735495826473, "dur": 1, "ph": "X", "name": "ProcessMessages 263", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735495826475, "dur": 30, "ph": "X", "name": "ReadAsync 263", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735495826507, "dur": 1, "ph": "X", "name": "ProcessMessages 341", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735495826509, "dur": 27, "ph": "X", "name": "ReadAsync 341", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735495826538, "dur": 25, "ph": "X", "name": "ReadAsync 240", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735495826565, "dur": 25, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735495826591, "dur": 23, "ph": "X", "name": "ReadAsync 374", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735495826616, "dur": 21, "ph": "X", "name": "ReadAsync 351", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735495826638, "dur": 25, "ph": "X", "name": "ReadAsync 330", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735495826665, "dur": 22, "ph": "X", "name": "ReadAsync 343", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735495826689, "dur": 26, "ph": "X", "name": "ReadAsync 126", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735495826717, "dur": 22, "ph": "X", "name": "ReadAsync 343", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735495826741, "dur": 25, "ph": "X", "name": "ReadAsync 348", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735495826768, "dur": 1, "ph": "X", "name": "ProcessMessages 306", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735495826770, "dur": 38, "ph": "X", "name": "ReadAsync 306", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735495826810, "dur": 21, "ph": "X", "name": "ReadAsync 237", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735495826832, "dur": 113, "ph": "X", "name": "ReadAsync 236", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735495826950, "dur": 35, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735495826986, "dur": 36, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735495827026, "dur": 1, "ph": "X", "name": "ProcessMessages 108", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735495827028, "dur": 38, "ph": "X", "name": "ReadAsync 108", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735495827069, "dur": 33, "ph": "X", "name": "ReadAsync 116", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735495827104, "dur": 1, "ph": "X", "name": "ProcessMessages 112", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735495827106, "dur": 34, "ph": "X", "name": "ReadAsync 112", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735495827142, "dur": 1, "ph": "X", "name": "ProcessMessages 112", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735495827143, "dur": 25, "ph": "X", "name": "ReadAsync 112", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735495827172, "dur": 30, "ph": "X", "name": "ReadAsync 92", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735495827204, "dur": 1, "ph": "X", "name": "ProcessMessages 80", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735495827206, "dur": 30, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735495827238, "dur": 1, "ph": "X", "name": "ProcessMessages 192", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735495827240, "dur": 20, "ph": "X", "name": "ReadAsync 192", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735495827263, "dur": 35, "ph": "X", "name": "ReadAsync 96", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735495827301, "dur": 1, "ph": "X", "name": "ProcessMessages 116", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735495827303, "dur": 29, "ph": "X", "name": "ReadAsync 116", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735495827334, "dur": 1, "ph": "X", "name": "ProcessMessages 172", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735495827335, "dur": 25, "ph": "X", "name": "ReadAsync 172", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735495827363, "dur": 45, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735495827414, "dur": 36, "ph": "X", "name": "ReadAsync 28", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735495827452, "dur": 1, "ph": "X", "name": "ProcessMessages 276", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735495827454, "dur": 26, "ph": "X", "name": "ReadAsync 276", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735495827483, "dur": 42, "ph": "X", "name": "ReadAsync 156", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735495827527, "dur": 33, "ph": "X", "name": "ReadAsync 116", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735495827563, "dur": 1, "ph": "X", "name": "ProcessMessages 220", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735495827565, "dur": 34, "ph": "X", "name": "ReadAsync 220", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735495827601, "dur": 32, "ph": "X", "name": "ReadAsync 132", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735495827636, "dur": 28, "ph": "X", "name": "ReadAsync 96", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735495827667, "dur": 49, "ph": "X", "name": "ReadAsync 112", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735495827719, "dur": 31, "ph": "X", "name": "ReadAsync 124", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735495827754, "dur": 1, "ph": "X", "name": "ProcessMessages 244", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735495827757, "dur": 52, "ph": "X", "name": "ReadAsync 244", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735495827811, "dur": 1, "ph": "X", "name": "ProcessMessages 160", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735495827813, "dur": 35, "ph": "X", "name": "ReadAsync 160", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735495827849, "dur": 1, "ph": "X", "name": "ProcessMessages 224", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735495827851, "dur": 32, "ph": "X", "name": "ReadAsync 224", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735495827885, "dur": 1, "ph": "X", "name": "ProcessMessages 128", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735495827886, "dur": 30, "ph": "X", "name": "ReadAsync 128", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735495827919, "dur": 1, "ph": "X", "name": "ProcessMessages 172", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735495827921, "dur": 31, "ph": "X", "name": "ReadAsync 172", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735495827954, "dur": 1, "ph": "X", "name": "ProcessMessages 180", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735495827955, "dur": 32, "ph": "X", "name": "ReadAsync 180", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735495827990, "dur": 30, "ph": "X", "name": "ReadAsync 96", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735495828030, "dur": 1, "ph": "X", "name": "ProcessMessages 172", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735495828032, "dur": 28, "ph": "X", "name": "ReadAsync 172", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735495828062, "dur": 1, "ph": "X", "name": "ProcessMessages 196", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735495828064, "dur": 39, "ph": "X", "name": "ReadAsync 196", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735495828107, "dur": 1, "ph": "X", "name": "ProcessMessages 176", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735495828109, "dur": 30, "ph": "X", "name": "ReadAsync 176", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735495828142, "dur": 32, "ph": "X", "name": "ReadAsync 140", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735495828176, "dur": 1, "ph": "X", "name": "ProcessMessages 128", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735495828177, "dur": 32, "ph": "X", "name": "ReadAsync 128", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735495828212, "dur": 1, "ph": "X", "name": "ProcessMessages 208", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735495828213, "dur": 26, "ph": "X", "name": "ReadAsync 208", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735495828242, "dur": 18, "ph": "X", "name": "ReadAsync 68", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735495828261, "dur": 20, "ph": "X", "name": "ReadAsync 92", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735495828284, "dur": 27, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735495828314, "dur": 1, "ph": "X", "name": "ProcessMessages 112", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735495828316, "dur": 32, "ph": "X", "name": "ReadAsync 112", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735495828349, "dur": 1, "ph": "X", "name": "ProcessMessages 192", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735495828351, "dur": 26, "ph": "X", "name": "ReadAsync 192", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735495828379, "dur": 2, "ph": "X", "name": "ProcessMessages 116", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735495828382, "dur": 22, "ph": "X", "name": "ReadAsync 116", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735495828406, "dur": 22, "ph": "X", "name": "ReadAsync 144", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735495828431, "dur": 59, "ph": "X", "name": "ReadAsync 76", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735495828493, "dur": 1, "ph": "X", "name": "ProcessMessages 128", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735495828520, "dur": 48, "ph": "X", "name": "ReadAsync 128", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735495828569, "dur": 1, "ph": "X", "name": "ProcessMessages 336", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735495828571, "dur": 21, "ph": "X", "name": "ReadAsync 336", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735495828595, "dur": 88, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735495828685, "dur": 25, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735495828713, "dur": 19, "ph": "X", "name": "ReadAsync 116", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735495828734, "dur": 1, "ph": "X", "name": "ProcessMessages 60", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735495828735, "dur": 31, "ph": "X", "name": "ReadAsync 60", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735495828768, "dur": 1, "ph": "X", "name": "ProcessMessages 116", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735495828770, "dur": 38, "ph": "X", "name": "ReadAsync 116", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735495828812, "dur": 1, "ph": "X", "name": "ProcessMessages 64", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735495828815, "dur": 236, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735495829055, "dur": 42, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735495829100, "dur": 1140, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735495830244, "dur": 43, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735495830291, "dur": 2735, "ph": "X", "name": "ProcessMessages 78", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735495833030, "dur": 63, "ph": "X", "name": "ReadAsync 78", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735495833095, "dur": 1, "ph": "X", "name": "ProcessMessages 80", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735495833097, "dur": 1037, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735495834139, "dur": 48, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735495834192, "dur": 35, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735495834232, "dur": 30, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735495834267, "dur": 196, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735495834465, "dur": 35, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735495834503, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735495834505, "dur": 115, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735495834625, "dur": 97, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735495834724, "dur": 1, "ph": "X", "name": "ProcessMessages 132", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735495834726, "dur": 60, "ph": "X", "name": "ReadAsync 132", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735495834788, "dur": 1, "ph": "X", "name": "ProcessMessages 112", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735495834790, "dur": 245, "ph": "X", "name": "ReadAsync 112", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735495835039, "dur": 14, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735495835054, "dur": 58, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735495835114, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735495835117, "dur": 9, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735495835127, "dur": 38, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735495835169, "dur": 115, "ph": "X", "name": "ReadAsync 44", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735495835287, "dur": 1, "ph": "X", "name": "ProcessMessages 100", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735495835289, "dur": 43, "ph": "X", "name": "ReadAsync 100", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735495835334, "dur": 1, "ph": "X", "name": "ProcessMessages 336", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735495835335, "dur": 27, "ph": "X", "name": "ReadAsync 336", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735495835365, "dur": 27, "ph": "X", "name": "ReadAsync 60", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735495835394, "dur": 28, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735495835752, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735495835754, "dur": 44, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735495835802, "dur": 1, "ph": "X", "name": "ProcessMessages 212", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735495835805, "dur": 40, "ph": "X", "name": "ReadAsync 212", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735495835848, "dur": 33, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735495835884, "dur": 1, "ph": "X", "name": "ProcessMessages 124", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735495835886, "dur": 19, "ph": "X", "name": "ReadAsync 124", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735495835908, "dur": 1, "ph": "X", "name": "ProcessMessages 68", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735495835910, "dur": 99, "ph": "X", "name": "ReadAsync 68", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735495836011, "dur": 25, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735495836040, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735495836041, "dur": 37, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735495836081, "dur": 33, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735495836121, "dur": 40, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735495836163, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735495836165, "dur": 50, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735495836218, "dur": 2, "ph": "X", "name": "ProcessMessages 64", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735495836221, "dur": 40, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735495836263, "dur": 52, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735495836320, "dur": 31, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735495836353, "dur": 26, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735495836381, "dur": 24, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735495836410, "dur": 15, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735495836429, "dur": 3, "ph": "X", "name": "ProcessMessages 40", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735495836433, "dur": 31, "ph": "X", "name": "ReadAsync 40", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735495836467, "dur": 34, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735495836503, "dur": 33, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735495836538, "dur": 198, "ph": "X", "name": "ReadAsync 52", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735495836741, "dur": 43, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735495836788, "dur": 36, "ph": "X", "name": "ReadAsync 68", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735495836829, "dur": 34, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735495836866, "dur": 56, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735495836926, "dur": 34, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735495836965, "dur": 37, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735495837004, "dur": 316, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735495837325, "dur": 32, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735495837359, "dur": 19, "ph": "X", "name": "ReadAsync 68", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735495837380, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735495837382, "dur": 44, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735495837428, "dur": 26, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735495837456, "dur": 220, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735495837681, "dur": 43, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735495837727, "dur": 1, "ph": "X", "name": "ProcessMessages 68", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735495837729, "dur": 28, "ph": "X", "name": "ReadAsync 68", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735495837759, "dur": 41, "ph": "X", "name": "ReadAsync 44", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735495837804, "dur": 1, "ph": "X", "name": "ProcessMessages 52", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735495837805, "dur": 39, "ph": "X", "name": "ReadAsync 52", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735495837849, "dur": 119, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735495837970, "dur": 25, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735495837996, "dur": 92, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735495838089, "dur": 26, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735495838119, "dur": 28, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735495838149, "dur": 27, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735495838178, "dur": 37, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735495838218, "dur": 1, "ph": "X", "name": "ProcessMessages 36", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735495838220, "dur": 40, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735495838261, "dur": 24, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735495838288, "dur": 89, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735495838380, "dur": 27, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735495838410, "dur": 30, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735495838444, "dur": 25, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735495838472, "dur": 20, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735495838493, "dur": 120, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735495838617, "dur": 39, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735495838660, "dur": 36, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735495838700, "dur": 33, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735495838737, "dur": 645, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735495839386, "dur": 24, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735495839412, "dur": 4, "ph": "X", "name": "ProcessMessages 8", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735495839416, "dur": 315237, "ph": "X", "name": "ReadAsync 8", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735496154664, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735496154668, "dur": 34, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735496154703, "dur": 23, "ph": "X", "name": "ProcessMessages 192", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735496154727, "dur": 3477, "ph": "X", "name": "ReadAsync 192", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735496158211, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735496158214, "dur": 41, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735496158257, "dur": 51, "ph": "X", "name": "ReadAsync 68", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735496158313, "dur": 1349, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735496159666, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735496159668, "dur": 49, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735496159720, "dur": 1, "ph": "X", "name": "ProcessMessages 160", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735496159723, "dur": 162, "ph": "X", "name": "ReadAsync 160", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735496159890, "dur": 188, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735496160083, "dur": 1, "ph": "X", "name": "ProcessMessages 64", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735496160085, "dur": 736, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735496160827, "dur": 39, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735496160869, "dur": 1, "ph": "X", "name": "ProcessMessages 96", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735496160872, "dur": 242, "ph": "X", "name": "ReadAsync 96", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735496161116, "dur": 293, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735496161413, "dur": 23, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735496161437, "dur": 2, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735496161440, "dur": 100, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735496161543, "dur": 19, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735496161564, "dur": 19, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735496161585, "dur": 125, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735496161711, "dur": 19, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735496161732, "dur": 47, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735496161781, "dur": 18, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735496161801, "dur": 200, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735496162003, "dur": 693, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735496162702, "dur": 39, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735496162750, "dur": 6, "ph": "X", "name": "ProcessMessages 848", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735496162756, "dur": 15, "ph": "X", "name": "ReadAsync 848", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735496162773, "dur": 34, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735496162812, "dur": 59, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735496162874, "dur": 30, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735496162908, "dur": 1, "ph": "X", "name": "ProcessMessages 96", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735496162910, "dur": 167, "ph": "X", "name": "ReadAsync 96", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735496163081, "dur": 1, "ph": "X", "name": "ProcessMessages 112", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735496163083, "dur": 48, "ph": "X", "name": "ReadAsync 112", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735496163133, "dur": 2, "ph": "X", "name": "ProcessMessages 416", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735496163136, "dur": 34, "ph": "X", "name": "ReadAsync 416", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735496163173, "dur": 29, "ph": "X", "name": "ReadAsync 84", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735496163204, "dur": 1, "ph": "X", "name": "ProcessMessages 92", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735496163205, "dur": 30, "ph": "X", "name": "ReadAsync 92", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735496163238, "dur": 168, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735496163410, "dur": 30, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735496163443, "dur": 28, "ph": "X", "name": "ReadAsync 52", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735496163474, "dur": 25, "ph": "X", "name": "ReadAsync 124", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735496163503, "dur": 29, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735496163535, "dur": 23, "ph": "X", "name": "ReadAsync 28", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735496163560, "dur": 37526, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735496201096, "dur": 3, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735496201100, "dur": 68, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735496201172, "dur": 3785, "ph": "X", "name": "ProcessMessages 1425", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735496204962, "dur": 44, "ph": "X", "name": "ReadAsync 1425", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735496205009, "dur": 1, "ph": "X", "name": "ProcessMessages 25", "args": {}}, {"pid": 3100, "tid": 21474836480, "ts": 1754735496205011, "dur": 5128, "ph": "X", "name": "ReadAsync 25", "args": {}}, {"pid": 3100, "tid": 24787, "ts": 1754735496216998, "dur": 833, "ph": "X", "name": "ReadEntireBinlogFromIpcAsync", "args": {}}, {"pid": 3100, "tid": 17179869184, "ph": "M", "name": "thread_name", "args": {"name": "WaitForBuildProgramInputDataBeingWrittenAndSendDagReadyMessageAsync"}}, {"pid": 3100, "tid": 17179869184, "ts": 1754735495569839, "dur": 4, "ph": "X", "name": "await writeBuildProgramInputDataTask", "args": {}}, {"pid": 3100, "tid": 17179869184, "ts": 1754735495569844, "dur": 137255, "ph": "X", "name": "WritePipe.WaitConnectionAsync", "args": {}}, {"pid": 3100, "tid": 17179869184, "ts": 1754735495707102, "dur": 48, "ph": "X", "name": "WriteDagReadyMessage", "args": {}}, {"pid": 3100, "tid": 24787, "ts": 1754735496217833, "dur": 4, "ph": "X", "name": "WaitForBuildProgramInputDataBeingWrittenAndSendDagReadyMessageAsync", "args": {}}, {"pid": 3100, "tid": 1, "ph": "M", "name": "thread_name", "args": {"name": ""}}, {"pid": 3100, "tid": 1, "ts": 1754735494599030, "dur": 4876, "ph": "X", "name": "<Add>b__0", "args": {}}, {"pid": 3100, "tid": 1, "ts": 1754735494603912, "dur": 29384, "ph": "X", "name": "<Add>b__0", "args": {}}, {"pid": 3100, "tid": 1, "ts": 1754735494633307, "dur": 99941, "ph": "X", "name": "WriteJson", "args": {}}, {"pid": 3100, "tid": 24787, "ts": 1754735496217838, "dur": 3, "ph": "X", "name": "", "args": {}}, {"pid": 3100, "tid": 12884901888, "ph": "M", "name": "thread_name", "args": {"name": "ReadEntireBinlogFromIpcAsync"}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735494597288, "dur": 179757, "ph": "X", "name": "WaitForConnectionAsync", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735494777047, "dur": 35044, "ph": "X", "name": "UpdateFromStreamAsync", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735494778316, "dur": 1298, "ph": "X", "name": "ReadAsync 0", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735494779622, "dur": 1077, "ph": "X", "name": "ProcessMessages 282", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735494780704, "dur": 207, "ph": "X", "name": "ReadAsync 282", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735494780914, "dur": 6, "ph": "X", "name": "ProcessMessages 20204", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735494780921, "dur": 101, "ph": "X", "name": "ReadAsync 20204", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735494781026, "dur": 1, "ph": "X", "name": "ProcessMessages 758", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735494781028, "dur": 45, "ph": "X", "name": "ReadAsync 758", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735494781075, "dur": 47, "ph": "X", "name": "ReadAsync 489", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735494781124, "dur": 1, "ph": "X", "name": "ProcessMessages 428", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735494781126, "dur": 36, "ph": "X", "name": "ReadAsync 428", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735494781165, "dur": 27, "ph": "X", "name": "ReadAsync 442", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735494781193, "dur": 196, "ph": "X", "name": "ReadAsync 226", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735494781391, "dur": 44, "ph": "X", "name": "ReadAsync 336", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735494781437, "dur": 2, "ph": "X", "name": "ProcessMessages 2648", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735494781440, "dur": 42, "ph": "X", "name": "ReadAsync 2648", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735494781485, "dur": 1, "ph": "X", "name": "ProcessMessages 400", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735494781487, "dur": 37, "ph": "X", "name": "ReadAsync 400", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735494781527, "dur": 32, "ph": "X", "name": "ReadAsync 613", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735494781562, "dur": 1, "ph": "X", "name": "ProcessMessages 448", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735494781564, "dur": 41, "ph": "X", "name": "ReadAsync 448", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735494781608, "dur": 1, "ph": "X", "name": "ProcessMessages 554", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735494781610, "dur": 53, "ph": "X", "name": "ReadAsync 554", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735494781667, "dur": 34, "ph": "X", "name": "ReadAsync 681", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735494781703, "dur": 1, "ph": "X", "name": "ProcessMessages 218", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735494781705, "dur": 43, "ph": "X", "name": "ReadAsync 218", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735494781750, "dur": 29, "ph": "X", "name": "ReadAsync 667", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735494781782, "dur": 29, "ph": "X", "name": "ReadAsync 491", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735494781813, "dur": 26, "ph": "X", "name": "ReadAsync 437", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735494781842, "dur": 28, "ph": "X", "name": "ReadAsync 348", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735494781873, "dur": 37, "ph": "X", "name": "ReadAsync 267", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735494781914, "dur": 1, "ph": "X", "name": "ProcessMessages 369", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735494781916, "dur": 39, "ph": "X", "name": "ReadAsync 369", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735494781957, "dur": 30, "ph": "X", "name": "ReadAsync 459", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735494781989, "dur": 26, "ph": "X", "name": "ReadAsync 369", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735494782017, "dur": 28, "ph": "X", "name": "ReadAsync 339", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735494782049, "dur": 29, "ph": "X", "name": "ReadAsync 315", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735494782082, "dur": 24, "ph": "X", "name": "ReadAsync 431", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735494782108, "dur": 28, "ph": "X", "name": "ReadAsync 257", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735494782140, "dur": 32, "ph": "X", "name": "ReadAsync 254", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735494782174, "dur": 1, "ph": "X", "name": "ProcessMessages 191", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735494782175, "dur": 27, "ph": "X", "name": "ReadAsync 191", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735494782204, "dur": 43, "ph": "X", "name": "ReadAsync 286", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735494782250, "dur": 27, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735494782279, "dur": 25, "ph": "X", "name": "ReadAsync 321", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735494782306, "dur": 30, "ph": "X", "name": "ReadAsync 339", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735494782337, "dur": 33, "ph": "X", "name": "ReadAsync 355", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735494782372, "dur": 37, "ph": "X", "name": "ReadAsync 316", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735494782413, "dur": 1, "ph": "X", "name": "ProcessMessages 332", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735494782414, "dur": 35, "ph": "X", "name": "ReadAsync 332", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735494782454, "dur": 34, "ph": "X", "name": "ReadAsync 489", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735494782490, "dur": 1, "ph": "X", "name": "ProcessMessages 242", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735494782491, "dur": 30, "ph": "X", "name": "ReadAsync 242", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735494782524, "dur": 37, "ph": "X", "name": "ReadAsync 294", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735494782563, "dur": 34, "ph": "X", "name": "ReadAsync 284", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735494782600, "dur": 34, "ph": "X", "name": "ReadAsync 312", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735494782637, "dur": 35, "ph": "X", "name": "ReadAsync 330", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735494782675, "dur": 1, "ph": "X", "name": "ProcessMessages 137", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735494782677, "dur": 47, "ph": "X", "name": "ReadAsync 137", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735494782729, "dur": 42, "ph": "X", "name": "ReadAsync 258", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735494782774, "dur": 1, "ph": "X", "name": "ProcessMessages 262", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735494782776, "dur": 41, "ph": "X", "name": "ReadAsync 262", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735494782823, "dur": 74, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735494782902, "dur": 43, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735494782949, "dur": 109, "ph": "X", "name": "ReadAsync 547", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735494783062, "dur": 44, "ph": "X", "name": "ReadAsync 259", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735494783108, "dur": 37, "ph": "X", "name": "ReadAsync 304", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735494783149, "dur": 1, "ph": "X", "name": "ProcessMessages 433", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735494783151, "dur": 52, "ph": "X", "name": "ReadAsync 433", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735494783206, "dur": 1, "ph": "X", "name": "ProcessMessages 275", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735494783208, "dur": 37, "ph": "X", "name": "ReadAsync 275", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735494783250, "dur": 39, "ph": "X", "name": "ReadAsync 191", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735494783292, "dur": 26, "ph": "X", "name": "ReadAsync 476", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735494783321, "dur": 1, "ph": "X", "name": "ProcessMessages 196", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735494783322, "dur": 25, "ph": "X", "name": "ReadAsync 196", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735494783350, "dur": 35, "ph": "X", "name": "ReadAsync 306", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735494783388, "dur": 1, "ph": "X", "name": "ProcessMessages 238", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735494783390, "dur": 32, "ph": "X", "name": "ReadAsync 238", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735494783424, "dur": 1, "ph": "X", "name": "ProcessMessages 284", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735494783426, "dur": 37, "ph": "X", "name": "ReadAsync 284", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735494783464, "dur": 1, "ph": "X", "name": "ProcessMessages 269", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735494783466, "dur": 48, "ph": "X", "name": "ReadAsync 269", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735494783519, "dur": 36, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735494783558, "dur": 38, "ph": "X", "name": "ReadAsync 241", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735494783599, "dur": 27, "ph": "X", "name": "ReadAsync 264", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735494783629, "dur": 1, "ph": "X", "name": "ProcessMessages 405", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735494783631, "dur": 37, "ph": "X", "name": "ReadAsync 405", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735494783671, "dur": 25, "ph": "X", "name": "ReadAsync 552", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735494783697, "dur": 29, "ph": "X", "name": "ReadAsync 225", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735494783730, "dur": 31, "ph": "X", "name": "ReadAsync 445", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735494783763, "dur": 36, "ph": "X", "name": "ReadAsync 417", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735494783802, "dur": 1, "ph": "X", "name": "ProcessMessages 508", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735494783804, "dur": 36, "ph": "X", "name": "ReadAsync 508", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735494783843, "dur": 1, "ph": "X", "name": "ProcessMessages 548", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735494783845, "dur": 36, "ph": "X", "name": "ReadAsync 548", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735494783883, "dur": 26, "ph": "X", "name": "ReadAsync 302", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735494783911, "dur": 34, "ph": "X", "name": "ReadAsync 76", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735494783947, "dur": 23, "ph": "X", "name": "ReadAsync 316", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735494783972, "dur": 47, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735494784023, "dur": 36, "ph": "X", "name": "ReadAsync 28", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735494784061, "dur": 105, "ph": "X", "name": "ReadAsync 255", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735494784171, "dur": 36, "ph": "X", "name": "ReadAsync 231", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735494784208, "dur": 1, "ph": "X", "name": "ProcessMessages 490", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735494784210, "dur": 26, "ph": "X", "name": "ReadAsync 490", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735494784239, "dur": 24, "ph": "X", "name": "ReadAsync 176", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735494784266, "dur": 1, "ph": "X", "name": "ProcessMessages 216", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735494784268, "dur": 30, "ph": "X", "name": "ReadAsync 216", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735494784301, "dur": 31, "ph": "X", "name": "ReadAsync 231", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735494784333, "dur": 29, "ph": "X", "name": "ReadAsync 233", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735494784365, "dur": 31, "ph": "X", "name": "ReadAsync 339", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735494784399, "dur": 25, "ph": "X", "name": "ReadAsync 348", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735494784428, "dur": 1, "ph": "X", "name": "ProcessMessages 285", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735494784430, "dur": 29, "ph": "X", "name": "ReadAsync 285", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735494784462, "dur": 44, "ph": "X", "name": "ReadAsync 410", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735494784509, "dur": 1, "ph": "X", "name": "ProcessMessages 89", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735494784511, "dur": 37, "ph": "X", "name": "ReadAsync 89", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735494784550, "dur": 35, "ph": "X", "name": "ReadAsync 385", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735494784589, "dur": 41, "ph": "X", "name": "ReadAsync 198", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735494784632, "dur": 27, "ph": "X", "name": "ReadAsync 420", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735494784663, "dur": 1, "ph": "X", "name": "ProcessMessages 282", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735494784664, "dur": 37, "ph": "X", "name": "ReadAsync 282", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735494784704, "dur": 31, "ph": "X", "name": "ReadAsync 379", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735494784740, "dur": 29, "ph": "X", "name": "ReadAsync 494", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735494784771, "dur": 25, "ph": "X", "name": "ReadAsync 108", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735494784798, "dur": 23, "ph": "X", "name": "ReadAsync 378", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735494784823, "dur": 194, "ph": "X", "name": "ReadAsync 386", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735494785019, "dur": 36, "ph": "X", "name": "ReadAsync 257", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735494785056, "dur": 2, "ph": "X", "name": "ProcessMessages 2172", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735494785059, "dur": 21, "ph": "X", "name": "ReadAsync 2172", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735494785081, "dur": 22, "ph": "X", "name": "ReadAsync 65", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735494785105, "dur": 26, "ph": "X", "name": "ReadAsync 287", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735494785132, "dur": 21, "ph": "X", "name": "ReadAsync 271", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735494785154, "dur": 19, "ph": "X", "name": "ReadAsync 261", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735494785175, "dur": 21, "ph": "X", "name": "ReadAsync 331", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735494785198, "dur": 26, "ph": "X", "name": "ReadAsync 289", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735494785226, "dur": 23, "ph": "X", "name": "ReadAsync 429", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735494785250, "dur": 22, "ph": "X", "name": "ReadAsync 299", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735494785274, "dur": 25, "ph": "X", "name": "ReadAsync 386", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735494785300, "dur": 21, "ph": "X", "name": "ReadAsync 322", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735494785324, "dur": 28, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735494785354, "dur": 25, "ph": "X", "name": "ReadAsync 460", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735494785381, "dur": 23, "ph": "X", "name": "ReadAsync 352", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735494785406, "dur": 22, "ph": "X", "name": "ReadAsync 198", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735494785430, "dur": 21, "ph": "X", "name": "ReadAsync 280", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735494785453, "dur": 23, "ph": "X", "name": "ReadAsync 271", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735494785478, "dur": 1, "ph": "X", "name": "ProcessMessages 342", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735494785479, "dur": 32, "ph": "X", "name": "ReadAsync 342", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735494785514, "dur": 25, "ph": "X", "name": "ReadAsync 258", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735494785541, "dur": 25, "ph": "X", "name": "ReadAsync 287", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735494785567, "dur": 24, "ph": "X", "name": "ReadAsync 452", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735494785592, "dur": 21, "ph": "X", "name": "ReadAsync 431", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735494785615, "dur": 23, "ph": "X", "name": "ReadAsync 342", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735494785639, "dur": 2, "ph": "X", "name": "ProcessMessages 369", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735494785641, "dur": 22, "ph": "X", "name": "ReadAsync 369", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735494785664, "dur": 18, "ph": "X", "name": "ReadAsync 467", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735494785684, "dur": 23, "ph": "X", "name": "ReadAsync 209", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735494785708, "dur": 33, "ph": "X", "name": "ReadAsync 391", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735494785745, "dur": 38, "ph": "X", "name": "ReadAsync 360", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735494785785, "dur": 27, "ph": "X", "name": "ReadAsync 503", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735494785815, "dur": 25, "ph": "X", "name": "ReadAsync 594", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735494785841, "dur": 31, "ph": "X", "name": "ReadAsync 387", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735494785874, "dur": 1, "ph": "X", "name": "ProcessMessages 346", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735494785876, "dur": 30, "ph": "X", "name": "ReadAsync 346", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735494785908, "dur": 21, "ph": "X", "name": "ReadAsync 285", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735494785931, "dur": 26, "ph": "X", "name": "ReadAsync 408", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735494785960, "dur": 1, "ph": "X", "name": "ProcessMessages 199", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735494785961, "dur": 31, "ph": "X", "name": "ReadAsync 199", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735494785994, "dur": 24, "ph": "X", "name": "ReadAsync 537", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735494786021, "dur": 24, "ph": "X", "name": "ReadAsync 252", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735494786046, "dur": 21, "ph": "X", "name": "ReadAsync 335", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735494786071, "dur": 30, "ph": "X", "name": "ReadAsync 319", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735494786103, "dur": 26, "ph": "X", "name": "ReadAsync 125", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735494786131, "dur": 21, "ph": "X", "name": "ReadAsync 316", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735494786155, "dur": 30, "ph": "X", "name": "ReadAsync 430", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735494786188, "dur": 1, "ph": "X", "name": "ProcessMessages 278", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735494786190, "dur": 36, "ph": "X", "name": "ReadAsync 278", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735494786228, "dur": 59, "ph": "X", "name": "ReadAsync 538", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735494786290, "dur": 1, "ph": "X", "name": "ProcessMessages 81", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735494786291, "dur": 32, "ph": "X", "name": "ReadAsync 81", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735494786326, "dur": 27, "ph": "X", "name": "ReadAsync 327", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735494786356, "dur": 35, "ph": "X", "name": "ReadAsync 421", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735494786396, "dur": 24, "ph": "X", "name": "ReadAsync 470", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735494786423, "dur": 30, "ph": "X", "name": "ReadAsync 296", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735494786456, "dur": 31, "ph": "X", "name": "ReadAsync 314", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735494786489, "dur": 24, "ph": "X", "name": "ReadAsync 467", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735494786516, "dur": 26, "ph": "X", "name": "ReadAsync 171", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735494786543, "dur": 18, "ph": "X", "name": "ReadAsync 372", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735494786563, "dur": 24, "ph": "X", "name": "ReadAsync 219", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735494786589, "dur": 31, "ph": "X", "name": "ReadAsync 393", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735494786623, "dur": 26, "ph": "X", "name": "ReadAsync 364", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735494786650, "dur": 22, "ph": "X", "name": "ReadAsync 313", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735494786674, "dur": 21, "ph": "X", "name": "ReadAsync 461", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735494786697, "dur": 135, "ph": "X", "name": "ReadAsync 193", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735494786835, "dur": 32, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735494786869, "dur": 28, "ph": "X", "name": "ReadAsync 540", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735494786900, "dur": 26, "ph": "X", "name": "ReadAsync 264", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735494786929, "dur": 1, "ph": "X", "name": "ProcessMessages 313", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735494786930, "dur": 31, "ph": "X", "name": "ReadAsync 313", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735494786964, "dur": 22, "ph": "X", "name": "ReadAsync 420", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735494786988, "dur": 38, "ph": "X", "name": "ReadAsync 354", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735494787029, "dur": 1, "ph": "X", "name": "ProcessMessages 258", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735494787031, "dur": 52, "ph": "X", "name": "ReadAsync 258", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735494787085, "dur": 32, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735494787119, "dur": 26, "ph": "X", "name": "ReadAsync 510", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735494787148, "dur": 28, "ph": "X", "name": "ReadAsync 511", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735494787178, "dur": 24, "ph": "X", "name": "ReadAsync 393", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735494787204, "dur": 27, "ph": "X", "name": "ReadAsync 154", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735494787234, "dur": 31, "ph": "X", "name": "ReadAsync 426", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735494787267, "dur": 25, "ph": "X", "name": "ReadAsync 359", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735494787296, "dur": 30, "ph": "X", "name": "ReadAsync 82", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735494787328, "dur": 35, "ph": "X", "name": "ReadAsync 367", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735494787367, "dur": 26, "ph": "X", "name": "ReadAsync 381", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735494787396, "dur": 22, "ph": "X", "name": "ReadAsync 426", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735494787420, "dur": 27, "ph": "X", "name": "ReadAsync 287", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735494787450, "dur": 1, "ph": "X", "name": "ProcessMessages 361", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735494787452, "dur": 30, "ph": "X", "name": "ReadAsync 361", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735494787485, "dur": 25, "ph": "X", "name": "ReadAsync 163", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735494787512, "dur": 25, "ph": "X", "name": "ReadAsync 256", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735494787540, "dur": 26, "ph": "X", "name": "ReadAsync 412", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735494787569, "dur": 1, "ph": "X", "name": "ProcessMessages 334", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735494787571, "dur": 39, "ph": "X", "name": "ReadAsync 334", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735494787611, "dur": 24, "ph": "X", "name": "ReadAsync 457", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735494787637, "dur": 51, "ph": "X", "name": "ReadAsync 276", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735494787691, "dur": 23, "ph": "X", "name": "ReadAsync 15", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735494787715, "dur": 37, "ph": "X", "name": "ReadAsync 367", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735494787754, "dur": 23, "ph": "X", "name": "ReadAsync 324", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735494787779, "dur": 23, "ph": "X", "name": "ReadAsync 344", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735494787805, "dur": 28, "ph": "X", "name": "ReadAsync 362", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735494787835, "dur": 24, "ph": "X", "name": "ReadAsync 415", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735494787861, "dur": 22, "ph": "X", "name": "ReadAsync 185", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735494787884, "dur": 29, "ph": "X", "name": "ReadAsync 195", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735494787916, "dur": 24, "ph": "X", "name": "ReadAsync 216", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735494787942, "dur": 24, "ph": "X", "name": "ReadAsync 76", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735494787968, "dur": 30, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735494788001, "dur": 25, "ph": "X", "name": "ReadAsync 477", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735494788027, "dur": 27, "ph": "X", "name": "ReadAsync 242", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735494788056, "dur": 18, "ph": "X", "name": "ReadAsync 328", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735494788075, "dur": 26, "ph": "X", "name": "ReadAsync 302", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735494788103, "dur": 23, "ph": "X", "name": "ReadAsync 224", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735494788128, "dur": 48, "ph": "X", "name": "ReadAsync 390", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735494788178, "dur": 1, "ph": "X", "name": "ProcessMessages 254", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735494788179, "dur": 24, "ph": "X", "name": "ReadAsync 254", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735494788205, "dur": 68, "ph": "X", "name": "ReadAsync 86", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735494788277, "dur": 29, "ph": "X", "name": "ReadAsync 141", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735494788307, "dur": 1, "ph": "X", "name": "ProcessMessages 548", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735494788308, "dur": 24, "ph": "X", "name": "ReadAsync 548", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735494788334, "dur": 30, "ph": "X", "name": "ReadAsync 442", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735494788367, "dur": 1, "ph": "X", "name": "ProcessMessages 271", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735494788369, "dur": 41, "ph": "X", "name": "ReadAsync 271", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735494788411, "dur": 1, "ph": "X", "name": "ProcessMessages 520", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735494788413, "dur": 19, "ph": "X", "name": "ReadAsync 520", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735494788433, "dur": 30, "ph": "X", "name": "ReadAsync 261", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735494788465, "dur": 23, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735494788491, "dur": 36, "ph": "X", "name": "ReadAsync 375", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735494788537, "dur": 1, "ph": "X", "name": "ProcessMessages 379", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735494788539, "dur": 42, "ph": "X", "name": "ReadAsync 379", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735494788585, "dur": 1, "ph": "X", "name": "ProcessMessages 364", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735494788587, "dur": 49, "ph": "X", "name": "ReadAsync 364", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735494788639, "dur": 1, "ph": "X", "name": "ProcessMessages 534", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735494788640, "dur": 38, "ph": "X", "name": "ReadAsync 534", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735494788682, "dur": 1, "ph": "X", "name": "ProcessMessages 439", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735494788684, "dur": 49, "ph": "X", "name": "ReadAsync 439", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735494788736, "dur": 1, "ph": "X", "name": "ProcessMessages 322", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735494788738, "dur": 53, "ph": "X", "name": "ReadAsync 322", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735494788794, "dur": 1, "ph": "X", "name": "ProcessMessages 738", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735494788796, "dur": 38, "ph": "X", "name": "ReadAsync 738", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735494788837, "dur": 1, "ph": "X", "name": "ProcessMessages 656", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735494788839, "dur": 47, "ph": "X", "name": "ReadAsync 656", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735494788890, "dur": 1, "ph": "X", "name": "ProcessMessages 621", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735494788892, "dur": 44, "ph": "X", "name": "ReadAsync 621", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735494788938, "dur": 17, "ph": "X", "name": "ReadAsync 322", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735494788957, "dur": 36, "ph": "X", "name": "ReadAsync 336", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735494788997, "dur": 59, "ph": "X", "name": "ReadAsync 44", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735494789059, "dur": 1, "ph": "X", "name": "ProcessMessages 401", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735494789061, "dur": 81, "ph": "X", "name": "ReadAsync 401", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735494789145, "dur": 1, "ph": "X", "name": "ProcessMessages 289", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735494789147, "dur": 30, "ph": "X", "name": "ReadAsync 289", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735494789180, "dur": 62, "ph": "X", "name": "ReadAsync 254", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735494789245, "dur": 1, "ph": "X", "name": "ProcessMessages 256", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735494789246, "dur": 34, "ph": "X", "name": "ReadAsync 256", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735494789283, "dur": 33, "ph": "X", "name": "ReadAsync 450", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735494789319, "dur": 1, "ph": "X", "name": "ProcessMessages 21", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735494789321, "dur": 40, "ph": "X", "name": "ReadAsync 21", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735494789364, "dur": 33, "ph": "X", "name": "ReadAsync 254", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735494789399, "dur": 161, "ph": "X", "name": "ReadAsync 407", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735494789563, "dur": 1, "ph": "X", "name": "ProcessMessages 331", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735494789565, "dur": 57, "ph": "X", "name": "ReadAsync 331", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735494789626, "dur": 1, "ph": "X", "name": "ProcessMessages 1624", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735494789628, "dur": 34, "ph": "X", "name": "ReadAsync 1624", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735494789665, "dur": 1, "ph": "X", "name": "ProcessMessages 527", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735494789667, "dur": 37, "ph": "X", "name": "ReadAsync 527", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735494789707, "dur": 1, "ph": "X", "name": "ProcessMessages 324", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735494789708, "dur": 42, "ph": "X", "name": "ReadAsync 324", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735494789753, "dur": 1, "ph": "X", "name": "ProcessMessages 441", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735494789755, "dur": 40, "ph": "X", "name": "ReadAsync 441", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735494789797, "dur": 34, "ph": "X", "name": "ReadAsync 459", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735494789835, "dur": 1, "ph": "X", "name": "ProcessMessages 197", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735494789837, "dur": 48, "ph": "X", "name": "ReadAsync 197", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735494789887, "dur": 40, "ph": "X", "name": "ReadAsync 569", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735494789930, "dur": 1, "ph": "X", "name": "ProcessMessages 713", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735494789932, "dur": 35, "ph": "X", "name": "ReadAsync 713", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735494789970, "dur": 24, "ph": "X", "name": "ReadAsync 334", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735494789996, "dur": 26, "ph": "X", "name": "ReadAsync 329", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735494790025, "dur": 25, "ph": "X", "name": "ReadAsync 299", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735494790053, "dur": 26, "ph": "X", "name": "ReadAsync 489", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735494790082, "dur": 37, "ph": "X", "name": "ReadAsync 148", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735494790123, "dur": 1, "ph": "X", "name": "ProcessMessages 473", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735494790127, "dur": 97, "ph": "X", "name": "ReadAsync 473", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735494790227, "dur": 1, "ph": "X", "name": "ProcessMessages 427", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735494790229, "dur": 84, "ph": "X", "name": "ReadAsync 427", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735494790316, "dur": 1, "ph": "X", "name": "ProcessMessages 847", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735494790319, "dur": 37, "ph": "X", "name": "ReadAsync 847", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735494790359, "dur": 1, "ph": "X", "name": "ProcessMessages 461", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735494790361, "dur": 33, "ph": "X", "name": "ReadAsync 461", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735494790399, "dur": 1, "ph": "X", "name": "ProcessMessages 91", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735494790401, "dur": 44, "ph": "X", "name": "ReadAsync 91", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735494790447, "dur": 36, "ph": "X", "name": "ReadAsync 319", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735494790486, "dur": 1, "ph": "X", "name": "ProcessMessages 319", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735494790488, "dur": 43, "ph": "X", "name": "ReadAsync 319", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735494790535, "dur": 1, "ph": "X", "name": "ProcessMessages 476", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735494790537, "dur": 38, "ph": "X", "name": "ReadAsync 476", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735494790578, "dur": 1, "ph": "X", "name": "ProcessMessages 287", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735494790580, "dur": 34, "ph": "X", "name": "ReadAsync 287", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735494790617, "dur": 1, "ph": "X", "name": "ProcessMessages 426", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735494790619, "dur": 34, "ph": "X", "name": "ReadAsync 426", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735494790657, "dur": 32, "ph": "X", "name": "ReadAsync 158", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735494790691, "dur": 23, "ph": "X", "name": "ReadAsync 547", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735494790717, "dur": 24, "ph": "X", "name": "ReadAsync 356", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735494790743, "dur": 23, "ph": "X", "name": "ReadAsync 287", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735494790767, "dur": 30, "ph": "X", "name": "ReadAsync 259", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735494790799, "dur": 24, "ph": "X", "name": "ReadAsync 287", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735494790825, "dur": 26, "ph": "X", "name": "ReadAsync 381", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735494790853, "dur": 28, "ph": "X", "name": "ReadAsync 231", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735494790884, "dur": 32, "ph": "X", "name": "ReadAsync 285", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735494790918, "dur": 23, "ph": "X", "name": "ReadAsync 416", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735494790943, "dur": 21, "ph": "X", "name": "ReadAsync 255", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735494790966, "dur": 26, "ph": "X", "name": "ReadAsync 359", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735494790993, "dur": 20, "ph": "X", "name": "ReadAsync 385", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735494791015, "dur": 20, "ph": "X", "name": "ReadAsync 76", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735494791036, "dur": 27, "ph": "X", "name": "ReadAsync 420", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735494791067, "dur": 1, "ph": "X", "name": "ProcessMessages 395", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735494791068, "dur": 28, "ph": "X", "name": "ReadAsync 395", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735494791098, "dur": 29, "ph": "X", "name": "ReadAsync 710", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735494791129, "dur": 25, "ph": "X", "name": "ReadAsync 328", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735494791156, "dur": 21, "ph": "X", "name": "ReadAsync 628", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735494791178, "dur": 21, "ph": "X", "name": "ReadAsync 98", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735494791202, "dur": 22, "ph": "X", "name": "ReadAsync 378", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735494791226, "dur": 21, "ph": "X", "name": "ReadAsync 407", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735494791249, "dur": 22, "ph": "X", "name": "ReadAsync 315", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735494791273, "dur": 31, "ph": "X", "name": "ReadAsync 339", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735494791307, "dur": 1, "ph": "X", "name": "ProcessMessages 494", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735494791309, "dur": 30, "ph": "X", "name": "ReadAsync 494", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735494791341, "dur": 27, "ph": "X", "name": "ReadAsync 426", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735494791371, "dur": 35, "ph": "X", "name": "ReadAsync 348", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735494791409, "dur": 26, "ph": "X", "name": "ReadAsync 570", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735494791438, "dur": 44, "ph": "X", "name": "ReadAsync 285", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735494791485, "dur": 1, "ph": "X", "name": "ProcessMessages 445", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735494791486, "dur": 35, "ph": "X", "name": "ReadAsync 445", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735494791525, "dur": 34, "ph": "X", "name": "ReadAsync 491", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735494791561, "dur": 22, "ph": "X", "name": "ReadAsync 236", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735494791584, "dur": 28, "ph": "X", "name": "ReadAsync 403", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735494791613, "dur": 27, "ph": "X", "name": "ReadAsync 240", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735494791642, "dur": 22, "ph": "X", "name": "ReadAsync 166", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735494791666, "dur": 41, "ph": "X", "name": "ReadAsync 291", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735494791709, "dur": 27, "ph": "X", "name": "ReadAsync 231", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735494791739, "dur": 1, "ph": "X", "name": "ProcessMessages 279", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735494791741, "dur": 36, "ph": "X", "name": "ReadAsync 279", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735494791780, "dur": 39, "ph": "X", "name": "ReadAsync 432", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735494791820, "dur": 34, "ph": "X", "name": "ReadAsync 270", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735494791856, "dur": 26, "ph": "X", "name": "ReadAsync 511", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735494791884, "dur": 21, "ph": "X", "name": "ReadAsync 526", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735494791907, "dur": 22, "ph": "X", "name": "ReadAsync 328", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735494791930, "dur": 22, "ph": "X", "name": "ReadAsync 285", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735494791954, "dur": 21, "ph": "X", "name": "ReadAsync 344", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735494791978, "dur": 36, "ph": "X", "name": "ReadAsync 8", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735494792017, "dur": 1, "ph": "X", "name": "ProcessMessages 387", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735494792019, "dur": 33, "ph": "X", "name": "ReadAsync 387", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735494792053, "dur": 24, "ph": "X", "name": "ReadAsync 139", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735494792080, "dur": 36, "ph": "X", "name": "ReadAsync 240", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735494792118, "dur": 24, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735494792143, "dur": 24, "ph": "X", "name": "ReadAsync 244", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735494792169, "dur": 26, "ph": "X", "name": "ReadAsync 379", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735494792197, "dur": 27, "ph": "X", "name": "ReadAsync 286", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735494792227, "dur": 65, "ph": "X", "name": "ReadAsync 68", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735494792294, "dur": 24, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735494792319, "dur": 24, "ph": "X", "name": "ReadAsync 366", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735494792346, "dur": 26, "ph": "X", "name": "ReadAsync 303", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735494792374, "dur": 32, "ph": "X", "name": "ReadAsync 430", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735494792409, "dur": 1, "ph": "X", "name": "ProcessMessages 502", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735494792411, "dur": 35, "ph": "X", "name": "ReadAsync 502", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735494792448, "dur": 26, "ph": "X", "name": "ReadAsync 554", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735494792477, "dur": 24, "ph": "X", "name": "ReadAsync 298", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735494792503, "dur": 27, "ph": "X", "name": "ReadAsync 163", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735494792532, "dur": 47, "ph": "X", "name": "ReadAsync 139", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735494792582, "dur": 30, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735494792615, "dur": 25, "ph": "X", "name": "ReadAsync 314", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735494792641, "dur": 1, "ph": "X", "name": "ProcessMessages 223", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735494792643, "dur": 24, "ph": "X", "name": "ReadAsync 223", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735494792669, "dur": 24, "ph": "X", "name": "ReadAsync 381", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735494792694, "dur": 1, "ph": "X", "name": "ProcessMessages 314", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735494792696, "dur": 22, "ph": "X", "name": "ReadAsync 314", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735494792720, "dur": 22, "ph": "X", "name": "ReadAsync 299", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735494792744, "dur": 26, "ph": "X", "name": "ReadAsync 315", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735494792773, "dur": 1, "ph": "X", "name": "ProcessMessages 212", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735494792775, "dur": 37, "ph": "X", "name": "ReadAsync 212", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735494792821, "dur": 33, "ph": "X", "name": "ReadAsync 298", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735494792856, "dur": 25, "ph": "X", "name": "ReadAsync 148", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735494792883, "dur": 27, "ph": "X", "name": "ReadAsync 84", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735494792913, "dur": 1, "ph": "X", "name": "ProcessMessages 251", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735494792915, "dur": 37, "ph": "X", "name": "ReadAsync 251", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735494792954, "dur": 28, "ph": "X", "name": "ReadAsync 448", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735494792984, "dur": 1, "ph": "X", "name": "ProcessMessages 356", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735494792986, "dur": 34, "ph": "X", "name": "ReadAsync 356", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735494793023, "dur": 27, "ph": "X", "name": "ReadAsync 423", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735494793054, "dur": 31, "ph": "X", "name": "ReadAsync 238", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735494793087, "dur": 23, "ph": "X", "name": "ReadAsync 296", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735494793114, "dur": 59, "ph": "X", "name": "ReadAsync 237", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735494793174, "dur": 25, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735494793201, "dur": 25, "ph": "X", "name": "ReadAsync 323", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735494793229, "dur": 24, "ph": "X", "name": "ReadAsync 347", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735494793255, "dur": 66, "ph": "X", "name": "ReadAsync 258", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735494793323, "dur": 25, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735494793349, "dur": 23, "ph": "X", "name": "ReadAsync 412", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735494793374, "dur": 25, "ph": "X", "name": "ReadAsync 334", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735494793400, "dur": 59, "ph": "X", "name": "ReadAsync 240", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735494793464, "dur": 37, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735494793503, "dur": 27, "ph": "X", "name": "ReadAsync 430", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735494793532, "dur": 30, "ph": "X", "name": "ReadAsync 345", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735494793564, "dur": 39, "ph": "X", "name": "ReadAsync 225", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735494793607, "dur": 29, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735494793638, "dur": 20, "ph": "X", "name": "ReadAsync 383", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735494793661, "dur": 38, "ph": "X", "name": "ReadAsync 295", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735494793701, "dur": 23, "ph": "X", "name": "ReadAsync 121", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735494793726, "dur": 51, "ph": "X", "name": "ReadAsync 237", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735494793778, "dur": 29, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735494793809, "dur": 30, "ph": "X", "name": "ReadAsync 341", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735494793841, "dur": 23, "ph": "X", "name": "ReadAsync 558", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735494793865, "dur": 53, "ph": "X", "name": "ReadAsync 257", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735494793919, "dur": 26, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735494793948, "dur": 22, "ph": "X", "name": "ReadAsync 404", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735494793972, "dur": 21, "ph": "X", "name": "ReadAsync 360", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735494793994, "dur": 22, "ph": "X", "name": "ReadAsync 456", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735494794018, "dur": 56, "ph": "X", "name": "ReadAsync 63", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735494794074, "dur": 29, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735494794105, "dur": 22, "ph": "X", "name": "ReadAsync 427", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735494794129, "dur": 24, "ph": "X", "name": "ReadAsync 495", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735494794154, "dur": 59, "ph": "X", "name": "ReadAsync 315", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735494794214, "dur": 26, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735494794242, "dur": 23, "ph": "X", "name": "ReadAsync 315", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735494794267, "dur": 23, "ph": "X", "name": "ReadAsync 312", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735494794291, "dur": 18, "ph": "X", "name": "ReadAsync 331", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735494794310, "dur": 69, "ph": "X", "name": "ReadAsync 136", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735494794383, "dur": 55, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735494794441, "dur": 1, "ph": "X", "name": "ProcessMessages 452", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735494794443, "dur": 43, "ph": "X", "name": "ReadAsync 452", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735494794490, "dur": 1, "ph": "X", "name": "ProcessMessages 362", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735494794491, "dur": 35, "ph": "X", "name": "ReadAsync 362", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735494794529, "dur": 77, "ph": "X", "name": "ReadAsync 258", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735494794610, "dur": 42, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735494794655, "dur": 1, "ph": "X", "name": "ProcessMessages 452", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735494794656, "dur": 37, "ph": "X", "name": "ReadAsync 452", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735494794697, "dur": 36, "ph": "X", "name": "ReadAsync 434", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735494794737, "dur": 50, "ph": "X", "name": "ReadAsync 144", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735494794789, "dur": 30, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735494794822, "dur": 1, "ph": "X", "name": "ProcessMessages 253", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735494794824, "dur": 47, "ph": "X", "name": "ReadAsync 253", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735494794874, "dur": 32, "ph": "X", "name": "ReadAsync 492", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735494794908, "dur": 90, "ph": "X", "name": "ReadAsync 237", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735494795002, "dur": 41, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735494795046, "dur": 33, "ph": "X", "name": "ReadAsync 378", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735494795082, "dur": 1, "ph": "X", "name": "ProcessMessages 67", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735494795084, "dur": 30, "ph": "X", "name": "ReadAsync 67", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735494795115, "dur": 26, "ph": "X", "name": "ReadAsync 123", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735494795144, "dur": 21, "ph": "X", "name": "ReadAsync 350", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735494795167, "dur": 30, "ph": "X", "name": "ReadAsync 342", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735494795198, "dur": 33, "ph": "X", "name": "ReadAsync 204", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735494795233, "dur": 39, "ph": "X", "name": "ReadAsync 362", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735494795274, "dur": 1, "ph": "X", "name": "ProcessMessages 278", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735494795276, "dur": 36, "ph": "X", "name": "ReadAsync 278", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735494795315, "dur": 40, "ph": "X", "name": "ReadAsync 410", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735494795358, "dur": 1, "ph": "X", "name": "ProcessMessages 77", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735494795360, "dur": 34, "ph": "X", "name": "ReadAsync 77", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735494795399, "dur": 48, "ph": "X", "name": "ReadAsync 231", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735494795449, "dur": 30, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735494795480, "dur": 23, "ph": "X", "name": "ReadAsync 402", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735494795505, "dur": 18, "ph": "X", "name": "ReadAsync 184", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735494795525, "dur": 22, "ph": "X", "name": "ReadAsync 330", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735494795548, "dur": 20, "ph": "X", "name": "ReadAsync 251", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735494795569, "dur": 20, "ph": "X", "name": "ReadAsync 383", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735494795591, "dur": 20, "ph": "X", "name": "ReadAsync 326", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735494795612, "dur": 41, "ph": "X", "name": "ReadAsync 69", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735494795655, "dur": 32, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735494795689, "dur": 25, "ph": "X", "name": "ReadAsync 331", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735494795718, "dur": 28, "ph": "X", "name": "ReadAsync 239", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735494795748, "dur": 25, "ph": "X", "name": "ReadAsync 178", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735494795775, "dur": 57, "ph": "X", "name": "ReadAsync 62", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735494795833, "dur": 21, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735494795856, "dur": 24, "ph": "X", "name": "ReadAsync 221", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735494795882, "dur": 21, "ph": "X", "name": "ReadAsync 239", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735494795904, "dur": 21, "ph": "X", "name": "ReadAsync 307", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735494795927, "dur": 18, "ph": "X", "name": "ReadAsync 248", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735494795947, "dur": 21, "ph": "X", "name": "ReadAsync 265", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735494795969, "dur": 65, "ph": "X", "name": "ReadAsync 196", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735494796037, "dur": 36, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735494796076, "dur": 1, "ph": "X", "name": "ProcessMessages 310", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735494796078, "dur": 44, "ph": "X", "name": "ReadAsync 310", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735494796126, "dur": 38, "ph": "X", "name": "ReadAsync 498", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735494796168, "dur": 1, "ph": "X", "name": "ProcessMessages 637", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735494796170, "dur": 34, "ph": "X", "name": "ReadAsync 637", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735494796208, "dur": 30, "ph": "X", "name": "ReadAsync 270", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735494796240, "dur": 22, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735494796264, "dur": 36, "ph": "X", "name": "ReadAsync 293", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735494796301, "dur": 21, "ph": "X", "name": "ReadAsync 260", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735494796324, "dur": 19, "ph": "X", "name": "ReadAsync 300", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735494796345, "dur": 20, "ph": "X", "name": "ReadAsync 93", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735494796366, "dur": 69, "ph": "X", "name": "ReadAsync 144", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735494796436, "dur": 24, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735494796462, "dur": 21, "ph": "X", "name": "ReadAsync 321", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735494796484, "dur": 21, "ph": "X", "name": "ReadAsync 245", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735494796508, "dur": 21, "ph": "X", "name": "ReadAsync 311", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735494796531, "dur": 59, "ph": "X", "name": "ReadAsync 144", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735494796594, "dur": 39, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735494796638, "dur": 28, "ph": "X", "name": "ReadAsync 388", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735494796667, "dur": 1, "ph": "X", "name": "ProcessMessages 423", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735494796669, "dur": 22, "ph": "X", "name": "ReadAsync 423", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735494796693, "dur": 71, "ph": "X", "name": "ReadAsync 225", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735494796767, "dur": 30, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735494796800, "dur": 29, "ph": "X", "name": "ReadAsync 267", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735494796831, "dur": 22, "ph": "X", "name": "ReadAsync 599", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735494796856, "dur": 22, "ph": "X", "name": "ReadAsync 351", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735494796880, "dur": 30, "ph": "X", "name": "ReadAsync 255", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735494796912, "dur": 21, "ph": "X", "name": "ReadAsync 307", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735494796934, "dur": 23, "ph": "X", "name": "ReadAsync 475", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735494796959, "dur": 18, "ph": "X", "name": "ReadAsync 334", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735494796979, "dur": 26, "ph": "X", "name": "ReadAsync 183", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735494797006, "dur": 20, "ph": "X", "name": "ReadAsync 187", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735494797030, "dur": 42, "ph": "X", "name": "ReadAsync 53", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735494797073, "dur": 27, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735494797101, "dur": 23, "ph": "X", "name": "ReadAsync 291", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735494797126, "dur": 22, "ph": "X", "name": "ReadAsync 330", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735494797150, "dur": 25, "ph": "X", "name": "ReadAsync 399", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735494797176, "dur": 45, "ph": "X", "name": "ReadAsync 68", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735494797223, "dur": 26, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735494797250, "dur": 22, "ph": "X", "name": "ReadAsync 230", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735494797274, "dur": 22, "ph": "X", "name": "ReadAsync 410", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735494797298, "dur": 22, "ph": "X", "name": "ReadAsync 349", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735494797322, "dur": 52, "ph": "X", "name": "ReadAsync 134", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735494797375, "dur": 30, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735494797408, "dur": 1, "ph": "X", "name": "ProcessMessages 260", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735494797410, "dur": 32, "ph": "X", "name": "ReadAsync 260", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735494797444, "dur": 24, "ph": "X", "name": "ReadAsync 577", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735494797470, "dur": 20, "ph": "X", "name": "ReadAsync 252", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735494797491, "dur": 53, "ph": "X", "name": "ReadAsync 8", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735494797545, "dur": 26, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735494797573, "dur": 25, "ph": "X", "name": "ReadAsync 270", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735494797601, "dur": 21, "ph": "X", "name": "ReadAsync 423", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735494797624, "dur": 23, "ph": "X", "name": "ReadAsync 295", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735494797648, "dur": 52, "ph": "X", "name": "ReadAsync 57", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735494797702, "dur": 27, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735494797730, "dur": 29, "ph": "X", "name": "ReadAsync 233", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735494797762, "dur": 26, "ph": "X", "name": "ReadAsync 477", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735494797790, "dur": 67, "ph": "X", "name": "ReadAsync 360", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735494797859, "dur": 27, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735494797887, "dur": 22, "ph": "X", "name": "ReadAsync 280", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735494797911, "dur": 21, "ph": "X", "name": "ReadAsync 439", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735494797934, "dur": 24, "ph": "X", "name": "ReadAsync 327", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735494797961, "dur": 26, "ph": "X", "name": "ReadAsync 361", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735494797990, "dur": 22, "ph": "X", "name": "ReadAsync 327", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735494798013, "dur": 22, "ph": "X", "name": "ReadAsync 329", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735494798037, "dur": 22, "ph": "X", "name": "ReadAsync 389", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735494798060, "dur": 22, "ph": "X", "name": "ReadAsync 177", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735494798085, "dur": 63, "ph": "X", "name": "ReadAsync 237", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735494798149, "dur": 21, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735494798172, "dur": 20, "ph": "X", "name": "ReadAsync 351", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735494798194, "dur": 20, "ph": "X", "name": "ReadAsync 430", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735494798215, "dur": 26, "ph": "X", "name": "ReadAsync 366", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735494798243, "dur": 22, "ph": "X", "name": "ReadAsync 416", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735494798266, "dur": 20, "ph": "X", "name": "ReadAsync 406", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735494798288, "dur": 27, "ph": "X", "name": "ReadAsync 318", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735494798317, "dur": 24, "ph": "X", "name": "ReadAsync 365", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735494798344, "dur": 19, "ph": "X", "name": "ReadAsync 325", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735494798364, "dur": 66, "ph": "X", "name": "ReadAsync 240", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735494798432, "dur": 23, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735494798457, "dur": 26, "ph": "X", "name": "ReadAsync 287", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735494798484, "dur": 18, "ph": "X", "name": "ReadAsync 507", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735494798504, "dur": 21, "ph": "X", "name": "ReadAsync 343", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735494798528, "dur": 21, "ph": "X", "name": "ReadAsync 383", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735494798551, "dur": 21, "ph": "X", "name": "ReadAsync 317", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735494798574, "dur": 18, "ph": "X", "name": "ReadAsync 350", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735494798593, "dur": 20, "ph": "X", "name": "ReadAsync 340", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735494798615, "dur": 20, "ph": "X", "name": "ReadAsync 316", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735494798636, "dur": 22, "ph": "X", "name": "ReadAsync 98", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735494798660, "dur": 53, "ph": "X", "name": "ReadAsync 139", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735494798714, "dur": 23, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735494798739, "dur": 22, "ph": "X", "name": "ReadAsync 372", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735494798763, "dur": 19, "ph": "X", "name": "ReadAsync 336", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735494798784, "dur": 22, "ph": "X", "name": "ReadAsync 357", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735494798807, "dur": 56, "ph": "X", "name": "ReadAsync 146", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735494798866, "dur": 33, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735494798900, "dur": 27, "ph": "X", "name": "ReadAsync 401", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735494798929, "dur": 26, "ph": "X", "name": "ReadAsync 417", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735494798957, "dur": 31, "ph": "X", "name": "ReadAsync 302", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735494798990, "dur": 58, "ph": "X", "name": "ReadAsync 57", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735494799051, "dur": 34, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735494799086, "dur": 22, "ph": "X", "name": "ReadAsync 481", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735494799110, "dur": 26, "ph": "X", "name": "ReadAsync 228", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735494799137, "dur": 26, "ph": "X", "name": "ReadAsync 376", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735494799165, "dur": 62, "ph": "X", "name": "ReadAsync 141", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735494799229, "dur": 27, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735494799257, "dur": 26, "ph": "X", "name": "ReadAsync 296", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735494799285, "dur": 22, "ph": "X", "name": "ReadAsync 405", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735494799308, "dur": 24, "ph": "X", "name": "ReadAsync 338", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735494799336, "dur": 52, "ph": "X", "name": "ReadAsync 144", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735494799389, "dur": 22, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735494799413, "dur": 20, "ph": "X", "name": "ReadAsync 361", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735494799436, "dur": 1, "ph": "X", "name": "ProcessMessages 144", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735494799438, "dur": 29, "ph": "X", "name": "ReadAsync 144", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735494799469, "dur": 21, "ph": "X", "name": "ReadAsync 535", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735494799491, "dur": 50, "ph": "X", "name": "ReadAsync 62", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735494799542, "dur": 24, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735494799568, "dur": 22, "ph": "X", "name": "ReadAsync 333", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735494799591, "dur": 21, "ph": "X", "name": "ReadAsync 277", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735494799614, "dur": 22, "ph": "X", "name": "ReadAsync 418", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735494799638, "dur": 21, "ph": "X", "name": "ReadAsync 363", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735494799662, "dur": 22, "ph": "X", "name": "ReadAsync 328", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735494799685, "dur": 2, "ph": "X", "name": "ProcessMessages 285", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735494799687, "dur": 22, "ph": "X", "name": "ReadAsync 285", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735494799710, "dur": 22, "ph": "X", "name": "ReadAsync 320", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735494799735, "dur": 24, "ph": "X", "name": "ReadAsync 280", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735494799764, "dur": 34, "ph": "X", "name": "ReadAsync 170", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735494799802, "dur": 51, "ph": "X", "name": "ReadAsync 67", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735494799857, "dur": 40, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735494799899, "dur": 28, "ph": "X", "name": "ReadAsync 517", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735494799931, "dur": 36, "ph": "X", "name": "ReadAsync 400", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735494799971, "dur": 1, "ph": "X", "name": "ProcessMessages 240", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735494799973, "dur": 64, "ph": "X", "name": "ReadAsync 240", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735494800041, "dur": 35, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735494800078, "dur": 31, "ph": "X", "name": "ReadAsync 460", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735494800111, "dur": 1, "ph": "X", "name": "ProcessMessages 347", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735494800113, "dur": 31, "ph": "X", "name": "ReadAsync 347", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735494800145, "dur": 64, "ph": "X", "name": "ReadAsync 301", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735494800213, "dur": 41, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735494800257, "dur": 1, "ph": "X", "name": "ProcessMessages 337", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735494800259, "dur": 53, "ph": "X", "name": "ReadAsync 337", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735494800316, "dur": 1, "ph": "X", "name": "ProcessMessages 638", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735494800318, "dur": 45, "ph": "X", "name": "ReadAsync 638", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735494800366, "dur": 1, "ph": "X", "name": "ProcessMessages 146", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735494800368, "dur": 51, "ph": "X", "name": "ReadAsync 146", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735494800421, "dur": 34, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735494800458, "dur": 38, "ph": "X", "name": "ReadAsync 438", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735494800499, "dur": 1, "ph": "X", "name": "ProcessMessages 307", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735494800501, "dur": 40, "ph": "X", "name": "ReadAsync 307", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735494800544, "dur": 1, "ph": "X", "name": "ProcessMessages 363", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735494800545, "dur": 64, "ph": "X", "name": "ReadAsync 363", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735494800612, "dur": 46, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735494800660, "dur": 31, "ph": "X", "name": "ReadAsync 251", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735494800696, "dur": 32, "ph": "X", "name": "ReadAsync 142", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735494800731, "dur": 28, "ph": "X", "name": "ReadAsync 364", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735494800762, "dur": 41, "ph": "X", "name": "ReadAsync 104", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735494800805, "dur": 1, "ph": "X", "name": "ProcessMessages 371", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735494800807, "dur": 43, "ph": "X", "name": "ReadAsync 371", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735494800853, "dur": 1, "ph": "X", "name": "ProcessMessages 397", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735494800854, "dur": 85, "ph": "X", "name": "ReadAsync 397", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735494800942, "dur": 1, "ph": "X", "name": "ProcessMessages 333", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735494800944, "dur": 35, "ph": "X", "name": "ReadAsync 333", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735494800982, "dur": 31, "ph": "X", "name": "ReadAsync 320", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735494801014, "dur": 66, "ph": "X", "name": "ReadAsync 237", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735494801085, "dur": 52, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735494801141, "dur": 1, "ph": "X", "name": "ProcessMessages 365", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735494801143, "dur": 47, "ph": "X", "name": "ReadAsync 365", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735494801194, "dur": 1, "ph": "X", "name": "ProcessMessages 529", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735494801196, "dur": 37, "ph": "X", "name": "ReadAsync 529", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735494801235, "dur": 1, "ph": "X", "name": "ProcessMessages 715", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735494801236, "dur": 32, "ph": "X", "name": "ReadAsync 715", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735494801271, "dur": 35, "ph": "X", "name": "ReadAsync 271", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735494801308, "dur": 31, "ph": "X", "name": "ReadAsync 366", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735494801342, "dur": 1, "ph": "X", "name": "ProcessMessages 289", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735494801344, "dur": 29, "ph": "X", "name": "ReadAsync 289", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735494801375, "dur": 67, "ph": "X", "name": "ReadAsync 240", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735494801444, "dur": 25, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735494801472, "dur": 21, "ph": "X", "name": "ReadAsync 310", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735494801496, "dur": 21, "ph": "X", "name": "ReadAsync 227", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735494801519, "dur": 22, "ph": "X", "name": "ReadAsync 295", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735494801543, "dur": 22, "ph": "X", "name": "ReadAsync 287", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735494801566, "dur": 20, "ph": "X", "name": "ReadAsync 253", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735494801588, "dur": 20, "ph": "X", "name": "ReadAsync 222", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735494801610, "dur": 27, "ph": "X", "name": "ReadAsync 225", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735494801641, "dur": 35, "ph": "X", "name": "ReadAsync 262", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735494801678, "dur": 17, "ph": "X", "name": "ReadAsync 312", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735494801697, "dur": 91, "ph": "X", "name": "ReadAsync 68", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735494801791, "dur": 20, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735494801813, "dur": 20, "ph": "X", "name": "ReadAsync 177", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735494801835, "dur": 23, "ph": "X", "name": "ReadAsync 272", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735494801859, "dur": 31, "ph": "X", "name": "ReadAsync 339", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735494801892, "dur": 25, "ph": "X", "name": "ReadAsync 256", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735494801918, "dur": 87, "ph": "X", "name": "ReadAsync 62", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735494802009, "dur": 33, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735494802045, "dur": 35, "ph": "X", "name": "ReadAsync 556", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735494802083, "dur": 1, "ph": "X", "name": "ProcessMessages 353", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735494802085, "dur": 27, "ph": "X", "name": "ReadAsync 353", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735494802114, "dur": 78, "ph": "X", "name": "ReadAsync 322", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735494802197, "dur": 38, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735494802238, "dur": 1, "ph": "X", "name": "ProcessMessages 697", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735494802239, "dur": 28, "ph": "X", "name": "ReadAsync 697", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735494802269, "dur": 19, "ph": "X", "name": "ReadAsync 432", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735494802289, "dur": 75, "ph": "X", "name": "ReadAsync 57", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735494802365, "dur": 27, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735494802394, "dur": 35, "ph": "X", "name": "ReadAsync 260", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735494802431, "dur": 22, "ph": "X", "name": "ReadAsync 418", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735494802455, "dur": 23, "ph": "X", "name": "ReadAsync 369", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735494802480, "dur": 28, "ph": "X", "name": "ReadAsync 392", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735494802510, "dur": 1, "ph": "X", "name": "ProcessMessages 338", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735494802512, "dur": 51, "ph": "X", "name": "ReadAsync 338", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735494802564, "dur": 24, "ph": "X", "name": "ReadAsync 479", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735494802591, "dur": 23, "ph": "X", "name": "ReadAsync 336", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735494802616, "dur": 20, "ph": "X", "name": "ReadAsync 336", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735494802638, "dur": 20, "ph": "X", "name": "ReadAsync 171", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735494802659, "dur": 65, "ph": "X", "name": "ReadAsync 66", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735494802727, "dur": 33, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735494802763, "dur": 22, "ph": "X", "name": "ReadAsync 466", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735494802787, "dur": 23, "ph": "X", "name": "ReadAsync 209", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735494802811, "dur": 15, "ph": "X", "name": "ReadAsync 366", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735494802828, "dur": 20, "ph": "X", "name": "ReadAsync 382", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735494802849, "dur": 20, "ph": "X", "name": "ReadAsync 215", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735494802870, "dur": 19, "ph": "X", "name": "ReadAsync 292", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735494802891, "dur": 19, "ph": "X", "name": "ReadAsync 295", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735494802911, "dur": 21, "ph": "X", "name": "ReadAsync 338", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735494802934, "dur": 19, "ph": "X", "name": "ReadAsync 322", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735494802956, "dur": 20, "ph": "X", "name": "ReadAsync 40", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735494802978, "dur": 78, "ph": "X", "name": "ReadAsync 200", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735494803057, "dur": 23, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735494803082, "dur": 28, "ph": "X", "name": "ReadAsync 240", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735494803112, "dur": 23, "ph": "X", "name": "ReadAsync 313", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735494803136, "dur": 21, "ph": "X", "name": "ReadAsync 381", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735494803159, "dur": 21, "ph": "X", "name": "ReadAsync 375", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735494803182, "dur": 22, "ph": "X", "name": "ReadAsync 373", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735494803206, "dur": 22, "ph": "X", "name": "ReadAsync 301", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735494803230, "dur": 26, "ph": "X", "name": "ReadAsync 336", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735494803258, "dur": 19, "ph": "X", "name": "ReadAsync 433", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735494803278, "dur": 21, "ph": "X", "name": "ReadAsync 201", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735494803304, "dur": 26, "ph": "X", "name": "ReadAsync 216", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735494803331, "dur": 44, "ph": "X", "name": "ReadAsync 21", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735494803377, "dur": 23, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735494803403, "dur": 22, "ph": "X", "name": "ReadAsync 269", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735494803427, "dur": 21, "ph": "X", "name": "ReadAsync 341", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735494803450, "dur": 21, "ph": "X", "name": "ReadAsync 257", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735494803473, "dur": 34, "ph": "X", "name": "ReadAsync 371", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735494803510, "dur": 1, "ph": "X", "name": "ProcessMessages 181", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735494803512, "dur": 35, "ph": "X", "name": "ReadAsync 181", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735494803549, "dur": 22, "ph": "X", "name": "ReadAsync 485", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735494803573, "dur": 24, "ph": "X", "name": "ReadAsync 287", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735494803599, "dur": 22, "ph": "X", "name": "ReadAsync 271", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735494803623, "dur": 18, "ph": "X", "name": "ReadAsync 170", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735494803643, "dur": 61, "ph": "X", "name": "ReadAsync 67", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735494803705, "dur": 25, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735494803731, "dur": 20, "ph": "X", "name": "ReadAsync 221", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735494803753, "dur": 22, "ph": "X", "name": "ReadAsync 397", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735494803776, "dur": 16, "ph": "X", "name": "ReadAsync 346", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735494803794, "dur": 57, "ph": "X", "name": "ReadAsync 144", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735494803853, "dur": 21, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735494803876, "dur": 21, "ph": "X", "name": "ReadAsync 263", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735494803898, "dur": 22, "ph": "X", "name": "ReadAsync 203", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735494803921, "dur": 24, "ph": "X", "name": "ReadAsync 379", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735494803947, "dur": 50, "ph": "X", "name": "ReadAsync 419", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735494804000, "dur": 21, "ph": "X", "name": "ReadAsync 266", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735494804023, "dur": 24, "ph": "X", "name": "ReadAsync 212", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735494804048, "dur": 23, "ph": "X", "name": "ReadAsync 383", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735494804073, "dur": 28, "ph": "X", "name": "ReadAsync 328", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735494804102, "dur": 21, "ph": "X", "name": "ReadAsync 383", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735494804125, "dur": 16, "ph": "X", "name": "ReadAsync 200", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735494804143, "dur": 51, "ph": "X", "name": "ReadAsync 57", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735494804197, "dur": 32, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735494804232, "dur": 21, "ph": "X", "name": "ReadAsync 436", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735494804255, "dur": 25, "ph": "X", "name": "ReadAsync 211", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735494804282, "dur": 22, "ph": "X", "name": "ReadAsync 351", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735494804306, "dur": 22, "ph": "X", "name": "ReadAsync 466", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735494804329, "dur": 22, "ph": "X", "name": "ReadAsync 340", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735494804353, "dur": 23, "ph": "X", "name": "ReadAsync 297", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735494804377, "dur": 20, "ph": "X", "name": "ReadAsync 441", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735494804398, "dur": 27, "ph": "X", "name": "ReadAsync 183", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735494804426, "dur": 23, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735494804451, "dur": 77, "ph": "X", "name": "ReadAsync 225", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735494804530, "dur": 27, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735494804558, "dur": 23, "ph": "X", "name": "ReadAsync 234", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735494804583, "dur": 31, "ph": "X", "name": "ReadAsync 337", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735494804616, "dur": 28, "ph": "X", "name": "ReadAsync 318", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735494804646, "dur": 27, "ph": "X", "name": "ReadAsync 490", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735494804674, "dur": 23, "ph": "X", "name": "ReadAsync 402", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735494804699, "dur": 18, "ph": "X", "name": "ReadAsync 340", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735494804719, "dur": 22, "ph": "X", "name": "ReadAsync 265", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735494804742, "dur": 17, "ph": "X", "name": "ReadAsync 173", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735494804761, "dur": 53, "ph": "X", "name": "ReadAsync 67", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735494804818, "dur": 30, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735494804850, "dur": 27, "ph": "X", "name": "ReadAsync 379", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735494804879, "dur": 23, "ph": "X", "name": "ReadAsync 293", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735494804904, "dur": 21, "ph": "X", "name": "ReadAsync 317", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735494804926, "dur": 22, "ph": "X", "name": "ReadAsync 344", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735494804950, "dur": 28, "ph": "X", "name": "ReadAsync 195", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735494804981, "dur": 1, "ph": "X", "name": "ProcessMessages 258", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735494804982, "dur": 30, "ph": "X", "name": "ReadAsync 258", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735494805014, "dur": 30, "ph": "X", "name": "ReadAsync 564", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735494805046, "dur": 25, "ph": "X", "name": "ReadAsync 171", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735494805072, "dur": 22, "ph": "X", "name": "ReadAsync 229", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735494805097, "dur": 37, "ph": "X", "name": "ReadAsync 8", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735494805135, "dur": 15, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735494805152, "dur": 120, "ph": "X", "name": "ReadAsync 224", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735494805277, "dur": 40, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735494805319, "dur": 373, "ph": "X", "name": "ProcessMessages 96", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735494805694, "dur": 86, "ph": "X", "name": "ReadAsync 96", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735494805782, "dur": 2, "ph": "X", "name": "ProcessMessages 676", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735494805785, "dur": 269, "ph": "X", "name": "ReadAsync 676", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735494806059, "dur": 36, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735494806097, "dur": 263, "ph": "X", "name": "ProcessMessages 13", "args": {}}, {"pid": 3100, "tid": 12884901888, "ts": 1754735494806362, "dur": 5627, "ph": "X", "name": "ReadAsync 13", "args": {}}, {"pid": 3100, "tid": 24787, "ts": 1754735496217842, "dur": 635, "ph": "X", "name": "ReadEntireBinlogFromIpcAsync", "args": {}}, {"pid": 3100, "tid": 8589934592, "ph": "M", "name": "thread_name", "args": {"name": "WaitForBuildProgramInputDataBeingWrittenAndSendDagReadyMessageAsync"}}, {"pid": 3100, "tid": 8589934592, "ts": 1754735494595045, "dur": 138299, "ph": "X", "name": "await writeBuildProgramInputDataTask", "args": {}}, {"pid": 3100, "tid": 8589934592, "ts": 1754735494733346, "dur": 43658, "ph": "X", "name": "WritePipe.WaitConnectionAsync", "args": {}}, {"pid": 3100, "tid": 8589934592, "ts": 1754735494777008, "dur": 2100, "ph": "X", "name": "WriteDagReadyMessage", "args": {}}, {"pid": 3100, "tid": 24787, "ts": 1754735496218479, "dur": 2, "ph": "X", "name": "WaitForBuildProgramInputDataBeingWrittenAndSendDagReadyMessageAsync", "args": {}}, {"pid": 3100, "tid": 4294967296, "ph": "M", "name": "thread_name", "args": {"name": "BuildAsync"}}, {"pid": 3100, "tid": 4294967296, "ts": 1754735494420821, "dur": 392643, "ph": "X", "name": "RunBackend", "args": {}}, {"pid": 3100, "tid": 4294967296, "ts": 1754735494423425, "dur": 167276, "ph": "X", "name": "BackendProgram.Start", "args": {}}, {"pid": 3100, "tid": 4294967296, "ts": 1754735494813658, "dur": 640208, "ph": "X", "name": "await ExecuteBuildProgram", "args": {}}, {"pid": 3100, "tid": 4294967296, "ts": 1754735495454110, "dur": 756075, "ph": "X", "name": "RunBackend", "args": {}}, {"pid": 3100, "tid": 4294967296, "ts": 1754735495454233, "dur": 115551, "ph": "X", "name": "BackendProgram.Start", "args": {}}, {"pid": 3100, "tid": 4294967296, "ts": 1754735496210194, "dur": 2727, "ph": "X", "name": "await WaitForAndApplyScriptUpdaters", "args": {}}, {"pid": 3100, "tid": 4294967296, "ts": 1754735496212017, "dur": 27, "ph": "X", "name": "await <PERSON><PERSON>tUp<PERSON><PERSON>", "args": {}}, {"pid": 3100, "tid": 4294967296, "ts": 1754735496212925, "dur": 10, "ph": "X", "name": "await taskToReadBuildProgramOutput", "args": {}}, {"pid": 3100, "tid": 24787, "ts": 1754735496218482, "dur": 12, "ph": "X", "name": "BuildAsync", "args": {}}, {"cat": "", "pid": 12345, "tid": 0, "ts": 0, "ph": "M", "name": "process_name", "args": {"name": "bee_backend"}}, {"pid": 12345, "tid": 0, "ts": 1754735495706457, "dur": 98625, "ph": "X", "name": "DriverInitData", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1754735495805095, "dur": 279, "ph": "X", "name": "RemoveStaleOutputs", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1754735495805470, "dur": 52, "ph": "X", "name": "<PERSON><PERSON>", "args": {"detail": "PrepareNodes"}}, {"pid": 12345, "tid": 0, "ts": 1754735495805522, "dur": 488, "ph": "X", "name": "BuildQueueInit", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1754735495807225, "dur": 153, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.InputLegacyModule.dll_93739C87338A598F.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1754735495811256, "dur": 318, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipeline.Universal.ShaderLibrary.dll.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1754735495811870, "dur": 69, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Core.Editor.rsp"}}, {"pid": 12345, "tid": 0, "ts": 1754735495812233, "dur": 79, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Searcher.Editor.rsp"}}, {"pid": 12345, "tid": 0, "ts": 1754735495823539, "dur": 81, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.RenderPipelines.Universal.Editor.pdb"}}, {"pid": 12345, "tid": 0, "ts": 1754735495806035, "dur": 20154, "ph": "X", "name": "EnqueueRequestedNodes", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1754735495826203, "dur": 375490, "ph": "X", "name": "WaitForBuildFinished", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1754735496201695, "dur": 95, "ph": "X", "name": "JoinBuildThread", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1754735496201790, "dur": 113, "ph": "X", "name": "ThreadStateDestroy", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1754735496202865, "dur": 63, "ph": "X", "name": "BuildQueueDestroyTail", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1754735496202945, "dur": 1051, "ph": "X", "name": "<PERSON><PERSON>", "args": {"detail": "Write AllBuiltNodes"}}, {"pid": 12345, "tid": 1, "ts": 1754735495806334, "dur": 19882, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1754735495826223, "dur": 3220, "ph": "X", "name": "CheckDagSignatures", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1754735495829443, "dur": 313, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1754735495829757, "dur": 674, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1754735495830431, "dur": 217, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1754735495830648, "dur": 166, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1754735495830814, "dur": 196, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1754735495831010, "dur": 163, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1754735495831173, "dur": 391, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1754735495831564, "dur": 538, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1754735495832103, "dur": 163, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1754735495832266, "dur": 230, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1754735495832496, "dur": 220, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1754735495832716, "dur": 152, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1754735495832868, "dur": 342, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1754735495833236, "dur": 69, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1754735495833305, "dur": 243, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1754735495833548, "dur": 322, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1754735495833871, "dur": 100, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.Mathematics.dll.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1754735495834086, "dur": 101, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\GameSoft\\Unity\\Editor\\2022.3.38f1c1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.UnityTestProtocolModule.dll"}}, {"pid": 12345, "tid": 1, "ts": 1754735495834003, "dur": 526, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Mathematics.dll (+2 others)"}}, {"pid": 12345, "tid": 1, "ts": 1754735495834591, "dur": 92, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.Collections.dll.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1754735495834986, "dur": 139, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.collections@1.2.4\\Unity.Collections\\DebugView.cs"}}, {"pid": 12345, "tid": 1, "ts": 1754735495834701, "dur": 486, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Collections.dll (+2 others)"}}, {"pid": 12345, "tid": 1, "ts": 1754735495835276, "dur": 121, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1754735495835412, "dur": 100, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/CrashKonijn.Goap.Resolver.dll.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1754735495835562, "dur": 282, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/CrashKonijn.Goap.Resolver.dll (+2 others)"}}, {"pid": 12345, "tid": 1, "ts": 1754735495835884, "dur": 407, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/CrashKonijn.Goap.Runtime.dll (+2 others)"}}, {"pid": 12345, "tid": 1, "ts": 1754735495836348, "dur": 361, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/CrashKonijn.Goap.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 1, "ts": 1754735495836736, "dur": 267, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/com.crashkonijn.goap.demos.simple.dll (+2 others)"}}, {"pid": 12345, "tid": 1, "ts": 1754735495837034, "dur": 318960, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1754735496156010, "dur": 1823, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.RenderPipelines.Universal.Shaders.dll (+pdb)"}}, {"pid": 12345, "tid": 1, "ts": 1754735496157878, "dur": 78, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\Bee\\artifacts\\1900b0aE.dag\\Unity.Burst.dll"}}, {"pid": 12345, "tid": 1, "ts": 1754735496157877, "dur": 1646, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Burst.dll (+pdb)"}}, {"pid": 12345, "tid": 1, "ts": 1754735496160180, "dur": 240, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\GameSoft\\Unity\\Editor\\2022.3.38f1c1\\Editor\\Data\\Tools\\ilpp\\Unity.ILPP.Runner\\Microsoft.Extensions.Configuration.Xml.dll"}}, {"pid": 12345, "tid": 1, "ts": 1754735496159562, "dur": 1641, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.TextMeshPro.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 1, "ts": 1754735496161203, "dur": 238, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1754735496161446, "dur": 1342, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.ShaderGraph.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 1, "ts": 1754735496162825, "dur": 38897, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1754735495806336, "dur": 19891, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1754735495826586, "dur": 80, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.PropertiesModule.dll_370C590F53957EFB.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1754735495826668, "dur": 80, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\GameSoft\\Unity\\Editor\\2022.3.38f1c1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.UnityWebRequestTextureModule.dll"}}, {"pid": 12345, "tid": 2, "ts": 1754735495826667, "dur": 83, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityWebRequestTextureModule.dll_3AB3AEE512D0C853.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1754735495826902, "dur": 57, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Core.rsp"}}, {"pid": 12345, "tid": 2, "ts": 1754735495827056, "dur": 128, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Searcher.Editor.rsp2"}}, {"pid": 12345, "tid": 2, "ts": 1754735495827314, "dur": 137, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.PlasticSCM.Editor.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 2, "ts": 1754735495827530, "dur": 102, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/GlassSystem.rsp2"}}, {"pid": 12345, "tid": 2, "ts": 1754735495827903, "dur": 139, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1754735495828042, "dur": 71, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/17766336155681823506.rsp"}}, {"pid": 12345, "tid": 2, "ts": 1754735495828114, "dur": 213, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1754735495828327, "dur": 160, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1754735495828487, "dur": 175, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1754735495828662, "dur": 471, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1754735495829133, "dur": 522, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1754735495829655, "dur": 167, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1754735495829822, "dur": 620, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1754735495830442, "dur": 180, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1754735495830622, "dur": 287, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1754735495830910, "dur": 181, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1754735495831091, "dur": 155, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1754735495831246, "dur": 208, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1754735495831454, "dur": 160, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1754735495831614, "dur": 204, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1754735495831818, "dur": 294, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1754735495832113, "dur": 170, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1754735495832283, "dur": 236, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1754735495832519, "dur": 216, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1754735495832735, "dur": 240, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1754735495832976, "dur": 248, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1754735495833304, "dur": 216, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1754735495833521, "dur": 353, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1754735495833878, "dur": 66, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.Searcher.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1754735495833975, "dur": 363, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Searcher.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 2, "ts": 1754735495834339, "dur": 216, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1754735495834600, "dur": 96, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.Timeline.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1754735495834707, "dur": 719, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Timeline.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 2, "ts": 1754735495835488, "dur": 298, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1754735495835787, "dur": 58, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Core.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1754735495835862, "dur": 309, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Core.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 2, "ts": 1754735495836274, "dur": 815, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.ShaderGraph.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 2, "ts": 1754735495837180, "dur": 334, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Universal.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 2, "ts": 1754735495837557, "dur": 55, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/com.unity.cinemachine.editor.dll.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1754735495837623, "dur": 206, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/com.unity.cinemachine.editor.dll (+2 others)"}}, {"pid": 12345, "tid": 2, "ts": 1754735495837847, "dur": 318124, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1754735496155986, "dur": 1803, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.VisualScripting.State.dll (+pdb)"}}, {"pid": 12345, "tid": 2, "ts": 1754735496157816, "dur": 1323, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Collections.dll (+pdb)"}}, {"pid": 12345, "tid": 2, "ts": 1754735496160181, "dur": 211, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\GameSoft\\Unity\\Editor\\2022.3.38f1c1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.Physics2DModule.dll"}}, {"pid": 12345, "tid": 2, "ts": 1754735496159169, "dur": 1631, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.RenderPipelines.Core.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 2, "ts": 1754735496160801, "dur": 98, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1754735496161370, "dur": 746, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\GameSoft\\Unity\\Editor\\2022.3.38f1c1\\Editor\\Data\\Tools\\ilpp\\Unity.ILPP.Runner\\api-ms-win-core-debug-l1-1-0.dll"}}, {"pid": 12345, "tid": 2, "ts": 1754735496160907, "dur": 1955, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Collections.DocCodeSamples.dll (+pdb)"}}, {"pid": 12345, "tid": 2, "ts": 1754735496162893, "dur": 38807, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1754735495806536, "dur": 19760, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1754735495826506, "dur": 172, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.GameCenterModule.dll_09D119D0E0AA1ABF.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1754735495826682, "dur": 83, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1754735495826952, "dur": 74, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/CrashKonijn.Agent.Core.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 3, "ts": 1754735495827028, "dur": 55, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Core.Editor.rsp"}}, {"pid": 12345, "tid": 3, "ts": 1754735495827274, "dur": 111, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Mathematics.Editor.rsp"}}, {"pid": 12345, "tid": 3, "ts": 1754735495827421, "dur": 62, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Cinemachine.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 3, "ts": 1754735495827516, "dur": 79, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.TextMeshPro.Editor.rsp"}}, {"pid": 12345, "tid": 3, "ts": 1754735495827659, "dur": 77, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Assembly-CSharp.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 3, "ts": 1754735495827892, "dur": 151, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1754735495828111, "dur": 215, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1754735495828326, "dur": 147, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1754735495828473, "dur": 175, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1754735495828648, "dur": 162, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1754735495828810, "dur": 150, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1754735495828960, "dur": 131, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1754735495829091, "dur": 205, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1754735495829296, "dur": 141, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1754735495829437, "dur": 171, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1754735495829608, "dur": 148, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1754735495829757, "dur": 506, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1754735495830263, "dur": 186, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1754735495830449, "dur": 250, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1754735495830699, "dur": 353, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1754735495831052, "dur": 409, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1754735495831461, "dur": 152, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1754735495831613, "dur": 181, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1754735495831794, "dur": 268, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1754735495832062, "dur": 236, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1754735495832298, "dur": 144, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1754735495832443, "dur": 251, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1754735495832694, "dur": 162, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1754735495832856, "dur": 162, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1754735495833019, "dur": 224, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1754735495833263, "dur": 103, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1754735495833366, "dur": 192, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1754735495833559, "dur": 353, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1754735495833913, "dur": 63, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.VisualStudio.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1754735495834012, "dur": 97, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.ShaderGraph.ShaderGraphLibrary.dll.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1754735495834122, "dur": 314, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.ShaderGraph.ShaderGraphLibrary.dll (+2 others)"}}, {"pid": 12345, "tid": 3, "ts": 1754735495834464, "dur": 63, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.ShaderGraph.ShaderGraphLibrary.dll (+2 others)"}}, {"pid": 12345, "tid": 3, "ts": 1754735495834588, "dur": 101, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.Burst.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1754735495834690, "dur": 90, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1754735495834786, "dur": 377, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Burst.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 3, "ts": 1754735495835265, "dur": 149, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1754735495835415, "dur": 98, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.Collections.DocCodeSamples.dll.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1754735495835538, "dur": 244, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Collections.DocCodeSamples.dll (+2 others)"}}, {"pid": 12345, "tid": 3, "ts": 1754735495835812, "dur": 542, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1754735495836361, "dur": 312, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/com.crashkonijn.goap.demos.complex.dll (+2 others)"}}, {"pid": 12345, "tid": 3, "ts": 1754735495836701, "dur": 319250, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1754735496155952, "dur": 1546, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.RenderPipelines.Universal.Config.Runtime.dll (+pdb)"}}, {"pid": 12345, "tid": 3, "ts": 1754735496157498, "dur": 57, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1754735496157561, "dur": 1433, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.VisualScripting.Flow.dll (+pdb)"}}, {"pid": 12345, "tid": 3, "ts": 1754735496158995, "dur": 199, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1754735496160181, "dur": 259, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\Bee\\artifacts\\1900b0aE.dag\\UnityEngine.TestRunner.dll"}}, {"pid": 12345, "tid": 3, "ts": 1754735496159199, "dur": 1713, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.RenderPipelines.Universal.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 3, "ts": 1754735496160913, "dur": 225, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1754735496161150, "dur": 1559, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.VisualScripting.Shared.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 3, "ts": 1754735496162780, "dur": 38915, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1754735495806388, "dur": 19869, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1754735495826263, "dur": 58, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.UIElementsSamplesModule.dll_623E7535F4BD3DF5.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1754735495826665, "dur": 80, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1754735495826793, "dur": 67, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/UnityEditor.UI.rsp2"}}, {"pid": 12345, "tid": 4, "ts": 1754735495826904, "dur": 84, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.Burst.Cecil.dll_778247FFEF05A9EE.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1754735495826997, "dur": 106, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/CrashKonijn.Goap.Core.rsp2"}}, {"pid": 12345, "tid": 4, "ts": 1754735495827146, "dur": 67, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Searcher.Editor.rsp"}}, {"pid": 12345, "tid": 4, "ts": 1754735495827332, "dur": 148, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VSCode.Editor.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 4, "ts": 1754735495827566, "dur": 126, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/com.unity.cinemachine.editor.rsp2"}}, {"pid": 12345, "tid": 4, "ts": 1754735495827906, "dur": 162, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1754735495828132, "dur": 144, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1754735495828276, "dur": 181, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1754735495828457, "dur": 277, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1754735495828735, "dur": 165, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1754735495828900, "dur": 505, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1754735495829405, "dur": 869, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1754735495830274, "dur": 198, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1754735495830472, "dur": 173, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1754735495830645, "dur": 285, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1754735495830930, "dur": 172, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1754735495831102, "dur": 186, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1754735495831289, "dur": 196, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1754735495831486, "dur": 194, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1754735495831681, "dur": 161, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1754735495831842, "dur": 168, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1754735495832010, "dur": 218, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1754735495832229, "dur": 160, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1754735495832390, "dur": 222, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1754735495832612, "dur": 203, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1754735495832816, "dur": 196, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1754735495833012, "dur": 349, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1754735495833361, "dur": 192, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1754735495833554, "dur": 389, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1754735495833944, "dur": 140, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.Rider.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1754735495834110, "dur": 379, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Rider.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 4, "ts": 1754735495834599, "dur": 105, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/CrashKonijn.Goap.Runtime.dll.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1754735495834721, "dur": 211, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1754735495834932, "dur": 225, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1754735495835157, "dur": 92, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1754735495835249, "dur": 191, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1754735495835441, "dur": 348, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1754735495835790, "dur": 565, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1754735495836355, "dur": 360, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1754735495836715, "dur": 319229, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1754735496155946, "dur": 1540, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/CrashKonijn.Goap.Resolver.dll (+pdb)"}}, {"pid": 12345, "tid": 4, "ts": 1754735496157487, "dur": 54, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1754735496159018, "dur": 119, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\GameSoft\\Unity\\Editor\\2022.3.38f1c1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\System.Xml.Linq.dll"}}, {"pid": 12345, "tid": 4, "ts": 1754735496157552, "dur": 1616, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/com.crashkonijn.goap.demos.turn-based.dll (+pdb)"}}, {"pid": 12345, "tid": 4, "ts": 1754735496160181, "dur": 211, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\GameSoft\\Unity\\Editor\\2022.3.38f1c1\\Editor\\Data\\Managed\\UnityEngine\\UnityEditor.UIElementsSamplesModule.dll"}}, {"pid": 12345, "tid": 4, "ts": 1754735496159221, "dur": 1651, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.PlasticSCM.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 4, "ts": 1754735496160873, "dur": 53, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1754735496160933, "dur": 1517, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Timeline.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 4, "ts": 1754735496162534, "dur": 39285, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1754735495806373, "dur": 19873, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1754735495826338, "dur": 68, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.VirtualTexturingModule.dll_38C1DD3B59418845.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1754735495826408, "dur": 51, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\GameSoft\\Unity\\Editor\\2022.3.38f1c1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.AssetBundleModule.dll"}}, {"pid": 12345, "tid": 5, "ts": 1754735495826408, "dur": 55, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.AssetBundleModule.dll_022E81689B98A39E.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1754735495826506, "dur": 119, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.DSPGraphModule.dll_4190EB41F081C965.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1754735495826658, "dur": 69, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1754735495826814, "dur": 84, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Burst.rsp2"}}, {"pid": 12345, "tid": 5, "ts": 1754735495826917, "dur": 129, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Burst.CodeGen.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 5, "ts": 1754735495827076, "dur": 68, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Core.Editor.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 5, "ts": 1754735495827312, "dur": 101, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Universal.Editor.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 5, "ts": 1754735495827454, "dur": 51, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Core.ShaderLibrary.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 5, "ts": 1754735495827565, "dur": 114, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/com.unity.cinemachine.editor.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 5, "ts": 1754735495827906, "dur": 193, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1754735495828106, "dur": 210, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1754735495828316, "dur": 210, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1754735495828527, "dur": 203, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1754735495828730, "dur": 214, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1754735495828944, "dur": 313, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1754735495829258, "dur": 162, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1754735495829581, "dur": 593, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.timeline@1.7.6\\Editor\\Manipulators\\Trim\\TrimItemModeMix.cs"}}, {"pid": 12345, "tid": 5, "ts": 1754735495829424, "dur": 808, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1754735495830232, "dur": 423, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1754735495830655, "dur": 233, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1754735495830888, "dur": 193, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1754735495831081, "dur": 173, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1754735495831254, "dur": 419, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1754735495831673, "dur": 197, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1754735495831870, "dur": 614, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1754735495832485, "dur": 165, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1754735495832650, "dur": 194, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1754735495832844, "dur": 201, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1754735495833083, "dur": 240, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1754735495833323, "dur": 196, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1754735495833566, "dur": 311, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1754735495833879, "dur": 161, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/CrashKonijn.Agent.Runtime.dll.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1754735495834041, "dur": 58, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1754735495834102, "dur": 407, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/com.crashkonijn.docs.getting_started.dll.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1754735495834539, "dur": 116, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/GlassSystem.dll.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1754735495834901, "dur": 61, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\GameSoft\\Unity\\Editor\\2022.3.38f1c1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Security.Cryptography.Csp.dll"}}, {"pid": 12345, "tid": 5, "ts": 1754735495834671, "dur": 434, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/GlassSystem.dll (+2 others)"}}, {"pid": 12345, "tid": 5, "ts": 1754735495835152, "dur": 88, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1754735495835241, "dur": 200, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1754735495835441, "dur": 352, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1754735495835793, "dur": 563, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1754735495836356, "dur": 704, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1754735495837061, "dur": 52, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Flow.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1754735495837124, "dur": 296, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Flow.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 5, "ts": 1754735495837507, "dur": 207, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.State.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 5, "ts": 1754735495837806, "dur": 140, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Shared.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 5, "ts": 1754735495837980, "dur": 61, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Assembly-CSharp.dll.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1754735495838205, "dur": 211, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\Bee\\artifacts\\1900b0aE.dag\\Assembly-CSharp.rsp"}}, {"pid": 12345, "tid": 5, "ts": 1754735495838051, "dur": 366, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Assembly-CSharp.dll (+2 others)"}}, {"pid": 12345, "tid": 5, "ts": 1754735495838739, "dur": 361555, "ph": "X", "name": "Csc", "args": {"detail": "Library/Bee/artifacts/1900b0aE.dag/Assembly-CSharp.dll (+2 others)"}}, {"pid": 12345, "tid": 6, "ts": 1754735495806492, "dur": 19777, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1754735495826508, "dur": 70, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.GridModule.dll_B76D6751422DDE5E.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1754735495826598, "dur": 111, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.SharedInternalsModule.dll_C8666D03B273DA0D.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1754735495826711, "dur": 90, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.visualscripting@1.9.4\\Runtime\\VisualScripting.Flow\\Dependencies\\NCalc\\Unity.VisualScripting.Antlr3.Runtime.dll"}}, {"pid": 12345, "tid": 6, "ts": 1754735495826710, "dur": 93, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.VisualScripting.Antlr3.Runtime.dll_FBC62C826EB2B4B0.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1754735495826839, "dur": 62, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.Burst.Unsafe.dll_12BA7C2C14719C45.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1754735495827152, "dur": 136, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/CrashKonijn.Goap.Resolver.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 6, "ts": 1754735495827359, "dur": 66, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Rider.Editor.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 6, "ts": 1754735495827515, "dur": 188, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.InputSystem.ForUI.rsp"}}, {"pid": 12345, "tid": 6, "ts": 1754735495827792, "dur": 132, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/10010842633742469623.rsp"}}, {"pid": 12345, "tid": 6, "ts": 1754735495827938, "dur": 128, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1754735495828116, "dur": 227, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1754735495828343, "dur": 381, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1754735495828724, "dur": 226, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1754735495828950, "dur": 537, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1754735495829487, "dur": 169, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1754735495829656, "dur": 162, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1754735495829818, "dur": 519, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1754735495830337, "dur": 174, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1754735495830511, "dur": 392, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1754735495830903, "dur": 181, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1754735495831084, "dur": 187, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1754735495831271, "dur": 180, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1754735495831451, "dur": 165, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1754735495831616, "dur": 262, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1754735495831878, "dur": 176, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1754735495832054, "dur": 185, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1754735495832240, "dur": 163, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1754735495832404, "dur": 595, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1754735495833000, "dur": 397, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1754735495833397, "dur": 141, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1754735495833538, "dur": 348, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1754735495833894, "dur": 73, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.PlasticSCM.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1754735495833968, "dur": 401, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1754735495834373, "dur": 850, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.PlasticSCM.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 6, "ts": 1754735495835268, "dur": 65, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.CollabProxy.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1754735495835357, "dur": 300, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.CollabProxy.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 6, "ts": 1754735495835700, "dur": 98, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1754735495835798, "dur": 569, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1754735495836367, "dur": 319580, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1754735496155984, "dur": 1878, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.RenderPipelines.Core.ShaderLibrary.dll (+pdb)"}}, {"pid": 12345, "tid": 6, "ts": 1754735496157863, "dur": 551, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1754735496159018, "dur": 72, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\GameSoft\\Unity\\Editor\\2022.3.38f1c1\\Editor\\Data\\Tools\\ilpp\\Unity.ILPP.Runner\\System.Diagnostics.DiagnosticSource.dll"}}, {"pid": 12345, "tid": 6, "ts": 1754735496159241, "dur": 61, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\GameSoft\\Unity\\Editor\\2022.3.38f1c1\\Editor\\Data\\Tools\\ilpp\\Unity.ILPP.Runner\\System.Text.RegularExpressions.dll"}}, {"pid": 12345, "tid": 6, "ts": 1754735496158419, "dur": 1383, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/UnityEngine.UI.dll (+pdb)"}}, {"pid": 12345, "tid": 6, "ts": 1754735496159803, "dur": 978, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1754735496160792, "dur": 1451, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/URPWizard.dll (+pdb)"}}, {"pid": 12345, "tid": 6, "ts": 1754735496162244, "dur": 163, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1754735496162441, "dur": 71, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1754735496162512, "dur": 290, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1754735496162815, "dur": 38873, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1754735495806520, "dur": 19761, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1754735495826406, "dur": 58, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\GameSoft\\Unity\\Editor\\2022.3.38f1c1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.AnimationModule.dll"}}, {"pid": 12345, "tid": 7, "ts": 1754735495826404, "dur": 61, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.AnimationModule.dll_BBD7613341ED5E70.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1754735495826473, "dur": 111, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.AnimationModule.dll_BBD7613341ED5E70.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1754735495826608, "dur": 73, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.SpriteShapeModule.dll_1B8FAEC3513ADBA8.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1754735495826706, "dur": 66, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1754735495826772, "dur": 62, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/UnityEngine.TestRunner.rsp"}}, {"pid": 12345, "tid": 7, "ts": 1754735495826957, "dur": 88, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Core.Editor.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 7, "ts": 1754735495827108, "dur": 89, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.State.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 7, "ts": 1754735495827233, "dur": 93, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Flow.Editor.rsp"}}, {"pid": 12345, "tid": 7, "ts": 1754735495827452, "dur": 85, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.InputSystem.ForUI.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 7, "ts": 1754735495827548, "dur": 98, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/com.crashkonijn.goap.demos.turn-based.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 7, "ts": 1754735495827707, "dur": 61, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.CollabProxy.Editor.rsp2"}}, {"pid": 12345, "tid": 7, "ts": 1754735495827902, "dur": 151, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1754735495828123, "dur": 335, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1754735495828458, "dur": 271, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1754735495828729, "dur": 637, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1754735495829367, "dur": 333, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1754735495829700, "dur": 598, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1754735495830298, "dur": 181, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1754735495830480, "dur": 494, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1754735495830974, "dur": 290, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1754735495831264, "dur": 319, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1754735495831583, "dur": 194, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1754735495831777, "dur": 278, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1754735495832055, "dur": 517, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1754735495832573, "dur": 177, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1754735495832750, "dur": 168, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1754735495832918, "dur": 257, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1754735495833175, "dur": 232, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1754735495833407, "dur": 131, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1754735495833538, "dur": 334, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1754735495833873, "dur": 147, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/CrashKonijn.Goap.Core.dll.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1754735495834033, "dur": 449, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.VisualStudio.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 7, "ts": 1754735495834505, "dur": 54, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.VisualStudio.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 7, "ts": 1754735495834575, "dur": 82, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Tests.dll.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1754735495834668, "dur": 216, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Tests.dll (+2 others)"}}, {"pid": 12345, "tid": 7, "ts": 1754735495834925, "dur": 119, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/com.crashkonijn.goap.demos.complex.dll.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1754735495835044, "dur": 94, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1754735495835144, "dur": 94, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1754735495835270, "dur": 143, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1754735495835414, "dur": 97, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.Collections.BurstCompatibilityGen.dll.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1754735495835526, "dur": 202, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Collections.BurstCompatibilityGen.dll (+2 others)"}}, {"pid": 12345, "tid": 7, "ts": 1754735495835795, "dur": 571, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1754735495836366, "dur": 319618, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1754735496155986, "dur": 1489, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/CrashKonijn.Goap.Runtime.dll (+pdb)"}}, {"pid": 12345, "tid": 7, "ts": 1754735496157477, "dur": 79, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1754735496159018, "dur": 233, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\GameSoft\\Unity\\Editor\\2022.3.38f1c1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.StreamingModule.dll"}}, {"pid": 12345, "tid": 7, "ts": 1754735496157568, "dur": 2067, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.RenderPipelines.Core.Runtime.dll (+pdb)"}}, {"pid": 12345, "tid": 7, "ts": 1754735496159636, "dur": 332, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1754735496160180, "dur": 204, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\GameSoft\\Unity\\Editor\\2022.3.38f1c1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Resources.ResourceManager.dll"}}, {"pid": 12345, "tid": 7, "ts": 1754735496159972, "dur": 1647, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.ShaderGraph.Utilities.dll (+pdb)"}}, {"pid": 12345, "tid": 7, "ts": 1754735496161620, "dur": 112, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1754735496161738, "dur": 334, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1754735496162105, "dur": 138, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.Burst.Editor.pdb"}}, {"pid": 12345, "tid": 7, "ts": 1754735496162269, "dur": 50, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1754735496162435, "dur": 65, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1754735496162541, "dur": 39268, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1754735495806563, "dur": 19742, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1754735495826670, "dur": 88, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\GameSoft\\Unity\\Editor\\2022.3.38f1c1\\Editor\\Data\\Managed\\UnityEditor.Graphs.dll"}}, {"pid": 12345, "tid": 8, "ts": 1754735495826669, "dur": 91, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.Graphs.dll_34C604C212D62B6E.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1754735495826887, "dur": 87, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.InputSystem.rsp"}}, {"pid": 12345, "tid": 8, "ts": 1754735495826998, "dur": 87, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/CrashKonijn.Goap.Core.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 8, "ts": 1754735495827155, "dur": 114, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Universal.Runtime.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 8, "ts": 1754735495827312, "dur": 58, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Timeline.Editor.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 8, "ts": 1754735495827424, "dur": 92, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/CrashKonijn.Goap.Editor.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 8, "ts": 1754735495827524, "dur": 140, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/URPWizard.rsp2"}}, {"pid": 12345, "tid": 8, "ts": 1754735495827690, "dur": 62, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Collections.DocCodeSamples.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 8, "ts": 1754735495827910, "dur": 148, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1754735495828129, "dur": 186, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1754735495828315, "dur": 156, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1754735495828471, "dur": 201, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1754735495828673, "dur": 505, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1754735495829178, "dur": 155, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1754735495829333, "dur": 170, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1754735495829504, "dur": 256, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1754735495829760, "dur": 564, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1754735495830324, "dur": 218, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1754735495830542, "dur": 219, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1754735495830761, "dur": 262, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1754735495831024, "dur": 305, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1754735495831329, "dur": 289, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1754735495831632, "dur": 264, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1754735495831896, "dur": 221, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1754735495832117, "dur": 177, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1754735495832294, "dur": 184, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1754735495832479, "dur": 199, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1754735495832678, "dur": 197, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1754735495832875, "dur": 322, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1754735495833198, "dur": 165, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1754735495833363, "dur": 189, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1754735495833552, "dur": 337, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1754735495833890, "dur": 63, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.VSCode.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1754735495833980, "dur": 89, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Core.ShaderLibrary.dll.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1754735495834086, "dur": 51, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\GameSoft\\Unity\\Editor\\2022.3.38f1c1\\Editor\\Data\\Managed\\UnityEngine\\UnityEditor.CoreModule.dll"}}, {"pid": 12345, "tid": 8, "ts": 1754735495834083, "dur": 344, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Core.ShaderLibrary.dll (+2 others)"}}, {"pid": 12345, "tid": 8, "ts": 1754735495834472, "dur": 69, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Core.ShaderLibrary.dll (+2 others)"}}, {"pid": 12345, "tid": 8, "ts": 1754735495834545, "dur": 70, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1754735495834653, "dur": 300, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1754735495834953, "dur": 124, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1754735495835077, "dur": 166, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1754735495835243, "dur": 190, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1754735495835433, "dur": 357, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1754735495835790, "dur": 571, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1754735495836361, "dur": 1199, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1754735495837627, "dur": 176, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/PPv2URPConverters.dll (+2 others)"}}, {"pid": 12345, "tid": 8, "ts": 1754735495837822, "dur": 318192, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1754735496156015, "dur": 1835, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/CrashKonijn.Goap.Core.dll (+pdb)"}}, {"pid": 12345, "tid": 8, "ts": 1754735496157893, "dur": 1915, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Timeline.dll (+pdb)"}}, {"pid": 12345, "tid": 8, "ts": 1754735496160181, "dur": 208, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\GameSoft\\Unity\\Editor\\2022.3.38f1c1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Net.NetworkInformation.dll"}}, {"pid": 12345, "tid": 8, "ts": 1754735496159844, "dur": 1629, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.VisualScripting.State.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 8, "ts": 1754735496161474, "dur": 118, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1754735496161812, "dur": 373, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1754735496162227, "dur": 52, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1754735496162321, "dur": 95, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1754735496162444, "dur": 82, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1754735496162526, "dur": 290, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1754735496162817, "dur": 38977, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1754735495806595, "dur": 19723, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1754735495826484, "dur": 105, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.DirectorModule.dll_3C3F89D87E20F844.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1754735495826619, "dur": 79, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.SubsystemsModule.dll_5DAAF2C2D061984C.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1754735495826700, "dur": 73, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.collections@1.2.4\\Unity.Collections.LowLevel.ILSupport\\Unity.Collections.LowLevel.ILSupport.dll"}}, {"pid": 12345, "tid": 9, "ts": 1754735495826699, "dur": 76, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.Collections.LowLevel.ILSupport.dll_2E40CE80E145E521.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1754735495826918, "dur": 152, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/CrashKonijn.Agent.Core.rsp2"}}, {"pid": 12345, "tid": 9, "ts": 1754735495827112, "dur": 139, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Core.Editor.rsp"}}, {"pid": 12345, "tid": 9, "ts": 1754735495827316, "dur": 133, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.PlasticSCM.Editor.rsp2"}}, {"pid": 12345, "tid": 9, "ts": 1754735495827520, "dur": 86, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Shared.Editor.rsp"}}, {"pid": 12345, "tid": 9, "ts": 1754735495827658, "dur": 81, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/com.crashkonijn.goap.demos.turn-based.rsp"}}, {"pid": 12345, "tid": 9, "ts": 1754735495827774, "dur": 69, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/4342594774289556727.rsp"}}, {"pid": 12345, "tid": 9, "ts": 1754735495827844, "dur": 60, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/9149840420361016890.rsp"}}, {"pid": 12345, "tid": 9, "ts": 1754735495827905, "dur": 142, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1754735495828112, "dur": 173, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1754735495828285, "dur": 186, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1754735495828472, "dur": 182, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1754735495828655, "dur": 143, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1754735495828799, "dur": 174, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1754735495828974, "dur": 753, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1754735495829727, "dur": 498, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1754735495830225, "dur": 171, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1754735495830396, "dur": 369, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1754735495830765, "dur": 233, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1754735495830998, "dur": 193, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1754735495831191, "dur": 180, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1754735495831371, "dur": 226, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1754735495831598, "dur": 183, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1754735495831781, "dur": 168, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1754735495831949, "dur": 206, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1754735495832155, "dur": 172, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1754735495832327, "dur": 256, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1754735495832583, "dur": 227, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1754735495832810, "dur": 182, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1754735495832992, "dur": 276, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1754735495833305, "dur": 217, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1754735495833522, "dur": 349, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1754735495833872, "dur": 85, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/CrashKonijn.Agent.Core.dll.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1754735495834036, "dur": 338, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/CrashKonijn.Agent.Core.dll (+2 others)"}}, {"pid": 12345, "tid": 9, "ts": 1754735495834375, "dur": 88, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1754735495834494, "dur": 384, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/CrashKonijn.Goap.Core.dll (+2 others)"}}, {"pid": 12345, "tid": 9, "ts": 1754735495834921, "dur": 95, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/com.crashkonijn.goap.demos.simple.dll.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1754735495835071, "dur": 177, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1754735495835248, "dur": 179, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1754735495835427, "dur": 368, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1754735495835795, "dur": 562, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1754735495836357, "dur": 1105, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1754735495837540, "dur": 204, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.SettingsProvider.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 9, "ts": 1754735495837785, "dur": 318205, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1754735496155991, "dur": 1807, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.InputSystem.ForUI.dll (+pdb)"}}, {"pid": 12345, "tid": 9, "ts": 1754735496157799, "dur": 82, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1754735496158774, "dur": 253, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\GameSoft\\Unity\\Editor\\2022.3.38f1c1\\Editor\\Data\\Tools\\ilpp\\Unity.ILPP.Runner\\System.Net.NetworkInformation.dll"}}, {"pid": 12345, "tid": 9, "ts": 1754735496157889, "dur": 1796, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/GlassSystem.dll (+pdb)"}}, {"pid": 12345, "tid": 9, "ts": 1754735496160181, "dur": 201, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.collections@1.2.4\\Unity.Collections.LowLevel.ILSupport\\Unity.Collections.LowLevel.ILSupport.dll"}}, {"pid": 12345, "tid": 9, "ts": 1754735496159723, "dur": 1589, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Searcher.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 9, "ts": 1754735496161312, "dur": 133, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1754735496161518, "dur": 90, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1754735496161613, "dur": 105, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1754735496161770, "dur": 63, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1754735496161885, "dur": 91, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1754735496162103, "dur": 120, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.RenderPipelines.Universal.Editor.dll"}}, {"pid": 12345, "tid": 9, "ts": 1754735496162447, "dur": 105, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1754735496162562, "dur": 39134, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1754735495806624, "dur": 19707, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1754735495826468, "dur": 116, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.UnityConnectModule.dll_591A98B8662B29F2.mvfrm"}}, {"pid": 12345, "tid": 10, "ts": 1754735495826690, "dur": 71, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1754735495826803, "dur": 76, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/UnityEngine.UI.rsp"}}, {"pid": 12345, "tid": 10, "ts": 1754735495826902, "dur": 128, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.Burst.Cecil.Pdb.dll_99AE285BDA61FA2E.mvfrm"}}, {"pid": 12345, "tid": 10, "ts": 1754735495827105, "dur": 69, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.State.rsp2"}}, {"pid": 12345, "tid": 10, "ts": 1754735495827250, "dur": 57, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.ShaderGraph.Editor.rsp"}}, {"pid": 12345, "tid": 10, "ts": 1754735495827314, "dur": 86, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.ShaderGraph.Editor.rsp"}}, {"pid": 12345, "tid": 10, "ts": 1754735495827667, "dur": 137, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.InputSystem.TestFramework.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 10, "ts": 1754735495827910, "dur": 144, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1754735495828138, "dur": 155, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1754735495828293, "dur": 172, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1754735495828465, "dur": 202, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1754735495828668, "dur": 309, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1754735495828978, "dur": 216, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1754735495829194, "dur": 191, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1754735495829386, "dur": 143, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1754735495829529, "dur": 169, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1754735495829698, "dur": 485, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1754735495830184, "dur": 330, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1754735495830514, "dur": 185, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1754735495830699, "dur": 284, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1754735495830983, "dur": 163, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1754735495831146, "dur": 190, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1754735495831336, "dur": 206, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1754735495831543, "dur": 183, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1754735495831726, "dur": 169, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1754735495831895, "dur": 193, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1754735495832088, "dur": 159, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1754735495832248, "dur": 158, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1754735495832406, "dur": 188, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1754735495832594, "dur": 177, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1754735495832772, "dur": 197, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1754735495832970, "dur": 296, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1754735495833308, "dur": 243, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1754735495833551, "dur": 327, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1754735495833880, "dur": 59, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.Timeline.dll.mvfrm"}}, {"pid": 12345, "tid": 10, "ts": 1754735495833957, "dur": 520, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Timeline.dll (+2 others)"}}, {"pid": 12345, "tid": 10, "ts": 1754735495834505, "dur": 350, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/CrashKonijn.Agent.Runtime.dll (+2 others)"}}, {"pid": 12345, "tid": 10, "ts": 1754735495834926, "dur": 121, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/com.crashkonijn.goap.demos.turn-based.dll.mvfrm"}}, {"pid": 12345, "tid": 10, "ts": 1754735495835067, "dur": 81, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Core.dll.mvfrm"}}, {"pid": 12345, "tid": 10, "ts": 1754735495835211, "dur": 830, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Core.dll (+2 others)"}}, {"pid": 12345, "tid": 10, "ts": 1754735495836115, "dur": 62, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Flow.dll.mvfrm"}}, {"pid": 12345, "tid": 10, "ts": 1754735495836187, "dur": 761, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Flow.dll (+2 others)"}}, {"pid": 12345, "tid": 10, "ts": 1754735495836949, "dur": 85, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1754735495837055, "dur": 61, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.State.dll.mvfrm"}}, {"pid": 12345, "tid": 10, "ts": 1754735495837127, "dur": 176, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.State.dll (+2 others)"}}, {"pid": 12345, "tid": 10, "ts": 1754735495837337, "dur": 318673, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1754735496156013, "dur": 1845, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.RenderPipelines.Universal.Runtime.dll (+pdb)"}}, {"pid": 12345, "tid": 10, "ts": 1754735496158556, "dur": 227, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\GameSoft\\Unity\\Editor\\2022.3.38f1c1\\Editor\\Data\\Tools\\ilpp\\Unity.ILPP.Runner\\Microsoft.Extensions.Logging.Console.dll"}}, {"pid": 12345, "tid": 10, "ts": 1754735496159018, "dur": 67, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\GameSoft\\Unity\\Editor\\2022.3.38f1c1\\Editor\\Data\\Tools\\ilpp\\Unity.ILPP.Runner\\System.Security.dll"}}, {"pid": 12345, "tid": 10, "ts": 1754735496157885, "dur": 1739, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.TextMeshPro.dll (+pdb)"}}, {"pid": 12345, "tid": 10, "ts": 1754735496160181, "dur": 325, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\GameSoft\\Unity\\Editor\\2022.3.38f1c1\\Editor\\Data\\Tools\\ilpp\\Unity.ILPP.Runner\\Microsoft.AspNetCore.Authentication.dll"}}, {"pid": 12345, "tid": 10, "ts": 1754735496159649, "dur": 1762, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Collections.BurstCompatibilityGen.dll (+pdb)"}}, {"pid": 12345, "tid": 10, "ts": 1754735496162059, "dur": 54, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\GameSoft\\Unity\\Editor\\2022.3.38f1c1\\Editor\\Data\\Tools\\ilpp\\Unity.ILPP.Runner\\Microsoft.Extensions.Configuration.KeyPerFile.dll"}}, {"pid": 12345, "tid": 10, "ts": 1754735496161451, "dur": 1323, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Mathematics.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 10, "ts": 1754735496162823, "dur": 38908, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1754735495806665, "dur": 19676, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1754735495826490, "dur": 59, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.dll_D66E5D73A81B547E.mvfrm"}}, {"pid": 12345, "tid": 11, "ts": 1754735495826658, "dur": 96, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\GameSoft\\Unity\\Editor\\2022.3.38f1c1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.UnityWebRequestAudioModule.dll"}}, {"pid": 12345, "tid": 11, "ts": 1754735495826657, "dur": 106, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityWebRequestAudioModule.dll_180837F1985FCEBE.mvfrm"}}, {"pid": 12345, "tid": 11, "ts": 1754735495826782, "dur": 92, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityWebRequestAudioModule.dll_180837F1985FCEBE.mvfrm"}}, {"pid": 12345, "tid": 11, "ts": 1754735495826885, "dur": 50, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/UnityEngine.TestRunner.dll.mvfrm"}}, {"pid": 12345, "tid": 11, "ts": 1754735495827901, "dur": 164, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\GameSoft\\Unity\\Editor\\2022.3.38f1c1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Security.Claims.dll"}}, {"pid": 12345, "tid": 11, "ts": 1754735495828871, "dur": 70, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.test-framework@1.1.33\\UnityEngine.TestRunner\\NUnitExtensions\\Commands\\UnityTestMethodCommand.cs"}}, {"pid": 12345, "tid": 11, "ts": 1754735495829302, "dur": 51, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.test-framework@1.1.33\\UnityEngine.TestRunner\\TestRunner\\Callbacks\\TestResultRenderer.cs"}}, {"pid": 12345, "tid": 11, "ts": 1754735495829447, "dur": 123, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.test-framework@1.1.33\\UnityEngine.TestRunner\\TestRunner\\RemoteHelpers\\RemoteTestResultDataWithTestData.cs"}}, {"pid": 12345, "tid": 11, "ts": 1754735495829571, "dur": 209, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.test-framework@1.1.33\\UnityEngine.TestRunner\\TestRunner\\RuntimeTestRunnerFilter.cs"}}, {"pid": 12345, "tid": 11, "ts": 1754735495829834, "dur": 318, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.test-framework@1.1.33\\UnityEngine.TestRunner\\Utils\\AssemblyProvider\\AssemblyWrapper.cs"}}, {"pid": 12345, "tid": 11, "ts": 1754735495830406, "dur": 67, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.test-framework@1.1.33\\UnityEngine.TestRunner\\Utils\\Vector3EqualityComparer.cs"}}, {"pid": 12345, "tid": 11, "ts": 1754735495830483, "dur": 52, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.test-framework@1.1.33\\UnityEngine.TestRunner\\Utils\\Vector4EqualityComparer.cs"}}, {"pid": 12345, "tid": 11, "ts": 1754735495826951, "dur": 3591, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/UnityEngine.TestRunner.dll (+2 others)"}}, {"pid": 12345, "tid": 11, "ts": 1754735495830612, "dur": 108, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/UnityEditor.TestRunner.dll.mvfrm"}}, {"pid": 12345, "tid": 11, "ts": 1754735495833249, "dur": 79, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.test-framework@1.1.33\\UnityEditor.TestRunner\\TestRun\\Tasks\\LegacyEditModeRunTask.cs"}}, {"pid": 12345, "tid": 11, "ts": 1754735495830730, "dur": 2729, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/UnityEditor.TestRunner.dll (+2 others)"}}, {"pid": 12345, "tid": 11, "ts": 1754735495833537, "dur": 55, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/UnityEditor.UI.dll.mvfrm"}}, {"pid": 12345, "tid": 11, "ts": 1754735495833602, "dur": 202, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/UnityEditor.UI.dll (+2 others)"}}, {"pid": 12345, "tid": 11, "ts": 1754735495833879, "dur": 58, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.Burst.dll.mvfrm"}}, {"pid": 12345, "tid": 11, "ts": 1754735495833951, "dur": 483, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Burst.dll (+2 others)"}}, {"pid": 12345, "tid": 11, "ts": 1754735495834435, "dur": 104, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1754735495834585, "dur": 84, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.Burst.CodeGen.dll.mvfrm"}}, {"pid": 12345, "tid": 11, "ts": 1754735495834669, "dur": 227, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1754735495834899, "dur": 286, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Burst.CodeGen.dll (+2 others)"}}, {"pid": 12345, "tid": 11, "ts": 1754735495835241, "dur": 415, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPP-Configuration Library/ilpp-configuration.nevergeneratedoutput"}}, {"pid": 12345, "tid": 11, "ts": 1754735495835693, "dur": 77, "ph": "X", "name": "StoreTimestampsOfNonGeneratedInputFiles", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1754735495835779, "dur": 318207, "ph": "X", "name": "ILPP-Configuration", "args": {"detail": "Library/ilpp-configuration.nevergeneratedoutput"}}, {"pid": 12345, "tid": 11, "ts": 1754735496155948, "dur": 1542, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/com.crashkonijn.goap.demos.complex.dll (+pdb)"}}, {"pid": 12345, "tid": 11, "ts": 1754735496157490, "dur": 164, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1754735496157664, "dur": 1268, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.InputSystem.dll (+pdb)"}}, {"pid": 12345, "tid": 11, "ts": 1754735496158933, "dur": 88, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1754735496160181, "dur": 201, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\GameSoft\\Unity\\Editor\\2022.3.38f1c1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Reflection.DispatchProxy.dll"}}, {"pid": 12345, "tid": 11, "ts": 1754735496159028, "dur": 1639, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/CrashKonijn.Agent.Runtime.dll (+pdb)"}}, {"pid": 12345, "tid": 11, "ts": 1754735496160668, "dur": 112, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1754735496162235, "dur": 116, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\GameSoft\\Unity\\Editor\\2022.3.38f1c1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Text.RegularExpressions.dll"}}, {"pid": 12345, "tid": 11, "ts": 1754735496160786, "dur": 1659, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/CrashKonijn.Goap.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 11, "ts": 1754735496162531, "dur": 356, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1754735496162899, "dur": 38842, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1754735495806697, "dur": 19659, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1754735495826442, "dur": 52, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ContentLoadModule.dll_9B44DAA96A1B4B8B.mvfrm"}}, {"pid": 12345, "tid": 12, "ts": 1754735495826674, "dur": 83, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1754735495826792, "dur": 63, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/UnityEditor.TestRunner.rsp2"}}, {"pid": 12345, "tid": 12, "ts": 1754735495826916, "dur": 103, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Core.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 12, "ts": 1754735495827147, "dur": 77, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Timeline.rsp2"}}, {"pid": 12345, "tid": 12, "ts": 1754735495827495, "dur": 63, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualStudio.Editor.rsp"}}, {"pid": 12345, "tid": 12, "ts": 1754735495827571, "dur": 123, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/com.crashkonijn.docs.getting_started.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 12, "ts": 1754735495827898, "dur": 152, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1754735495828135, "dur": 167, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1754735495828303, "dur": 240, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1754735495828544, "dur": 197, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1754735495828742, "dur": 550, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1754735495829293, "dur": 161, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1754735495829458, "dur": 277, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1754735495829735, "dur": 647, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1754735495830382, "dur": 288, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1754735495830670, "dur": 178, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1754735495830849, "dur": 185, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1754735495831034, "dur": 189, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1754735495831223, "dur": 172, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1754735495831395, "dur": 754, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1754735495832150, "dur": 151, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1754735495832302, "dur": 150, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1754735495832452, "dur": 215, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1754735495832667, "dur": 153, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1754735495832821, "dur": 202, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1754735495833024, "dur": 184, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1754735495833308, "dur": 209, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1754735495833550, "dur": 330, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1754735495833880, "dur": 61, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.TextMeshPro.dll.mvfrm"}}, {"pid": 12345, "tid": 12, "ts": 1754735495833987, "dur": 123, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\GameSoft\\Unity\\Editor\\2022.3.38f1c1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.AudioModule.dll"}}, {"pid": 12345, "tid": 12, "ts": 1754735495834151, "dur": 316, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\GameSoft\\Unity\\Editor\\2022.3.38f1c1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.TextCoreFontEngineModule.dll"}}, {"pid": 12345, "tid": 12, "ts": 1754735495833960, "dur": 860, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.TextMeshPro.dll (+2 others)"}}, {"pid": 12345, "tid": 12, "ts": 1754735495834820, "dur": 71, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1754735495834920, "dur": 268, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.TextMeshPro.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 12, "ts": 1754735495835213, "dur": 380, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.TextMeshPro.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 12, "ts": 1754735495835593, "dur": 66, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1754735495835680, "dur": 123, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1754735495835803, "dur": 545, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1754735495836349, "dur": 282, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/com.crashkonijn.docs.getting_started.dll (+2 others)"}}, {"pid": 12345, "tid": 12, "ts": 1754735495836677, "dur": 319272, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1754735496155950, "dur": 1548, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Tests.dll (+pdb)"}}, {"pid": 12345, "tid": 12, "ts": 1754735496157499, "dur": 67, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1754735496158546, "dur": 141, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\GameSoft\\Unity\\Editor\\2022.3.38f1c1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.Physics2DModule.dll"}}, {"pid": 12345, "tid": 12, "ts": 1754735496158784, "dur": 175, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\GameSoft\\Unity\\Editor\\2022.3.38f1c1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netfx\\System.dll"}}, {"pid": 12345, "tid": 12, "ts": 1754735496159018, "dur": 93, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\GameSoft\\Unity\\Editor\\2022.3.38f1c1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.IO.FileSystem.dll"}}, {"pid": 12345, "tid": 12, "ts": 1754735496157572, "dur": 1920, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/com.crashkonijn.docs.getting_started.dll (+pdb)"}}, {"pid": 12345, "tid": 12, "ts": 1754735496160153, "dur": 216, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\GameSoft\\Unity\\Editor\\2022.3.38f1c1\\Editor\\Data\\Tools\\ilpp\\Unity.ILPP.Runner\\Microsoft.AspNetCore.Authorization.dll"}}, {"pid": 12345, "tid": 12, "ts": 1754735496160478, "dur": 86, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\GameSoft\\Unity\\Editor\\2022.3.38f1c1\\Editor\\Data\\Tools\\ilpp\\Unity.ILPP.Runner\\Microsoft.AspNetCore.SignalR.Common.dll"}}, {"pid": 12345, "tid": 12, "ts": 1754735496159534, "dur": 1804, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.VisualScripting.Core.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 12, "ts": 1754735496161369, "dur": 1418, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.VisualScripting.Flow.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 12, "ts": 1754735496162818, "dur": 38932, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1754735495806735, "dur": 19634, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1754735495826556, "dur": 131, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.XRModule.dll_5E2DACE274283492.mvfrm"}}, {"pid": 12345, "tid": 13, "ts": 1754735495826689, "dur": 74, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.collab-proxy@2.8.2\\Lib\\Editor\\Unity.Plastic.Newtonsoft.Json.dll"}}, {"pid": 12345, "tid": 13, "ts": 1754735495826688, "dur": 76, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.Plastic.Newtonsoft.Json.dll_CF17D4CA2A31CD8C.mvfrm"}}, {"pid": 12345, "tid": 13, "ts": 1754735495826848, "dur": 100, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.InputSystem.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 13, "ts": 1754735495826974, "dur": 88, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipeline.Universal.ShaderLibrary.rsp2"}}, {"pid": 12345, "tid": 13, "ts": 1754735495827149, "dur": 119, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.ShaderGraph.Utilities.rsp"}}, {"pid": 12345, "tid": 13, "ts": 1754735495827392, "dur": 135, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Shared.Editor.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 13, "ts": 1754735495828357, "dur": 477, "ph": "X", "name": "WriteText", "args": {"detail": "Library/Bee/artifacts/1900b0aE.dag/Assembly-CSharp.rsp"}}, {"pid": 12345, "tid": 13, "ts": 1754735495829645, "dur": 525, "ph": "X", "name": "File", "args": {"detail": "D:\\GameSoft\\Unity\\Editor\\2022.3.38f1c1\\Editor\\Data\\Tools\\ilpp\\Unity.ILPP.Runner\\Microsoft.AspNetCore.Mvc.RazorPages.dll"}}, {"pid": 12345, "tid": 13, "ts": 1754735495828835, "dur": 1453, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1754735495830288, "dur": 151, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1754735495830439, "dur": 159, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1754735495830628, "dur": 170, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1754735495830798, "dur": 305, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1754735495831103, "dur": 367, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1754735495831471, "dur": 233, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1754735495831704, "dur": 166, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1754735495831870, "dur": 506, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1754735495832376, "dur": 231, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1754735495832607, "dur": 146, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1754735495832753, "dur": 235, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1754735495832989, "dur": 177, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1754735495833166, "dur": 180, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1754735495833346, "dur": 208, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1754735495833554, "dur": 442, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1754735495833997, "dur": 69, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Universal.Shaders.dll.mvfrm"}}, {"pid": 12345, "tid": 13, "ts": 1754735495834086, "dur": 76, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\GameSoft\\Unity\\Editor\\2022.3.38f1c1\\Editor\\Data\\Managed\\UnityEngine\\UnityEditor.dll"}}, {"pid": 12345, "tid": 13, "ts": 1754735495834080, "dur": 375, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Universal.Shaders.dll (+2 others)"}}, {"pid": 12345, "tid": 13, "ts": 1754735495834475, "dur": 76, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Universal.Shaders.dll (+2 others)"}}, {"pid": 12345, "tid": 13, "ts": 1754735495834593, "dur": 98, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.Mathematics.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 13, "ts": 1754735495834704, "dur": 278, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Mathematics.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 13, "ts": 1754735495834982, "dur": 109, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1754735495835112, "dur": 133, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1754735495835245, "dur": 196, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1754735495835441, "dur": 346, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1754735495835788, "dur": 332, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1754735495836121, "dur": 51, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Core.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 13, "ts": 1754735495836195, "dur": 794, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Core.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 13, "ts": 1754735495837044, "dur": 318935, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1754735496155994, "dur": 1819, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.RenderPipelines.ShaderGraph.ShaderGraphLibrary.dll (+pdb)"}}, {"pid": 12345, "tid": 13, "ts": 1754735496159019, "dur": 232, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\GameSoft\\Unity\\Editor\\2022.3.38f1c1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.ComponentModel.Primitives.dll"}}, {"pid": 12345, "tid": 13, "ts": 1754735496157844, "dur": 1560, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Cinemachine.dll (+pdb)"}}, {"pid": 12345, "tid": 13, "ts": 1754735496160181, "dur": 241, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\GameSoft\\Unity\\Editor\\2022.3.38f1c1\\Editor\\Data\\Tools\\ilpp\\Unity.ILPP.Runner\\System.Net.Requests.dll"}}, {"pid": 12345, "tid": 13, "ts": 1754735496159428, "dur": 1618, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Burst.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 13, "ts": 1754735496161079, "dur": 1439, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/UnityEditor.UI.dll (+pdb)"}}, {"pid": 12345, "tid": 13, "ts": 1754735496162560, "dur": 39138, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1754735495806768, "dur": 19613, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1754735495826470, "dur": 139, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.CrashReportingModule.dll_E0E4ACDF62B3A25F.mvfrm"}}, {"pid": 12345, "tid": 14, "ts": 1754735495826659, "dur": 85, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1754735495827111, "dur": 186, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Flow.Editor.rsp2"}}, {"pid": 12345, "tid": 14, "ts": 1754735495827347, "dur": 83, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Universal.Editor.rsp"}}, {"pid": 12345, "tid": 14, "ts": 1754735495827527, "dur": 92, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/com.crashkonijn.docs.getting_started.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 14, "ts": 1754735495827898, "dur": 140, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1754735495828121, "dur": 422, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1754735495828543, "dur": 206, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1754735495828749, "dur": 212, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1754735495828962, "dur": 527, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1754735495829489, "dur": 889, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1754735495830378, "dur": 324, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1754735495830702, "dur": 202, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1754735495830905, "dur": 457, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1754735495831363, "dur": 205, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1754735495831568, "dur": 182, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1754735495831750, "dur": 205, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1754735495831955, "dur": 202, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1754735495832157, "dur": 177, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1754735495832335, "dur": 253, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1754735495832588, "dur": 279, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1754735495832867, "dur": 366, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1754735495833234, "dur": 50, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1754735495833301, "dur": 233, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1754735495833534, "dur": 335, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1754735495833870, "dur": 55, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.InputSystem.dll.mvfrm"}}, {"pid": 12345, "tid": 14, "ts": 1754735495833966, "dur": 921, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.InputSystem.dll (+2 others)"}}, {"pid": 12345, "tid": 14, "ts": 1754735495834887, "dur": 140, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1754735495835067, "dur": 85, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Core.Runtime.dll.mvfrm"}}, {"pid": 12345, "tid": 14, "ts": 1754735495835195, "dur": 504, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Core.Runtime.dll (+2 others)"}}, {"pid": 12345, "tid": 14, "ts": 1754735495835792, "dur": 61, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipeline.Universal.ShaderLibrary.dll.mvfrm"}}, {"pid": 12345, "tid": 14, "ts": 1754735495835866, "dur": 214, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipeline.Universal.ShaderLibrary.dll (+2 others)"}}, {"pid": 12345, "tid": 14, "ts": 1754735495836122, "dur": 228, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1754735495836351, "dur": 227, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/com.crashkonijn.goap.demos.turn-based.dll (+2 others)"}}, {"pid": 12345, "tid": 14, "ts": 1754735495836579, "dur": 128, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1754735495836713, "dur": 61, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/URPWizard.dll.mvfrm"}}, {"pid": 12345, "tid": 14, "ts": 1754735495836793, "dur": 241, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/URPWizard.dll (+2 others)"}}, {"pid": 12345, "tid": 14, "ts": 1754735495837077, "dur": 318909, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1754735496155988, "dur": 1868, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/CrashKonijn.Agent.Core.dll (+pdb)"}}, {"pid": 12345, "tid": 14, "ts": 1754735496157882, "dur": 1321, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Mathematics.dll (+pdb)"}}, {"pid": 12345, "tid": 14, "ts": 1754735496159203, "dur": 1260, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1754735496160473, "dur": 1447, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/PPv2URPConverters.dll (+pdb)"}}, {"pid": 12345, "tid": 14, "ts": 1754735496161921, "dur": 87, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1754735496162101, "dur": 81, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.Burst.Editor.dll"}}, {"pid": 12345, "tid": 14, "ts": 1754735496162201, "dur": 274, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1754735496162512, "dur": 300, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1754735496162831, "dur": 38880, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1754735495806799, "dur": 19591, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1754735495826401, "dur": 68, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\GameSoft\\Unity\\Editor\\2022.3.38f1c1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.AccessibilityModule.dll"}}, {"pid": 12345, "tid": 15, "ts": 1754735495826395, "dur": 76, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.AccessibilityModule.dll_4B3448FA7561224A.mvfrm"}}, {"pid": 12345, "tid": 15, "ts": 1754735495826479, "dur": 187, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.AccessibilityModule.dll_4B3448FA7561224A.mvfrm"}}, {"pid": 12345, "tid": 15, "ts": 1754735495826670, "dur": 85, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\GameSoft\\Unity\\Editor\\2022.3.38f1c1\\Editor\\Data\\PlaybackEngines\\WindowsStandaloneSupport\\UnityEditor.WindowsStandalone.Extensions.dll"}}, {"pid": 12345, "tid": 15, "ts": 1754735495826668, "dur": 91, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.WindowsStandalone.Extensions.dll_35385077F0CEC424.mvfrm"}}, {"pid": 12345, "tid": 15, "ts": 1754735495826792, "dur": 247, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.NVIDIAModule.dll_3B326C6243819FB3.mvfrm"}}, {"pid": 12345, "tid": 15, "ts": 1754735495827382, "dur": 126, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.SettingsProvider.Editor.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 15, "ts": 1754735495827572, "dur": 191, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/GlassSystem.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 15, "ts": 1754735495827902, "dur": 150, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1754735495828052, "dur": 114, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/1723042715960067801.rsp"}}, {"pid": 12345, "tid": 15, "ts": 1754735495828167, "dur": 141, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1754735495828308, "dur": 174, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1754735495828482, "dur": 161, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1754735495828643, "dur": 266, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1754735495828909, "dur": 139, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1754735495829049, "dur": 261, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1754735495829311, "dur": 341, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1754735495829653, "dur": 174, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1754735495829827, "dur": 577, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1754735495830404, "dur": 341, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1754735495830746, "dur": 198, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1754735495830944, "dur": 169, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1754735495831113, "dur": 165, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1754735495831279, "dur": 200, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1754735495831479, "dur": 232, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1754735495831711, "dur": 161, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1754735495831873, "dur": 208, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1754735495832081, "dur": 168, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1754735495832249, "dur": 170, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1754735495832419, "dur": 161, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1754735495832580, "dur": 178, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1754735495832758, "dur": 224, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1754735495832982, "dur": 219, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1754735495833303, "dur": 222, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1754735495833525, "dur": 350, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1754735495833876, "dur": 65, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.ShaderGraph.Utilities.dll.mvfrm"}}, {"pid": 12345, "tid": 15, "ts": 1754735495833996, "dur": 396, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.ShaderGraph.Utilities.dll (+2 others)"}}, {"pid": 12345, "tid": 15, "ts": 1754735495834393, "dur": 97, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1754735495834510, "dur": 56, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.ShaderGraph.Utilities.ref.dll_0524057423981A9D.mvfrm"}}, {"pid": 12345, "tid": 15, "ts": 1754735495834650, "dur": 296, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1754735495834946, "dur": 122, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1754735495835069, "dur": 87, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.InputSystem.ForUI.dll.mvfrm"}}, {"pid": 12345, "tid": 15, "ts": 1754735495835174, "dur": 62, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.InputSystem.ForUI.dll.mvfrm"}}, {"pid": 12345, "tid": 15, "ts": 1754735495835240, "dur": 266, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.InputSystem.ForUI.dll (+2 others)"}}, {"pid": 12345, "tid": 15, "ts": 1754735495835574, "dur": 210, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1754735495835786, "dur": 63, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Universal.Config.Runtime.dll.mvfrm"}}, {"pid": 12345, "tid": 15, "ts": 1754735495835875, "dur": 224, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Universal.Config.Runtime.dll (+2 others)"}}, {"pid": 12345, "tid": 15, "ts": 1754735495836142, "dur": 61, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Universal.Runtime.dll.mvfrm"}}, {"pid": 12345, "tid": 15, "ts": 1754735495836214, "dur": 451, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Universal.Runtime.dll (+2 others)"}}, {"pid": 12345, "tid": 15, "ts": 1754735495836709, "dur": 63, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Cinemachine.dll.mvfrm"}}, {"pid": 12345, "tid": 15, "ts": 1754735495836786, "dur": 310, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Cinemachine.dll (+2 others)"}}, {"pid": 12345, "tid": 15, "ts": 1754735495837122, "dur": 318854, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1754735496155985, "dur": 1504, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/com.crashkonijn.goap.demos.simple.dll (+pdb)"}}, {"pid": 12345, "tid": 15, "ts": 1754735496157490, "dur": 101, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1754735496158482, "dur": 103, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\GameSoft\\Unity\\Editor\\2022.3.38f1c1\\Editor\\Data\\Tools\\ilpp\\Unity.ILPP.Runner\\System.Security.Cryptography.Primitives.dll"}}, {"pid": 12345, "tid": 15, "ts": 1754735496157596, "dur": 1462, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.InputSystem.TestFramework.dll (+pdb)"}}, {"pid": 12345, "tid": 15, "ts": 1754735496159241, "dur": 140, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\GameSoft\\Unity\\Editor\\2022.3.38f1c1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.AppContext.dll"}}, {"pid": 12345, "tid": 15, "ts": 1754735496160180, "dur": 201, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\GameSoft\\Unity\\Editor\\2022.3.38f1c1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.CrashReportingModule.dll"}}, {"pid": 12345, "tid": 15, "ts": 1754735496160477, "dur": 921, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\GameSoft\\Unity\\Editor\\2022.3.38f1c1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Diagnostics.StackTrace.dll"}}, {"pid": 12345, "tid": 15, "ts": 1754735496159101, "dur": 2636, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.VisualScripting.SettingsProvider.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 15, "ts": 1754735496161917, "dur": 175, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1754735496162132, "dur": 63, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1754735496162308, "dur": 134, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1754735496162450, "dur": 320, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1754735496162783, "dur": 39000, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1754735495806841, "dur": 19565, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1754735495826410, "dur": 52, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\GameSoft\\Unity\\Editor\\2022.3.38f1c1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.ARModule.dll"}}, {"pid": 12345, "tid": 16, "ts": 1754735495826406, "dur": 58, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ARModule.dll_969BEA2E4A09BAD9.mvfrm"}}, {"pid": 12345, "tid": 16, "ts": 1754735495826508, "dur": 88, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.GIModule.dll_75656E38889B442A.mvfrm"}}, {"pid": 12345, "tid": 16, "ts": 1754735495826661, "dur": 80, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\GameSoft\\Unity\\Editor\\2022.3.38f1c1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.UnityWebRequestModule.dll"}}, {"pid": 12345, "tid": 16, "ts": 1754735495826660, "dur": 82, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityWebRequestModule.dll_55F435D8463A793C.mvfrm"}}, {"pid": 12345, "tid": 16, "ts": 1754735495826811, "dur": 79, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.VisualScripting.TextureAssets.dll_E1772EF4FEB70289.mvfrm"}}, {"pid": 12345, "tid": 16, "ts": 1754735495826895, "dur": 55, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/UnityEngine.UI.dll.mvfrm"}}, {"pid": 12345, "tid": 16, "ts": 1754735495827812, "dur": 72, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\GameSoft\\Unity\\Editor\\2022.3.38f1c1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Runtime.Extensions.dll"}}, {"pid": 12345, "tid": 16, "ts": 1754735495827892, "dur": 157, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\GameSoft\\Unity\\Editor\\2022.3.38f1c1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Runtime.Serialization.Xml.dll"}}, {"pid": 12345, "tid": 16, "ts": 1754735495828680, "dur": 118, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.ugui@1.0.0\\Runtime\\EventSystem\\UIElements\\PanelRaycaster.cs"}}, {"pid": 12345, "tid": 16, "ts": 1754735495826967, "dur": 2421, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/UnityEngine.UI.dll (+2 others)"}}, {"pid": 12345, "tid": 16, "ts": 1754735495829457, "dur": 259, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1754735495829716, "dur": 511, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1754735495830227, "dur": 440, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1754735495830668, "dur": 157, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1754735495830826, "dur": 486, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1754735495831312, "dur": 197, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1754735495831510, "dur": 203, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1754735495831713, "dur": 190, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1754735495831903, "dur": 226, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1754735495832129, "dur": 202, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1754735495832331, "dur": 210, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1754735495832541, "dur": 156, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1754735495832697, "dur": 162, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1754735495832859, "dur": 180, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1754735495833039, "dur": 340, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1754735495833379, "dur": 166, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1754735495833545, "dur": 377, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1754735495833924, "dur": 91, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/CrashKonijn.Goap.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 16, "ts": 1754735495834016, "dur": 57, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1754735495834077, "dur": 353, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.VSCode.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 16, "ts": 1754735495834430, "dur": 104, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1754735495834590, "dur": 71, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.VisualStudio.Editor.pdb"}}, {"pid": 12345, "tid": 16, "ts": 1754735495834661, "dur": 278, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1754735495834939, "dur": 131, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1754735495835070, "dur": 81, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.InputSystem.TestFramework.dll.mvfrm"}}, {"pid": 12345, "tid": 16, "ts": 1754735495835173, "dur": 54, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.InputSystem.TestFramework.dll.mvfrm"}}, {"pid": 12345, "tid": 16, "ts": 1754735495835259, "dur": 282, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.InputSystem.TestFramework.dll (+2 others)"}}, {"pid": 12345, "tid": 16, "ts": 1754735495835571, "dur": 228, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1754735495835799, "dur": 565, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1754735495836364, "dur": 1623, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1754735495837988, "dur": 51, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Assembly-CSharp-Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 16, "ts": 1754735495838062, "dur": 317891, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1754735496155956, "dur": 1533, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.RenderPipeline.Universal.ShaderLibrary.dll (+pdb)"}}, {"pid": 12345, "tid": 16, "ts": 1754735496157490, "dur": 331, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1754735496157828, "dur": 1382, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.VisualScripting.Core.dll (+pdb)"}}, {"pid": 12345, "tid": 16, "ts": 1754735496159211, "dur": 57, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1754735496160181, "dur": 205, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\GameSoft\\Unity\\Editor\\2022.3.38f1c1\\Editor\\Data\\Tools\\ilpp\\Unity.ILPP.Runner\\Unity.ILPP.Runner.exe"}}, {"pid": 12345, "tid": 16, "ts": 1754735496159276, "dur": 1605, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.CollabProxy.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 16, "ts": 1754735496160945, "dur": 1535, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/com.unity.cinemachine.editor.dll (+pdb)"}}, {"pid": 12345, "tid": 16, "ts": 1754735496162538, "dur": 39229, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1754735496207704, "dur": 1480, "ph": "X", "name": "ProfilerWriteOutput"}, {"pid": 35942, "tid": -1, "ph": "M", "name": "process_name", "args": {"name": "netcorerun.dll"}}, {"pid": 35942, "tid": -1, "ph": "M", "name": "process_sort_index", "args": {"sort_index": "-1"}}, {"pid": 35942, "tid": 1, "ph": "M", "name": "thread_name", "args": {"name": ""}}, {"pid": 35942, "tid": 1, "ts": 1754735495142093, "dur": 288669, "ph": "X", "name": "BuildProgram", "args": {}}, {"pid": 35942, "tid": 1, "ts": 1754735495142849, "dur": 46019, "ph": "X", "name": "BuildProgramContextConstructor", "args": {}}, {"pid": 35942, "tid": 1, "ts": 1754735495376937, "dur": 2675, "ph": "X", "name": "OutputData.Write", "args": {}}, {"pid": 35942, "tid": 1, "ts": 1754735495379614, "dur": 51131, "ph": "X", "name": "Backend.Write", "args": {}}, {"pid": 35942, "tid": 1, "ts": 1754735495380914, "dur": 31498, "ph": "X", "name": "JsonToString", "args": {}}, {"pid": 35942, "tid": 1, "ts": 1754735495437185, "dur": 1091, "ph": "X", "name": "", "args": {}}, {"pid": 35942, "tid": 1, "ts": 1754735495436640, "dur": 1860, "ph": "X", "name": "Write chrome-trace events", "args": {}}, {"cat": "", "pid": 12345, "tid": 0, "ts": 0, "ph": "M", "name": "process_name", "args": {"name": "bee_backend"}}, {"pid": 12345, "tid": 0, "ts": 1754735494774898, "dur": 1755, "ph": "X", "name": "DriverInitData", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1754735494776669, "dur": 1016, "ph": "X", "name": "RemoveStaleOutputs", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1754735494777851, "dur": 65, "ph": "X", "name": "<PERSON><PERSON>", "args": {"detail": "PrepareNodes"}}, {"pid": 12345, "tid": 0, "ts": 1754735494777916, "dur": 574, "ph": "X", "name": "BuildQueueInit", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1754735494778560, "dur": 61, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.Graphs.dll_34C604C212D62B6E.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1754735494780119, "dur": 189, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.TerrainPhysicsModule.dll_34719EFEEA08287A.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1754735494780335, "dur": 71, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.TextCoreTextEngineModule.dll_EF7BA45317B31323.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1754735494780627, "dur": 53, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityAnalyticsCommonModule.dll_3B4AA1572BDDA284.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1754735494782062, "dur": 69, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/UnityEngine.UI.dll.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1754735494782350, "dur": 108, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/UnityEditor.UI.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 0, "ts": 1754735494783353, "dur": 84, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Mathematics.dll (+2 others)"}}, {"pid": 12345, "tid": 0, "ts": 1754735494783454, "dur": 108, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Mathematics.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 0, "ts": 1754735494785607, "dur": 72, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.Searcher.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1754735494787023, "dur": 66, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.Mathematics.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1754735494787607, "dur": 74, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.VisualScripting.State.ref.dll_D91762B7076BE462.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1754735494787914, "dur": 53, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Universal.Editor.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 0, "ts": 1754735494788363, "dur": 52, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Timeline.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 0, "ts": 1754735494788417, "dur": 54, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Timeline.Editor.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 0, "ts": 1754735494788476, "dur": 86, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Timeline.Editor.rsp"}}, {"pid": 12345, "tid": 0, "ts": 1754735494788986, "dur": 51, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Core.ShaderLibrary.dll (+2 others)"}}, {"pid": 12345, "tid": 0, "ts": 1754735494789537, "dur": 84, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Universal.Shaders.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 0, "ts": 1754735494793789, "dur": 52, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.ShaderGraph.Utilities.dll"}}, {"pid": 12345, "tid": 0, "ts": 1754735494799996, "dur": 51, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.VisualScripting.State.pdb"}}, {"pid": 12345, "tid": 0, "ts": 1754735494800270, "dur": 70, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Cinemachine.dll.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1754735494803351, "dur": 55, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/com.crashkonijn.goap.demos.simple.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 0, "ts": 1754735494778517, "dur": 26037, "ph": "X", "name": "EnqueueRequestedNodes", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1754735494804568, "dur": 319, "ph": "X", "name": "WaitForBuildFinished", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1754735494804888, "dur": 288, "ph": "X", "name": "JoinBuildThread", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1754735494805382, "dur": 55, "ph": "X", "name": "BuildQueueDestroyTail", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1754735494805453, "dur": 1484, "ph": "X", "name": "<PERSON><PERSON>", "args": {"detail": "Write AllBuiltNodes"}}, {"pid": 12345, "tid": 1, "ts": 1754735494778716, "dur": 25864, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1754735494804588, "dur": 293, "ph": "X", "name": "CheckDagSignatures", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1754735494778811, "dur": 25809, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1754735494804766, "dur": 65, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1754735494778743, "dur": 25849, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1754735494778792, "dur": 25817, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1754735494804880, "dur": 118, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.IMGUIModule.dll_31158FA896333743.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1754735494778852, "dur": 25782, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1754735494804836, "dur": 140, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ClusterRendererModule.dll_D863307DB92126C9.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1754735494778898, "dur": 25749, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1754735494804712, "dur": 52, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1754735494804821, "dur": 172, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.AIModule.dll_DD29252AECEC5C10.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1754735494778951, "dur": 25708, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1754735494804827, "dur": 63, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ClothModule.dll_5F9E4D3CAA1E8246.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1754735494778994, "dur": 25682, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1754735494779038, "dur": 25652, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1754735494779062, "dur": 25641, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1754735494804709, "dur": 53, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.dll_A3CA169B49A7C799.mvfrm"}}, {"pid": 12345, "tid": 10, "ts": 1754735494804763, "dur": 73, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1754735494779114, "dur": 25604, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1754735494804728, "dur": 67, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.PresetsUIModule.dll_A8B81DEC69E8E1CF.mvfrm"}}, {"pid": 12345, "tid": 12, "ts": 1754735494779162, "dur": 25577, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1754735494804807, "dur": 80, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.SceneTemplateModule.dll_188EDD6374EAFAAD.mvfrm"}}, {"pid": 12345, "tid": 13, "ts": 1754735494779196, "dur": 25557, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1754735494804760, "dur": 53, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.UIElementsModule.dll_613AE6E2D149BAF8.mvfrm"}}, {"pid": 12345, "tid": 14, "ts": 1754735494779239, "dur": 25537, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1754735494804880, "dur": 84, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ImageConversionModule.dll_44552E2E3B45DEBD.mvfrm"}}, {"pid": 12345, "tid": 15, "ts": 1754735494779274, "dur": 25514, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1754735494779316, "dur": 25483, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1754735494810621, "dur": 319, "ph": "X", "name": "ProfilerWriteOutput"}, {"pid": 3100, "tid": 24787, "ts": 1754735496218765, "dur": 1281, "ph": "X", "name": "Wait for external events", "args": {"First to finish": "backend2.traceevents"}}, {"pid": 3100, "tid": 24787, "ts": 1754735496220997, "dur": 32, "ph": "X", "name": "Wait for external events", "args": {"First to finish": "buildprogram0.traceevents"}}, {"pid": 3100, "tid": 24787, "ts": 1754735496221208, "dur": 17, "ph": "X", "name": "Wait for external events", "args": {"First to finish": "backend1.traceevents"}}, {"pid": 3100, "tid": 24787, "ts": 1754735496220128, "dur": 867, "ph": "X", "name": "backend2.traceevents", "args": {}}, {"pid": 3100, "tid": 24787, "ts": 1754735496221083, "dur": 124, "ph": "X", "name": "buildprogram0.traceevents", "args": {}}, {"pid": 3100, "tid": 24787, "ts": 1754735496221261, "dur": 110, "ph": "X", "name": "backend1.traceevents", "args": {}}, {"pid": 3100, "tid": 24787, "ts": 1754735496215923, "dur": 5921, "ph": "X", "name": "Write chrome-trace events", "args": {}}, {}]}