fileFormatVersion: 2
guid: b3135898f48cd944b968d52b3ab3e3df
timeCreated: **********
licenseType: Store
ModelImporter:
  serializedVersion: 18
  fileIDToRecycleName:
    100000: Glass_<PERSON>i_Mobile_forPiotr
    100002: <PERSON>_<PERSON>i_Mobile_forPiotr_Shard
    100004: Glass_Marini_Mobile_forPiotr_Shard.001
    100006: Glass_Marini_Mobile_forPiotr_Shard.002
    100008: Glass_Marini_Mobile_forPiotr_Shard.003
    100010: Glass_Marini_Mobile_forPiotr_Shard.004
    100012: Glass_<PERSON>i_Mobile_forPiotr_Shard.005
    100014: Glass_Martini_MOBILE_Broken01
    100016: //RootNode
    400000: Glass_Marini_Mobile_forPiotr
    400002: Glass_Marini_Mobile_forPiotr_Shard
    400004: Glass_Marini_Mobile_forPiotr_Shard.001
    400006: Glass_Marini_Mobile_forPiotr_Shard.002
    400008: Glass_Marini_Mobile_forPiotr_Shard.003
    400010: Glass_Marini_Mobile_forPiotr_Shard.004
    400012: Glass_Marini_Mobile_forPiotr_Shard.005
    400014: Glass_Martini_MOBILE_Broken01
    400016: //RootNode
    2300000: Glass_Marini_Mobile_forPiotr
    2300002: Glass_Marini_Mobile_forPiotr_Shard
    2300004: Glass_Marini_Mobile_forPiotr_Shard.001
    2300006: Glass_Marini_Mobile_forPiotr_Shard.002
    2300008: Glass_Marini_Mobile_forPiotr_Shard.003
    2300010: Glass_Marini_Mobile_forPiotr_Shard.004
    2300012: Glass_Marini_Mobile_forPiotr_Shard.005
    3300000: Glass_Marini_Mobile_forPiotr
    3300002: Glass_Marini_Mobile_forPiotr_Shard
    3300004: Glass_Marini_Mobile_forPiotr_Shard.001
    3300006: Glass_Marini_Mobile_forPiotr_Shard.002
    3300008: Glass_Marini_Mobile_forPiotr_Shard.003
    3300010: Glass_Marini_Mobile_forPiotr_Shard.004
    3300012: Glass_Marini_Mobile_forPiotr_Shard.005
    4300000: Glass_Marini_Mobile_forPiotr_Shard.005
    4300002: Glass_Marini_Mobile_forPiotr_Shard.004
    4300004: Glass_Marini_Mobile_forPiotr_Shard.003
    4300006: Glass_Marini_Mobile_forPiotr_Shard.002
    4300008: Glass_Marini_Mobile_forPiotr_Shard.001
    4300010: Glass_Marini_Mobile_forPiotr_Shard
    4300012: Glass_Marini_Mobile_forPiotr
  materials:
    importMaterials: 0
    materialName: 0
    materialSearch: 1
  animations:
    legacyGenerateAnimations: 4
    bakeSimulation: 0
    optimizeGameObjects: 0
    motionNodeName: 
    animationCompression: 1
    animationRotationError: .5
    animationPositionError: .5
    animationScaleError: .5
    animationWrapMode: 0
    extraExposedTransformPaths: []
    clipAnimations: []
    isReadable: 1
  meshes:
    lODScreenPercentages: []
    globalScale: 1
    meshCompression: 0
    addColliders: 0
    importBlendShapes: 1
    swapUVChannels: 0
    generateSecondaryUV: 0
    useFileUnits: 1
    optimizeMeshForGPU: 1
    keepQuads: 0
    weldVertices: 1
    secondaryUVAngleDistortion: 8
    secondaryUVAreaDistortion: 15.000001
    secondaryUVHardAngle: 88
    secondaryUVPackMargin: 4
    useFileScale: 1
  tangentSpace:
    normalSmoothAngle: 60
    splitTangentsAcrossUV: 1
    normalImportMode: 0
    tangentImportMode: 1
  importAnimation: 1
  copyAvatar: 0
  humanDescription:
    human: []
    skeleton: []
    armTwist: .5
    foreArmTwist: .5
    upperLegTwist: .5
    legTwist: .5
    armStretch: .0500000007
    legStretch: .0500000007
    feetSpacing: 0
    rootMotionBoneName: 
  lastHumanDescriptionAvatarSource: {instanceID: 0}
  animationType: 0
  additionalBone: 0
  userData: 
  assetBundleName: 
  assetBundleVariant: 
