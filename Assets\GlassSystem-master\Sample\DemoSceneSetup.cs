using UnityEngine;
using GlassSystem.Scripts;

namespace GlassSystem.Sample
{
    public class DemoSceneSetup : MonoBehaviour
    {
        [Header("Scene Setup")]
        public bool setupOnStart = true;
        public bool createFloor = true;
        public bool createWalls = true;
        
        [Header("Glass Settings")]
        public Material glassMaterial;
        public Material glassSideMaterial;
        public Mesh[] glassPatterns;
        
        [Header("Environment")]
        public Material floorMaterial;
        public Material wallMaterial;
        
        void Start()
        {
            if (setupOnStart)
            {
                SetupDemoScene();
            }
        }
        
        [ContextMenu("Setup Demo Scene")]
        public void SetupDemoScene()
        {
            // 加载默认资源
            LoadDefaultResources();
            
            // 创建环境
            if (createFloor) CreateFloor();
            if (createWalls) CreateWalls();
            
            // 创建玻璃窗户
            CreateGlassWindows();
            
            // 设置相机
            SetupCamera();
            
            // 设置光照
            SetupLighting();
        }
        
        void LoadDefaultResources()
        {
            if (glassMaterial == null)
            {
                glassMaterial = Resources.Load<Material>("Glass");
                if (glassMaterial == null)
                {
                    glassMaterial = CreateURPGlassMaterial("URP Glass Material");
                }
            }

            if (glassSideMaterial == null)
            {
                glassSideMaterial = Resources.Load<Material>("GlassSide");
                if (glassSideMaterial == null)
                {
                    glassSideMaterial = CreateURPMaterial("URP Glass Side Material");
                    glassSideMaterial.SetColor("_BaseColor", new Color(0.8f, 0.9f, 1f, 0.3f));
                    glassSideMaterial.SetFloat("_Metallic", 0.1f);
                    glassSideMaterial.SetFloat("_Smoothness", 0.9f);
                }
            }

            if (glassPatterns == null || glassPatterns.Length == 0)
            {
                glassPatterns = new Mesh[2];
                glassPatterns[0] = Resources.Load<Mesh>("GlassPattern1");
                glassPatterns[1] = Resources.Load<Mesh>("GlassPattern2");
            }
        }

        Material CreateURPMaterial(string materialName)
        {
            // 尝试不同的URP着色器名称
            string[] urpShaderNames = {
                "Universal Render Pipeline/Lit",
                "URP/Lit",
                "Shader Graphs/Lit"
            };

            Material mat = null;
            foreach (string shaderName in urpShaderNames)
            {
                Shader shader = Shader.Find(shaderName);
                if (shader != null)
                {
                    mat = new Material(shader);
                    mat.name = materialName;
                    break;
                }
            }

            // 如果找不到URP着色器，回退到标准着色器
            if (mat == null)
            {
                mat = new Material(Shader.Find("Standard"));
                mat.name = materialName + " (Standard Fallback)";
                Debug.LogWarning($"URP着色器未找到，使用标准着色器作为后备: {materialName}");
            }

            return mat;
        }

        Material CreateURPGlassMaterial(string materialName)
        {
            Material glassMat = CreateURPMaterial(materialName);

            // 设置玻璃属性
            glassMat.SetColor("_BaseColor", new Color(0.8f, 0.9f, 1f, 0.1f));
            glassMat.SetFloat("_Metallic", 0.0f);
            glassMat.SetFloat("_Smoothness", 0.95f);

            // 尝试启用透明模式
            if (glassMat.HasProperty("_Surface"))
            {
                glassMat.SetFloat("_Surface", 1); // 透明表面
                glassMat.SetFloat("_Blend", 0); // Alpha混合
            }

            // 设置渲染队列为透明
            glassMat.renderQueue = 3000;

            // 启用关键字
            glassMat.EnableKeyword("_SURFACE_TYPE_TRANSPARENT");
            glassMat.EnableKeyword("_ALPHAPREMULTIPLY_ON");

            return glassMat;
        }
        
        void CreateFloor()
        {
            GameObject floor = GameObject.CreatePrimitive(PrimitiveType.Plane);
            floor.name = "Floor";
            floor.transform.position = Vector3.zero;
            floor.transform.localScale = new Vector3(2, 1, 2);

            if (floorMaterial != null)
                floor.GetComponent<Renderer>().material = floorMaterial;
            else
            {
                Material mat = CreateURPMaterial("Floor Material");
                mat.SetColor("_BaseColor", new Color(0.8f, 0.8f, 0.8f, 1f));
                mat.SetFloat("_Metallic", 0.1f);
                mat.SetFloat("_Smoothness", 0.3f);
                floor.GetComponent<Renderer>().material = mat;
            }
        }
        
        void CreateWalls()
        {
            // 后墙
            GameObject backWall = GameObject.CreatePrimitive(PrimitiveType.Cube);
            backWall.name = "BackWall";
            backWall.transform.position = new Vector3(0, 2.5f, -5);
            backWall.transform.localScale = new Vector3(20, 5, 0.5f);
            SetWallMaterial(backWall);
            
            // 左墙
            GameObject leftWall = GameObject.CreatePrimitive(PrimitiveType.Cube);
            leftWall.name = "LeftWall";
            leftWall.transform.position = new Vector3(-10, 2.5f, 0);
            leftWall.transform.localScale = new Vector3(0.5f, 5, 10);
            SetWallMaterial(leftWall);
            
            // 右墙
            GameObject rightWall = GameObject.CreatePrimitive(PrimitiveType.Cube);
            rightWall.name = "RightWall";
            rightWall.transform.position = new Vector3(10, 2.5f, 0);
            rightWall.transform.localScale = new Vector3(0.5f, 5, 10);
            SetWallMaterial(rightWall);
        }
        
        void SetWallMaterial(GameObject wall)
        {
            if (wallMaterial != null)
                wall.GetComponent<Renderer>().material = wallMaterial;
            else
            {
                Material mat = CreateURPMaterial("Wall Material");
                mat.SetColor("_BaseColor", new Color(0.9f, 0.9f, 0.8f, 1f));
                mat.SetFloat("_Metallic", 0.0f);
                mat.SetFloat("_Smoothness", 0.2f);
                wall.GetComponent<Renderer>().material = mat;
            }
        }
        
        void CreateGlassWindows()
        {
            // 创建多个不同大小的玻璃窗户
            CreateGlassWindow("Window_Large", new Vector3(-3, 2, -4.5f), new Vector3(3, 2, 0.1f));
            CreateGlassWindow("Window_Medium", new Vector3(3, 2, -4.5f), new Vector3(2, 1.5f, 0.1f));
            CreateGlassWindow("Window_Small", new Vector3(0, 1, -4.5f), new Vector3(1, 1, 0.1f));
            
            // 侧面玻璃
            CreateGlassWindow("SideGlass_Left", new Vector3(-9.5f, 2, -2), new Vector3(0.1f, 2, 2));
            CreateGlassWindow("SideGlass_Right", new Vector3(9.5f, 2, 2), new Vector3(0.1f, 2, 2));
        }
        
        void CreateGlassWindow(string name, Vector3 position, Vector3 size)
        {
            GameObject glassWindow = new GameObject(name);
            glassWindow.transform.position = position;
            
            // 添加网格组件
            MeshFilter meshFilter = glassWindow.AddComponent<MeshFilter>();
            MeshRenderer meshRenderer = glassWindow.AddComponent<MeshRenderer>();
            BoxCollider collider = glassWindow.AddComponent<BoxCollider>();
            
            // 创建简单的矩形网格
            Mesh glassMesh = CreateRectangleMesh(size);
            meshFilter.mesh = glassMesh;
            
            // 设置材质
            Material[] materials = new Material[2];
            materials[0] = glassMaterial;
            materials[1] = glassSideMaterial;
            meshRenderer.materials = materials;
            
            // 设置碰撞器
            collider.size = size;
            
            // 添加玻璃面板组件
            GlassPanel glassPanel = glassWindow.AddComponent<GlassPanel>();
            glassPanel.Patterns = glassPatterns;
            glassPanel.health = Random.Range(1, 4);
        }
        
        Mesh CreateRectangleMesh(Vector3 size)
        {
            Mesh mesh = new Mesh();
            mesh.name = "Rectangle Glass";
            
            float halfX = size.x / 2;
            float halfY = size.y / 2;
            float halfZ = size.z / 2;
            
            // 顶点
            Vector3[] vertices = new Vector3[8];
            vertices[0] = new Vector3(-halfX, -halfY, halfZ);   // 前左下
            vertices[1] = new Vector3(halfX, -halfY, halfZ);    // 前右下
            vertices[2] = new Vector3(halfX, halfY, halfZ);     // 前右上
            vertices[3] = new Vector3(-halfX, halfY, halfZ);    // 前左上
            vertices[4] = new Vector3(-halfX, -halfY, -halfZ);  // 后左下
            vertices[5] = new Vector3(halfX, -halfY, -halfZ);   // 后右下
            vertices[6] = new Vector3(halfX, halfY, -halfZ);    // 后右上
            vertices[7] = new Vector3(-halfX, halfY, -halfZ);   // 后左上
            
            mesh.vertices = vertices;
            
            // UV坐标
            Vector2[] uvs = new Vector2[8];
            uvs[0] = new Vector2(0, 0);
            uvs[1] = new Vector2(1, 0);
            uvs[2] = new Vector2(1, 1);
            uvs[3] = new Vector2(0, 1);
            uvs[4] = new Vector2(1, 0);
            uvs[5] = new Vector2(0, 0);
            uvs[6] = new Vector2(0, 1);
            uvs[7] = new Vector2(1, 1);
            mesh.uv = uvs;
            
            // 子网格
            mesh.subMeshCount = 2;
            
            // 主表面（前后面）
            int[] mainTriangles = new int[12];
            mainTriangles[0] = 0; mainTriangles[1] = 2; mainTriangles[2] = 1;
            mainTriangles[3] = 0; mainTriangles[4] = 3; mainTriangles[5] = 2;
            mainTriangles[6] = 4; mainTriangles[7] = 5; mainTriangles[8] = 6;
            mainTriangles[9] = 4; mainTriangles[10] = 6; mainTriangles[11] = 7;
            
            // 边缘
            int[] edgeTriangles = new int[24];
            // 底边
            edgeTriangles[0] = 0; edgeTriangles[1] = 1; edgeTriangles[2] = 5;
            edgeTriangles[3] = 0; edgeTriangles[4] = 5; edgeTriangles[5] = 4;
            // 右边
            edgeTriangles[6] = 1; edgeTriangles[7] = 2; edgeTriangles[8] = 6;
            edgeTriangles[9] = 1; edgeTriangles[10] = 6; edgeTriangles[11] = 5;
            // 顶边
            edgeTriangles[12] = 2; edgeTriangles[13] = 3; edgeTriangles[14] = 7;
            edgeTriangles[15] = 2; edgeTriangles[16] = 7; edgeTriangles[17] = 6;
            // 左边
            edgeTriangles[18] = 3; edgeTriangles[19] = 0; edgeTriangles[20] = 4;
            edgeTriangles[21] = 3; edgeTriangles[22] = 4; edgeTriangles[23] = 7;
            
            mesh.SetTriangles(mainTriangles, 0);
            mesh.SetTriangles(edgeTriangles, 1);
            
            mesh.RecalculateNormals();
            mesh.RecalculateBounds();
            
            return mesh;
        }
        
        void SetupCamera()
        {
            Camera mainCamera = Camera.main;
            if (mainCamera == null)
                mainCamera = FindObjectOfType<Camera>();

            if (mainCamera != null)
            {
                mainCamera.transform.position = new Vector3(0, 2, 5);
                mainCamera.transform.rotation = Quaternion.identity;

                // 移除旧的飞行相机（如果存在）
                FreeFlyCamera oldFlyCamera = mainCamera.GetComponent<FreeFlyCamera>();
                if (oldFlyCamera != null)
                    DestroyImmediate(oldFlyCamera);

                ImprovedFlyCamera oldImprovedCamera = mainCamera.GetComponent<ImprovedFlyCamera>();
                if (oldImprovedCamera != null)
                    DestroyImmediate(oldImprovedCamera);

                // 添加简化版飞行相机
                if (mainCamera.GetComponent<SimpleFlyCamera>() == null)
                    mainCamera.gameObject.AddComponent<SimpleFlyCamera>();

                // 添加演示枪
                if (mainCamera.GetComponent<DemoGun>() == null)
                    mainCamera.gameObject.AddComponent<DemoGun>();
            }
        }
        
        void SetupLighting()
        {
            Light directionalLight = FindObjectOfType<Light>();
            if (directionalLight != null)
            {
                directionalLight.type = LightType.Directional;
                directionalLight.intensity = 1.2f;
                directionalLight.shadows = LightShadows.Soft;
                directionalLight.transform.rotation = Quaternion.Euler(50, -30, 0);
            }
        }
    }
}
