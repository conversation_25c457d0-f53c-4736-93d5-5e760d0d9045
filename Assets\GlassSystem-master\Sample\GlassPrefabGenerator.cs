using UnityEngine;
using GlassSystem.Scripts;

namespace GlassSystem.Sample
{
    [System.Serializable]
    public class GlassConfiguration
    {
        public string name;
        public Vector3 size = Vector3.one;
        public Material glassMaterial;
        public Material edgeMaterial;
        public Mesh[] patterns;
        public int health = 2;
    }

    public class GlassPrefabGenerator : MonoBehaviour
    {
        [Header("Glass Configurations")]
        public GlassConfiguration[] glassConfigs;
        
        [Header("Default Resources")]
        public Material defaultGlassMaterial;
        public Material defaultEdgeMaterial;
        public Mesh[] defaultPatterns;
        
        [Header("Generation Settings")]
        public bool generateOnStart = true;
        public Transform parentTransform;
        
        void Start()
        {
            if (generateOnStart)
            {
                GenerateAllGlassPrefabs();
            }
        }
        
        [ContextMenu("Generate All Glass Prefabs")]
        public void GenerateAllGlassPrefabs()
        {
            for (int i = 0; i < glassConfigs.Length; i++)
            {
                GenerateGlassPrefab(glassConfigs[i], i);
            }
        }
        
        public GameObject GenerateGlassPrefab(GlassConfiguration config, int index)
        {
            // 创建基础游戏对象
            GameObject glassObject = new GameObject($"Glass_{config.name}_{index}");
            
            if (parentTransform != null)
                glassObject.transform.SetParent(parentTransform);
            
            // 添加基础组件
            MeshFilter meshFilter = glassObject.AddComponent<MeshFilter>();
            MeshRenderer meshRenderer = glassObject.AddComponent<MeshRenderer>();
            BoxCollider collider = glassObject.AddComponent<BoxCollider>();
            
            // 创建玻璃网格
            Mesh glassMesh = CreateGlassMesh(config.size);
            meshFilter.mesh = glassMesh;
            
            // 设置材质
            Material[] materials = new Material[2];
            materials[0] = config.glassMaterial != null ? config.glassMaterial : defaultGlassMaterial;
            materials[1] = config.edgeMaterial != null ? config.edgeMaterial : defaultEdgeMaterial;
            meshRenderer.materials = materials;
            
            // 设置碰撞器
            collider.size = config.size;
            
            // 添加玻璃组件
            GlassPanel glassPanel = glassObject.AddComponent<GlassPanel>();
            glassPanel.health = config.health;
            
            // 设置破碎图案
            Mesh[] patterns = config.patterns != null && config.patterns.Length > 0 
                            ? config.patterns 
                            : defaultPatterns;
            glassPanel.Patterns = patterns;
            
            // 添加刚体（可选）
            Rigidbody rb = glassObject.AddComponent<Rigidbody>();
            rb.isKinematic = true; // 默认为运动学刚体
            
            return glassObject;
        }
        
        Mesh CreateGlassMesh(Vector3 size)
        {
            Mesh mesh = new Mesh();
            mesh.name = "Generated Glass Mesh";
            
            // 定义顶点（矩形玻璃）
            Vector3[] vertices = new Vector3[8];
            
            // 前面4个顶点
            vertices[0] = new Vector3(-size.x/2, -size.y/2, size.z/2);
            vertices[1] = new Vector3(size.x/2, -size.y/2, size.z/2);
            vertices[2] = new Vector3(size.x/2, size.y/2, size.z/2);
            vertices[3] = new Vector3(-size.x/2, size.y/2, size.z/2);
            
            // 后面4个顶点
            vertices[4] = new Vector3(-size.x/2, -size.y/2, -size.z/2);
            vertices[5] = new Vector3(size.x/2, -size.y/2, -size.z/2);
            vertices[6] = new Vector3(size.x/2, size.y/2, -size.z/2);
            vertices[7] = new Vector3(-size.x/2, size.y/2, -size.z/2);
            
            mesh.vertices = vertices;
            
            // 定义UV坐标
            Vector2[] uvs = new Vector2[8];
            uvs[0] = new Vector2(0, 0);
            uvs[1] = new Vector2(1, 0);
            uvs[2] = new Vector2(1, 1);
            uvs[3] = new Vector2(0, 1);
            uvs[4] = new Vector2(1, 0);
            uvs[5] = new Vector2(0, 0);
            uvs[6] = new Vector2(0, 1);
            uvs[7] = new Vector2(1, 1);
            
            mesh.uv = uvs;
            
            // 定义子网格
            mesh.subMeshCount = 2;
            
            // 主表面三角形（前面和后面）
            int[] mainTriangles = new int[12];
            // 前面
            mainTriangles[0] = 0; mainTriangles[1] = 2; mainTriangles[2] = 1;
            mainTriangles[3] = 0; mainTriangles[4] = 3; mainTriangles[5] = 2;
            // 后面
            mainTriangles[6] = 4; mainTriangles[7] = 5; mainTriangles[8] = 6;
            mainTriangles[9] = 4; mainTriangles[10] = 6; mainTriangles[11] = 7;
            
            // 边缘三角形
            int[] edgeTriangles = new int[24];
            // 底边
            edgeTriangles[0] = 0; edgeTriangles[1] = 1; edgeTriangles[2] = 5;
            edgeTriangles[3] = 0; edgeTriangles[4] = 5; edgeTriangles[5] = 4;
            // 右边
            edgeTriangles[6] = 1; edgeTriangles[7] = 2; edgeTriangles[8] = 6;
            edgeTriangles[9] = 1; edgeTriangles[10] = 6; edgeTriangles[11] = 5;
            // 顶边
            edgeTriangles[12] = 2; edgeTriangles[13] = 3; edgeTriangles[14] = 7;
            edgeTriangles[15] = 2; edgeTriangles[16] = 7; edgeTriangles[17] = 6;
            // 左边
            edgeTriangles[18] = 3; edgeTriangles[19] = 0; edgeTriangles[20] = 4;
            edgeTriangles[21] = 3; edgeTriangles[22] = 4; edgeTriangles[23] = 7;
            
            mesh.SetTriangles(mainTriangles, 0);
            mesh.SetTriangles(edgeTriangles, 1);
            
            mesh.RecalculateNormals();
            mesh.RecalculateBounds();
            
            return mesh;
        }
        
        [ContextMenu("Create Window Glass")]
        public void CreateWindowGlass()
        {
            GlassConfiguration config = new GlassConfiguration
            {
                name = "Window",
                size = new Vector3(2f, 3f, 0.1f),
                health = 1
            };
            GenerateGlassPrefab(config, 0);
        }
        
        [ContextMenu("Create Large Panel")]
        public void CreateLargePanel()
        {
            GlassConfiguration config = new GlassConfiguration
            {
                name = "LargePanel",
                size = new Vector3(4f, 2f, 0.1f),
                health = 3
            };
            GenerateGlassPrefab(config, 1);
        }
        
        [ContextMenu("Create Small Glass")]
        public void CreateSmallGlass()
        {
            GlassConfiguration config = new GlassConfiguration
            {
                name = "Small",
                size = new Vector3(1f, 1f, 0.05f),
                health = 1
            };
            GenerateGlassPrefab(config, 2);
        }
    }
}
