using UnityEngine;
using UnityEngine.Rendering;

namespace GlassSystem.Sample
{
    /// <summary>
    /// URP设置检查器 - 检测当前渲染管线并提供设置建议
    /// </summary>
    public class URPSetupChecker : MonoBehaviour
    {
        [Header("检查设置")]
        public bool checkOnStart = true;
        public bool showDetailedLog = true;
        
        [Header("检查结果")]
        [SerializeField] private bool isURPActive = false;
        [SerializeField] private string currentPipeline = "Unknown";
        [SerializeField] private string[] availableURPShaders;
        
        void Start()
        {
            if (checkOnStart)
            {
                CheckURPSetup();
            }
        }
        
        [ContextMenu("检查URP设置")]
        public void CheckURPSetup()
        {
            Debug.Log("=== Glass System URP设置检查 ===");
            
            // 检查当前渲染管线
            CheckCurrentRenderPipeline();
            
            // 检查URP着色器可用性
            CheckURPShaderAvailability();
            
            // 检查URP资源
            CheckURPAssets();
            
            // 提供设置建议
            ProvideSetupRecommendations();
            
            Debug.Log("=== URP设置检查完成 ===");
        }
        
        void CheckCurrentRenderPipeline()
        {
            RenderPipelineAsset currentRP = GraphicsSettings.renderPipelineAsset;
            
            if (currentRP == null)
            {
                currentPipeline = "Built-in Render Pipeline";
                isURPActive = false;
                if (showDetailedLog)
                    Debug.Log("当前使用: 内置渲染管线 (Built-in Render Pipeline)");
            }
            else
            {
                currentPipeline = currentRP.GetType().Name;
                isURPActive = currentPipeline.Contains("Universal") || currentPipeline.Contains("URP");
                
                if (showDetailedLog)
                {
                    Debug.Log($"当前使用: {currentPipeline}");
                    Debug.Log($"URP状态: {(isURPActive ? "✓ 已启用" : "✗ 未启用")}");
                }
            }
        }
        
        void CheckURPShaderAvailability()
        {
            string[] urpShaderNames = {
                "Universal Render Pipeline/Lit",
                "Universal Render Pipeline/Simple Lit",
                "Universal Render Pipeline/Unlit",
                "URP/Lit",
                "Shader Graphs/Lit"
            };
            
            System.Collections.Generic.List<string> availableShaders = new System.Collections.Generic.List<string>();
            
            if (showDetailedLog)
                Debug.Log("--- URP着色器可用性检查 ---");
            
            foreach (string shaderName in urpShaderNames)
            {
                Shader shader = Shader.Find(shaderName);
                if (shader != null)
                {
                    availableShaders.Add(shaderName);
                    if (showDetailedLog)
                        Debug.Log($"✓ 可用: {shaderName}");
                }
                else
                {
                    if (showDetailedLog)
                        Debug.Log($"✗ 不可用: {shaderName}");
                }
            }
            
            availableURPShaders = availableShaders.ToArray();
            
            if (availableShaders.Count == 0)
            {
                Debug.LogWarning("警告: 未找到任何URP着色器！");
            }
            else
            {
                Debug.Log($"找到 {availableShaders.Count} 个可用的URP着色器");
            }
        }
        
        void CheckURPAssets()
        {
            if (showDetailedLog)
                Debug.Log("--- URP资源检查 ---");
            
            // 检查URP材质
            Material urpGlass = Resources.Load<Material>("URP_Glass");
            Material urpGlassSide = Resources.Load<Material>("URP_GlassSide");
            Material urpFloor = Resources.Load<Material>("URP_Floor");
            Material urpWall = Resources.Load<Material>("URP_Wall");
            
            if (urpGlass != null)
                Debug.Log("✓ 找到URP玻璃材质");
            else
                Debug.Log("✗ 未找到URP玻璃材质");
                
            if (urpGlassSide != null)
                Debug.Log("✓ 找到URP玻璃边缘材质");
            else
                Debug.Log("✗ 未找到URP玻璃边缘材质");
                
            if (urpFloor != null)
                Debug.Log("✓ 找到URP地面材质");
            else
                Debug.Log("✗ 未找到URP地面材质");
                
            if (urpWall != null)
                Debug.Log("✓ 找到URP墙壁材质");
            else
                Debug.Log("✗ 未找到URP墙壁材质");
        }
        
        void ProvideSetupRecommendations()
        {
            Debug.Log("--- 设置建议 ---");
            
            if (!isURPActive)
            {
                Debug.LogWarning("建议: 当前未使用URP渲染管线");
                Debug.Log("• 要启用URP，请在Project Settings > Graphics中设置URP资源");
                Debug.Log("• 或者继续使用内置渲染管线，脚本会自动回退到标准着色器");
            }
            else
            {
                Debug.Log("✓ URP渲染管线已正确配置");
                
                if (availableURPShaders.Length == 0)
                {
                    Debug.LogWarning("警告: URP已启用但找不到URP着色器");
                    Debug.Log("• 请确保URP包已正确安装");
                    Debug.Log("• 检查Package Manager中的Universal RP包");
                }
                else
                {
                    Debug.Log($"✓ 找到 {availableURPShaders.Length} 个URP着色器");
                }
            }
            
            // 材质创建建议
            Material urpGlass = Resources.Load<Material>("URP_Glass");
            if (urpGlass == null)
            {
                Debug.Log("建议: 创建URP材质");
                Debug.Log("• 添加URPMaterialCreator脚本并运行'创建所有URP材质'");
                Debug.Log("• 或者在编辑器中使用'在编辑器中创建材质资源'功能");
            }
        }
        
        [ContextMenu("创建URP材质")]
        public void CreateURPMaterials()
        {
            URPMaterialCreator materialCreator = GetComponent<URPMaterialCreator>();
            if (materialCreator == null)
            {
                materialCreator = gameObject.AddComponent<URPMaterialCreator>();
            }
            
            materialCreator.CreateAllURPMaterials();
            Debug.Log("URP材质创建完成");
        }
        
        [ContextMenu("自动修复URP设置")]
        public void AutoFixURPSetup()
        {
            Debug.Log("开始自动修复URP设置...");
            
            // 检查当前状态
            CheckURPSetup();
            
            // 如果没有URP材质，创建它们
            Material urpGlass = Resources.Load<Material>("URP_Glass");
            if (urpGlass == null)
            {
                Debug.Log("创建缺失的URP材质...");
                CreateURPMaterials();
            }
            
            // 检查演示场景设置
            CompleteDemoSetup demoSetup = FindObjectOfType<CompleteDemoSetup>();
            if (demoSetup == null)
            {
                Debug.Log("添加演示场景设置脚本...");
                GameObject setupGO = new GameObject("Complete Demo Setup");
                setupGO.AddComponent<CompleteDemoSetup>();
            }
            
            Debug.Log("URP设置自动修复完成！");
        }
        
        // 公共方法供其他脚本调用
        public bool IsURPActive()
        {
            CheckCurrentRenderPipeline();
            return isURPActive;
        }
        
        public string[] GetAvailableURPShaders()
        {
            CheckURPShaderAvailability();
            return availableURPShaders;
        }
        
        public string GetCurrentPipeline()
        {
            CheckCurrentRenderPipeline();
            return currentPipeline;
        }
        
        void OnGUI()
        {
            // 在游戏视图中显示URP状态
            if (Application.isPlaying)
            {
                GUI.Box(new Rect(10, Screen.height - 80, 200, 70), "URP状态");
                GUI.Label(new Rect(20, Screen.height - 60, 180, 20), $"渲染管线: {currentPipeline}");
                GUI.Label(new Rect(20, Screen.height - 40, 180, 20), $"URP状态: {(isURPActive ? "已启用" : "未启用")}");
                GUI.Label(new Rect(20, Screen.height - 20, 180, 20), $"URP着色器: {availableURPShaders?.Length ?? 0}个");
            }
        }
    }
}
