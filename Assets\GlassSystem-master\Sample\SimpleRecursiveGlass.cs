using System.Collections.Generic;
using UnityEngine;
using GlassSystem.Scripts;

namespace GlassSystem.Sample
{
    /// <summary>
    /// 简单递归玻璃 - 避免复杂继承问题的完整解决方案
    /// </summary>
    public class SimpleRecursiveGlass : MonoBehaviour
    {
        [Header("破碎设置")]
        public Mesh[] Patterns;
        public int health = 2;
        
        [Header("递归破碎设置")]
        public bool enableRecursiveBreaking = true;
        public int maxRecursionDepth = 3;
        public float minShardSizeForBreaking = 0.05f;
        public bool autoDestroySmallShards = true;
        public float shardLifetime = 8f;
        
        [Header("物理设置")]
        public float breakForceMultiplier = 1f;
        public float shardMassMultiplier = 1f;
        
        private List<GameObject> managedShards = new List<GameObject>();
        private bool hasBeenBroken = false;
        
        public void Break(Vector3 breakPosition, Vector3 originVector, int patternIndex = -1, float rotation = float.NaN)
        {
            if (hasBeenBroken)
            {
                Debug.Log("这个玻璃已经破碎过了");
                return;
            }
            
            hasBeenBroken = true;
            Debug.Log("破碎玻璃 - 创建递归碎片");
            
            // 简单的破碎实现：创建多个可递归破碎的碎片
            CreateRecursiveShards(breakPosition, originVector, 0);
            
            // 隐藏原始玻璃
            var renderer = GetComponent<MeshRenderer>();
            if (renderer != null)
                renderer.enabled = false;
                
            var collider = GetComponent<MeshCollider>();
            if (collider != null)
                collider.enabled = false;
        }
        
        void CreateRecursiveShards(Vector3 breakPosition, Vector3 originVector, int depth)
        {
            int shardCount = Random.Range(4, 8);
            var materials = GetComponent<Renderer>().sharedMaterials;
            
            for (int i = 0; i < shardCount; i++)
            {
                // 创建碎片
                GameObject shardGO = GameObject.CreatePrimitive(PrimitiveType.Cube);
                shardGO.name = $"RecursiveShard_D{depth}_{i}";
                shardGO.tag = gameObject.tag;
                
                // 随机位置和大小
                Vector3 randomOffset = Random.insideUnitSphere * 0.8f;
                shardGO.transform.position = transform.position + randomOffset;
                shardGO.transform.rotation = Random.rotation;
                
                float sizeReduction = Random.Range(0.2f, 0.6f);
                Vector3 originalScale = transform.localScale;
                shardGO.transform.localScale = originalScale * sizeReduction;
                shardGO.transform.parent = transform.parent;
                
                // 设置材质
                var renderer = shardGO.GetComponent<Renderer>();
                if (renderer != null && materials != null && materials.Length > 0)
                {
                    renderer.material = materials[0];
                }
                
                // 添加物理效果
                var rb = shardGO.AddComponent<Rigidbody>();
                float shardSurface = shardGO.transform.localScale.x * shardGO.transform.localScale.y;
                rb.mass = shardSurface * shardMassMultiplier;
                rb.collisionDetectionMode = CollisionDetectionMode.Continuous;
                
                // 添加随机力
                Vector3 randomForce = (originVector + Random.insideUnitSphere * 0.5f).normalized;
                float forceReduction = Mathf.Pow(0.8f, depth);
                rb.AddForce(randomForce * breakForceMultiplier * forceReduction * Random.Range(50f, 200f));
                
                // 决定是否可以继续破碎
                bool canBreakFurther = enableRecursiveBreaking && 
                                     shardSurface >= minShardSizeForBreaking && 
                                     depth < maxRecursionDepth;
                
                if (canBreakFurther)
                {
                    // 添加递归破碎组件
                    SimpleRecursiveShardComponent shardComponent = shardGO.AddComponent<SimpleRecursiveShardComponent>();
                    shardComponent.Initialize(this, depth + 1, Patterns);
                    shardComponent.maxRecursionDepth = maxRecursionDepth;
                    shardComponent.minShardSizeForBreaking = minShardSizeForBreaking;
                    shardComponent.enableRecursiveBreaking = enableRecursiveBreaking;
                    shardComponent.breakForceMultiplier = breakForceMultiplier;
                    shardComponent.shardMassMultiplier = shardMassMultiplier;
                }
                
                // 管理碎片
                managedShards.Add(shardGO);
                
                // 自动销毁小碎片
                if (autoDestroySmallShards && !canBreakFurther)
                {
                    Destroy(shardGO, shardLifetime);
                }
            }
        }
        
        public void OnShardDestroyed(GameObject shard)
        {
            managedShards.Remove(shard);
            
            if (health > 0)
            {
                health -= 1;
                if (health == 0)
                {
                    // 让所有剩余碎片下落
                    foreach (GameObject managedShard in managedShards)
                    {
                        if (managedShard != null)
                        {
                            var rb = managedShard.GetComponent<Rigidbody>();
                            if (rb != null)
                            {
                                rb.AddForce(Vector3.down * 5f, ForceMode.Impulse);
                            }
                        }
                    }
                }
            }
        }
        
        public void OnNewShard(GameObject shard)
        {
            managedShards.Add(shard);
        }
        
        // 公共配置方法
        public void SetRecursiveBreaking(bool enabled)
        {
            enableRecursiveBreaking = enabled;
        }
        
        public void SetMaxRecursionDepth(int depth)
        {
            maxRecursionDepth = depth;
        }
        
        public void SetMinShardSize(float size)
        {
            minShardSizeForBreaking = size;
        }
        
        public void SetAutoDestroy(bool enabled)
        {
            autoDestroySmallShards = enabled;
        }
        
        // 调试信息
        void OnGUI()
        {
            if (Application.isPlaying && managedShards != null)
            {
                GUI.Box(new Rect(10, Screen.height - 120, 200, 100), "简单递归玻璃状态");
                GUI.Label(new Rect(20, Screen.height - 100, 180, 20), $"管理的碎片: {managedShards.Count}");
                GUI.Label(new Rect(20, Screen.height - 80, 180, 20), $"递归破碎: {(enableRecursiveBreaking ? "启用" : "禁用")}");
                GUI.Label(new Rect(20, Screen.height - 60, 180, 20), $"最大深度: {maxRecursionDepth}");
                GUI.Label(new Rect(20, Screen.height - 40, 180, 20), $"生命值: {health}");
            }
        }
    }
    
    /// <summary>
    /// 简单递归碎片组件
    /// </summary>
    public class SimpleRecursiveShardComponent : MonoBehaviour
    {
        public SimpleRecursiveGlass parentGlass;
        public int recursionDepth = 0;
        public int maxRecursionDepth = 3;
        public float minShardSizeForBreaking = 0.05f;
        public bool enableRecursiveBreaking = true;
        public float breakForceMultiplier = 1f;
        public float shardMassMultiplier = 1f;
        
        private Mesh[] patterns;
        private bool hasBeenBroken = false;
        
        public void Initialize(SimpleRecursiveGlass parent, int depth, Mesh[] glassPatterns)
        {
            parentGlass = parent;
            recursionDepth = depth;
            patterns = glassPatterns;
        }
        
        public void Break(Vector3 breakPosition, Vector3 originVector)
        {
            if (hasBeenBroken)
            {
                Debug.Log($"碎片已经破碎过了 (深度: {recursionDepth})");
                return;
            }
            
            if (!enableRecursiveBreaking)
            {
                Debug.Log("递归破碎已禁用");
                return;
            }
            
            if (recursionDepth >= maxRecursionDepth)
            {
                Debug.Log($"已达到最大递归深度 ({maxRecursionDepth})");
                return;
            }
            
            float shardSurface = GetShardSurface();
            if (shardSurface < minShardSizeForBreaking)
            {
                Debug.Log($"碎片太小无法破碎 (表面积: {shardSurface:F3})");
                return;
            }
            
            Debug.Log($"递归破碎碎片 - 深度: {recursionDepth}");
            
            hasBeenBroken = true;
            CreateSubShards(breakPosition, originVector);
            
            // 通知父玻璃
            if (parentGlass != null)
            {
                parentGlass.OnShardDestroyed(gameObject);
            }
            
            Destroy(gameObject);
        }
        
        void CreateSubShards(Vector3 breakPosition, Vector3 originVector)
        {
            int subShardCount = Random.Range(2, 5);
            var materials = GetComponent<Renderer>().sharedMaterials;
            
            for (int i = 0; i < subShardCount; i++)
            {
                GameObject subShardGO = GameObject.CreatePrimitive(PrimitiveType.Cube);
                subShardGO.name = $"SubShard_D{recursionDepth}_{i}";
                subShardGO.tag = gameObject.tag;
                
                Vector3 randomOffset = Random.insideUnitSphere * 0.3f;
                subShardGO.transform.position = transform.position + randomOffset;
                subShardGO.transform.rotation = Random.rotation;
                
                float sizeReduction = Random.Range(0.4f, 0.8f);
                subShardGO.transform.localScale = transform.localScale * sizeReduction;
                subShardGO.transform.parent = transform.parent;
                
                if (materials != null && materials.Length > 0)
                {
                    subShardGO.GetComponent<Renderer>().material = materials[0];
                }
                
                var rb = subShardGO.AddComponent<Rigidbody>();
                float subShardSurface = subShardGO.transform.localScale.x * subShardGO.transform.localScale.y;
                rb.mass = subShardSurface * shardMassMultiplier;
                
                Vector3 randomForce = (originVector + Random.insideUnitSphere * 0.3f).normalized;
                float forceReduction = Mathf.Pow(0.7f, recursionDepth);
                rb.AddForce(randomForce * breakForceMultiplier * forceReduction * Random.Range(30f, 100f));
                
                bool canBreakFurther = enableRecursiveBreaking && 
                                     subShardSurface >= minShardSizeForBreaking && 
                                     recursionDepth < maxRecursionDepth;
                
                if (canBreakFurther)
                {
                    SimpleRecursiveShardComponent subComponent = subShardGO.AddComponent<SimpleRecursiveShardComponent>();
                    subComponent.Initialize(parentGlass, recursionDepth + 1, patterns);
                    subComponent.maxRecursionDepth = maxRecursionDepth;
                    subComponent.minShardSizeForBreaking = minShardSizeForBreaking;
                    subComponent.enableRecursiveBreaking = enableRecursiveBreaking;
                    subComponent.breakForceMultiplier = breakForceMultiplier;
                    subComponent.shardMassMultiplier = shardMassMultiplier;
                    
                    if (parentGlass != null)
                    {
                        parentGlass.OnNewShard(subShardGO);
                    }
                }
                else
                {
                    Destroy(subShardGO, Random.Range(4f, 8f));
                }
            }
        }
        
        float GetShardSurface()
        {
            var bounds = GetComponent<Renderer>().bounds;
            return bounds.size.x * bounds.size.y;
        }
        
        void OnMouseDown()
        {
            Vector3 mousePos = Input.mousePosition;
            Ray ray = Camera.main.ScreenPointToRay(mousePos);
            
            if (Physics.Raycast(ray, out RaycastHit hit))
            {
                if (hit.collider.gameObject == gameObject)
                {
                    Break(hit.point, ray.direction * 100f);
                }
            }
        }
        
        void OnGUI()
        {
            if (Application.isPlaying && Camera.main != null)
            {
                Vector3 screenPos = Camera.main.WorldToScreenPoint(transform.position);
                if (screenPos.z > 0 && screenPos.x > 0 && screenPos.x < Screen.width && 
                    screenPos.y > 0 && screenPos.y < Screen.height)
                {
                    float distance = Vector3.Distance(Camera.main.transform.position, transform.position);
                    if (distance < 8f)
                    {
                        GUI.color = Color.cyan;
                        GUI.Label(new Rect(screenPos.x - 15, Screen.height - screenPos.y - 10, 30, 20), 
                                 $"D{recursionDepth}");
                        GUI.color = Color.white;
                    }
                }
            }
        }
    }
}
